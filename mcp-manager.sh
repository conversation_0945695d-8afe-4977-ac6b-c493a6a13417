#!/bin/bash

# MCP Manager - プロジェクト単位のMCPサーバー管理ツール
# 
# 使用方法:
#   mcp-manager install                    # カレントディレクトリのプロジェクトにMCPを登録
#   mcp-manager install /path/to/project   # 指定プロジェクトにMCPを登録
#   mcp-manager list                       # カレントプロジェクトのMCP一覧
#   mcp-manager list-all                   # 全プロジェクトのMCP一覧
#   mcp-manager remove <server-name>       # MCPサーバーを削除
#   mcp-manager init                       # プロジェクトにMCP設定ファイルを初期化

# 色付けのための定数
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# グローバル設定
GLOBAL_MCP_REGISTRY="$HOME/.claude/mcp-registry.json"
PROJECT_MCP_CONFIG=".claude/mcp-config.json"

# コマンドとプロジェクトディレクトリの取得
COMMAND="${1:-help}"
PROJECT_DIR="${2:-$(pwd)}"

# バックアップディレクトリ
BACKUP_DIR="$HOME/.claude/mcp-backups"

# プロジェクトディレクトリの絶対パス化
PROJECT_DIR=$(cd "$PROJECT_DIR" 2>/dev/null && pwd || echo "$PROJECT_DIR")

# プロジェクト名の取得（ディレクトリ名）
PROJECT_NAME=$(basename "$PROJECT_DIR")

# ヘルプ表示
show_help() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}        MCP Manager - Help              ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo "使用方法:"
    echo "  mcp-manager install [project-dir]     # MCPサーバーをインストール"
    echo "  mcp-manager list [project-dir]        # プロジェクトのMCP一覧"
    echo "  mcp-manager list-all                  # 全プロジェクトのMCP一覧"
    echo "  mcp-manager remove <server-name>      # MCPサーバーを削除"
    echo "  mcp-manager init [project-dir]        # MCP設定ファイルを初期化"
    echo ""
    echo "一括操作:"
    echo "  mcp-manager install-all               # 全プロジェクトのMCPを再インストール"
    echo "  mcp-manager remove-pattern <pattern>  # パターンに一致するMCPを削除"
    echo "  mcp-manager cleanup                   # 無効なMCP登録をクリーンアップ"
    echo ""
    echo "エクスポート/インポート:"
    echo "  mcp-manager export [project-dir]      # MCP設定をエクスポート"
    echo "  mcp-manager export-github [project]   # GitHub Actions用にエクスポート"
    echo "  mcp-manager import <file>             # MCP設定をインポート"
    echo ""
    echo "  mcp-manager help                      # このヘルプを表示"
    echo ""
    echo "プロジェクトディレクトリを省略した場合は、カレントディレクトリが使用されます。"
    echo ""
    echo "MCP設定ファイル:"
    echo "  各プロジェクトの .claude/mcp-config.json に設定を記述します。"
    echo "  グローバル設定: ~/.claude/.mcp.json"
}

# 依存関係のチェック
check_dependencies() {
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}エラー: jqコマンドが見つかりません。${NC}"
        echo "jqをインストールしてください："
        echo "  Ubuntu/Debian: sudo apt-get install jq"
        echo "  macOS: brew install jq"
        echo "  Windows: choco install jq"
        exit 1
    fi
    
    if ! command -v claude &> /dev/null; then
        echo -e "${RED}エラー: claudeコマンドが見つかりません。${NC}"
        echo "Claude Code CLIをインストールしてください。"
        exit 1
    fi
}

# レジストリの初期化
init_registry() {
    if [ ! -f "$GLOBAL_MCP_REGISTRY" ]; then
        mkdir -p "$(dirname "$GLOBAL_MCP_REGISTRY")"
        echo '{}' > "$GLOBAL_MCP_REGISTRY"
    fi
}

# プロジェクトのMCP設定を初期化
init_project() {
    local config_dir="$PROJECT_DIR/.claude"
    local config_file="$config_dir/mcp-config.json"
    
    if [ -f "$config_file" ]; then
        echo -e "${YELLOW}警告: MCP設定ファイルは既に存在します: $config_file${NC}"
        read -p "上書きしますか？ (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "初期化をキャンセルしました。"
            return
        fi
    fi
    
    mkdir -p "$config_dir"
    
    # サンプル設定ファイルの作成
    cat > "$config_file" << 'EOF'
{
  "mcpServers": {
    "example-server": {
      "command": "node",
      "args": ["path/to/server.js"],
      "env": {
        "API_KEY": "your-api-key"
      }
    }
  }
}
EOF
    
    echo -e "${GREEN}✓ MCP設定ファイルを作成しました: $config_file${NC}"
    echo ""
    echo "設定ファイルを編集して、プロジェクト固有のMCPサーバーを定義してください。"
}

# MCPサーバーのインストール
install_mcp() {
    local config_file="$PROJECT_DIR/$PROJECT_MCP_CONFIG"
    local global_config="$HOME/.claude/.mcp.json"
    local use_global=false
    
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}     MCP Servers Installation           ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo -e "${CYAN}プロジェクト: $PROJECT_NAME${NC}"
    echo -e "${CYAN}ディレクトリ: $PROJECT_DIR${NC}"
    echo ""
    
    # 設定ファイルの選択
    if [ -f "$config_file" ]; then
        echo -e "${YELLOW}プロジェクト固有の設定を使用: $config_file${NC}"
    elif [ -f "$global_config" ]; then
        echo -e "${YELLOW}グローバル設定を使用: $global_config${NC}"
        config_file="$global_config"
        use_global=true
    else
        echo -e "${RED}エラー: MCP設定ファイルが見つかりません。${NC}"
        echo ""
        echo "以下のいずれかを実行してください："
        echo "1. mcp-manager init でプロジェクト設定を初期化"
        echo "2. ~/.claude/.mcp.json にグローバル設定を作成"
        exit 1
    fi
    
    echo ""
    
    # JSONファイルの妥当性確認
    if ! jq empty "$config_file" 2>/dev/null; then
        echo -e "${RED}エラー: JSONファイルの形式が正しくありません。${NC}"
        exit 1
    fi
    
    # サーバー数のカウント
    local server_count=$(jq -r '.mcpServers | keys | length' "$config_file")
    echo -e "${YELLOW}検出されたMCPサーバー数: $server_count${NC}"
    echo ""
    
    # 成功・失敗カウンター
    local success_count=0
    local failed_count=0
    local installed_servers=()
    
    # 各MCPサーバーをループ処理
    while IFS= read -r entry; do
        # サーバー名と設定を取得
        local server_name=$(echo "$entry" | base64 -d | jq -r '.key')
        local config=$(echo "$entry" | base64 -d | jq -r '.value')
        
        # プロジェクト固有の名前にプレフィックスを付ける（グローバル設定の場合は除く）
        local registered_name="$server_name"
        if [ "$use_global" = false ]; then
            registered_name="${PROJECT_NAME}__${server_name}"
        fi
        
        echo -e "${BLUE}処理中: $server_name → $registered_name${NC}"
        
        # JSON設定の構築
        local command=$(echo "$config" | jq -r '.command')
        local args=$(echo "$config" | jq -c '.args // []')
        local env=$(echo "$config" | jq -c '.env // {}')
        
        # claude mcp add-json用のJSONを構築
        local json_config
        if [ "$env" != "{}" ]; then
            json_config=$(jq -n \
                --arg cmd "$command" \
                --argjson args "$args" \
                --argjson env "$env" \
                '{"type": "stdio", "command": $cmd, "args": $args, "env": $env}')
        else
            json_config=$(jq -n \
                --arg cmd "$command" \
                --argjson args "$args" \
                '{"type": "stdio", "command": $cmd, "args": $args}')
        fi
        
        # コマンドの実行
        echo -e "  実行: claude mcp add-json $registered_name ..."
        
        if claude mcp add-json "$registered_name" "$json_config" -s user 2>/dev/null; then
            echo -e "  ${GREEN}✓ 成功${NC}"
            ((success_count++))
            installed_servers+=("$registered_name")
        else
            echo -e "  ${RED}✗ 失敗${NC}"
            ((failed_count++))
        fi
        
        echo ""
    done < <(jq -r '.mcpServers | to_entries | .[] | @base64' "$config_file")
    
    # レジストリに登録
    if [ ${#installed_servers[@]} -gt 0 ] && [ "$use_global" = false ]; then
        init_registry
        
        # 既存のレジストリを読み込み
        local registry=$(cat "$GLOBAL_MCP_REGISTRY")
        
        # プロジェクト情報を更新
        local project_info=$(jq -n \
            --arg path "$PROJECT_DIR" \
            --arg name "$PROJECT_NAME" \
            --argjson servers "$(printf '%s\n' "${installed_servers[@]}" | jq -R . | jq -s .)" \
            --arg date "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
            '{path: $path, name: $name, servers: $servers, lastUpdated: $date}')
        
        # レジストリを更新
        echo "$registry" | jq --arg proj "$PROJECT_DIR" --argjson info "$project_info" \
            '.[$proj] = $info' > "$GLOBAL_MCP_REGISTRY"
    fi
    
    # 結果の表示
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}          インストール結果             ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}成功: $success_count${NC}"
    echo -e "${RED}失敗: $failed_count${NC}"
    echo ""
}

# プロジェクトのMCP一覧表示
list_project_mcp() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Project MCP Servers                 ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo -e "${CYAN}プロジェクト: $PROJECT_NAME${NC}"
    echo -e "${CYAN}ディレクトリ: $PROJECT_DIR${NC}"
    echo ""
    
    init_registry
    
    # レジストリから情報を取得
    local project_info=$(jq -r --arg proj "$PROJECT_DIR" '.[$proj] // empty' "$GLOBAL_MCP_REGISTRY")
    
    if [ -z "$project_info" ]; then
        echo -e "${YELLOW}このプロジェクトにはMCPサーバーが登録されていません。${NC}"
        echo ""
        echo "mcp-manager install でMCPサーバーをインストールしてください。"
    else
        local servers=$(echo "$project_info" | jq -r '.servers[]')
        local last_updated=$(echo "$project_info" | jq -r '.lastUpdated')
        
        echo -e "${GREEN}登録済みMCPサーバー:${NC}"
        while IFS= read -r server; do
            echo "  • $server"
        done <<< "$servers"
        
        echo ""
        echo -e "${YELLOW}最終更新: $last_updated${NC}"
    fi
    
    # グローバルに登録されているMCPも表示
    echo ""
    echo -e "${BLUE}グローバルMCPサーバー:${NC}"
    claude mcp list 2>/dev/null | grep -v "${PROJECT_NAME}__" || echo "  なし"
}

# 全プロジェクトのMCP一覧表示
list_all_projects() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    All Projects MCP Registry           ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    init_registry
    
    local project_count=$(jq -r '. | length' "$GLOBAL_MCP_REGISTRY")
    
    if [ "$project_count" -eq 0 ]; then
        echo -e "${YELLOW}登録されているプロジェクトはありません。${NC}"
    else
        jq -r 'to_entries | .[] | "\n\(.value.name) [\(.key)]:\n  サーバー数: \(.value.servers | length)\n  最終更新: \(.value.lastUpdated)\n  サーバー: \(.value.servers | join(", "))"' "$GLOBAL_MCP_REGISTRY"
    fi
    
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Global MCP Servers (claude mcp)     ${NC}"
    echo -e "${BLUE}========================================${NC}"
    claude mcp list
}

# MCPサーバーの削除
remove_mcp() {
    local server_name="$2"
    
    if [ -z "$server_name" ]; then
        echo -e "${RED}エラー: サーバー名を指定してください。${NC}"
        echo "使用方法: mcp-manager remove <server-name>"
        exit 1
    fi
    
    echo -e "${YELLOW}MCPサーバーを削除: $server_name${NC}"
    
    if claude mcp remove "$server_name" 2>/dev/null; then
        echo -e "${GREEN}✓ 削除しました${NC}"
        
        # レジストリからも削除
        init_registry
        local temp_file=$(mktemp)
        jq --arg server "$server_name" \
            'to_entries | map(
                .value.servers = (.value.servers | map(select(. != $server)))
            ) | from_entries' "$GLOBAL_MCP_REGISTRY" > "$temp_file"
        mv "$temp_file" "$GLOBAL_MCP_REGISTRY"
    else
        echo -e "${RED}✗ 削除に失敗しました${NC}"
    fi
}

# 全プロジェクトのMCPを再インストール
install_all_projects() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Install All Projects MCPs           ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    init_registry
    
    local project_count=$(jq -r '. | length' "$GLOBAL_MCP_REGISTRY")
    if [ "$project_count" -eq 0 ]; then
        echo -e "${YELLOW}登録されているプロジェクトはありません。${NC}"
        return
    fi
    
    local success_count=0
    local failed_count=0
    
    # 各プロジェクトを処理
    jq -r 'to_entries | .[] | .key' "$GLOBAL_MCP_REGISTRY" | while read -r project_path; do
        if [ -d "$project_path" ]; then
            echo -e "${CYAN}処理中: $project_path${NC}"
            cd "$project_path"
            install_mcp
            if [ $? -eq 0 ]; then
                ((success_count++))
            else
                ((failed_count++))
            fi
            echo ""
        else
            echo -e "${RED}スキップ: $project_path (ディレクトリが存在しません)${NC}"
            ((failed_count++))
        fi
    done
    
    echo -e "${GREEN}成功: $success_count プロジェクト${NC}"
    echo -e "${RED}失敗: $failed_count プロジェクト${NC}"
}

# パターンに一致するMCPサーバーを削除
remove_pattern() {
    local pattern="$2"
    
    if [ -z "$pattern" ]; then
        echo -e "${RED}エラー: パターンを指定してください。${NC}"
        echo "使用方法: mcp-manager remove-pattern <pattern>"
        exit 1
    fi
    
    echo -e "${YELLOW}パターン '$pattern' に一致するMCPサーバーを検索中...${NC}"
    echo ""
    
    # claudeコマンドからMCP一覧を取得してパターンマッチング
    local servers=$(claude mcp list 2>/dev/null | grep -E "$pattern" | awk '{print $1}')
    
    if [ -z "$servers" ]; then
        echo -e "${YELLOW}一致するMCPサーバーが見つかりませんでした。${NC}"
        return
    fi
    
    echo "以下のMCPサーバーが見つかりました："
    echo "$servers" | while read -r server; do
        echo "  • $server"
    done
    echo ""
    
    read -p "これらを削除しますか？ (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "削除をキャンセルしました。"
        return
    fi
    
    echo "$servers" | while read -r server; do
        if [ -n "$server" ]; then
            remove_mcp remove "$server"
        fi
    done
}

# 無効なMCP登録をクリーンアップ
cleanup_mcp() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    MCP Registry Cleanup                ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    init_registry
    
    # レジストリから無効なエントリを削除
    local temp_file=$(mktemp)
    local removed_count=0
    
    jq -r 'to_entries | .[] | .key' "$GLOBAL_MCP_REGISTRY" | while read -r project_path; do
        if [ ! -d "$project_path" ]; then
            echo -e "${YELLOW}削除: $project_path (ディレクトリが存在しません)${NC}"
            ((removed_count++))
        fi
    done | jq -R -s -c 'split("\n")[:-1]' > "$temp_file"
    
    # 有効なプロジェクトのみを保持
    jq 'to_entries | map(select(.key as $k | $k | test("^/"))) | 
        map(select(.key as $k | ($k | split("/") | length) > 1)) |
        from_entries' "$GLOBAL_MCP_REGISTRY" > "${GLOBAL_MCP_REGISTRY}.tmp"
    mv "${GLOBAL_MCP_REGISTRY}.tmp" "$GLOBAL_MCP_REGISTRY"
    
    echo ""
    echo -e "${GREEN}クリーンアップが完了しました。${NC}"
}

# MCP設定をエクスポート
export_mcp() {
    local config_file="$PROJECT_DIR/$PROJECT_MCP_CONFIG"
    local export_file="${PROJECT_NAME}-mcp-config.json"
    
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Export MCP Configuration            ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}エラー: MCP設定ファイルが見つかりません: $config_file${NC}"
        exit 1
    fi
    
    # エクスポート用のメタデータを追加
    jq --arg proj "$PROJECT_NAME" \
       --arg path "$PROJECT_DIR" \
       --arg date "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
       '. + {_metadata: {project: $proj, path: $path, exported: $date}}' \
       "$config_file" > "$export_file"
    
    echo -e "${GREEN}✓ エクスポートしました: $export_file${NC}"
    echo ""
    echo "このファイルを他のプロジェクトで使用するには："
    echo "  mcp-manager import $export_file"
}

# GitHub Actions用にエクスポート
export_github() {
    local project="${2:-$PROJECT_NAME}"
    local config_file="$PROJECT_DIR/$PROJECT_MCP_CONFIG"
    
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Export for GitHub Actions           ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}エラー: MCP設定ファイルが見つかりません: $config_file${NC}"
        exit 1
    fi
    
    # Base64エンコード
    local encoded=$(base64 -w 0 "$config_file" 2>/dev/null || base64 "$config_file")
    
    echo -e "${YELLOW}以下の内容をGitHub Secretsに追加してください:${NC}"
    echo ""
    echo "Secret名: MCP_CONFIG_${project^^}"
    echo "Secret値:"
    echo "$encoded"
    echo ""
    echo -e "${YELLOW}GitHub Actionsでの使用例:${NC}"
    echo ""
    cat << 'EOF'
- name: Restore MCP Configuration
  run: |
    echo "${{ secrets.MCP_CONFIG_PROJECT }}" | base64 -d > .claude/mcp-config.json
    
- name: Install MCP Servers
  run: |
    # mcp-managerのインストール
    curl -sSL https://raw.githubusercontent.com/your-repo/main/mcp-manager.sh -o mcp-manager.sh
    chmod +x mcp-manager.sh
    ./mcp-manager.sh install
EOF
}

# MCP設定をインポート
import_mcp() {
    local import_file="$2"
    
    if [ -z "$import_file" ]; then
        echo -e "${RED}エラー: インポートファイルを指定してください。${NC}"
        echo "使用方法: mcp-manager import <file>"
        exit 1
    fi
    
    if [ ! -f "$import_file" ]; then
        echo -e "${RED}エラー: ファイルが見つかりません: $import_file${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Import MCP Configuration            ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # メタデータの確認
    local metadata=$(jq -r '._metadata // empty' "$import_file")
    if [ -n "$metadata" ]; then
        echo "インポート元情報:"
        echo "$metadata" | jq -r '"  プロジェクト: \(.project)\n  エクスポート日時: \(.exported)"'
        echo ""
    fi
    
    local config_dir="$PROJECT_DIR/.claude"
    local config_file="$config_dir/mcp-config.json"
    
    mkdir -p "$config_dir"
    
    # メタデータを除いてコピー
    jq 'del(._metadata)' "$import_file" > "$config_file"
    
    echo -e "${GREEN}✓ インポートしました: $config_file${NC}"
    echo ""
    echo "MCPサーバーをインストールするには:"
    echo "  mcp-manager install"
}

# メイン処理
main() {
    check_dependencies
    
    case "$COMMAND" in
        install)
            install_mcp
            ;;
        list)
            list_project_mcp
            ;;
        list-all)
            list_all_projects
            ;;
        remove)
            remove_mcp "$@"
            ;;
        init)
            init_project
            ;;
        install-all)
            install_all_projects
            ;;
        remove-pattern)
            remove_pattern "$@"
            ;;
        cleanup)
            cleanup_mcp
            ;;
        export)
            export_mcp
            ;;
        export-github)
            export_github "$@"
            ;;
        import)
            import_mcp "$@"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}エラー: 不明なコマンド '$COMMAND'${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# スクリプトの実行
main "$@"