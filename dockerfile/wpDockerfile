# xdebug3.1.6を使用する
# 最新のaptパッケージをインストールする
# 最新のcomposerをインストールする
# vimをインストールする
# 最新のnpmをインストールする
FROM wordpress:php7.4

# ENV PHP_IDE_CONFIG="serverName=wp"

RUN apt-get update && apt-get install -y \
  vim \
  libzip-dev \
  unzip \
  && docker-php-ext-install zip

# php7の場合
RUN pecl install xdebug-3.1.6 && docker-php-ext-enable xdebug
# RUN pecl install xdebug-3.3.2 && docker-php-ext-enable xdebug

#composerのインストール
RUN curl -sS https://getcomposer.org/installer | php && mv composer.phar /usr/local/bin/composer

