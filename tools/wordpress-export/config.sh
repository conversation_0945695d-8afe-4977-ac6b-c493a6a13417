#!/bin/bash

# プロジェクト設定
PROJECT_NAME="${PROJECT_NAME:-wordpress-bizcan}"
CONTAINER_NAME="${CONTAINER_NAME:-${PROJECT_NAME}}"

# スクリプトのディレクトリを取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../" && pwd)"

# 環境変数から出力ディレクトリを設定（未設定の場合はプロジェクト内のoutputディレクトリを使用）
OUTPUT_BASE_DIR="${WP_EXPORT_OUTPUT_DIR:-${PROJECT_ROOT}/tools/wordpress-export/output}"

# 出力ディレクトリの構成
DB_OUTPUT_DIR="${OUTPUT_BASE_DIR}/database"
ACF_OUTPUT_DIR="${OUTPUT_BASE_DIR}/acf"
WP_OUTPUT_DIR="${OUTPUT_BASE_DIR}/wordpress"

# コンテナ側のパス設定
CONTAINER_BASE_DIR="/var/www/html"
CONTAINER_DOCS_DIR="${CONTAINER_BASE_DIR}/docs"
CONTAINER_DB_DIR="${CONTAINER_DOCS_DIR}/database"
CONTAINER_ACF_DIR="${CONTAINER_DOCS_DIR}/acf"
CONTAINER_WP_DIR="${CONTAINER_DOCS_DIR}/wordpress"
CONTAINER_TOOLS_DIR="${CONTAINER_BASE_DIR}/tools"

# デフォルト設定
SAMPLE_POSTS_COUNT="${SAMPLE_POSTS_COUNT:-3}"      # サンプル投稿の取得数
SAMPLE_TERMS_COUNT="${SAMPLE_TERMS_COUNT:-5}"      # サンプルタームの取得数
MASK_SENSITIVE_DATA="${MASK_SENSITIVE_DATA:-true}" # 機密情報のマスク有効化