<?php

/**
 * カスタム投稿タイプとタクソノミーエクスポートツール
 *
 * WordPressのカスタム投稿タイプとタクソノミーの定義をJSON形式とMarkdown形式でエクスポートします。
 * AIエージェントがデータ構造を理解するために使用します。
 *
 * 使用方法:
 * 1. WordPressのルートディレクトリで実行します
 * 2. php tools/export-custom-post-types.php [--json-only] [--include-builtin]
 *
 * オプション:
 * --json-only: JSONファイルのみ出力（Markdownファイルは出力しない）
 * --include-builtin: 組み込みの投稿タイプとタクソノミーも含める
 * --term-limit=N: サンプルタームの最大表示数（デフォルト: 5）
 */

// コマンドライン引数を解析
$options = getopt('', ['json-only', 'include-builtin', 'term-limit:']);
$jsonOnly = isset($options['json-only']);
$includeBuiltin = isset($options['include-builtin']);
$termLimit = isset($options['term-limit']) ? (int)$options['term-limit'] : (getenv('SAMPLE_TERMS_COUNT') ?: 5);

// WordPressの設定ファイルを読み込む
if (file_exists('/var/www/html/wp-load.php')) {
    require_once('/var/www/html/wp-load.php');
} else {
    die("WordPress設定ファイルが見つかりません。WordPressのルートディレクトリで実行してください。\n");
}

// 出力ディレクトリの作成
$output_dir = '/var/www/html/docs/wordpress';
if (!file_exists($output_dir)) {
    if (!mkdir($output_dir, 0755, true)) {
        die("エラー: 出力ディレクトリの作成に失敗しました: {$output_dir}\n");
    }
}

// 投稿タイプ一覧を取得
$args = $includeBuiltin ? [] : ['_builtin' => false];
$post_types = get_post_types($args, 'objects');

$cpt_info = [];
foreach ($post_types as $post_type) {
    $cpt_info[$post_type->name] = [
        'label' => $post_type->label,
        'description' => $post_type->description,
        'hierarchical' => $post_type->hierarchical,
        'public' => $post_type->public,
        'has_archive' => $post_type->has_archive,
        'menu_position' => $post_type->menu_position,
        'menu_icon' => $post_type->menu_icon,
        'supports' => get_all_post_type_supports($post_type->name),
        'taxonomies' => get_object_taxonomies($post_type->name),
        'rewrite' => $post_type->rewrite,
        'capability_type' => $post_type->capability_type,
        'labels' => (array) $post_type->labels,
    ];

    // 投稿数を取得
    $count = wp_count_posts($post_type->name);
    $cpt_info[$post_type->name]['count'] = [
        'publish' => $count->publish,
        'draft' => $count->draft,
        'total' => array_sum((array) $count)
    ];
}

// タクソノミー一覧を取得
$tax_args = $includeBuiltin ? [] : ['_builtin' => false];
$taxonomies = get_taxonomies($tax_args, 'objects');

$tax_info = [];
foreach ($taxonomies as $taxonomy) {
    $tax_info[$taxonomy->name] = [
        'label' => $taxonomy->label,
        'description' => $taxonomy->description,
        'hierarchical' => $taxonomy->hierarchical,
        'public' => $taxonomy->public,
        'object_type' => $taxonomy->object_type,
        'rewrite' => $taxonomy->rewrite,
        'labels' => (array) $taxonomy->labels,
    ];

    // タームの数を取得
    $terms = get_terms([
        'taxonomy' => $taxonomy->name,
        'hide_empty' => false
    ]);
    $tax_info[$taxonomy->name]['term_count'] = is_wp_error($terms) ? 0 : count($terms);

    // サンプルタームを取得
    if (!is_wp_error($terms) && !empty($terms)) {
        $sample_terms = array_slice($terms, 0, $termLimit);
        $tax_info[$taxonomy->name]['sample_terms'] = [];

        foreach ($sample_terms as $term) {
            $tax_info[$taxonomy->name]['sample_terms'][] = [
                'id' => $term->term_id,
                'name' => $term->name,
                'slug' => $term->slug,
                'count' => $term->count
            ];
        }
    }
}

$data = [
    'post_types' => $cpt_info,
    'taxonomies' => $tax_info
];

// JSONとして出力
$jsonContent = json_encode($data, JSON_PRETTY_PRINT);
if ($jsonContent === false) {
    die("エラー: JSON変換に失敗しました: " . json_last_error_msg() . "\n");
}
if (file_put_contents("{$output_dir}/custom-post-types.json", $jsonContent) === false) {
    die("エラー: JSONファイルの書き込みに失敗しました: {$output_dir}/custom-post-types.json\n");
}

// Markdownとしても出力（json-onlyオプションが指定されていない場合）
if (!$jsonOnly) {
    $markdown = "# カスタム投稿タイプとタクソノミー\n\n";
    $markdown .= "このドキュメントはAIエージェントがWordPressのデータ構造を理解するために自動生成されました。\n\n";
    $markdown .= "生成日時: " . date('Y-m-d H:i:s') . "\n\n";
    
    // オプション情報を追加
    if ($includeBuiltin) {
        $markdown .= "**注**: 組み込みの投稿タイプとタクソノミーも含まれています。\n\n";
    }

$markdown .= "## カスタム投稿タイプ\n\n";
foreach ($cpt_info as $name => $info) {
    $markdown .= "### {$info['label']} (`{$name}`)\n\n";
    $markdown .= "- 説明: {$info['description']}\n";
    $markdown .= "- 階層型: " . ($info['hierarchical'] ? 'はい' : 'いいえ') . "\n";
    $markdown .= "- 公開: " . ($info['public'] ? 'はい' : 'いいえ') . "\n";
    $markdown .= "- アーカイブあり: " . ($info['has_archive'] ? 'はい' : 'いいえ') . "\n";
    $markdown .= "- サポート機能: " . implode(', ', array_keys($info['supports'])) . "\n";
    $markdown .= "- 関連タクソノミー: " . implode(', ', $info['taxonomies']) . "\n";
    $markdown .= "- 投稿数: 公開 {$info['count']['publish']}, 下書き {$info['count']['draft']}, 合計 {$info['count']['total']}\n\n";
}

$markdown .= "## カスタムタクソノミー\n\n";
foreach ($tax_info as $name => $info) {
    $markdown .= "### {$info['label']} (`{$name}`)\n\n";
    $markdown .= "- 説明: {$info['description']}\n";
    $markdown .= "- 階層型: " . ($info['hierarchical'] ? 'はい' : 'いいえ') . "\n";
    $markdown .= "- 公開: " . ($info['public'] ? 'はい' : 'いいえ') . "\n";
    $markdown .= "- 関連投稿タイプ: " . implode(', ', $info['object_type']) . "\n";
    $markdown .= "- ターム数: {$info['term_count']}\n";

    if (!empty($info['sample_terms'])) {
        $markdown .= "\n#### サンプルターム\n\n";
        $markdown .= "| ID | 名前 | スラッグ | 投稿数 |\n";
        $markdown .= "| --- | --- | --- | --- |\n";

        foreach ($info['sample_terms'] as $term) {
            $markdown .= "| {$term['id']} | {$term['name']} | {$term['slug']} | {$term['count']} |\n";
        }
        $markdown .= "\n";
    }
    }

    if (file_put_contents("{$output_dir}/custom-post-types.md", $markdown) === false) {
        die("エラー: Markdownファイルの書き込みに失敗しました: {$output_dir}/custom-post-types.md\n");
    }
}

// 完了メッセージ
echo "カスタム投稿タイプとタクソノミーのエクスポートが完了しました。\n";
echo "出力ファイル:\n";
echo "- {$output_dir}/custom-post-types.json\n";
if (!$jsonOnly) {
    echo "- {$output_dir}/custom-post-types.md\n";
}
