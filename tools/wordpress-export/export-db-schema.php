<?php

/**
 * データベーススキーマエクスポートツール
 *
 * WordPressのデータベーススキーマ情報をJSON形式とMarkdown形式でエクスポートします。
 * AIエージェントがデータベース構造を理解するために使用します。
 *
 * 使用方法:
 * 1. WordPressのルートディレクトリで実行します
 * 2. php tools/export-db-schema.php [--json-only] [--table-prefix=PREFIX]
 *
 * オプション:
 * --json-only: JSONファイルのみ出力（Markdownファイルは出力しない）
 * --table-prefix=PREFIX: 特定のプレフィックスを持つテーブルのみ出力
 * --exclude-core: WordPressコアテーブルを除外
 * --compact: CREATE TABLE文を含めない（ファイルサイズ削減）
 */

// コマンドライン引数を解析
$options = getopt('', ['json-only', 'table-prefix:', 'exclude-core', 'compact']);
$jsonOnly = isset($options['json-only']);
$tablePrefix = $options['table-prefix'] ?? null;
$excludeCore = isset($options['exclude-core']);
$compact = isset($options['compact']);

// WordPressの設定ファイルを読み込む
if (file_exists('/var/www/html/wp-config.php')) {
    require_once('/var/www/html/wp-config.php');
} else {
    die("WordPress設定ファイルが見つかりません。WordPressのルートディレクトリで実行してください。\n");
}

// 出力ディレクトリの作成
$output_dir = '/var/www/html/docs/database';
if (!file_exists($output_dir)) {
    if (!mkdir($output_dir, 0755, true)) {
        die("エラー: 出力ディレクトリの作成に失敗しました: {$output_dir}\n");
    }
}

// データベース接続
try {
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($mysqli->connect_error) {
        throw new Exception($mysqli->connect_error);
    }
    $mysqli->set_charset("utf8");
} catch (Exception $e) {
    die("データベース接続エラー: " . $e->getMessage() . "\n");
}

// WordPressコアテーブルのリスト
$coreTableSuffixes = [
    'commentmeta', 'comments', 'links', 'options', 'postmeta', 'posts',
    'term_relationships', 'term_taxonomy', 'termmeta', 'terms', 'usermeta', 'users'
];

// テーブル一覧を取得
$tables = [];
$result = $mysqli->query("SHOW TABLES");
while ($row = $result->fetch_array(MYSQLI_NUM)) {
    $tableName = $row[0];
    
    // テーブルプレフィックスでフィルタリング
    if ($tablePrefix && strpos($tableName, $tablePrefix) !== 0) {
        continue;
    }
    
    // WordPressコアテーブルを除外
    if ($excludeCore) {
        $isCore = false;
        foreach ($coreTableSuffixes as $suffix) {
            if ($tableName === $table_prefix . $suffix) {
                $isCore = true;
                break;
            }
        }
        if ($isCore) {
            continue;
        }
    }
    
    $tables[] = $tableName;
}

// スキーマ情報を格納する配列
$schema = [];

// 各テーブルの構造を取得
foreach ($tables as $table) {
    // テーブル構造を取得
    $columns = [];
    $result = $mysqli->query("DESCRIBE `{$table}`");
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row;
    }

    // インデックス情報を取得
    $indexes = [];
    $result = $mysqli->query("SHOW INDEX FROM `{$table}`");
    while ($row = $result->fetch_assoc()) {
        $indexes[] = $row;
    }

    // スキーマ情報を格納
    $tableInfo = [
        'columns' => $columns,
        'indexes' => $indexes
    ];
    
    // コンパクトモードでない場合はCREATE TABLE文も取得
    if (!$compact) {
        $result = $mysqli->query("SHOW CREATE TABLE `{$table}`");
        $create_table = $result->fetch_assoc()['Create Table'];
        $tableInfo['create_table'] = $create_table;
    }
    
    $schema[$table] = $tableInfo;
}

// JSONとして出力
$jsonContent = json_encode($schema, JSON_PRETTY_PRINT);
if ($jsonContent === false) {
    die("エラー: JSON変換に失敗しました: " . json_last_error_msg() . "\n");
}
if (file_put_contents("{$output_dir}/db-schema.json", $jsonContent) === false) {
    die("エラー: JSONファイルの書き込みに失敗しました: {$output_dir}/db-schema.json\n");
}

// Markdownとしても出力（json-onlyオプションが指定されていない場合）
if (!$jsonOnly) {
    $markdown = "# WordPressデータベーススキーマ\n\n";
    $markdown .= "このドキュメントはAIエージェントがデータベース構造を理解するために自動生成されました。\n\n";
    $markdown .= "生成日時: " . date('Y-m-d H:i:s') . "\n\n";
    
    // オプション情報を追加
    if ($excludeCore) {
        $markdown .= "**注**: WordPressコアテーブルは除外されています。\n\n";
    }
    if ($tablePrefix) {
        $markdown .= "**フィルター**: プレフィックス `{$tablePrefix}` を持つテーブルのみ表示。\n\n";
    }

foreach ($schema as $table_name => $table_info) {
    $markdown .= "## {$table_name}\n\n";

    // カラム情報
    $markdown .= "### カラム\n\n";
    $markdown .= "| フィールド | タイプ | Null | キー | デフォルト | 追加情報 |\n";
    $markdown .= "| --- | --- | --- | --- | --- | --- |\n";

    foreach ($table_info['columns'] as $column) {
        $default = $column['Default'] !== null ? $column['Default'] : 'NULL';
        $markdown .= "| {$column['Field']} | {$column['Type']} | {$column['Null']} | {$column['Key']} | {$default} | {$column['Extra']} |\n";
    }

    // インデックス情報
    if (!empty($table_info['indexes'])) {
        $markdown .= "\n### インデックス\n\n";
        $markdown .= "| キー名 | カラム | 非ユニーク | タイプ |\n";
        $markdown .= "| --- | --- | --- | --- |\n";

        $index_groups = [];
        foreach ($table_info['indexes'] as $index) {
            $key_name = $index['Key_name'];
            if (!isset($index_groups[$key_name])) {
                $index_groups[$key_name] = [
                    'columns' => [],
                    'non_unique' => $index['Non_unique'],
                    'index_type' => $index['Index_type']
                ];
            }
            $index_groups[$key_name]['columns'][] = $index['Column_name'];
        }

        foreach ($index_groups as $key_name => $index) {
            $columns = implode(', ', $index['columns']);
            $non_unique = $index['non_unique'] ? 'はい' : 'いいえ';
            $markdown .= "| {$key_name} | {$columns} | {$non_unique} | {$index['index_type']} |\n";
        }
    }

    // テーブル作成クエリ（コンパクトモードでない場合）
    if (!$compact && isset($table_info['create_table'])) {
        $markdown .= "\n### テーブル作成SQL\n\n";
        $markdown .= "```sql\n{$table_info['create_table']}\n```\n\n";
    }
    }

    if (file_put_contents("{$output_dir}/db-schema.md", $markdown) === false) {
        die("エラー: Markdownファイルの書き込みに失敗しました: {$output_dir}/db-schema.md\n");
    }
}

// ACFフィールドグループ情報を取得するためのクエリ
$acf_query = "SELECT * FROM {$table_prefix}posts WHERE post_type = 'acf-field-group' AND post_status = 'publish'";
$result = $mysqli->query($acf_query);

$acf_groups = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $acf_groups[] = $row;
    }
}

// ACFフィールド情報を取得するためのクエリ
$acf_fields_query = "SELECT * FROM {$table_prefix}posts WHERE post_type = 'acf-field' AND post_status = 'publish'";
$result = $mysqli->query($acf_fields_query);

$acf_fields = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $acf_fields[] = $row;
    }
}

// ACF情報をJSONとして出力
$acf_data = [
    'groups' => $acf_groups,
    'fields' => $acf_fields
];
$acfJsonContent = json_encode($acf_data, JSON_PRETTY_PRINT);
if ($acfJsonContent === false) {
    die("エラー: ACF情報のJSON変換に失敗しました: " . json_last_error_msg() . "\n");
}
if (file_put_contents("{$output_dir}/acf-database.json", $acfJsonContent) === false) {
    die("エラー: ACF JSONファイルの書き込みに失敗しました: {$output_dir}/acf-database.json\n");
}

// 完了メッセージ
echo "データベーススキーマのエクスポートが完了しました。\n";
echo "出力ファイル:\n";
echo "- {$output_dir}/db-schema.json\n";
echo "- {$output_dir}/db-schema.md\n";
echo "- {$output_dir}/acf-database.json\n";

// データベース接続を閉じる
$mysqli->close();
