# WordPress データエクスポートツール

WordPressのデータベース構造、ACFフィールド、カスタム投稿タイプの情報をエクスポートするツール群です。AIエージェントがプロジェクト構造を効率的に理解するために使用されます。

## 概要

このツールセットは以下の3つのPHPスクリプトで構成されています：

1. **export-db-schema.php** - データベーススキーマをエクスポート
2. **export-acf-usage.php** - ACFフィールドの使用例をエクスポート
3. **export-custom-post-types.php** - カスタム投稿タイプとタクソノミーをエクスポート

## 使用方法

### 一括実行

すべてのエクスポートを実行する場合：

```bash
./export-all.sh [オプション]
```

### 個別実行

Dockerコンテナ内で個別にスクリプトを実行する場合：

```bash
docker exec wordpress-bizcan php /var/www/html/tools/wordpress-export/export-db-schema.php [オプション]
```

## 環境設定

### 環境変数

| 変数名 | 説明 | デフォルト値 |
|--------|------|-------------|
| `WP_EXPORT_OUTPUT_DIR` | 出力ディレクトリのパス | `./output` |
| `SAMPLE_POSTS_COUNT` | サンプル投稿の取得数 | `3` |
| `SAMPLE_TERMS_COUNT` | サンプルタームの取得数 | `5` |
| `MASK_SENSITIVE_DATA` | 機密情報のマスク有効化 | `true` |
| `PROJECT_NAME` | プロジェクト名 | `wordpress-bizcan` |
| `CONTAINER_NAME` | Dockerコンテナ名 | `wordpress-bizcan` |

### 設定例

```bash
export WP_EXPORT_OUTPUT_DIR="/path/to/output"
export SAMPLE_POSTS_COUNT=10
export MASK_SENSITIVE_DATA=false
./export-all.sh
```

## 各ツールの詳細

### export-db-schema.php

データベーススキーマをJSON形式とMarkdown形式でエクスポートします。

**オプション:**
- `--json-only` - JSONファイルのみ出力（Markdownファイルは出力しない）
- `--table-prefix=PREFIX` - 特定のプレフィックスを持つテーブルのみ出力
- `--exclude-core` - WordPressコアテーブルを除外
- `--compact` - CREATE TABLE文を含めない（ファイルサイズ削減）

**出力ファイル:**
- `database/db-schema.json` - データベーススキーマ（JSON形式）
- `database/db-schema.md` - データベーススキーマ（Markdown形式）
- `database/acf-database.json` - ACFフィールドグループとフィールド情報

### export-acf-usage.php

ACFフィールドの使用例をエクスポートします。各投稿タイプごとにフィールドグループとサンプルデータを出力します。

**オプション:**
- `--json-only` - JSONファイルのみ出力
- `--post-types=TYPE1,TYPE2` - 特定の投稿タイプのみ出力
- `--sample-count=N` - サンプル投稿の取得数（デフォルト: 3）
- `--no-mask` - 機密情報のマスクを無効化

**出力ファイル:**
- `acf/acf-usage-examples.json` - ACFフィールド使用例（JSON形式）
- `acf/acf-usage-examples.md` - ACFフィールド使用例（Markdown形式）

### export-custom-post-types.php

カスタム投稿タイプとタクソノミーの定義をエクスポートします。

**オプション:**
- `--json-only` - JSONファイルのみ出力
- `--include-builtin` - 組み込みの投稿タイプとタクソノミーも含める
- `--term-limit=N` - サンプルタームの最大表示数（デフォルト: 5）

**出力ファイル:**
- `wordpress/custom-post-types.json` - カスタム投稿タイプとタクソノミー（JSON形式）
- `wordpress/custom-post-types.md` - カスタム投稿タイプとタクソノミー（Markdown形式）

## 出力サイズの最適化

大規模なWordPressサイトでは、出力ファイルが大きくなる可能性があります。以下の方法で出力サイズを最適化できます：

1. **JSONのみ出力**: `--json-only` オプションを使用してMarkdownファイルの生成をスキップ
2. **フィルタリング**: 特定のテーブルや投稿タイプのみをエクスポート
3. **コンパクトモード**: `--compact` オプションでCREATE TABLE文を除外
4. **サンプル数の調整**: 環境変数でサンプルデータの取得数を制限

### 例：最小限の出力

```bash
# WordPressコアテーブルを除外し、JSONのみ出力、サンプル数を1に制限
export SAMPLE_POSTS_COUNT=1
export SAMPLE_TERMS_COUNT=1
./export-all.sh --json-only --exclude-core --compact
```

## エラーハンドリング

各スクリプトには以下のエラーチェックが含まれています：

- WordPress設定ファイルの存在確認
- データベース接続の確認
- 出力ディレクトリの作成確認
- ファイル書き込み権限の確認
- JSON変換エラーの検出
- ACFプラグインのアクティブ状態確認（export-acf-usage.php）

## トラブルシューティング

### よくある問題

1. **「WordPress設定ファイルが見つかりません」エラー**
   - WordPressのルートディレクトリで実行しているか確認してください
   - Dockerコンテナ内で実行している場合は、正しいパスを指定しているか確認してください

2. **「出力ディレクトリの作成に失敗しました」エラー**
   - 書き込み権限があるか確認してください
   - 親ディレクトリが存在するか確認してください

3. **「ACFプラグインがアクティブではありません」エラー**
   - ACF（Advanced Custom Fields）プラグインがインストールされ、有効化されているか確認してください

4. **出力ファイルが大きすぎる**
   - オプションを使用して出力を制限してください
   - `--json-only` と `--compact` オプションの使用を検討してください

## セキュリティ上の注意

- デフォルトでメールアドレスやパスワードなどの機密情報はマスクされます
- 本番環境のデータをエクスポートする際は、出力ファイルの取り扱いに注意してください
- `--no-mask` オプションは開発環境でのみ使用することを推奨します

## ライセンス

このツールはプロジェクトのライセンスに従います。