#!/bin/bash

# WordPressデータベース情報とACFフィールド情報をエクスポートするスクリプト
# AIエージェントがプロジェクト構造を理解するために使用します

# スクリプトのディレクトリを取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../" && pwd)"

# 設定ファイルの読み込み
if [ -f "${SCRIPT_DIR}/config.sh" ]; then
    source "${SCRIPT_DIR}/config.sh"
else
    echo "エラー: 設定ファイルが見つかりません: ${SCRIPT_DIR}/config.sh"
    exit 1
fi

# 現在のディレクトリを保存
CURRENT_DIR=$(pwd)

# 出力ディレクトリの作成
mkdir -p "${DB_OUTPUT_DIR}"
mkdir -p "${ACF_OUTPUT_DIR}"
mkdir -p "${WP_OUTPUT_DIR}"

echo "WordPressデータベース情報とACFフィールド情報のエクスポートを開始します..."
echo "プロジェクト: ${PROJECT_NAME}"
echo "出力先: ${OUTPUT_BASE_DIR}"

# WordPressコンテナが起動しているか確認
if ! docker ps | grep -q "${CONTAINER_NAME}"; then
    echo "WordPressコンテナが起動していません。docker-composeを実行します..."
    cd "${PROJECT_ROOT}"
    docker-compose up -d
    cd "${SCRIPT_DIR}"
    # コンテナの起動を待つ
    echo "コンテナの起動を待っています..."
    sleep 10
fi

# toolsディレクトリをWordPressコンテナにコピー
echo "スクリプトファイルをWordPressコンテナにコピーしています..."
docker cp "${SCRIPT_DIR}" "${CONTAINER_NAME}:${CONTAINER_TOOLS_DIR}/"

# WordPressコンテナ内でdocsディレクトリを作成
echo "WordPressコンテナ内で出力ディレクトリを作成しています..."
docker exec "${CONTAINER_NAME}" mkdir -p "${CONTAINER_DB_DIR}" "${CONTAINER_ACF_DIR}" "${CONTAINER_WP_DIR}"

# オプションを渡す（すべての引数をそのまま渡す）
ARGS="$@"

# データベーススキーマをエクスポート
echo "データベーススキーマをエクスポートしています..."
docker exec "${CONTAINER_NAME}" php "${CONTAINER_TOOLS_DIR}/export-db-schema.php" ${ARGS}

# ACFフィールド使用例をエクスポート
echo "ACFフィールド使用例をエクスポートしています..."
docker exec "${CONTAINER_NAME}" php "${CONTAINER_TOOLS_DIR}/export-acf-usage.php" ${ARGS}

# カスタム投稿タイプとタクソノミーをエクスポート
echo "カスタム投稿タイプとタクソノミーをエクスポートしています..."
docker exec "${CONTAINER_NAME}" php "${CONTAINER_TOOLS_DIR}/export-custom-post-types.php" ${ARGS}

# エクスポートされたファイルをホストマシンにコピー
echo "エクスポートされたファイルをホストマシンにコピーしています..."
docker cp "${CONTAINER_NAME}:${CONTAINER_DB_DIR}/." "${DB_OUTPUT_DIR}/"
docker cp "${CONTAINER_NAME}:${CONTAINER_ACF_DIR}/." "${ACF_OUTPUT_DIR}/"
docker cp "${CONTAINER_NAME}:${CONTAINER_WP_DIR}/." "${WP_OUTPUT_DIR}/"

echo "エクスポートが完了しました。"
echo "出力ディレクトリ:"
echo "- ${DB_OUTPUT_DIR}"
echo "- ${ACF_OUTPUT_DIR}"
echo "- ${WP_OUTPUT_DIR}"

# 元のディレクトリに戻る
cd "${CURRENT_DIR}"