<?php

/**
 * ACFフィールド使用例エクスポートツール
 *
 * ACFフィールドの使用例をJSON形式でエクスポートします。
 * AIエージェントがACFフィールドの使われ方を理解するために使用します。
 *
 * 使用方法:
 * 1. WordPressのルートディレクトリで実行します
 * 2. php tools/export-acf-usage.php [--json-only] [--post-types=TYPE1,TYPE2]
 *
 * オプション:
 * --json-only: JSONファイルのみ出力（Markdownファイルは出力しない）
 * --post-types=TYPE1,TYPE2: 特定の投稿タイプのみ出力
 * --sample-count=N: サンプル投稿の取得数（デフォルト: 3）
 * --no-mask: 機密情報のマスクを無効化
 */

// コマンドライン引数を解析
$options = getopt('', ['json-only', 'post-types:', 'sample-count:', 'no-mask']);
$jsonOnly = isset($options['json-only']);
$postTypes = isset($options['post-types']) ? explode(',', $options['post-types']) : null;
$sampleCount = isset($options['sample-count']) ? (int)$options['sample-count'] : (getenv('SAMPLE_POSTS_COUNT') ?: 3);
$maskSensitive = !isset($options['no-mask']) && (getenv('MASK_SENSITIVE_DATA') !== 'false');

// WordPressの設定ファイルを読み込む
if (file_exists('/var/www/html/wp-load.php')) {
    require_once('/var/www/html/wp-load.php');
} else {
    die("WordPress設定ファイルが見つかりません。WordPressのルートディレクトリで実行してください。\n");
}

// ACFプラグインがアクティブか確認
if (!function_exists('acf_get_field_groups')) {
    die("エラー: ACFプラグインがアクティブではありません。\n");
}

// 出力ディレクトリの作成
$output_dir = '/var/www/html/docs/acf';
if (!file_exists($output_dir)) {
    if (!mkdir($output_dir, 0755, true)) {
        die("エラー: 出力ディレクトリの作成に失敗しました: {$output_dir}\n");
    }
}

// カスタム投稿タイプ一覧を取得
$post_types = get_post_types(['public' => true], 'objects');

// ACFフィールドの使用例を格納する配列
$usage = [];

// 各投稿タイプごとにサンプルデータを取得
foreach ($post_types as $post_type) {
    // 投稿タイプ名
    $post_type_name = $post_type->name;

    // 特定の投稿タイプのみ出力する場合のフィルタリング
    if ($postTypes && !in_array($post_type_name, $postTypes)) {
        continue;
    }

    // この投稿タイプのACFフィールドグループを取得
    $field_groups = acf_get_field_groups(['post_type' => $post_type_name]);

    if (empty($field_groups)) continue;

    $usage[$post_type_name] = [
        'label' => $post_type->label,
        'field_groups' => [],
        'examples' => []
    ];

    // フィールドグループ情報を格納
    foreach ($field_groups as $field_group) {
        $fields = acf_get_fields($field_group);

        $field_info = [];
        foreach ($fields as $field) {
            $field_info[] = [
                'key' => $field['key'],
                'name' => $field['name'],
                'label' => $field['label'],
                'type' => $field['type'],
                'required' => isset($field['required']) ? $field['required'] : false
            ];
        }

        $usage[$post_type_name]['field_groups'][] = [
            'key' => $field_group['key'],
            'title' => $field_group['title'],
            'fields' => $field_info
        ];
    }

    // サンプルデータを取得
    $posts = get_posts([
        'post_type' => $post_type_name,
        'posts_per_page' => $sampleCount,
        'post_status' => 'publish'
    ]);

    if (empty($posts)) continue;

    // 各投稿のACFフィールド値を取得
    foreach ($posts as $post) {
        $fields = get_fields($post->ID);

        if ($fields) {
            // 機密情報をマスク（オプション）
            if ($maskSensitive) {
                foreach ($fields as $key => $value) {
                    // メールアドレスなど機密情報を含むフィールドをマスク
                    if (strpos($key, 'email') !== false || strpos($key, 'mail') !== false) {
                        $fields[$key] = '[MASKED_EMAIL]';
                    }
                    // パスワードなど機密情報を含むフィールドをマスク
                    if (strpos($key, 'password') !== false || strpos($key, 'pass') !== false) {
                        $fields[$key] = '[MASKED_PASSWORD]';
                    }
                }
            }

            $usage[$post_type_name]['examples'][] = [
                'id' => $post->ID,
                'title' => $post->post_title,
                'fields' => $fields
            ];
        }
    }
}

// JSONとして出力
$jsonContent = json_encode($usage, JSON_PRETTY_PRINT);
if ($jsonContent === false) {
    die("エラー: JSON変換に失敗しました: " . json_last_error_msg() . "\n");
}
if (file_put_contents("{$output_dir}/acf-usage-examples.json", $jsonContent) === false) {
    die("エラー: JSONファイルの書き込みに失敗しました: {$output_dir}/acf-usage-examples.json\n");
}

// Markdownとしても出力（json-onlyオプションが指定されていない場合）
if (!$jsonOnly) {
    $markdown = "# ACFフィールド使用例\n\n";
    $markdown .= "このドキュメントはAIエージェントがACFフィールドの使われ方を理解するために自動生成されました。\n\n";
    $markdown .= "生成日時: " . date('Y-m-d H:i:s') . "\n\n";
    
    // オプション情報を追加
    if ($postTypes) {
        $markdown .= "**フィルター**: 投稿タイプ " . implode(', ', $postTypes) . " のみ表示。\n\n";
    }
    if ($sampleCount !== 3) {
        $markdown .= "**サンプル数**: 各投稿タイプにつき最大 {$sampleCount} 件。\n\n";
    }

foreach ($usage as $post_type_name => $post_type_info) {
    $markdown .= "## {$post_type_info['label']} (`{$post_type_name}`)\n\n";

    // フィールドグループ情報
    $markdown .= "### フィールドグループ\n\n";
    foreach ($post_type_info['field_groups'] as $group) {
        $markdown .= "#### {$group['title']} (`{$group['key']}`)\n\n";

        $markdown .= "| フィールド名 | ラベル | タイプ | 必須 |\n";
        $markdown .= "| --- | --- | --- | --- |\n";

        foreach ($group['fields'] as $field) {
            $required = $field['required'] ? 'はい' : 'いいえ';
            $markdown .= "| {$field['name']} | {$field['label']} | {$field['type']} | {$required} |\n";
        }

        $markdown .= "\n";
    }

    // 使用例
    if (!empty($post_type_info['examples'])) {
        $markdown .= "### 使用例\n\n";

        foreach ($post_type_info['examples'] as $example) {
            $markdown .= "#### {$example['title']} (ID: {$example['id']})\n\n";
            $markdown .= "```json\n" . json_encode($example['fields'], JSON_PRETTY_PRINT) . "\n```\n\n";
        }
    }
    }

    if (file_put_contents("{$output_dir}/acf-usage-examples.md", $markdown) === false) {
        die("エラー: Markdownファイルの書き込みに失敗しました: {$output_dir}/acf-usage-examples.md\n");
    }
}

// 完了メッセージ
echo "ACFフィールド使用例のエクスポートが完了しました。\n";
echo "出力ファイル:\n";
echo "- {$output_dir}/acf-usage-examples.json\n";
if (!$jsonOnly) {
    echo "- {$output_dir}/acf-usage-examples.md\n";
}
