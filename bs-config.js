module.exports = {
  files: [
    './mount_wordpress/wp-content/themes/appmart/*.{html,css,js,php}',
    './mount_wordpress/wp-content/themes/appmart/**/*.{html,css,js,php}',
    './mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.css',
  ],
  proxy: 'http://localhost:20085',
  injectChanges: true,
  ghostMode: {
    clicks: true,
    scroll: true,
    forms: true,
  },
  host: '**************',
  open: false,
  port: 3000,
  ui: {
    port: 3001,
  },
  public: '************:3000', // ここにホストマシンのIPアドレスを設定
};
