#!/bin/bash

# ワークツリー自動セットアップスクリプト
# 現在のブランチに基づいてDocker環境を構築

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
readonly PORT_MANAGER="$SCRIPT_DIR/port_manager.sh"

# ポート管理ライブラリの読み込み
if [[ ! -f "$PORT_MANAGER" ]]; then
    echo "エラー: ポート管理ライブラリが見つかりません: $PORT_MANAGER" >&2
    exit 1
fi
source "$PORT_MANAGER"

# 色付きメッセージ
print_info() { echo -e "\e[36m[INFO]\e[0m $*"; }
print_success() { echo -e "\e[32m[SUCCESS]\e[0m $*"; }
print_warn() { echo -e "\e[33m[WARN]\e[0m $*"; }
print_error() { echo -e "\e[31m[ERROR]\e[0m $*"; }

# 現在のブランチ名を取得
get_current_branch() {
    git rev-parse --abbrev-ref HEAD 2>/dev/null || {
        print_error "Gitブランチの取得に失敗しました"
        return 1
    }
}

# ブランチ名をDocker安全な形式に変換
sanitize_branch_name() {
    local branch=$1
    echo "$branch" | sed 's|[^a-zA-Z0-9._-]|_|g' | tr '[:upper:]' '[:lower:]'
}

# 環境変数を生成
generate_env_file() {
    local branch=$1
    local branch_safe=$2
    local wp_port=$3
    local db_port=$4
    local env_file="$PROJECT_ROOT/.env.worktree"
    
    print_info "環境変数ファイルを生成中: $env_file"
    
    # 既存の.envから基本設定を読み込み
    local wsl_ip=""
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        wsl_ip=$(grep "^WSL_IP=" "$PROJECT_ROOT/.env" 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    cat > "$env_file" << EOF
# ワークツリー環境設定
# Generated at: $(date)

# ブランチ情報
WORKTREE_BRANCH=$branch
WORKTREE_BRANCH_SAFE=$branch_safe

# ポート設定
WORDPRESS_PORT=$wp_port
MYSQL_PORT=$db_port

# WordPress環境設定
WP_ENV=development
WORDPRESS_DEBUG=1
WSL_IP=${wsl_ip:-host.docker.internal}

# Docker設定
SAIL_XDEBUG_MODE=off
SAIL_XDEBUG_CONFIG=client_host=host.docker.internal
EOF
    
    print_success "環境変数ファイルを生成しました: $env_file"
}

# Docker Composeファイルを生成
generate_docker_compose() {
    local branch_safe=$1
    local compose_file="$PROJECT_ROOT/docker-compose.$branch_safe.yml"
    local template_file="$PROJECT_ROOT/docker-compose.worktree.template.yml"
    local env_file="$PROJECT_ROOT/.env.worktree"
    
    print_info "Docker Composeファイルを生成中: $compose_file"
    
    if [[ ! -f "$template_file" ]]; then
        print_error "テンプレートファイルが見つかりません: $template_file"
        return 1
    fi
    
    # envsubstで環境変数を展開してDocker Composeファイルを生成
    if ! (set -a; source "$env_file"; envsubst < "$template_file" > "$compose_file"); then
        print_error "Docker Composeファイルの生成に失敗しました"
        return 1
    fi
    
    print_success "Docker Composeファイルを生成しました: $compose_file"
}

# オリジナルのデータベースコンテナを確認・起動
ensure_database_running() {
    local db_container="bizcan"
    
    print_info "オリジナルのデータベースコンテナを確認中..."
    
    # コンテナの状態を確認
    if docker ps --format "{{.Names}}" | grep -q "^$db_container$"; then
        print_success "データベースコンテナは既に起動しています"
        return 0
    fi
    
    # コンテナが存在するが停止している場合
    if docker ps -a --format "{{.Names}}" | grep -q "^$db_container$"; then
        print_warn "データベースコンテナが停止しています"
        print_info "docker-composeを使用してデータベースを起動中..."
        
        # docker-composeを使用して起動（整合性を保つため）
        cd "$PROJECT_ROOT"
        if docker-compose up -d db; then
            print_success "データベースコンテナを起動しました"
            # 起動完了を待つ
            sleep 5
            return 0
        else
            print_error "データベースコンテナの起動に失敗しました"
            return 1
        fi
    fi
    
    # コンテナが存在しない場合
    print_warn "データベースコンテナが存在しません"
    print_info "docker-composeを使用してデータベースを起動中..."
    
    cd "$PROJECT_ROOT"
    if docker-compose up -d db; then
        print_success "データベースコンテナを起動しました"
        # 起動完了を待つ
        sleep 5
        return 0
    else
        print_error "データベースコンテナの起動に失敗しました"
        print_info "手動で起動してください: cd $PROJECT_ROOT && docker-compose up -d db"
        return 1
    fi
}

# Dockerコンテナを起動
start_containers() {
    local branch_safe=$1
    local compose_file="$PROJECT_ROOT/docker-compose.$branch_safe.yml"
    local wp_port=$2
    local db_port=$3
    
    # データベースコンテナの確認
    if ! ensure_database_running; then
        print_error "データベースコンテナが利用できません"
        print_info "ワークツリーのセットアップを中止します"
        return 1
    fi
    
    print_info "Dockerコンテナを起動中..."
    
    # Docker Composeでコンテナを起動
    if ! docker-compose -f "$compose_file" up -d; then
        print_error "Dockerコンテナの起動に失敗しました"
        return 1
    fi
    
    print_success "Dockerコンテナを起動しました"
    
    # コンテナ情報を更新
    local container_name="wordpress-bizcan-$branch_safe"
    local url="http://localhost:$wp_port"
    
    update_container_info "$branch_safe" "$container_name" "running" "$url"
    
    # WordPressの起動を待つ
    print_info "WordPressの起動を待機中..."
    local max_attempts=30
    local attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if docker exec "$container_name" wp-cli.phar --allow-root --path=/var/www/html core is-installed 2>/dev/null; then
            break
        fi
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        print_warn "WordPressの起動確認がタイムアウトしました"
    else
        # WordPressのサイトURLを更新
        print_info "WordPressのサイトURLを更新中..."
        local new_url="http://localhost:$wp_port"
        
        # wp-cliを使用してサイトURLを更新
        if docker exec "$container_name" wp-cli.phar --allow-root --path=/var/www/html option update siteurl "$new_url" 2>/dev/null && \
           docker exec "$container_name" wp-cli.phar --allow-root --path=/var/www/html option update home "$new_url" 2>/dev/null; then
            print_success "WordPressのサイトURLを更新しました: $new_url"
        else
            print_warn "WordPressのサイトURL更新に失敗しました。手動で更新が必要かもしれません。"
        fi
    fi
    
    # アクセス情報を表示
    echo ""
    print_success "=== セットアップ完了 ==="
    echo "WordPress URL: $url"
    echo "MySQL Port: $db_port"
    echo "Branch: $branch_safe"
    echo ""
    print_info "コンテナ管理コマンド:"
    echo "  停止: docker-compose -f $compose_file stop"
    echo "  再起動: docker-compose -f $compose_file restart"
    echo "  削除: ./scripts/delete_worktree.sh"
}

# 既存のポート確認
check_existing_ports() {
    local branch=$1
    
    # JSONから既存の割り当てを確認
    if [[ -f "$CONTAINERS_FILE" ]]; then
        local existing_wp=$(jq -r ".active_containers.\"$branch\".wordpress_port // empty" "$CONTAINERS_FILE" 2>/dev/null)
        local existing_db=$(jq -r ".active_containers.\"$branch\".mysql_port // empty" "$CONTAINERS_FILE" 2>/dev/null)
        
        if [[ -n "$existing_wp" && -n "$existing_db" ]]; then
            echo "$existing_wp $existing_db"
            return 0
        fi
    fi
    
    return 1
}

# メイン処理
main() {
    local auto_mode=false
    
    # コマンドライン引数の処理
    while [[ $# -gt 0 ]]; do
        case $1 in
            --auto)
                auto_mode=true
                shift
                ;;
            *)
                print_error "不明なオプション: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    cd "$PROJECT_ROOT"
    
    print_info "WordPress Bizcan ワークツリーセットアップ"
    print_info "Current directory: $(pwd)"
    
    # 現在のブランチを取得
    local branch
    if ! branch=$(get_current_branch); then
        exit 1
    fi
    
    local branch_safe
    branch_safe=$(sanitize_branch_name "$branch")
    
    print_info "Branch: $branch"
    print_info "Sanitized: $branch_safe"
    
    # 既存のポート確認
    local wp_port db_port
    if ports=$(check_existing_ports "$branch_safe"); then
        read -r wp_port db_port <<< "$ports"
        print_info "既存のポート割り当てを使用: WordPress=$wp_port, MySQL=$db_port"
    else
        # 新しいポートを割り当て
        print_info "新しいポートを割り当て中..."
        
        if ! wp_port=$(allocate_port "wordpress" "$branch_safe"); then
            print_error "WordPressポートの割り当てに失敗しました"
            exit 1
        fi
        
        if ! db_port=$(allocate_port "mysql" "$branch_safe"); then
            print_error "MySQLポートの割り当てに失敗しました"
            # WordPress ポートの割り当て解除
            deallocate_ports "$branch_safe"
            exit 1
        fi
        
        print_success "ポートを割り当てました: WordPress=$wp_port, MySQL=$db_port"
    fi
    
    # 対話モードでの確認
    if [[ "$auto_mode" == false ]]; then
        echo ""
        print_info "設定確認:"
        echo "  ブランチ: $branch"
        echo "  WordPressポート: $wp_port"
        echo "  MySQLポート: $db_port"
        echo ""
        
        read -p "この設定でセットアップを続行しますか？ (Y/n): " confirm
        if [[ ! "$confirm" =~ ^[yY]?$ ]]; then
            print_info "セットアップをキャンセルしました"
            exit 0
        fi
    fi
    
    # 環境変数ファイル生成
    generate_env_file "$branch" "$branch_safe" "$wp_port" "$db_port"
    
    # Docker Composeファイル生成
    generate_docker_compose "$branch_safe"
    
    # コンテナ起動
    start_containers "$branch_safe" "$wp_port" "$db_port"
    
    print_success "ワークツリーのセットアップが完了しました！"
}

# 使用法表示
show_usage() {
    cat << EOF
WordPress Bizcan ワークツリー自動セットアップ

使用法:
  $0 [OPTIONS]

オプション:
  --auto      対話なしで自動実行
  --help      この使用法を表示

このスクリプトは現在のワークツリーでDocker環境を自動セットアップします。
以下の処理を実行します:

1. 現在のブランチ名を取得
2. 利用可能なポートを動的割り当て
3. 環境変数ファイル (.env.worktree) を生成
4. Docker Composeファイルをテンプレートから生成
5. Dockerコンテナを起動
6. アクセス情報を表示

前提条件:
- Git worktree で作成されたディレクトリ内で実行
- Docker と docker-compose がインストール済み
- jq コマンドが利用可能
EOF
}

# コマンドライン引数の処理
case "${1:-}" in
    --help|-h)
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac