#!/bin/bash

# バックアップ管理スクリプト
# 新しいバックアップを作成し、古いバックアップを自動削除

set -e

# 設定
KEEP_BACKUPS=3
BACKUP_SOURCE=""
BACKUP_BASE_DIR=""

# 使用方法を表示
show_usage() {
    echo "使用方法: $0 <source_directory> <backup_base_directory>"
    echo ""
    echo "例:"
    echo "  $0 /path/to/wordpress/wp-content/themes/cat /path/to/backups"
    echo ""
    echo "オプション:"
    echo "  -k, --keep NUMBER    保持するバックアップ数 (デフォルト: 3)"
    echo "  -h, --help          このヘルプを表示"
}

# 引数の解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -k|--keep)
            KEEP_BACKUPS="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            if [ -z "$BACKUP_SOURCE" ]; then
                BACKUP_SOURCE="$1"
            elif [ -z "$BACKUP_BASE_DIR" ]; then
                BACKUP_BASE_DIR="$1"
            else
                echo "❌ エラー: 不明な引数: $1"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# 引数の検証
if [ -z "$BACKUP_SOURCE" ] || [ -z "$BACKUP_BASE_DIR" ]; then
    echo "❌ エラー: 必要な引数が不足しています"
    show_usage
    exit 1
fi

if [ ! -d "$BACKUP_SOURCE" ]; then
    echo "❌ エラー: バックアップ元ディレクトリが見つかりません: $BACKUP_SOURCE"
    exit 1
fi

# 数値の検証
if ! [[ "$KEEP_BACKUPS" =~ ^[0-9]+$ ]] || [ "$KEEP_BACKUPS" -lt 1 ]; then
    echo "❌ エラー: 保持数は1以上の数値である必要があります: $KEEP_BACKUPS"
    exit 1
fi

echo "🗂️  バックアップ管理を開始..."
echo "  バックアップ元: $BACKUP_SOURCE"
echo "  バックアップ先: $BACKUP_BASE_DIR"
echo "  保持数: $KEEP_BACKUPS"

# バックアップディレクトリの作成
mkdir -p "$BACKUP_BASE_DIR"

# 新しいバックアップの作成
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/$TIMESTAMP"

echo "📦 新しいバックアップを作成中..."
cp -r "$BACKUP_SOURCE" "$BACKUP_DIR"
echo "✅ バックアップを作成しました: $BACKUP_DIR"

# 現在のバックアップ数を確認
cd "$BACKUP_BASE_DIR"
CURRENT_COUNT=$(ls -1 | wc -l)
echo "📊 現在のバックアップ数: $CURRENT_COUNT"

# 古いバックアップの削除
if [ "$CURRENT_COUNT" -gt "$KEEP_BACKUPS" ]; then
    echo "🧹 古いバックアップを削除中..."
    
    # 最新のものから指定数を除いて削除
    DELETE_COUNT=$((CURRENT_COUNT - KEEP_BACKUPS))
    DELETED_BACKUPS=$(ls -1t | tail -n "$DELETE_COUNT")
    
    echo "  削除対象 ($DELETE_COUNT 個):"
    echo "$DELETED_BACKUPS" | sed 's/^/    /'
    
    echo "$DELETED_BACKUPS" | xargs -r rm -rf
    
    echo "✅ 古いバックアップを削除しました"
else
    echo "ℹ️  削除対象のバックアップはありません"
fi

# 最終状態の確認
FINAL_COUNT=$(ls -1 | wc -l)
echo "📊 最終バックアップ数: $FINAL_COUNT"

echo "📋 現在のバックアップ一覧:"
ls -1t | sed 's/^/  /'

echo "🎉 バックアップ管理が完了しました ($(date '+%Y-%m-%d %H:%M:%S'))"
