#!/bin/bash

# WP-CLI PHP バイナリ修正スクリプト
# WP-CLIが正しいPHPバージョンを使用するよう設定

echo "🔧 WP-CLI PHP Binary Fix Script"

# 現在のWP-CLI設定を確認
echo "📋 Current WP-CLI Configuration:"
wp --info 2>/dev/null || echo "WP-CLI not available"

# 適切なPHPバイナリを検出
echo "🔍 Detecting appropriate PHP binary..."
PHP_BINARY=""
for php_candidate in php8.1 php81 /usr/bin/php8.1 /usr/local/bin/php8.1 php; do
    if command -v "$php_candidate" >/dev/null 2>&1; then
        PHP_VER=$($php_candidate -r "echo PHP_VERSION;" 2>/dev/null || echo "unknown")
        echo "  Found: $php_candidate -> $PHP_VER"
        
        # PHP 8.1以上を優先
        if $php_candidate -r "exit(version_compare(PHP_VERSION, '8.1.0', '>=') ? 0 : 1);" 2>/dev/null; then
            PHP_BINARY="$php_candidate"
            echo "✅ Selected: $PHP_BINARY (Version: $PHP_VER)"
            break
        fi
    fi
done

if [ -z "$PHP_BINARY" ]; then
    echo "❌ No suitable PHP binary found"
    exit 1
fi

# WP-CLI設定ファイルを作成/更新
WP_CLI_CONFIG="wp-cli.yml"
echo "📝 Creating/updating WP-CLI configuration: $WP_CLI_CONFIG"

cat > "$WP_CLI_CONFIG" << EOF
# WP-CLI Configuration
# Generated by wp-cli-php-fix.sh

# PHP binary path
php: $PHP_BINARY

# Other settings
apache_modules:
  - mod_rewrite

disabled_commands: []

require: []

color: auto
EOF

echo "✅ WP-CLI configuration updated"
echo "📄 Configuration file: $(pwd)/$WP_CLI_CONFIG"

# 設定の確認
echo "🔍 Verifying WP-CLI configuration:"
$PHP_BINARY $(which wp) --info 2>/dev/null || echo "Verification failed"

echo "🎉 WP-CLI PHP binary fix completed"
