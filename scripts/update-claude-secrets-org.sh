#!/bin/bash

# Organization Secrets対応版 Claude OAuthトークン更新スクリプト
# 
# 使用方法:
# 1. claude /login を実行してOAuth認証を完了
# 2. このスクリプトを実行: ./scripts/update-claude-secrets-org.sh
# 
# 環境変数:
# - GITHUB_ORG: Organization名（必須）
# - UPDATE_REPO_SECRETS: リポジトリSecretも更新する場合は"true"
# - SELECTED_REPOS: カンマ区切りのリポジトリリスト（Organization Secrets用）

set -e

# 色付き出力
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🔄 Claude OAuth Token更新スクリプト (Organization対応版)${NC}"
echo ""

# Claude CLIでログイン
echo -e "${YELLOW}📱 Claude CLIでログインを開始します...${NC}"
echo -e "${YELLOW}   ブラウザが開きます。ログインを完了してください。${NC}"
echo ""
claude /login

# ブラウザでの認証完了を待つ
echo ""
echo -e "${GREEN}✅ ブラウザでの認証が完了しました${NC}"
echo ""

# Organization名の確認
if [ -z "$GITHUB_ORG" ]; then
    GITHUB_ORG="dkr-org"  # デフォルト値を設定
fi

echo -e "${BLUE}📦 Organization: $GITHUB_ORG${NC}"
echo ""

# credentials.jsonのパスを確認
CRED_FILE="$HOME/.claude/.credentials.json"

if [ ! -f "$CRED_FILE" ]; then
    echo -e "${RED}❌ Error: $CRED_FILE が見つかりません${NC}"
    echo -e "${YELLOW}先に 'claude /login' を実行してください${NC}"
    exit 1
fi

# JSONから値を抽出
ACCESS_TOKEN=$(jq -r '.claudeAiOauth.accessToken' "$CRED_FILE")
REFRESH_TOKEN=$(jq -r '.claudeAiOauth.refreshToken' "$CRED_FILE")
EXPIRES_AT_MS=$(jq -r '.claudeAiOauth.expiresAt' "$CRED_FILE")

# ミリ秒からISO 8601形式に変換
if [ -n "$EXPIRES_AT_MS" ] && [ "$EXPIRES_AT_MS" != "null" ]; then
    EXPIRES_AT=$(date -d "@$(($EXPIRES_AT_MS / 1000))" --iso-8601=seconds)
else
    EXPIRES_AT=""
fi

if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
    echo -e "${RED}❌ Error: アクセストークンが見つかりません${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 認証情報を読み込みました${NC}"
echo "   - Access Token: ${ACCESS_TOKEN:0:20}..."
echo "   - Refresh Token: ${REFRESH_TOKEN:0:20}..."
echo "   - Expires At: $EXPIRES_AT"
echo ""

# GitHub CLIがインストールされているか確認
if ! command -v gh &> /dev/null; then
    echo -e "${RED}❌ Error: GitHub CLI (gh) がインストールされていません${NC}"
    echo "インストール: https://cli.github.com/"
    exit 1
fi

# GitHub認証確認
if ! gh auth status &> /dev/null; then
    echo -e "${RED}❌ Error: GitHub CLIが認証されていません${NC}"
    echo "実行: gh auth login"
    exit 1
fi

# Organization Secretsを更新する関数
update_org_secret() {
    local secret_name=$1
    local secret_value=$2
    
    echo -n "  - $secret_name: "
    
    # シークレットの暗号化された値を作成
    # 注: GitHub APIを直接使用する場合は、sodium暗号化が必要
    if gh api -X PUT "/orgs/$GITHUB_ORG/actions/secrets/$secret_name" \
        --field encrypted_value="$(echo -n "$secret_value" | base64)" \
        --field visibility="selected" &>/dev/null; then
        echo -e "${GREEN}✅${NC}"
    else
        # gh secret setコマンドを使用（Organization対応）
        if echo -n "$secret_value" | gh secret set "$secret_name" --org="$GITHUB_ORG" 2>/dev/null; then
            echo -e "${GREEN}✅${NC}"
        else
            echo -e "${RED}❌ 失敗${NC}"
            return 1
        fi
    fi
}

# 即座に更新を実行
echo "🔄 Organization Secretsを更新中..."

# Organization Secretsを更新（visibilityオプションを削除してシンプルに）
echo -n "$ACCESS_TOKEN" | gh secret set CLAUDE_ACCESS_TOKEN --org="$GITHUB_ORG"
echo -e "  - CLAUDE_ACCESS_TOKEN: ${GREEN}✅${NC}"

echo -n "$REFRESH_TOKEN" | gh secret set CLAUDE_REFRESH_TOKEN --org="$GITHUB_ORG"
echo -e "  - CLAUDE_REFRESH_TOKEN: ${GREEN}✅${NC}"

echo -n "$EXPIRES_AT" | gh secret set CLAUDE_EXPIRES_AT --org="$GITHUB_ORG"
echo -e "  - CLAUDE_EXPIRES_AT: ${GREEN}✅${NC}"

echo ""
echo -e "${GREEN}🎉 Organization Secretsを更新しました！${NC}"
echo ""

# リポジトリSecretも更新する場合
if [ "$UPDATE_REPO_SECRETS" = "true" ] && [ -n "$(git remote get-url origin 2>/dev/null)" ]; then
    REPO=$(git remote get-url origin | sed 's/.*github.com[:/]\(.*\)\.git$/\1/')
    echo "📦 現在のリポジトリ: $REPO"
    echo "🔄 Repository Secretsも更新中..."
    
    gh secret set CLAUDE_ACCESS_TOKEN --body="$ACCESS_TOKEN" --repo="$REPO"
    gh secret set CLAUDE_REFRESH_TOKEN --body="$REFRESH_TOKEN" --repo="$REPO"
    gh secret set CLAUDE_EXPIRES_AT --body="$EXPIRES_AT" --repo="$REPO"
    
    echo -e "${GREEN}✅ Repository Secretsも更新しました${NC}"
fi

# 有効期限を表示（8時間の短期トークンのため残り時間は表示しない）
if [ -n "$EXPIRES_AT" ] && [ "$EXPIRES_AT" != "null" ]; then
    echo ""
    echo "📅 トークンの有効期限: $EXPIRES_AT"
fi

# 次のステップ
echo ""
echo "📝 次のステップ:"
echo "   1. Selected repositoriesにリポジトリを追加"
echo "      https://github.com/organizations/$GITHUB_ORG/settings/secrets/actions"
echo "   2. 各プロジェクトでGitHub Actionsを再実行してテスト"
echo "   3. 既存のRepository Secretsを削除（Organization Secretsが優先されるため）"
echo ""

# 選択されたリポジトリの設定
if [ -n "$SELECTED_REPOS" ]; then
    echo "📌 以下のリポジトリをSelected repositoriesに追加してください:"
    IFS=',' read -ra REPOS <<< "$SELECTED_REPOS"
    for repo in "${REPOS[@]}"; do
        echo "   - $repo"
    done
fi