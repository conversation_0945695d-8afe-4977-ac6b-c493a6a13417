#!/bin/bash
# Git Worktree環境でWordPress開発環境をセットアップするスクリプト

set -e

# 色付き出力用の定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 使用方法の表示
usage() {
    echo "使用方法: $0 [オプション]"
    echo ""
    echo "オプション:"
    echo "  -b, --branch BRANCH_NAME    ワークツリーで使用するブランチ名"
    echo "  -p, --path PATH             ワークツリーのパス（デフォルト: ../wordpress-bizcan.worktrees/BRANCH_NAME）"
    echo "  -o, --original PATH         オリジナルのWordPressパス（デフォルト: 現在のディレクトリ）"
    echo "  -h, --help                  このヘルプを表示"
    echo ""
    echo "例:"
    echo "  $0 -b feature/new-feature"
    echo "  $0 -b feature/new-feature -p ~/projects/wordpress-bizcan-feature"
}

# デフォルト値
BRANCH_NAME=""
WORKTREE_PATH=""
ORIGINAL_PATH=$(pwd)

# 引数の解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--branch)
            BRANCH_NAME="$2"
            shift 2
            ;;
        -p|--path)
            WORKTREE_PATH="$2"
            shift 2
            ;;
        -o|--original)
            ORIGINAL_PATH="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo -e "${RED}エラー: 不明なオプション: $1${NC}"
            usage
            exit 1
            ;;
    esac
done

# ブランチ名が指定されていない場合はエラー
if [ -z "$BRANCH_NAME" ]; then
    echo -e "${RED}エラー: ブランチ名を指定してください${NC}"
    usage
    exit 1
fi

# ワークツリーパスが指定されていない場合はデフォルト値を使用
if [ -z "$WORKTREE_PATH" ]; then
    WORKTREE_PATH="../wordpress-bizcan.worktrees/${BRANCH_NAME}"
fi

echo -e "${GREEN}=== Git Worktree WordPress開発環境セットアップ ===${NC}"
echo "ブランチ: $BRANCH_NAME"
echo "ワークツリーパス: $WORKTREE_PATH"
echo "オリジナルパス: $ORIGINAL_PATH"
echo ""

# ワークツリーの作成
if [ ! -d "$WORKTREE_PATH" ]; then
    echo -e "${YELLOW}ワークツリーを作成しています...${NC}"
    git worktree add "$WORKTREE_PATH" "$BRANCH_NAME"
else
    echo -e "${YELLOW}ワークツリーは既に存在します${NC}"
fi

# ワークツリーディレクトリに移動
cd "$WORKTREE_PATH"

# 必要なディレクトリの作成
echo -e "${YELLOW}必要なディレクトリを作成しています...${NC}"
mkdir -p mount_wordpress/wp-content/{plugins,uploads,languages}
mkdir -p conf
mkdir -p log
mkdir -p db

# 設定ファイルのコピー
echo -e "${YELLOW}設定ファイルをコピーしています...${NC}"
if [ -f "$ORIGINAL_PATH/mount_wordpress/wp-config.php" ]; then
    cp "$ORIGINAL_PATH/mount_wordpress/wp-config.php" mount_wordpress/
fi

# confディレクトリの内容をコピー
if [ -d "$ORIGINAL_PATH/conf" ]; then
    cp -r "$ORIGINAL_PATH/conf"/* conf/
fi

# .envファイルの作成
echo -e "${YELLOW}.envファイルを作成しています...${NC}"
cat > .env <<EOF
# WordPress Core Path (オリジナルのWordPressコアファイルへのパス)
WORDPRESS_CORE_PATH=$ORIGINAL_PATH/mount_wordpress

# Worktree Branch Name
WORKTREE_BRANCH=$BRANCH_NAME

# WordPress環境設定
WP_ENV=development
WORDPRESS_DEBUG=1

# WSL IP (必要に応じて設定)
WSL_IP=
EOF

# docker-compose.worktree.ymlのコピー
if [ -f "$ORIGINAL_PATH/docker-compose.worktree.yml" ]; then
    echo -e "${YELLOW}docker-compose.worktree.ymlをコピーしています...${NC}"
    cp "$ORIGINAL_PATH/docker-compose.worktree.yml" .
fi

# 起動スクリプトの作成
echo -e "${YELLOW}起動スクリプトを作成しています...${NC}"
cat > start-worktree.sh <<'EOF'
#!/bin/bash
# ワークツリー環境でDockerを起動するスクリプト

# 色付き出力用の定義
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}=== ワークツリー環境のDocker起動 ===${NC}"

# オリジナル環境のDockerが起動している場合は停止を促す
if docker ps | grep -q "wordpress-bizcan"; then
    echo -e "${YELLOW}警告: オリジナル環境のDockerコンテナが起動しています${NC}"
    echo "同じポートを使用するため、先に停止する必要があります。"
    echo ""
    echo "以下のコマンドを実行してください:"
    echo "  cd $WORDPRESS_CORE_PATH && docker-compose down"
    echo ""
    read -p "停止しましたか？ (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "中止しました"
        exit 1
    fi
fi

# Docker Composeの起動
echo -e "${YELLOW}Dockerコンテナを起動しています...${NC}"
docker-compose -f docker-compose.yml -f docker-compose.worktree.yml up -d

echo -e "${GREEN}起動完了！${NC}"
echo "WordPress: http://localhost:20080"
echo "phpMyAdmin: http://localhost:15081"
EOF

chmod +x start-worktree.sh

# 停止スクリプトの作成
cat > stop-worktree.sh <<'EOF'
#!/bin/bash
# ワークツリー環境のDockerを停止するスクリプト

echo "Dockerコンテナを停止しています..."
docker-compose -f docker-compose.yml -f docker-compose.worktree.yml down
echo "停止完了"
EOF

chmod +x stop-worktree.sh

echo -e "${GREEN}=== セットアップ完了！ ===${NC}"
echo ""
echo "次のステップ:"
echo "1. cd $WORKTREE_PATH"
echo "2. ./start-worktree.sh でDocker環境を起動"
echo "3. http://localhost:20080 でWordPressにアクセス"
echo ""
echo "注意事項:"
echo "- オリジナル環境とワークツリー環境を同時に起動することはできません"
echo "- テーマファイルの変更はワークツリー内で行ってください"
echo "- プラグインやWordPressコアの更新はオリジナル環境で行ってください"