#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * Claude OAuthトークンを自動的にリフレッシュするスクリプト
 * 
 * 使用方法:
 * 1. 環境変数またはGitHub Secretsから現在のトークン情報を取得
 * 2. リフレッシュトークンを使用して新しいアクセストークンを取得
 * 3. GitHub APIを使用してSecretsを更新
 */

// 設定
const CONFIG = {
  credentialsPath: path.join(process.env.HOME, '.claude', '.credentials.json'),
  githubRepo: 'dkr-system/wordpress-bizcan',
  secretNames: {
    accessToken: 'CLAUDE_ACCESS_TOKEN',
    refreshToken: 'CLAUDE_REFRESH_TOKEN',
    expiresAt: 'CLAUDE_EXPIRES_AT'
  }
};

// 現在のトークン情報を読み込む
function loadCurrentTokens() {
  try {
    if (fs.existsSync(CONFIG.credentialsPath)) {
      const credentials = JSON.parse(fs.readFileSync(CONFIG.credentialsPath, 'utf8'));
      // 実際のJSON構造に合わせて修正
      if (credentials.claudeAiOauth) {
        return {
          access_token: credentials.claudeAiOauth.accessToken,
          refresh_token: credentials.claudeAiOauth.refreshToken,
          expires_at: new Date(credentials.claudeAiOauth.expiresAt).toISOString()
        };
      }
    }
  } catch (error) {
    console.error('Error loading credentials:', error);
  }
  
  // 環境変数から取得
  return {
    access_token: process.env.CLAUDE_ACCESS_TOKEN,
    refresh_token: process.env.CLAUDE_REFRESH_TOKEN,
    expires_at: process.env.CLAUDE_EXPIRES_AT
  };
}

// トークンの有効期限をチェック
function isTokenExpiring(expiresAt) {
  if (!expiresAt) return true;
  
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  const hoursUntilExpiry = (expiryDate - now) / (1000 * 60 * 60);
  
  // 24時間以内に期限切れになる場合は更新
  return hoursUntilExpiry < 24;
}

// リフレッシュトークンを使用して新しいアクセストークンを取得
async function refreshAccessToken(refreshToken) {
  // ここにClaude OAuthプロバイダーのトークンリフレッシュエンドポイントを実装
  // 実際のエンドポイントURLは調査が必要
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  };
  
  const params = new URLSearchParams({
    grant_type: 'refresh_token',
    refresh_token: refreshToken,
    // client_id, client_secretなどが必要な場合は追加
  });
  
  // TODO: 実際のClaude OAuthエンドポイントに置き換える
  console.log('Refreshing token...');
  
  // 仮の実装
  return {
    access_token: 'new_access_token',
    refresh_token: refreshToken,
    expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
  };
}

// GitHub Secretsを更新
async function updateGitHubSecrets(tokens) {
  const { Octokit } = await import('@octokit/rest');
  const octokit = new Octokit({
    auth: process.env.GITHUB_TOKEN
  });
  
  const [owner, repo] = CONFIG.githubRepo.split('/');
  
  // 各シークレットを更新
  for (const [key, secretName] of Object.entries(CONFIG.secretNames)) {
    try {
      await octokit.actions.createOrUpdateRepoSecret({
        owner,
        repo,
        secret_name: secretName,
        encrypted_value: await encryptSecret(tokens[key], owner, repo)
      });
      console.log(`✅ Updated ${secretName}`);
    } catch (error) {
      console.error(`❌ Failed to update ${secretName}:`, error.message);
    }
  }
}

// シークレットの暗号化（GitHubの公開鍵を使用）
async function encryptSecret(value, owner, repo) {
  // GitHub APIを使用して公開鍵を取得し、値を暗号化
  // 実装は省略（sodium暗号化ライブラリが必要）
  return value; // 仮実装
}

// メイン処理
async function main() {
  console.log('🔄 Checking Claude OAuth token...');
  
  const currentTokens = loadCurrentTokens();
  
  if (!currentTokens.refresh_token) {
    console.error('❌ No refresh token found. Manual login required.');
    process.exit(1);
  }
  
  if (isTokenExpiring(currentTokens.expires_at)) {
    console.log('🔄 Token is expiring soon. Refreshing...');
    
    try {
      const newTokens = await refreshAccessToken(currentTokens.refresh_token);
      
      // ローカルファイルを更新
      if (fs.existsSync(CONFIG.credentialsPath)) {
        fs.writeFileSync(CONFIG.credentialsPath, JSON.stringify(newTokens, null, 2));
        console.log('✅ Updated local credentials');
      }
      
      // GitHub Secretsを更新
      if (process.env.GITHUB_TOKEN) {
        await updateGitHubSecrets(newTokens);
      }
      
      console.log('✅ Token refresh completed');
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      process.exit(1);
    }
  } else {
    console.log('✅ Token is still valid');
  }
}

// 実行
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { isTokenExpiring, refreshAccessToken };