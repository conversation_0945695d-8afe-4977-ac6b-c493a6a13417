#!/bin/bash

# デプロイ用キャッシュクリアスクリプト
# Autoptimizeのキャッシュクリアのみを実行

set -e

echo "🧹 Starting Autoptimize cache clear process..."

# 作業ディレクトリの確認
if [ ! -d "wp-content" ]; then
    echo "❌ Error: wp-content directory not found. Are you in the WordPress root directory?"
    exit 1
fi

# Autoptimizeのキャッシュクリア
if [ -d "wp-content/cache/autoptimize" ]; then
    echo "🔧 Clearing Autoptimize cache..."
    rm -rf wp-content/cache/autoptimize/*
    echo "✅ Autoptimize cache cleared"
else
    echo "ℹ️  Autoptimize cache directory not found"
fi

echo "🎉 Autoptimize cache clear process completed at $(date '+%Y-%m-%d %H:%M:%S')"
