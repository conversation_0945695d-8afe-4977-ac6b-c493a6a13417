#!/usr/bin/env node

/**
 * ビルド成果物の検証スクリプト
 * CI/CDパイプラインで使用される
 */

const fs = require('fs');
const path = require('path');
const glob = require('fast-glob');

const DIST_DIR = 'mount_wordpress/wp-content/themes/cat/dist';
// 環境に応じた必須ファイルパターンを決定
const isProduction = process.env.NODE_ENV === 'production';
const jsExtension = isProduction ? '.min.js' : '.js';

const REQUIRED_FILES = [
  `js/pages/search-page.*${jsExtension}`,
  `js/core/init-manager.*${jsExtension}`,
  'css/main.*.css',
];

function verifyBuild() {
  console.log('🔍 ビルド成果物の検証を開始...');

  // distディレクトリの存在確認
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ distディレクトリが見つかりません:', DIST_DIR);
    process.exit(1);
  }

  console.log('✅ distディレクトリが見つかりました');

  // 必須ファイルの確認
  let allFilesFound = true;

  for (const pattern of REQUIRED_FILES) {
    const fullPattern = path.join(DIST_DIR, pattern);
    const files = glob.sync(fullPattern);

    if (files.length === 0) {
      console.error(`❌ 必須ファイルが見つかりません: ${pattern}`);
      allFilesFound = false;
    } else {
      console.log(`✅ ${pattern} → ${files[0]}`);
    }
  }

  if (!allFilesFound) {
    console.error('❌ 一部の必須ファイルが見つかりませんでした');
    process.exit(1);
  }

  // ファイルサイズの確認
  console.log('\n📊 ファイルサイズ統計:');

  const jsPattern = isProduction ? 'js/**/*.min.js' : 'js/**/*.js';
  const jsFiles = glob.sync(path.join(DIST_DIR, jsPattern));
  const cssFiles = glob.sync(path.join(DIST_DIR, 'css/**/*.css'));

  let totalSize = 0;

  jsFiles.forEach((file) => {
    const stats = fs.statSync(file);
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`  JS: ${path.basename(file)} (${sizeKB}KB)`);
    totalSize += stats.size;
  });

  cssFiles.forEach((file) => {
    const stats = fs.statSync(file);
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`  CSS: ${path.basename(file)} (${sizeKB}KB)`);
    totalSize += stats.size;
  });

  const totalSizeKB = Math.round(totalSize / 1024);
  console.log(`\n📦 総サイズ: ${totalSizeKB}KB`);

  // 警告サイズのチェック
  if (totalSizeKB > 5000) {
    console.warn('⚠️  総ファイルサイズが大きすぎます (>5MB)');
  }

  console.log('\n✅ ビルド成果物の検証が完了しました');
}

// ハッシュ情報の出力
function outputHashInfo() {
  console.log('\n🔗 ハッシュ付きファイル一覧:');

  const allFiles = glob.sync(path.join(DIST_DIR, '**/*.{js,css}'));
  const hashFiles = allFiles.filter((file) => /\.[a-f0-9]{8}\.(min\.)?[jc]ss?$/.test(file));

  hashFiles.forEach((file) => {
    const relativePath = path.relative(DIST_DIR, file);
    console.log(`  ${relativePath}`);
  });
}

if (require.main === module) {
  try {
    verifyBuild();
    outputHashInfo();
  } catch (error) {
    console.error('❌ 検証中にエラーが発生しました:', error.message);
    process.exit(1);
  }
}

module.exports = { verifyBuild, outputHashInfo };
