<?php
/**
 * JavaScript読み込み問題デバッグスクリプト
 * page-service.phpとpage-issue.phpのJavaScript読み込み問題を調査
 */

// WordPressの初期化
define('WP_USE_THEMES', false);

// wp-load.phpのパスを検索
$wp_load_paths = [
    __DIR__ . '/../mount_wordpress/wp-load.php',
    __DIR__ . '/../wp-load.php',
    dirname(__DIR__) . '/wp-load.php',
    '/var/www/html/wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $wp_load_path) {
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
        $wp_loaded = true;
        echo "WordPress loaded from: $wp_load_path\n";
        break;
    }
}

if (!$wp_loaded) {
    echo "Error: wp-load.php not found\n";
    exit(1);
}

echo "=== JavaScript読み込み問題デバッグ ===\n\n";

// 1. 環境情報の確認
echo "1. 環境情報:\n";
if (class_exists('Bizcan\Utility\EnvironmentConfig')) {
    $env_type = \Bizcan\Utility\EnvironmentConfig::getEnvType();
    $is_production = \Bizcan\Utility\EnvironmentConfig::isProduction();
    $cache_enabled = \Bizcan\Utility\EnvironmentConfig::isCacheEnabled();
    
    echo "  ENV_TYPE: $env_type\n";
    echo "  Is Production: " . ($is_production ? 'YES' : 'NO') . "\n";
    echo "  Cache Enabled: " . ($cache_enabled ? 'YES' : 'NO') . "\n";
} else {
    echo "  EnvironmentConfig class not found\n";
}

// 2. ページの存在確認
echo "\n2. ページの存在確認:\n";
$service_page = get_page_by_path('service');
$issue_page = get_page_by_path('issue');

if ($service_page) {
    echo "  Service Page: ID={$service_page->ID}, Slug={$service_page->post_name}, Template=" . get_page_template_slug($service_page->ID) . "\n";
} else {
    echo "  Service Page: NOT FOUND\n";
}

if ($issue_page) {
    echo "  Issue Page: ID={$issue_page->ID}, Slug={$issue_page->post_name}, Template=" . get_page_template_slug($issue_page->ID) . "\n";
} else {
    echo "  Issue Page: NOT FOUND\n";
}

// 3. JavaScriptファイルの存在確認
echo "\n3. JavaScriptファイルの存在確認:\n";
$theme_dir = get_template_directory();
$js_files = [
    '/dist/js/components/industry-selector.js',
    '/dist/js/components/genre-display.js',
    '/dist/js/pages/search-page.js',
    '/js/core/init-manager.js',
    '/dist/js/core/dom-utils.js'
];

foreach ($js_files as $js_file) {
    $full_path = $theme_dir . $js_file;
    $exists = file_exists($full_path);
    $size = $exists ? filesize($full_path) : 0;
    echo "  $js_file: " . ($exists ? "EXISTS ({$size} bytes)" : "NOT FOUND") . "\n";
    
    // ハッシュ付きファイルも確認
    if (strpos($js_file, '/dist/js/') !== false) {
        $path_info = pathinfo($js_file);
        $dirname = $path_info['dirname'];
        $filename = $path_info['filename'];
        $js_dir = $theme_dir . $dirname . '/';
        
        if (is_dir($js_dir)) {
            $pattern = $js_dir . $filename . '.*';
            $files = glob($pattern);
            if (!empty($files)) {
                foreach ($files as $file) {
                    $rel_path = str_replace($theme_dir, '', $file);
                    $size = filesize($file);
                    echo "    Hash file: $rel_path ({$size} bytes)\n";
                }
            }
        }
    }
}

// 4. EnqueueManagerの動作確認
echo "\n4. EnqueueManagerの動作確認:\n";
if (class_exists('Bizcan\Utility\EnqueueManager')) {
    echo "  EnqueueManager class: EXISTS\n";
    
    // 各ページでの条件判定をシミュレート
    $test_pages = ['service', 'issue'];
    
    foreach ($test_pages as $page_slug) {
        echo "\n  Testing page: $page_slug\n";
        
        // ページを取得
        $page = get_page_by_path($page_slug);
        if (!$page) {
            echo "    Page not found\n";
            continue;
        }
        
        // グローバル変数を設定してis_page()をシミュレート
        global $post;
        $original_post = $post;
        $post = $page;
        
        // is_page()の結果を確認
        $is_page_result = is_page($page_slug);
        echo "    is_page('$page_slug'): " . ($is_page_result ? 'TRUE' : 'FALSE') . "\n";
        
        // 各スクリプトの読み込み条件をチェック
        $scripts_to_check = [
            'industry-selector' => ['page' => ['service', 'issue']],
            'genre-display' => ['page' => ['service', 'issue']],
            'search-page' => ['page' => ['service', 'issue']]
        ];
        
        foreach ($scripts_to_check as $script_name => $conditions) {
            $should_load = false;
            if (isset($conditions['page']) && is_page($conditions['page'])) {
                $should_load = true;
            }
            echo "    $script_name: " . ($should_load ? 'SHOULD LOAD' : 'SHOULD NOT LOAD') . "\n";
        }
        
        // 元のpostを復元
        $post = $original_post;
    }
} else {
    echo "  EnqueueManager class: NOT FOUND\n";
}

// 5. キャッシュ状況の確認
echo "\n5. キャッシュ状況の確認:\n";
if (function_exists('wp_cache_get')) {
    $cache_keys = [
        'bizcan_hashed_js_' . md5('/dist/js/components/industry-selector.js'),
        'bizcan_hashed_js_' . md5('/dist/js/components/genre-display.js'),
        'bizcan_hashed_js_' . md5('/dist/js/pages/search-page.js')
    ];
    
    foreach ($cache_keys as $cache_key) {
        $cached_value = wp_cache_get($cache_key, 'bizcan_assets');
        echo "  $cache_key: " . ($cached_value !== false ? "CACHED ($cached_value)" : "NOT CACHED") . "\n";
    }
} else {
    echo "  wp_cache_get function not available\n";
}

echo "\n=== デバッグ完了 ===\n";
?>
