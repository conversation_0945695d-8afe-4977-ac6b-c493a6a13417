#!/bin/bash

# ブランチ選択式ワークツリー作成スクリプト
# claude-task/ プレフィックスのブランチから選択してワークツリーを作成

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
readonly PORT_MANAGER="$SCRIPT_DIR/port_manager.sh"

# ポート管理ライブラリの読み込み
if [[ ! -f "$PORT_MANAGER" ]]; then
    echo "エラー: ポート管理ライブラリが見つかりません: $PORT_MANAGER" >&2
    exit 1
fi
source "$PORT_MANAGER"

# 色付きメッセージ
print_info() { echo -e "\e[36m[INFO]\e[0m $*"; }
print_success() { echo -e "\e[32m[SUCCESS]\e[0m $*"; }
print_warn() { echo -e "\e[33m[WARN]\e[0m $*"; }
print_error() { echo -e "\e[31m[ERROR]\e[0m $*"; }

# リモートブランチ一覧を取得
get_claude_task_branches() {
    print_info "リモートブランチ情報を更新中..."
    git fetch origin >/dev/null 2>&1 || {
        print_warn "リモートブランチの取得に失敗しました"
    }
    
    # claude-task/ で始まるリモートブランチを取得
    git branch -r --no-merged main 2>/dev/null | \
        grep 'origin/claude-task/' | \
        sed 's|.*origin/||' | \
        sort || {
        print_warn "claude-task/ ブランチが見つかりません"
        return 1
    }
}

# 既存のワークツリー一覧を取得
get_existing_worktrees() {
    git worktree list --porcelain 2>/dev/null | \
        awk '/^worktree/ { gsub(/.*\//, "", $2); print $2 }' | \
        grep -v "^$" || true
}

# ブランチ名からワークツリー名を生成
generate_worktree_name() {
    local branch=$1
    echo "${branch}" | sed 's|/|-|g'
}

# ブランチ選択メニューを表示
show_branch_menu() {
    local branches=("$@")
    local existing_worktrees
    
    existing_worktrees=$(get_existing_worktrees)
    
    echo ""
    print_info "=== Claude Task ブランチ一覧 ==="
    
    local i=1
    for branch in "${branches[@]}"; do
        local worktree_name
        worktree_name=$(generate_worktree_name "$branch")
        
        if echo "$existing_worktrees" | grep -q "^$worktree_name$"; then
            printf "%2d) %s \e[33m(既存)\e[0m\n" "$i" "$branch"
        else
            printf "%2d) %s\n" "$i" "$branch"
        fi
        ((i++))
    done
    
    echo ""
    echo " 0) 終了"
    echo ""
}

# ユーザーの選択を取得
get_user_choice() {
    local max_choice=$1
    local choice
    
    while true; do
        read -p "ブランチを選択してください (0-$max_choice): " choice
        
        if [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -ge 0 ]] && [[ "$choice" -le "$max_choice" ]]; then
            echo "$choice"
            return 0
        else
            print_error "無効な選択です。0から$max_choice の番号を入力してください。"
        fi
    done
}

# ワークツリーを作成
create_worktree() {
    local branch=$1
    local worktree_name
    local worktree_path
    
    worktree_name=$(generate_worktree_name "$branch")
    worktree_path="../$worktree_name"
    
    print_info "ワークツリーを作成中: $branch -> $worktree_path"
    
    # ワークツリー作成
    if ! git worktree add "$worktree_path" "origin/$branch"; then
        print_error "ワークツリーの作成に失敗しました"
        return 1
    fi
    
    print_success "ワークツリーを作成しました: $worktree_path"
    
    # ワークツリーディレクトリに移動してセットアップスクリプトの案内
    echo ""
    print_info "次のステップ:"
    echo "  cd $worktree_path"
    echo "  ./scripts/setup_worktree.sh"
    echo ""
    print_info "または、自動セットアップを実行:"
    echo "  cd $worktree_path && ./scripts/setup_worktree.sh --auto"
}

# メイン処理
main() {
    cd "$PROJECT_ROOT"
    
    print_info "WordPress Bizcan ワークツリー作成ツール"
    print_info "Current directory: $(pwd)"
    
    # ブランチ一覧を取得
    local branches
    if ! mapfile -t branches < <(get_claude_task_branches); then
        print_error "claude-task/ ブランチが見つかりません"
        print_info "使用可能なブランチを確認してください:"
        echo "  git branch -r | grep claude-task"
        exit 1
    fi
    
    if [[ ${#branches[@]} -eq 0 ]]; then
        print_error "claude-task/ ブランチが見つかりません"
        exit 1
    fi
    
    # メニュー表示
    show_branch_menu "${branches[@]}"
    
    # ユーザー選択
    local choice
    choice=$(get_user_choice ${#branches[@]})
    
    if [[ "$choice" -eq 0 ]]; then
        print_info "終了します"
        exit 0
    fi
    
    # 選択されたブランチ
    local selected_branch="${branches[$((choice - 1))]}"
    local worktree_name
    worktree_name=$(generate_worktree_name "$selected_branch")
    
    # 既存チェック
    if get_existing_worktrees | grep -q "^$worktree_name$"; then
        print_warn "ワークツリー '$worktree_name' は既に存在します"
        read -p "再作成しますか？ (y/N): " confirm
        if [[ ! "$confirm" =~ ^[yY]$ ]]; then
            print_info "キャンセルしました"
            exit 0
        fi
        
        print_info "既存のワークツリーを削除中..."
        git worktree remove "../$worktree_name" --force || {
            print_error "既存ワークツリーの削除に失敗しました"
            exit 1
        }
    fi
    
    # ワークツリー作成
    create_worktree "$selected_branch"
}

# 使用法表示
show_usage() {
    cat << EOF
WordPress Bizcan ワークツリー作成ツール

使用法:
  $0                  # 対話式でブランチを選択
  $0 --help          # この使用法を表示

このスクリプトは claude-task/ プレフィックスのブランチから
選択してワークツリーを作成します。

作成後は以下のコマンドでDocker環境をセットアップできます:
  cd ../ワークツリー名
  ./scripts/setup_worktree.sh
EOF
}

# コマンドライン引数の処理
case "${1:-}" in
    --help|-h)
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac