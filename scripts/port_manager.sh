#!/bin/bash

# ポート管理ライブラリ
# worktree環境用の動的ポート割り当て機能

# 設定
readonly CONTAINERS_FILE=".worktree-containers.json"
readonly WP_PORT_MIN=20080
readonly WP_PORT_MAX=20099
readonly DB_PORT_MIN=15081
readonly DB_PORT_MAX=15099

# JSONファイルの初期化
init_containers_file() {
    if [[ ! -f "$CONTAINERS_FILE" ]]; then
        cat > "$CONTAINERS_FILE" << 'EOF'
{
  "active_containers": {},
  "port_allocations": {
    "wordpress": {
      "range": [20080, 20099],
      "allocated": []
    },
    "mysql": {
      "range": [15081, 15099],
      "allocated": []
    }
  },
  "last_updated": null
}
EOF
    fi
}

# ポートの使用状況確認
is_port_in_use() {
    local port=$1
    
    # ポートが使用中かチェック（netstatとssの両方をサポート）
    if command -v ss >/dev/null 2>&1; then
        ss -tuln | grep ":$port " >/dev/null 2>&1
    elif command -v netstat >/dev/null 2>&1; then
        netstat -tuln 2>/dev/null | grep ":$port " >/dev/null 2>&1
    else
        # フォールバック: Dockerコンテナのポート確認
        docker ps --format "table {{.Ports}}" 2>/dev/null | grep ":$port->" >/dev/null 2>&1
    fi
}

# 利用可能なポートを検索
find_available_port() {
    local service_type=$1  # "wordpress" or "mysql"
    local min_port max_port
    
    case "$service_type" in
        "wordpress")
            min_port=$WP_PORT_MIN
            max_port=$WP_PORT_MAX
            ;;
        "mysql")
            min_port=$DB_PORT_MIN
            max_port=$DB_PORT_MAX
            ;;
        *)
            echo "エラー: 不正なサービスタイプ: $service_type" >&2
            return 1
            ;;
    esac
    
    # JSONから既に割り当て済みのポートを取得
    local allocated_ports
    if [[ -f "$CONTAINERS_FILE" ]]; then
        allocated_ports=$(jq -r ".port_allocations.${service_type}.allocated[]" "$CONTAINERS_FILE" 2>/dev/null | sort -n)
    fi
    
    # 利用可能なポートを検索
    for port in $(seq $min_port $max_port); do
        # 既に割り当て済みかチェック
        if echo "$allocated_ports" | grep -q "^$port$"; then
            continue
        fi
        
        # 実際にポートが使用中かチェック
        if ! is_port_in_use "$port"; then
            echo "$port"
            return 0
        fi
    done
    
    echo "エラー: ${service_type}用の利用可能なポートが見つかりません (${min_port}-${max_port})" >&2
    return 1
}

# ポートを割り当てて記録
allocate_port() {
    local service_type=$1
    local branch_name=$2
    local port
    
    init_containers_file
    
    port=$(find_available_port "$service_type")
    if [[ $? -ne 0 ]]; then
        return 1
    fi
    
    # JSONファイルに記録
    local temp_file=$(mktemp)
    jq \
        --arg service_type "$service_type" \
        --arg branch "$branch_name" \
        --argjson port "$port" \
        --arg timestamp "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
        '
        .port_allocations[$service_type].allocated += [$port] |
        .active_containers[$branch][$service_type + "_port"] = $port |
        .last_updated = $timestamp
        ' \
        "$CONTAINERS_FILE" > "$temp_file" && mv "$temp_file" "$CONTAINERS_FILE"
    
    echo "$port"
}

# ポートの割り当て解除
deallocate_ports() {
    local branch_name=$1
    
    if [[ ! -f "$CONTAINERS_FILE" ]]; then
        return 0
    fi
    
    # ブランチに割り当てられたポートを取得
    local wp_port=$(jq -r ".active_containers.\"$branch_name\".wordpress_port // empty" "$CONTAINERS_FILE")
    local db_port=$(jq -r ".active_containers.\"$branch_name\".mysql_port // empty" "$CONTAINERS_FILE")
    
    # JSONから削除
    local temp_file=$(mktemp)
    jq \
        --arg branch "$branch_name" \
        --argjson wp_port "${wp_port:-null}" \
        --argjson db_port "${db_port:-null}" \
        --arg timestamp "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
        '
        if $wp_port then .port_allocations.wordpress.allocated -= [$wp_port] else . end |
        if $db_port then .port_allocations.mysql.allocated -= [$db_port] else . end |
        del(.active_containers[$branch]) |
        .last_updated = $timestamp
        ' \
        "$CONTAINERS_FILE" > "$temp_file" && mv "$temp_file" "$CONTAINERS_FILE"
}

# 割り当て済みポート一覧表示
list_allocated_ports() {
    if [[ ! -f "$CONTAINERS_FILE" ]]; then
        echo "コンテナ管理ファイルが存在しません"
        return 1
    fi
    
    echo "=== アクティブなワークツリーコンテナ ==="
    jq -r '
        .active_containers | 
        to_entries[] | 
        "\(.key): WordPress=\(.value.wordpress_port // "未割当"), MySQL=\(.value.mysql_port // "未割当")"
    ' "$CONTAINERS_FILE" 2>/dev/null || echo "アクティブなコンテナはありません"
    
    echo ""
    echo "=== ポート使用状況 ==="
    echo "WordPress (20080-20099): $(jq -r '.port_allocations.wordpress.allocated | join(", ")' "$CONTAINERS_FILE" 2>/dev/null)"
    echo "MySQL (15081-15099): $(jq -r '.port_allocations.mysql.allocated | join(", ")' "$CONTAINERS_FILE" 2>/dev/null)"
}

# ブランチの情報を更新
update_container_info() {
    local branch_name=$1
    local container_name=$2
    local status=$3
    local url=$4
    
    init_containers_file
    
    local temp_file=$(mktemp)
    jq \
        --arg branch "$branch_name" \
        --arg container "$container_name" \
        --arg status "$status" \
        --arg url "$url" \
        --arg timestamp "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
        '
        .active_containers[$branch].container_name = $container |
        .active_containers[$branch].status = $status |
        .active_containers[$branch].url = $url |
        .active_containers[$branch].last_update = $timestamp |
        .last_updated = $timestamp
        ' \
        "$CONTAINERS_FILE" > "$temp_file" && mv "$temp_file" "$CONTAINERS_FILE"
}

# 使用法表示
show_usage() {
    cat << EOF
使用法: source port_manager.sh

利用可能な関数:
  find_available_port <service_type>     - 利用可能なポートを検索
  allocate_port <service_type> <branch>  - ポートを割り当て
  deallocate_ports <branch>              - ブランチのポート割り当て解除
  list_allocated_ports                   - 割り当て済みポート一覧表示
  update_container_info <branch> <name> <status> <url>  - コンテナ情報更新

service_type: "wordpress" または "mysql"
EOF
}

# 直接実行された場合は使用法を表示
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    show_usage
fi