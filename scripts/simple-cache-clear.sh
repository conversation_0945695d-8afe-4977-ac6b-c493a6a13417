#!/bin/bash

# シンプルなキャッシュクリアスクリプト
# エラーが発生しにくい基本的なキャッシュクリア機能のみ

echo "🧹 Starting simple cache clear process..."

# 0. PHP環境の詳細調査
echo "🔍 PHP Environment Investigation:"
echo "  System PHP: $(php -v 2>/dev/null | head -1 || echo 'Not found')"
echo "  PHP Binary: $(which php 2>/dev/null || echo 'Not found')"
echo "  WP-CLI PHP: $(wp --info 2>/dev/null | grep 'PHP version' || echo 'WP-CLI not available')"

# 複数のPHPバージョンが存在する可能性をチェック
for php_cmd in php php8.1 php81 /usr/bin/php /usr/local/bin/php; do
    if command -v "$php_cmd" >/dev/null 2>&1; then
        echo "  Found: $php_cmd -> $($php_cmd -v 2>/dev/null | head -1)"
    fi
done

# 1. 一般的なキャッシュディレクトリをクリア
if [ -d wp-content/cache ]; then
    find wp-content/cache -type f -delete 2>/dev/null || true
    echo "✅ wp-content/cache cleared"
else
    echo "ℹ️  wp-content/cache directory not found"
fi

# 2. オブジェクトキャッシュをクリア
if [ -f wp-content/object-cache.php ]; then
    rm -f wp-content/object-cache.php
    echo "✅ object-cache.php removed"
else
    echo "ℹ️  object-cache.php not found"
fi

# 3. WP-CLIを使用してキャッシュクリア（利用可能な場合）
if command -v wp >/dev/null 2>&1; then
    echo "🔧 Using WP-CLI for cache clearing..."

    # 適切なPHPバイナリを検出
    PHP_BINARY="php"
    for php_candidate in php8.1 php81 php; do
        if command -v "$php_candidate" >/dev/null 2>&1; then
            PHP_VER=$($php_candidate -r "echo PHP_VERSION;" 2>/dev/null || echo "unknown")
            if $php_candidate -r "exit(version_compare(PHP_VERSION, '7.0.0', '>=') ? 0 : 1);" 2>/dev/null; then
                PHP_BINARY="$php_candidate"
                echo "ℹ️  Using PHP Binary: $PHP_BINARY (Version: $PHP_VER)"
                break
            fi
        fi
    done

    # WP-CLIに適切なPHPバイナリを指定して実行
    echo "🔧 Executing WP-CLI commands with $PHP_BINARY..."

    # キャッシュクリアコマンドを実行
    $PHP_BINARY $(which wp) cache flush --allow-root 2>/dev/null || echo "⚠️  WP cache flush failed"
    $PHP_BINARY $(which wp) rewrite flush --allow-root 2>/dev/null || echo "⚠️  WP rewrite flush failed"

    # Autoptimizeプラグインのキャッシュクリア（WP-CLI経由）
    $PHP_BINARY $(which wp) autoptimize clear --allow-root 2>/dev/null || echo "ℹ️  Autoptimize cache clear not available"

    echo "✅ WP-CLI cache flush completed"
else
    echo "ℹ️  WP-CLI not available"
fi

# 4. 一般的なキャッシュプラグインのキャッシュディレクトリをクリア
CACHE_DIRS=(
    "wp-content/cache"
    "wp-content/uploads/cache"
    "wp-content/w3tc-cache"
    "wp-content/wp-rocket-cache"
    "wp-content/litespeed-cache"
    "wp-content/cache/autoptimize"
    "wp-content/uploads/autoptimize"
)

for cache_dir in "${CACHE_DIRS[@]}"; do
    if [ -d "$cache_dir" ]; then
        find "$cache_dir" -type f -name "*.php" -delete 2>/dev/null || true
        find "$cache_dir" -type f -name "*.html" -delete 2>/dev/null || true
        find "$cache_dir" -type f -name "*.css" -delete 2>/dev/null || true
        find "$cache_dir" -type f -name "*.js" -delete 2>/dev/null || true
        echo "✅ Cleared cache directory: $cache_dir"
    fi
done

# 5. Autoptimizeの手動キャッシュクリア（PHPが古い場合の代替手段）
echo "🔧 Manual Autoptimize cache clearing..."
AUTOPTIMIZE_DIRS=(
    "wp-content/cache/autoptimize"
    "wp-content/uploads/autoptimize"
    "wp-content/cache/minify"
)

for ao_dir in "${AUTOPTIMIZE_DIRS[@]}"; do
    if [ -d "$ao_dir" ]; then
        # Autoptimizeのキャッシュファイルを削除
        find "$ao_dir" -type f \( -name "*.css" -o -name "*.js" -o -name "*.json" \) -delete 2>/dev/null || true
        echo "✅ Cleared Autoptimize cache: $ao_dir"
    fi
done

echo "🎉 Simple cache clear process completed at $(date '+%Y-%m-%d %H:%M:%S')"
