#!/bin/bash

# ワークツリー環境のDocker操作統一管理スクリプト
# start/stop/restart/status サブコマンドによる操作

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
readonly PORT_MANAGER="$SCRIPT_DIR/port_manager.sh"

# ポート管理ライブラリの読み込み
if [[ ! -f "$PORT_MANAGER" ]]; then
    echo "エラー: ポート管理ライブラリが見つかりません: $PORT_MANAGER" >&2
    exit 1
fi
source "$PORT_MANAGER"

# 色付きメッセージ
print_info() { echo -e "\e[36m[INFO]\e[0m $*"; }
print_success() { echo -e "\e[32m[SUCCESS]\e[0m $*"; }
print_warn() { echo -e "\e[33m[WARN]\e[0m $*"; }
print_error() { echo -e "\e[31m[ERROR]\e[0m $*"; }

# 現在のブランチ名を取得
get_current_branch() {
    git rev-parse --abbrev-ref HEAD 2>/dev/null || {
        print_error "Gitブランチの取得に失敗しました"
        return 1
    }
}

# ブランチ名をDocker安全な形式に変換
sanitize_branch_name() {
    local branch=$1
    echo "$branch" | sed 's|[^a-zA-Z0-9._-]|_|g' | tr '[:upper:]' '[:lower:]'
}

# Docker Composeファイルのパスを取得
get_compose_file() {
    local branch_safe=$1
    echo "$PROJECT_ROOT/docker-compose.$branch_safe.yml"
}

# コンテナの状態を取得
get_container_status() {
    local branch_safe=$1
    local containers=(
        "wordpress-bizcan-$branch_safe"
        "bizcan-db-$branch_safe"
    )
    
    for container in "${containers[@]}"; do
        if docker ps --format "{{.Names}}\t{{.Status}}" | grep -q "^$container"; then
            echo "$container: $(docker ps --format "{{.Status}}" --filter "name=$container")"
        elif docker ps -a --format "{{.Names}}\t{{.Status}}" | grep -q "^$container"; then
            echo "$container: $(docker ps -a --format "{{.Status}}" --filter "name=$container")"
        else
            echo "$container: 存在しません"
        fi
    done
}

# アクティブなワークツリーコンテナ一覧を表示
list_all_containers() {
    print_info "=== ワークツリーコンテナ一覧 ==="
    
    if [[ ! -f "$CONTAINERS_FILE" ]]; then
        print_warn "コンテナ管理ファイルが存在しません"
        return 0
    fi
    
    # JSONから情報を取得
    local containers
    containers=$(jq -r '
        .active_containers | 
        to_entries[] | 
        "\(.key)|\(.value.wordpress_port // "未設定")|\(.value.mysql_port // "未設定")|\(.value.status // "不明")|\(.value.url // "未設定")"
    ' "$CONTAINERS_FILE" 2>/dev/null || echo "")
    
    if [[ -z "$containers" ]]; then
        print_info "アクティブなワークツリーコンテナはありません"
        return 0
    fi
    
    printf "%-30s %-8s %-8s %-12s %s\n" "ワークツリー" "WP Port" "DB Port" "ステータス" "URL"
    printf "%-30s %-8s %-8s %-12s %s\n" "$(printf '%*s' 30 '' | tr ' ' '-')" "$(printf '%*s' 8 '' | tr ' ' '-')" "$(printf '%*s' 8 '' | tr ' ' '-')" "$(printf '%*s' 12 '' | tr ' ' '-')" "$(printf '%*s' 20 '' | tr ' ' '-')"
    
    echo "$containers" | while IFS='|' read -r name wp_port db_port status url; do
        # 実際のコンテナ状態を確認
        local actual_status="不明"
        local container_name="wordpress-bizcan-$name"
        
        if docker ps --format "{{.Names}}" | grep -q "^$container_name$"; then
            actual_status="実行中"
        elif docker ps -a --format "{{.Names}}" | grep -q "^$container_name$"; then
            actual_status="停止中"
        else
            actual_status="存在しない"
        fi
        
        printf "%-30s %-8s %-8s %-12s %s\n" "$name" "$wp_port" "$db_port" "$actual_status" "$url"
    done
}

# オリジナルのデータベースコンテナを確認・起動
ensure_database_running() {
    local db_container="bizcan"
    
    # コンテナの状態を確認
    if docker ps --format "{{.Names}}" | grep -q "^$db_container$"; then
        return 0
    fi
    
    # コンテナが存在するが停止している場合
    if docker ps -a --format "{{.Names}}" | grep -q "^$db_container$"; then
        print_info "データベースコンテナが停止しています"
        print_info "docker-composeを使用してデータベースを起動中..."
        
        # docker-composeを使用して起動（整合性を保つため）
        cd "$PROJECT_ROOT"
        if docker-compose up -d db; then
            print_success "データベースコンテナを起動しました"
            sleep 5
            return 0
        else
            print_error "データベースコンテナの起動に失敗しました"
            return 1
        fi
    fi
    
    # コンテナが存在しない場合
    print_error "データベースコンテナが存在しません"
    print_info "docker-composeを使用してデータベースを起動してください:"
    print_info "  cd $PROJECT_ROOT && docker-compose up -d db"
    return 1
}

# コンテナを開始
start_containers() {
    local branch_safe=$1
    local compose_file
    compose_file=$(get_compose_file "$branch_safe")
    
    if [[ ! -f "$compose_file" ]]; then
        print_error "Docker Composeファイルが見つかりません: $compose_file"
        print_info "セットアップスクリプトを実行してください: ./scripts/setup_worktree.sh"
        return 1
    fi
    
    # データベースコンテナの確認
    if ! ensure_database_running; then
        return 1
    fi
    
    print_info "コンテナを開始中: $branch_safe"
    
    if docker-compose -f "$compose_file" up -d; then
        print_success "コンテナを開始しました: $branch_safe"
        
        # ステータスを更新
        if [[ -f "$CONTAINERS_FILE" ]]; then
            local wp_port
            wp_port=$(jq -r ".active_containers.\"$branch_safe\".wordpress_port // empty" "$CONTAINERS_FILE")
            local url="http://localhost:$wp_port"
            update_container_info "$branch_safe" "wordpress-bizcan-$branch_safe" "running" "$url"
            
            # WordPressの起動を待つ
            local container_name="wordpress-bizcan-$branch_safe"
            print_info "WordPressの起動を待機中..."
            local max_attempts=30
            local attempt=0
            while [ $attempt -lt $max_attempts ]; do
                if docker exec "$container_name" wp-cli.phar --allow-root --path=/var/www/html core is-installed 2>/dev/null; then
                    break
                fi
                sleep 2
                ((attempt++))
            done
            
            if [ $attempt -lt $max_attempts ]; then
                # WordPressのサイトURLを更新
                print_info "WordPressのサイトURLを更新中..."
                
                # wp-cliを使用してサイトURLを更新
                if docker exec "$container_name" wp-cli.phar --allow-root --path=/var/www/html option update siteurl "$url" 2>/dev/null && \
                   docker exec "$container_name" wp-cli.phar --allow-root --path=/var/www/html option update home "$url" 2>/dev/null; then
                    print_success "WordPressのサイトURLを更新しました: $url"
                else
                    print_warn "WordPressのサイトURL更新に失敗しました。手動で更新が必要かもしれません。"
                fi
            fi
        fi
        
        # アクセス情報を表示
        get_container_status "$branch_safe"
    else
        print_error "コンテナの開始に失敗しました: $branch_safe"
        return 1
    fi
}

# コンテナを停止
stop_containers() {
    local branch_safe=$1
    local compose_file
    compose_file=$(get_compose_file "$branch_safe")
    
    if [[ ! -f "$compose_file" ]]; then
        print_warn "Docker Composeファイルが見つかりません: $compose_file"
        print_info "個別のコンテナ停止を試行します"
        
        # 個別停止
        local containers=(
            "wordpress-bizcan-$branch_safe"
            "bizcan-db-$branch_safe"
        )
        
        for container in "${containers[@]}"; do
            if docker ps --format "{{.Names}}" | grep -q "^$container$"; then
                print_info "コンテナを停止: $container"
                docker stop "$container" >/dev/null 2>&1 || {
                    print_warn "コンテナの停止に失敗: $container"
                }
            fi
        done
    else
        print_info "コンテナを停止中: $branch_safe"
        
        if docker-compose -f "$compose_file" stop; then
            print_success "コンテナを停止しました: $branch_safe"
        else
            print_error "コンテナの停止に失敗しました: $branch_safe"
            return 1
        fi
    fi
    
    # ステータスを更新
    if [[ -f "$CONTAINERS_FILE" ]]; then
        update_container_info "$branch_safe" "wordpress-bizcan-$branch_safe" "stopped" ""
    fi
}

# コンテナを再起動
restart_containers() {
    local branch_safe=$1
    
    print_info "コンテナを再起動中: $branch_safe"
    
    stop_containers "$branch_safe"
    sleep 2
    start_containers "$branch_safe"
}

# コンテナの状態を表示
show_status() {
    local branch_safe=$1
    
    print_info "=== コンテナ状態: $branch_safe ==="
    
    get_container_status "$branch_safe"
    
    # ポート情報を表示
    if [[ -f "$CONTAINERS_FILE" ]]; then
        local wp_port db_port url
        wp_port=$(jq -r ".active_containers.\"$branch_safe\".wordpress_port // empty" "$CONTAINERS_FILE")
        db_port=$(jq -r ".active_containers.\"$branch_safe\".mysql_port // empty" "$CONTAINERS_FILE")
        url=$(jq -r ".active_containers.\"$branch_safe\".url // empty" "$CONTAINERS_FILE")
        
        if [[ -n "$wp_port" ]]; then
            echo ""
            print_info "ポート情報:"
            echo "  WordPress: $wp_port"
            echo "  MySQL: $db_port"
            echo "  URL: $url"
        fi
    fi
    
    # Docker Composeファイルの存在確認
    local compose_file
    compose_file=$(get_compose_file "$branch_safe")
    if [[ -f "$compose_file" ]]; then
        print_info "Docker Composeファイル: $compose_file"
    else
        print_warn "Docker Composeファイルが見つかりません: $compose_file"
    fi
}

# ログを表示
show_logs() {
    local branch_safe=$1
    local compose_file
    compose_file=$(get_compose_file "$branch_safe")
    
    if [[ ! -f "$compose_file" ]]; then
        print_error "Docker Composeファイルが見つかりません: $compose_file"
        return 1
    fi
    
    print_info "ログを表示: $branch_safe"
    docker-compose -f "$compose_file" logs -f
}

# メイン処理
main() {
    local command="${1:-}"
    local target_branch=""
    
    # 引数の処理
    if [[ $# -gt 1 ]]; then
        target_branch="$2"
    fi
    
    cd "$PROJECT_ROOT"
    
    # コマンドに応じた処理
    case "$command" in
        "start")
            if [[ -n "$target_branch" ]]; then
                local branch_safe
                branch_safe=$(sanitize_branch_name "$target_branch")
                start_containers "$branch_safe"
            else
                # 現在のブランチで開始
                local branch
                branch=$(get_current_branch)
                local branch_safe
                branch_safe=$(sanitize_branch_name "$branch")
                start_containers "$branch_safe"
            fi
            ;;
        "stop")
            if [[ -n "$target_branch" ]]; then
                local branch_safe
                branch_safe=$(sanitize_branch_name "$target_branch")
                stop_containers "$branch_safe"
            else
                # 現在のブランチで停止
                local branch
                branch=$(get_current_branch)
                local branch_safe
                branch_safe=$(sanitize_branch_name "$branch")
                stop_containers "$branch_safe"
            fi
            ;;
        "restart")
            if [[ -n "$target_branch" ]]; then
                local branch_safe
                branch_safe=$(sanitize_branch_name "$target_branch")
                restart_containers "$branch_safe"
            else
                # 現在のブランチで再起動
                local branch
                branch=$(get_current_branch)
                local branch_safe
                branch_safe=$(sanitize_branch_name "$branch")
                restart_containers "$branch_safe"
            fi
            ;;
        "status")
            if [[ -n "$target_branch" ]]; then
                local branch_safe
                branch_safe=$(sanitize_branch_name "$target_branch")
                show_status "$branch_safe"
            else
                # 現在のブランチのステータス
                local branch
                branch=$(get_current_branch)
                local branch_safe
                branch_safe=$(sanitize_branch_name "$branch")
                show_status "$branch_safe"
            fi
            ;;
        "logs")
            if [[ -n "$target_branch" ]]; then
                local branch_safe
                branch_safe=$(sanitize_branch_name "$target_branch")
                show_logs "$branch_safe"
            else
                # 現在のブランチのログ
                local branch
                branch=$(get_current_branch)
                local branch_safe
                branch_safe=$(sanitize_branch_name "$branch")
                show_logs "$branch_safe"
            fi
            ;;
        "list")
            list_all_containers
            ;;
        "ports")
            list_allocated_ports
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# 使用法表示
show_usage() {
    cat << EOF
WordPress Bizcan ワークツリーDocker管理ツール

使用法:
  $0 <command> [branch_name]

コマンド:
  start [branch]      コンテナを開始（デフォルト: 現在のブランチ）
  stop [branch]       コンテナを停止（デフォルト: 現在のブランチ）
  restart [branch]    コンテナを再起動（デフォルト: 現在のブランチ）
  status [branch]     コンテナの状態を表示（デフォルト: 現在のブランチ）
  logs [branch]       コンテナのログを表示（デフォルト: 現在のブランチ）
  list                すべてのワークツリーコンテナを一覧表示
  ports               ポート割り当て状況を表示

例:
  $0 start                              # 現在のブランチのコンテナを開始
  $0 stop claude-task-50-column-sticky-toc  # 特定のブランチのコンテナを停止
  $0 status                             # 現在のブランチの状態を確認
  $0 list                               # すべてのワークツリーコンテナを表示
  $0 ports                              # ポート使用状況を表示

注意:
- branch_name は元のブランチ名で指定してください（自動的にDocker安全名に変換されます）
- コンテナが存在しない場合は、セットアップスクリプトを先に実行してください
EOF
}

# コマンドライン引数の処理
case "${1:-help}" in
    --help|-h|help)
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac