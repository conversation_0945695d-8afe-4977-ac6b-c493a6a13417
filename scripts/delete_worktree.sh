#!/bin/bash

# ワークツリー完全削除スクリプト
# ワークツリーとDockerリソースの完全クリーンアップ

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
readonly PORT_MANAGER="$SCRIPT_DIR/port_manager.sh"

# ポート管理ライブラリの読み込み
if [[ ! -f "$PORT_MANAGER" ]]; then
    echo "エラー: ポート管理ライブラリが見つかりません: $PORT_MANAGER" >&2
    exit 1
fi
source "$PORT_MANAGER"

# 色付きメッセージ
print_info() { echo -e "\e[36m[INFO]\e[0m $*"; }
print_success() { echo -e "\e[32m[SUCCESS]\e[0m $*"; }
print_warn() { echo -e "\e[33m[WARN]\e[0m $*"; }
print_error() { echo -e "\e[31m[ERROR]\e[0m $*"; }

# 既存のワークツリー一覧を取得
get_existing_worktrees() {
    git worktree list --porcelain 2>/dev/null | \
        awk '
        /^worktree/ { worktree = $2; gsub(/.*\//, "", worktree) }
        /^branch/ { gsub(/.*\//, "", $2); branch = $2 }
        /^$/ { if (worktree && worktree != "wordpress-bizcan") print worktree ":" branch; worktree = ""; branch = "" }
        END { if (worktree && worktree != "wordpress-bizcan") print worktree ":" branch }
        ' | \
        grep -v "^$" || true
}

# アクティブなコンテナ一覧を取得
get_active_containers() {
    if [[ ! -f "$CONTAINERS_FILE" ]]; then
        return 0
    fi
    
    jq -r '.active_containers | keys[]' "$CONTAINERS_FILE" 2>/dev/null || true
}

# ワークツリー選択メニューを表示
show_worktree_menu() {
    local worktrees=("$@")
    local active_containers
    
    active_containers=$(get_active_containers)
    
    echo ""
    print_info "=== 削除可能なワークツリー一覧 ==="
    
    local i=1
    for entry in "${worktrees[@]}"; do
        local worktree_name="${entry%%:*}"
        local branch_name="${entry##*:}"
        
        # アクティブなコンテナかチェック
        if echo "$active_containers" | grep -q "^$worktree_name$"; then
            printf "%2d) %s (branch: %s) \e[31m[Docker活動中]\e[0m\n" "$i" "$worktree_name" "$branch_name"
        else
            printf "%2d) %s (branch: %s)\n" "$i" "$worktree_name" "$branch_name"
        fi
        ((i++))
    done
    
    echo ""
    echo " a) すべて削除"
    echo " 0) 終了"
    echo ""
}

# ユーザーの選択を取得
get_user_choice() {
    local max_choice=$1
    local choice
    
    while true; do
        read -p "削除するワークツリーを選択してください (0-$max_choice/a): " choice
        
        if [[ "$choice" == "0" ]] || [[ "$choice" == "a" ]]; then
            echo "$choice"
            return 0
        elif [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -ge 1 ]] && [[ "$choice" -le "$max_choice" ]]; then
            echo "$choice"
            return 0
        else
            print_error "無効な選択です。0-$max_choice, 'a' のいずれかを入力してください。"
        fi
    done
}

# Dockerコンテナとリソースを削除
cleanup_docker_resources() {
    local branch_safe=$1
    
    print_info "Dockerリソースを削除中: $branch_safe"
    
    # Docker Composeファイルのパス
    local compose_file="$PROJECT_ROOT/docker-compose.$branch_safe.yml"
    
    # コンテナを停止・削除
    if [[ -f "$compose_file" ]]; then
        # 起動中のコンテナを確認
        cd "$PROJECT_ROOT"
        local running_containers
        running_containers=$(docker-compose -f "$compose_file" ps -q 2>/dev/null | wc -l)
        
        if [[ "$running_containers" -gt 0 ]]; then
            print_warn "起動中のDockerコンテナを検出しました"
            print_info "コンテナを停止中..."
            docker-compose -f "$compose_file" stop 2>/dev/null || {
                print_warn "コンテナの停止に失敗しました"
            }
        fi
        
        print_info "Docker Composeファイルを使用してコンテナを削除: $compose_file"
        docker-compose -f "$compose_file" down --volumes --remove-orphans 2>/dev/null || {
            print_warn "Docker Composeによる削除に失敗しました。個別削除を試行します。"
        }
        
        # Composeファイルを削除
        rm -f "$compose_file"
        print_info "Docker Composeファイルを削除: $compose_file"
    fi
    
    # 個別コンテナ削除（フォールバック）
    local containers=(
        "wordpress-bizcan-$branch_safe"
        "bizcan-db-$branch_safe"
    )
    
    for container in "${containers[@]}"; do
        if docker ps -a --format "{{.Names}}" | grep -q "^$container$"; then
            # 起動中のコンテナかを確認
            if docker ps --format "{{.Names}}" | grep -q "^$container$"; then
                print_info "起動中のコンテナを停止: $container"
                docker stop "$container" 2>/dev/null || {
                    print_warn "コンテナの停止に失敗: $container"
                }
            fi
            
            print_info "コンテナを削除: $container"
            docker rm -f "$container" 2>/dev/null || {
                print_warn "コンテナの削除に失敗: $container"
            }
        fi
    done
    
    # ボリューム削除
    local volumes=(
        "bizcan-db-$branch_safe"
    )
    
    for volume in "${volumes[@]}"; do
        if docker volume ls --format "{{.Name}}" | grep -q "^$volume$"; then
            print_info "ボリュームを削除: $volume"
            docker volume rm "$volume" 2>/dev/null || {
                print_warn "ボリュームの削除に失敗: $volume"
            }
        fi
    done
    
    # ネットワーク削除
    local network="bizcan-worktree-$branch_safe"
    if docker network ls --format "{{.Name}}" | grep -q "^$network$"; then
        print_info "ネットワークを削除: $network"
        docker network rm "$network" 2>/dev/null || {
            print_warn "ネットワークの削除に失敗: $network"
        }
    fi
    
    # 未使用リソースのクリーンアップ
    print_info "未使用Dockerリソースをクリーンアップ中..."
    docker system prune -f >/dev/null 2>&1 || true
}

# ワークツリーを削除
delete_worktree() {
    local worktree_entry=$1
    local worktree_name="${worktree_entry%%:*}"
    local branch_name="${worktree_entry##*:}"
    local worktree_path="../$worktree_name"
    
    print_info "ワークツリーを削除中: $worktree_name (branch: $branch_name)"
    
    # Dockerリソースをクリーンアップ
    cleanup_docker_resources "$worktree_name"
    
    # ポート割り当て解除
    deallocate_ports "$worktree_name"
    
    # Gitワークツリーを削除
    if git worktree list --porcelain | grep -q "^worktree.*/$worktree_name$"; then
        print_info "Gitワークツリーを削除: $worktree_path"
        git worktree remove "$worktree_path" --force 2>/dev/null || {
            print_warn "Gitワークツリーの削除に失敗: $worktree_path"
            # 強制削除を試行
            if [[ -d "$worktree_path" ]]; then
                print_info "ディレクトリを強制削除: $worktree_path"
                rm -rf "$worktree_path" || {
                    print_error "ディレクトリの削除に失敗: $worktree_path"
                }
            fi
        }
    fi
    
    # ローカルブランチを削除（必要に応じて）
    if git branch --list | grep -q "  $branch_name$"; then
        read -p "ローカルブランチ '$branch_name' も削除しますか？ (y/N): " delete_branch
        if [[ "$delete_branch" =~ ^[yY]$ ]]; then
            print_info "ローカルブランチを削除: $branch_name"
            git branch -D "$branch_name" 2>/dev/null || {
                print_warn "ローカルブランチの削除に失敗: $branch_name"
            }
        fi
    fi
    
    # 関連ファイルを削除
    local env_file="$PROJECT_ROOT/.env.worktree"
    if [[ -f "$env_file" ]]; then
        print_info "環境設定ファイルを削除: $env_file"
        rm -f "$env_file"
    fi
    
    print_success "ワークツリー '$worktree_name' の削除が完了しました"
}

# すべてのワークツリーを削除
delete_all_worktrees() {
    local worktrees=("$@")
    
    print_warn "すべてのワークツリーを削除します"
    echo "対象: ${#worktrees[@]} 個のワークツリー"
    for entry in "${worktrees[@]}"; do
        echo "  - ${entry%%:*} (${entry##*:})"
    done
    echo ""
    
    read -p "本当にすべて削除しますか？ (y/N): " confirm
    if [[ ! "$confirm" =~ ^[yY](es)?$ ]]; then
        print_info "削除をキャンセルしました"
        return 0
    fi
    
    for entry in "${worktrees[@]}"; do
        delete_worktree "$entry"
        echo ""
    done
    
    print_success "すべてのワークツリーの削除が完了しました"
}

# メイン処理
main() {
    local force_mode=false
    local target_worktree=""
    
    # コマンドライン引数の処理
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_mode=true
                shift
                ;;
            --worktree)
                target_worktree="$2"
                shift 2
                ;;
            *)
                print_error "不明なオプション: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    cd "$PROJECT_ROOT"
    
    print_info "WordPress Bizcan ワークツリー削除ツール"
    print_info "Current directory: $(pwd)"
    
    # ワークツリー一覧を取得
    local worktrees
    if ! mapfile -t worktrees < <(get_existing_worktrees); then
        print_error "ワークツリー一覧の取得に失敗しました"
        exit 1
    fi
    
    if [[ ${#worktrees[@]} -eq 0 ]]; then
        print_info "削除可能なワークツリーはありません"
        exit 0
    fi
    
    # 特定のワークツリーを削除（--worktreeオプション）
    if [[ -n "$target_worktree" ]]; then
        local found=false
        for entry in "${worktrees[@]}"; do
            if [[ "${entry%%:*}" == "$target_worktree" ]]; then
                if [[ "$force_mode" == true ]]; then
                    delete_worktree "$entry"
                else
                    print_warn "ワークツリー '$target_worktree' を削除します"
                    read -p "続行しますか？ (y/N): " confirm
                    if [[ "$confirm" =~ ^[yY]$ ]]; then
                        delete_worktree "$entry"
                    else
                        print_info "削除をキャンセルしました"
                    fi
                fi
                found=true
                break
            fi
        done
        
        if [[ "$found" == false ]]; then
            print_error "ワークツリー '$target_worktree' が見つかりません"
            exit 1
        fi
        
        return 0
    fi
    
    # 対話的選択モード
    show_worktree_menu "${worktrees[@]}"
    
    # ユーザー選択
    local choice
    choice=$(get_user_choice ${#worktrees[@]})
    
    if [[ "$choice" == "0" ]]; then
        print_info "終了します"
        exit 0
    elif [[ "$choice" == "a" ]]; then
        delete_all_worktrees "${worktrees[@]}"
    else
        local selected_entry="${worktrees[$((choice - 1))]}"
        delete_worktree "$selected_entry"
    fi
    
    print_success "削除処理が完了しました"
}

# 使用法表示
show_usage() {
    cat << EOF
WordPress Bizcan ワークツリー削除ツール

使用法:
  $0 [OPTIONS]

オプション:
  --force                    確認なしで削除実行
  --worktree <name>          特定のワークツリーを削除
  --help                     この使用法を表示

削除処理内容:
1. Dockerコンテナ・ボリューム・ネットワークの削除
2. Docker Composeファイルの削除
3. ポート割り当ての解除
4. Gitワークツリーの削除
5. ローカルブランチの削除（確認後）
6. 関連設定ファイルの削除

例:
  $0                         # 対話式で選択削除
  $0 --worktree claude-task-50-column-sticky-toc
  $0 --force --worktree claude-task-50-column-sticky-toc
EOF
}

# コマンドライン引数の処理
case "${1:-}" in
    --help|-h)
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac