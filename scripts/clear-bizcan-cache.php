<?php
/**
 * Bizcanアセットキャッシュクリアスクリプト
 * CI/CDパイプラインから実行される
 */

// WordPressの初期化（テーマを読み込まない）
define('WP_USE_THEMES', false);

// wp-load.phpのパスを検索
$wp_load_paths = [
    __DIR__ . '/../mount_wordpress/wp-load.php',
    __DIR__ . '/../wp-load.php',
    dirname(__DIR__) . '/wp-load.php',
    '/var/www/html/wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $wp_load_path) {
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
        $wp_loaded = true;
        echo "WordPress loaded from: $wp_load_path\n";
        break;
    }
}

if (!$wp_loaded) {
    echo "Error: wp-load.php not found\n";
    echo "Searched paths:\n";
    foreach ($wp_load_paths as $path) {
        echo "  - $path\n";
    }
    exit(1);
}

// EnqueueManagerクラスの存在確認
if (!class_exists('Bizcan\Utility\EnqueueManager')) {
    echo "Warning: Bizcan\Utility\EnqueueManager class not found\n";
    echo "Available classes:\n";
    $classes = get_declared_classes();
    $bizcan_classes = array_filter($classes, function($class) {
        return strpos($class, 'Bizcan') !== false;
    });
    foreach ($bizcan_classes as $class) {
        echo "  - $class\n";
    }
    exit(0);
}

try {
    // Bizcanアセットキャッシュをクリア
    \Bizcan\Utility\EnqueueManager::clearAssetCache();
    echo "✅ Bizcan asset cache cleared successfully\n";
    
    // 環境情報を表示
    if (class_exists('Bizcan\Utility\EnvironmentConfig')) {
        $env_type = \Bizcan\Utility\EnvironmentConfig::getEnvType();
        echo "Environment: $env_type\n";
        echo "Cache enabled: " . (\Bizcan\Utility\EnvironmentConfig::isCacheEnabled() ? 'YES' : 'NO') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error clearing Bizcan cache: " . $e->getMessage() . "\n";
    exit(1);
}

echo "🎉 Cache clearing completed at " . date('Y-m-d H:i:s') . "\n";
?>
