#!/bin/bash
# Edge をリモートデバッグモードで起動するスクリプト

# 既存のEdgeプロセスを終了
pkill -f "msedge" || true

# 一時的なユーザーデータディレクトリを作成
TEMP_DIR="/tmp/edge-debug-profile"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Edgeをリモートデバッグモードで起動
# Windowsの場合は msedge.exe を使用
if command -v microsoft-edge &> /dev/null; then
    microsoft-edge \
        --remote-debugging-port=9222 \
        --user-data-dir="$TEMP_DIR" \
        --disable-web-security \
        --disable-features=CrossSiteDocumentBlockingIfIsolating \
        --disable-site-isolation-trials \
        "http://localhost:20080/column/" &
elif command -v msedge &> /dev/null; then
    msedge \
        --remote-debugging-port=9222 \
        --user-data-dir="$TEMP_DIR" \
        --disable-web-security \
        --disable-features=CrossSiteDocumentBlockingIfIsolating \
        --disable-site-isolation-trials \
        "http://localhost:20080/column/" &
else
    echo "Microsoft Edge が見つかりません。インストールしてください。"
    exit 1
fi

echo "Edge がリモートデバッグモード（ポート 9222）で起動しました。"
echo "VSCode で 'Launch Edge (Attach to existing)' 設定を使用してアタッチしてください。"