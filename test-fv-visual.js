const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // ビューポートサイズを設定
  await page.setViewportSize({ width: 1920, height: 1080 });
  
  try {
    // ページにアクセス
    await page.goto('http://localhost:20085/new-owned-media/', {
      waitUntil: 'networkidle'
    });
    
    // スクリーンショットを撮影
    await page.screenshot({ 
      path: 'fv-section-current.png',
      fullPage: false
    });
    
    console.log('Screenshot saved as fv-section-current.png');
    
    // 少し待機してブラウザで確認できるようにする
    await page.waitForTimeout(5000);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
})();