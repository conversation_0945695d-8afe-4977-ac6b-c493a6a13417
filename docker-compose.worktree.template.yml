# ワークツリー環境用のDocker Compose テンプレート
# 環境変数による動的設定生成
# 使用方法: envsubst < docker-compose.worktree.template.yml > docker-compose.${WORKTREE_BRANCH}.yml

services:
  wordpress-${WOR<PERSON><PERSON>EE_BRANCH_SAFE}:
    build:
      context: ./dockerfile
      dockerfile: wpDockerfile
    image: appmart
    container_name: wordpress-appmart-${WORKTREE_BRANCH_SAFE}
    restart: 'no'
    ports:
      - "${WORDPRESS_PORT}:80"
    volumes:
      # WordPress全体をボリュームマウント（オリジナル環境と同じボリュームを使用）
      - wordpress_volume:/var/www/html
      
      # テーマディレクトリ（ワークツリーのものを使用）
      - ./mount_wordpress/wp-content/themes/appmart:/var/www/html/wp-content/themes/appmart
      
      # mu-plugins（Must Use Plugins）もバインドマウント
      - ./mount_wordpress/wp-content/mu-plugins:/var/www/html/wp-content/mu-plugins
      
      # その他の設定ファイル
      - ./conf/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
      - ./conf/apache2.conf:/etc/apache2/apache2.conf
      - ./conf/php.ini:/usr/local/etc/php/php.ini
      - ./log:/var/log
    
    environment:
      WORDPRESS_DB_HOST: appmart_db:3306
      WORDPRESS_DB_USER: mysql
      WORDPRESS_DB_PASSWORD: Okiu15ot
      WORDPRESS_DB_NAME: wp_mysql
      WSL_IP: ${WSL_IP}
      # WordPress環境設定
      WP_ENV: ${WP_ENV:-development}
      WORDPRESS_DEBUG: ${WORDPRESS_DEBUG:-1}
      # ワークツリー環境であることを識別
      IS_WORKTREE: "true"
      WORKTREE_BRANCH: ${WORKTREE_BRANCH}
      WORKTREE_PORT_WP: ${WORDPRESS_PORT}
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    external_links:
      - appmart_db:appmart_db

# オリジナル環境と同じボリュームを参照（WordPress本体）
volumes:
  wordpress_volume:
    external: true
    name: wordpress-appmart-volume