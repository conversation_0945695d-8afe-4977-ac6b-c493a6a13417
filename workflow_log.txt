trigger-check	Set up job	﻿2025-06-09T15:15:22.1471090Z Current runner version: '2.325.0'
trigger-check	Set up job	2025-06-09T15:15:22.1534580Z ##[group]Operating System
trigger-check	Set up job	2025-06-09T15:15:22.1536031Z Ubuntu
trigger-check	Set up job	2025-06-09T15:15:22.1537310Z 24.04.2
trigger-check	Set up job	2025-06-09T15:15:22.1538134Z LTS
trigger-check	Set up job	2025-06-09T15:15:22.1539060Z ##[endgroup]
trigger-check	Set up job	2025-06-09T15:15:22.1539955Z ##[group]Runner Image
trigger-check	Set up job	2025-06-09T15:15:22.1541092Z Image: ubuntu-24.04
trigger-check	Set up job	2025-06-09T15:15:22.1542160Z Version: 20250602.3.0
trigger-check	Set up job	2025-06-09T15:15:22.1543926Z Included Software: https://github.com/actions/runner-images/blob/ubuntu24/20250602.3/images/ubuntu/Ubuntu2404-Readme.md
trigger-check	Set up job	2025-06-09T15:15:22.1546322Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu24%2F20250602.3
trigger-check	Set up job	2025-06-09T15:15:22.1568640Z ##[endgroup]
trigger-check	Set up job	2025-06-09T15:15:22.1569587Z ##[group]Runner Image Provisioner
trigger-check	Set up job	2025-06-09T15:15:22.1570581Z 2.0.437.1
trigger-check	Set up job	2025-06-09T15:15:22.1571523Z ##[endgroup]
trigger-check	Set up job	2025-06-09T15:15:22.1573371Z ##[group]GITHUB_TOKEN Permissions
trigger-check	Set up job	2025-06-09T15:15:22.1576207Z Contents: read
trigger-check	Set up job	2025-06-09T15:15:22.1577440Z Metadata: read
trigger-check	Set up job	2025-06-09T15:15:22.1578288Z Packages: read
trigger-check	Set up job	2025-06-09T15:15:22.1579740Z ##[endgroup]
trigger-check	Set up job	2025-06-09T15:15:22.1582943Z Secret source: Actions
trigger-check	Set up job	2025-06-09T15:15:22.1584408Z Prepare workflow directory
trigger-check	Set up job	2025-06-09T15:15:22.2248383Z Prepare all required actions
trigger-check	Set up job	2025-06-09T15:15:22.2306146Z Getting action download info
trigger-check	Set up job	2025-06-09T15:15:22.7471592Z ##[group]Download immutable action package 'actions/checkout@v4'
trigger-check	Set up job	2025-06-09T15:15:22.7472753Z Version: 4.2.2
trigger-check	Set up job	2025-06-09T15:15:22.7473762Z Digest: sha256:ccb2698953eaebd21c7bf6268a94f9c26518a7e38e27e0b83c1fe1ad049819b1
trigger-check	Set up job	2025-06-09T15:15:22.7474992Z Source commit SHA: 11bd71901bbe5b1630ceea73d27597364c9af683
trigger-check	Set up job	2025-06-09T15:15:22.7475700Z ##[endgroup]
trigger-check	Set up job	2025-06-09T15:15:22.8997844Z Complete job name: trigger-check
trigger-check	Checkout repository	﻿2025-06-09T15:15:22.9769939Z ##[group]Run actions/checkout@v4
trigger-check	Checkout repository	2025-06-09T15:15:22.9771121Z with:
trigger-check	Checkout repository	2025-06-09T15:15:22.9771553Z   sparse-checkout: .github/scripts
trigger-check	Checkout repository	
trigger-check	Checkout repository	2025-06-09T15:15:22.9772091Z   repository: dkr-system/wordpress-appmart
trigger-check	Checkout repository	2025-06-09T15:15:22.9772791Z   token: ***
trigger-check	Checkout repository	2025-06-09T15:15:22.9773192Z   ssh-strict: true
trigger-check	Checkout repository	2025-06-09T15:15:22.9773565Z   ssh-user: git
trigger-check	Checkout repository	2025-06-09T15:15:22.9773956Z   persist-credentials: true
trigger-check	Checkout repository	2025-06-09T15:15:22.9774393Z   clean: true
trigger-check	Checkout repository	2025-06-09T15:15:22.9774774Z   sparse-checkout-cone-mode: true
trigger-check	Checkout repository	2025-06-09T15:15:22.9775250Z   fetch-depth: 1
trigger-check	Checkout repository	2025-06-09T15:15:22.9775632Z   fetch-tags: false
trigger-check	Checkout repository	2025-06-09T15:15:22.9776033Z   show-progress: true
trigger-check	Checkout repository	2025-06-09T15:15:22.9776442Z   lfs: false
trigger-check	Checkout repository	2025-06-09T15:15:22.9776964Z   submodules: false
trigger-check	Checkout repository	2025-06-09T15:15:22.9777368Z   set-safe-directory: true
trigger-check	Checkout repository	2025-06-09T15:15:22.9778084Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.1882566Z Syncing repository: dkr-system/wordpress-appmart
trigger-check	Checkout repository	2025-06-09T15:15:23.1885470Z ##[group]Getting Git version info
trigger-check	Checkout repository	2025-06-09T15:15:23.1886954Z Working directory is '/home/<USER>/work/wordpress-appmart/wordpress-appmart'
trigger-check	Checkout repository	2025-06-09T15:15:23.1888813Z [command]/usr/bin/git version
trigger-check	Checkout repository	2025-06-09T15:15:23.1891394Z git version 2.49.0
trigger-check	Checkout repository	2025-06-09T15:15:23.1894047Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.1900037Z Temporarily overriding HOME='/home/<USER>/work/_temp/d972dd55-0de3-4b59-8cae-b84ea9c12e59' before making global git config changes
trigger-check	Checkout repository	2025-06-09T15:15:23.1902298Z Adding repository directory to the temporary git global config as a safe directory
trigger-check	Checkout repository	2025-06-09T15:15:23.1904650Z [command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/wordpress-appmart/wordpress-appmart
trigger-check	Checkout repository	2025-06-09T15:15:23.1920144Z Deleting the contents of '/home/<USER>/work/wordpress-appmart/wordpress-appmart'
trigger-check	Checkout repository	2025-06-09T15:15:23.1924803Z ##[group]Initializing the repository
trigger-check	Checkout repository	2025-06-09T15:15:23.1931879Z [command]/usr/bin/git init /home/<USER>/work/wordpress-appmart/wordpress-appmart
trigger-check	Checkout repository	2025-06-09T15:15:23.2004046Z hint: Using 'master' as the name for the initial branch. This default branch name
trigger-check	Checkout repository	2025-06-09T15:15:23.2009693Z hint: is subject to change. To configure the initial branch name to use in all
trigger-check	Checkout repository	2025-06-09T15:15:23.2013643Z hint: of your new repositories, which will suppress this warning, call:
trigger-check	Checkout repository	2025-06-09T15:15:23.2014867Z hint:
trigger-check	Checkout repository	2025-06-09T15:15:23.2015718Z hint: 	git config --global init.defaultBranch <name>
trigger-check	Checkout repository	2025-06-09T15:15:23.2017472Z hint:
trigger-check	Checkout repository	2025-06-09T15:15:23.2018444Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
trigger-check	Checkout repository	2025-06-09T15:15:23.2019989Z hint: 'development'. The just-created branch can be renamed via this command:
trigger-check	Checkout repository	2025-06-09T15:15:23.2021229Z hint:
trigger-check	Checkout repository	2025-06-09T15:15:23.2021871Z hint: 	git branch -m <name>
trigger-check	Checkout repository	2025-06-09T15:15:23.2023678Z Initialized empty Git repository in /home/<USER>/work/wordpress-appmart/wordpress-appmart/.git/
trigger-check	Checkout repository	2025-06-09T15:15:23.2028611Z [command]/usr/bin/git remote add origin https://github.com/dkr-system/wordpress-appmart
trigger-check	Checkout repository	2025-06-09T15:15:23.2067768Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.2068949Z ##[group]Disabling automatic garbage collection
trigger-check	Checkout repository	2025-06-09T15:15:23.2072303Z [command]/usr/bin/git config --local gc.auto 0
trigger-check	Checkout repository	2025-06-09T15:15:23.2106290Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.2107681Z ##[group]Setting up auth
trigger-check	Checkout repository	2025-06-09T15:15:23.2114183Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
trigger-check	Checkout repository	2025-06-09T15:15:23.2149906Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
trigger-check	Checkout repository	2025-06-09T15:15:23.3202195Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
trigger-check	Checkout repository	2025-06-09T15:15:23.3206327Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
trigger-check	Checkout repository	2025-06-09T15:15:23.3210815Z [command]/usr/bin/git config --local http.https://github.com/.extraheader AUTHORIZATION: basic ***
trigger-check	Checkout repository	2025-06-09T15:15:23.3213916Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.3215001Z ##[group]Fetching the repository
trigger-check	Checkout repository	2025-06-09T15:15:23.3218048Z [command]/usr/bin/git -c protocol.version=2 fetch --no-tags --prune --no-recurse-submodules --filter=blob:none --depth=1 origin +02afe538962273e233f138f14b9a558525d1dd10:refs/remotes/origin/develop
trigger-check	Checkout repository	2025-06-09T15:15:23.9345291Z From https://github.com/dkr-system/wordpress-appmart
trigger-check	Checkout repository	2025-06-09T15:15:23.9347967Z  * [new ref]         02afe538962273e233f138f14b9a558525d1dd10 -> origin/develop
trigger-check	Checkout repository	2025-06-09T15:15:23.9391311Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.9393078Z ##[group]Determining the checkout info
trigger-check	Checkout repository	2025-06-09T15:15:23.9395892Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.9397478Z ##[group]Setting up sparse checkout
trigger-check	Checkout repository	2025-06-09T15:15:23.9398660Z [command]/usr/bin/git sparse-checkout set .github/scripts
trigger-check	Checkout repository	2025-06-09T15:15:23.9461488Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:23.9464640Z ##[group]Checking out the ref
trigger-check	Checkout repository	2025-06-09T15:15:23.9487319Z [command]/usr/bin/git checkout --progress --force -B develop refs/remotes/origin/develop
trigger-check	Checkout repository	2025-06-09T15:15:24.5412553Z Switched to a new branch 'develop'
trigger-check	Checkout repository	2025-06-09T15:15:24.5422625Z branch 'develop' set up to track 'origin/develop'.
trigger-check	Checkout repository	2025-06-09T15:15:24.5426455Z ##[endgroup]
trigger-check	Checkout repository	2025-06-09T15:15:24.5465916Z [command]/usr/bin/git log -1 --format=%H
trigger-check	Checkout repository	2025-06-09T15:15:24.5491846Z 02afe538962273e233f138f14b9a558525d1dd10
trigger-check	Check trigger	﻿2025-06-09T15:15:24.5727577Z ##[group]Run bash .github/scripts/check-trigger.sh
trigger-check	Check trigger	2025-06-09T15:15:24.5729368Z [36;1mbash .github/scripts/check-trigger.sh[0m
trigger-check	Check trigger	2025-06-09T15:15:24.5798722Z shell: /usr/bin/bash -e {0}
trigger-check	Check trigger	2025-06-09T15:15:24.5799973Z env:
trigger-check	Check trigger	2025-06-09T15:15:24.5802056Z   GH_TOKEN: ***
trigger-check	Check trigger	2025-06-09T15:15:24.5803079Z   ISSUE_NUMBER: 10
trigger-check	Check trigger	2025-06-09T15:15:24.5804213Z   REPOSITORY: dkr-system/wordpress-appmart
trigger-check	Check trigger	2025-06-09T15:15:24.5805597Z   EVENT_NAME: issue_comment
trigger-check	Check trigger	2025-06-09T15:15:24.5806960Z ##[endgroup]
trigger-check	Check trigger	2025-06-09T15:15:24.5963655Z ##[notice]Checking labels for issue #10
trigger-check	Check trigger	2025-06-09T15:15:25.1010645Z ##[notice]Processing issue #10
trigger-check	Post Checkout repository	﻿2025-06-09T15:15:25.1145147Z Post job cleanup.
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2142133Z [command]/usr/bin/git version
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2187302Z git version 2.49.0
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2245739Z Temporarily overriding HOME='/home/<USER>/work/_temp/6c80ebdc-942b-43a4-aae4-1028ce07479e' before making global git config changes
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2248572Z Adding repository directory to the temporary git global config as a safe directory
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2266552Z [command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/wordpress-appmart/wordpress-appmart
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2307760Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2357788Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2619071Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2649239Z http.https://github.com/.extraheader
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2664495Z [command]/usr/bin/git config --local --unset-all http.https://github.com/.extraheader
trigger-check	Post Checkout repository	2025-06-09T15:15:25.2704149Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
trigger-check	Complete job	﻿2025-06-09T15:15:25.3066278Z Evaluate and set job outputs
trigger-check	Complete job	2025-06-09T15:15:25.3072840Z Set output 'should_run'
trigger-check	Complete job	2025-06-09T15:15:25.3074481Z Set output 'issue_number'
trigger-check	Complete job	2025-06-09T15:15:25.3075163Z Cleaning up orphan processes
