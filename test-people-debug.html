<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人物画像グループ表示デバッグ</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .debug-info {
            margin-bottom: 20px;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .iframe-container {
            margin-bottom: 20px;
            border: 2px solid #333;
        }
        
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        
        pre {
            background: #f0f0f0;
            padding: 10px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>人物画像グループ表示デバッグ</h1>
        
        <div class="debug-info">
            <h2>デバッグ情報</h2>
            <p>ウィンドウ幅: <span id="window-width"></span>px</p>
            <p>デバイスタイプ: <span id="device-type"></span></p>
        </div>
        
        <div class="iframe-container">
            <h2>LP全体表示</h2>
            <iframe src="/wp-content/themes/appmart/s-owned-media.php" id="lp-frame"></iframe>
        </div>
        
        <div class="debug-info">
            <h2>人物画像のスタイル情報</h2>
            <div id="style-info"></div>
        </div>
        
        <div class="debug-info">
            <h2>HTML構造</h2>
            <pre><code>&lt;div class="owned-media-first-appeal__people"&gt;
  &lt;img class="owned-media-first-appeal__person owned-media-first-appeal__person--1" 
       src="/wp-content/themes/appmart/assets/images/s-owned/person-1.png" 
       alt="サポートチームメンバー1"&gt;
  &lt;img class="owned-media-first-appeal__person owned-media-first-appeal__person--2" 
       src="/wp-content/themes/appmart/assets/images/s-owned/person-2.png" 
       alt="サポートチームメンバー2"&gt;
  &lt;img class="owned-media-first-appeal__person owned-media-first-appeal__person--3" 
       src="/wp-content/themes/appmart/assets/images/s-owned/person-3.png" 
       alt="サポートチームメンバー3"&gt;
  &lt;img class="owned-media-first-appeal__person owned-media-first-appeal__person--4" 
       src="/wp-content/themes/appmart/assets/images/s-owned/person-4.png" 
       alt="サポートチームメンバー4"&gt;
&lt;/div&gt;</code></pre>
        </div>
        
        <div class="debug-info">
            <h2>CSSスタイル</h2>
            <pre><code>// 人物画像グループ
&__people {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(20px, 5vw, 60px);
  align-items: flex-end;
  justify-content: center;
  width: 100%;
}

&__person {
  height: clamp(120px, 15vw, 184px);
  object-fit: cover;
  object-position: bottom;

  &--1 {
    width: clamp(35px, 8vw, 47px);
  }

  &--2 {
    width: clamp(55px, 12vw, 73px);
  }

  &--3 {
    width: clamp(38px, 9vw, 51px);
  }

  &--4 {
    width: clamp(49px, 11vw, 65px);
  }
}</code></pre>
        </div>
    </div>
    
    <script>
        // ウィンドウサイズ取得
        function updateWindowInfo() {
            const width = window.innerWidth;
            document.getElementById('window-width').textContent = width;
            
            let deviceType;
            if (width < 768) {
                deviceType = 'スマートフォン';
            } else if (width < 1024) {
                deviceType = 'タブレット';
            } else {
                deviceType = 'PC';
            }
            document.getElementById('device-type').textContent = deviceType;
        }
        
        // iframe内の要素を調査
        function checkPeopleStyles() {
            const iframe = document.getElementById('lp-frame');
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const peopleElement = iframeDoc.querySelector('.owned-media-first-appeal__people');
                    
                    if (peopleElement) {
                        const computedStyle = iframe.contentWindow.getComputedStyle(peopleElement);
                        const styleInfo = `
                            <p><strong>要素が見つかりました</strong></p>
                            <p>display: ${computedStyle.display}</p>
                            <p>visibility: ${computedStyle.visibility}</p>
                            <p>opacity: ${computedStyle.opacity}</p>
                            <p>width: ${computedStyle.width}</p>
                            <p>height: ${computedStyle.height}</p>
                            <p>position: ${computedStyle.position}</p>
                            <p>z-index: ${computedStyle.zIndex}</p>
                        `;
                        document.getElementById('style-info').innerHTML = styleInfo;
                        
                        // 各人物画像の状態もチェック
                        const personImages = iframeDoc.querySelectorAll('.owned-media-first-appeal__person');
                        let imagesInfo = '<h3>各画像の状態</h3>';
                        personImages.forEach((img, index) => {
                            const imgStyle = iframe.contentWindow.getComputedStyle(img);
                            imagesInfo += `
                                <p><strong>person-${index + 1}:</strong> 
                                width: ${imgStyle.width}, 
                                height: ${imgStyle.height}, 
                                display: ${imgStyle.display}</p>
                            `;
                        });
                        document.getElementById('style-info').innerHTML += imagesInfo;
                    } else {
                        document.getElementById('style-info').innerHTML = '<p style="color: red;">要素が見つかりません</p>';
                    }
                } catch (e) {
                    document.getElementById('style-info').innerHTML = '<p style="color: red;">エラー: ' + e.message + '</p>';
                }
            };
        }
        
        // 初期化
        updateWindowInfo();
        window.addEventListener('resize', updateWindowInfo);
        
        // iframe読み込み後にスタイルチェック
        setTimeout(checkPeopleStyles, 1000);
    </script>
</body>
</html>