# MCP Manager GitHub Actions 統合ガイド

## 概要

MCP Manager を使用して、GitHub Actions ワークフローで Claude Code に MCP サーバーを登録する方法を説明します。このガイドは、Claude AI の Max プランを使用し、OAuth 認証で GitHub Actions を実行するユーザー向けに最適化されています。

## MCP (Model Context Protocol) とは

MCP は、LLM アプリケーションと外部データソース・ツールをシームレスに統合するためのオープンプロトコルです。Claude Code や他の AI コーディングアシスタントが、データベース、API、ファイルシステムなどの外部リソースにアクセスできるようになります。

### 主な特徴

1. **標準化されたインターフェース**: LLM と外部ツール間の通信を統一
2. **セキュリティ重視**: タイムアウト処理、レート制限、URL検証を標準装備
3. **柔軟な実装**: Node.js、Python など複数言語でサーバー実装可能
4. **GitHub Actions 対応**: CI/CD パイプラインでの自動化が可能

## セットアップ手順

### 1. OAuth トークンの取得

Claude AI Max プランのユーザーは、以下の手順で OAuth トークンを取得します：

1. <PERSON> デスクトップアプリまたは Web 版にログイン
2. 開発者ツールでトークン情報を取得
3. または、プロジェクトの `refresh-claude-token.js` スクリプトを使用

```bash
# Claude OAuth トークンの更新
node scripts/refresh-claude-token.js
```

### 2. ローカル環境の準備

```bash
# MCP Managerのインストール
./install-mcp-manager.sh

# プロジェクトのMCP設定を初期化
mcp-manager init

# .claude/mcp-config.json を編集して必要なMCPサーバーを定義
```

### 3. GitHub Secrets への設定保存

```bash
# GitHub CLIのインストール（未インストールの場合）
# macOS: brew install gh
# Ubuntu: sudo apt install gh

# GitHub CLIでログイン
gh auth login

# OAuth トークンを GitHub Secrets に設定
gh secret set CLAUDE_ACCESS_TOKEN < access_token.txt
gh secret set CLAUDE_REFRESH_TOKEN < refresh_token.txt
gh secret set CLAUDE_EXPIRES_AT < expires_at.txt

# MCP設定をGitHub Secretsにアップロード
./mcp-github-sync.sh push

# または mcp-manager を使用
mcp-manager export-github
```

### 4. 必要な GitHub Secrets

以下の Secrets を設定する必要があります：

#### OAuth認証を使用する場合（推奨）
- `CLAUDE_ACCESS_TOKEN`: Claude OAuth アクセストークン
- `CLAUDE_REFRESH_TOKEN`: Claude OAuth リフレッシュトークン
- `CLAUDE_EXPIRES_AT`: トークンの有効期限
- `GH_TOKEN_WORKFLOW`: GitHub Personal Access Token（ワークフロー権限付き）
- `MCP_CONFIG_<PROJECT_NAME>`: プロジェクト固有のMCP設定（Base64エンコード）
- `GLOBAL_MCP_CONFIG`: （オプション）グローバルMCP設定

#### APIキー認証を使用する場合
- `ANTHROPIC_API_KEY`: Claude API キー（OAuth認証使用時は不要）
- その他は OAuth 認証と同じ

環境変数が必要な MCP サーバーの場合：
- 各環境変数を個別の Secret として設定

### 5. GitHub Actions ワークフローの設定

#### 基本的な設定例

```yaml
name: Claude Code with MCP

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  claude-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Restore MCP Configuration
      run: |
        mkdir -p .claude
        echo "${{ secrets.MCP_CONFIG_YOUR_PROJECT }}" | base64 -d > .claude/mcp-config.json
        
    - name: Install and Configure MCP
      run: |
        # mcp-managerをダウンロード
        curl -sSL https://raw.githubusercontent.com/${{ github.repository }}/main/mcp-manager.sh -o mcp-manager.sh
        chmod +x mcp-manager.sh
        ./mcp-manager.sh install
```

#### Claude Code Action との統合例

```yaml
- name: Execute Claude with MCP
  uses: grll/claude-code-base-action@beta
  env:
    GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
    # GitHub Actions MCP サーバー用の環境変数
    GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
  with:
    use_oauth: 'true'
    claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
    claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
    claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
    model: ${{ inputs.selected_model }}
    max_turns: ${{ inputs.max_turns }}
    # MCPサーバーが使用するツールを含める
    allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch,mcp__github__*'
    prompt: |
      GitHub Actions MCP サーバーを使用してワークフローを管理できます。
      利用可能なツール:
      - mcp__github__list_workflows: リポジトリのワークフロー一覧
      - mcp__github__trigger_workflow: ワークフローの実行
      - mcp__github__get_workflow_run: ワークフロー実行の詳細取得
```

## 運用のベストプラクティス

### 1. 環境変数の管理

```json
{
  "mcpServers": {
    "database-server": {
      "command": "node",
      "args": ["./mcp-servers/database.js"],
      "env": {
        "DB_HOST": "${DB_HOST}",
        "DB_USER": "${DB_USER}",
        "DB_PASS": "${DB_PASS}"
      }
    }
  }
}
```

GitHub Actions では環境変数を展開：

```yaml
- name: Install MCP Servers
  env:
    DB_HOST: ${{ secrets.DB_HOST }}
    DB_USER: ${{ secrets.DB_USER }}
    DB_PASS: ${{ secrets.DB_PASS }}
  run: |
    # 環境変数を展開してMCPをインストール
    envsubst < .claude/mcp-config.json > .claude/mcp-config-resolved.json
    mv .claude/mcp-config-resolved.json .claude/mcp-config.json
    ./mcp-manager.sh install
```

### 2. プロジェクトごとの設定分離

```bash
# 開発環境用
mcp-manager export-github dev

# 本番環境用
mcp-manager export-github prod
```

GitHub Actions で環境ごとに切り替え：

```yaml
- name: Select MCP Configuration
  run: |
    if [ "${{ github.base_ref }}" == "main" ]; then
      echo "${{ secrets.MCP_CONFIG_PROD }}" | base64 -d > .claude/mcp-config.json
    else
      echo "${{ secrets.MCP_CONFIG_DEV }}" | base64 -d > .claude/mcp-config.json
    fi
```

### 3. 複数プロジェクトの管理

組織全体で MCP を管理する場合：

```yaml
# .github/workflows/setup-mcp.yml (再利用可能ワークフロー)
name: Setup MCP

on:
  workflow_call:
    inputs:
      project_name:
        required: true
        type: string

jobs:
  setup:
    runs-on: ubuntu-latest
    steps:
    - name: Install MCP for ${{ inputs.project_name }}
      run: |
        # プロジェクト名に基づいてSecretを選択
        secret_name="MCP_CONFIG_${{ inputs.project_name }}"
        echo "${{ secrets[secret_name] }}" | base64 -d > .claude/mcp-config.json
```

### 4. セキュリティ考慮事項

1. **最小権限の原則**
   - MCP サーバーには必要最小限の権限のみ付与
   - 読み取り専用の認証情報を使用

2. **Secret のローテーション**
   - 定期的に API キーや認証情報を更新
   - GitHub の Secret scanning を有効化

3. **監査ログ**
   - MCP サーバーのアクセスログを記録
   - 異常なアクセスパターンを監視

## トラブルシューティング

### MCP サーバーが見つからない

```bash
# デバッグ情報を出力
mcp-manager list
claude mcp list
```

### 環境変数が展開されない

```bash
# envsubst がない場合
apt-get update && apt-get install -y gettext-base
```

### 権限エラー

```yaml
- name: Fix permissions
  run: |
    chmod +x mcp-manager.sh
    chmod -R 755 .claude/
```

## 高度な使用例

### 1. 条件付き MCP インストール

```yaml
- name: Install MCP based on changes
  run: |
    if git diff --name-only origin/main...HEAD | grep -q "database/"; then
      # データベース関連の変更がある場合のみDB MCPをインストール
      mcp-manager install database-mcp
    fi
```

### 2. 並列 MCP セットアップ

```yaml
strategy:
  matrix:
    mcp-server: [database, api, frontend]

steps:
- name: Install MCP Server
  run: |
    mcp-manager install ${{ matrix.mcp-server }}
```

### 3. キャッシュの活用

```yaml
- name: Cache MCP installations
  uses: actions/cache@v3
  with:
    path: ~/.claude/mcp/
    key: mcp-${{ hashFiles('.claude/mcp-config.json') }}
```

## MCP サーバーの種類と用途

### 1. GitHub Actions MCP サーバー

ワークフローの管理と操作を可能にします：

```json
{
  "mcpServers": {
    "github-actions": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github-actions"],
      "env": {
        "GITHUB_TOKEN": "${GITHUB_TOKEN}"
      }
    }
  }
}
```

**提供される機能**:
- ワークフローの一覧表示・詳細確認
- ワークフローの実行・キャンセル・再実行
- ワークフロー実行結果の分析
- ジョブステータスの確認

### 2. ファイルシステム MCP サーバー

特定のディレクトリへのアクセスを提供：

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y", 
        "@modelcontextprotocol/server-filesystem",
        "/path/to/project"
      ]
    }
  }
}
```

### 3. データベース MCP サーバー

PostgreSQL などのデータベースアクセス：

```json
{
  "mcpServers": {
    "postgres": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-postgres"],
      "env": {
        "POSTGRES_URL": "${DATABASE_URL}"
      }
    }
  }
}
```

## GitHub Actions での実装パターン

### パターン1: MCP を使用した自動レビュー（OAuth認証）

```yaml
name: AI Code Review with MCP

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  ai-review:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GH_TOKEN_WORKFLOW }}
        
    - name: Setup MCP Configuration
      run: |
        mkdir -p .claude
        cat > .claude/mcp-config.json << 'EOF'
        {
          "mcpServers": {
            "github": {
              "command": "npx",
              "args": ["-y", "@modelcontextprotocol/server-github"],
              "env": {
                "GITHUB_TOKEN": "${{ secrets.GH_TOKEN_WORKFLOW }}"
              }
            }
          }
        }
        EOF
        
    - name: AI Code Review
      uses: grll/claude-code-base-action@beta
      env:
        MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
      with:
        # OAuth認証を使用（ANTHROPIC_API_KEYは不要）
        use_oauth: 'true'
        claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
        claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
        claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
        model: 'claude-3-5-sonnet-20241022'
        allowed_tools: 'Read,Write,mcp__github__*'
        prompt: |
          このPRの変更をレビューしてください。
          GitHub MCPサーバーを使用して、PR情報とファイル変更を取得できます。
```

### パターン2: ワークフロー自動生成（OAuth認証）

```yaml
name: Generate Workflow with MCP

on:
  workflow_dispatch:
    inputs:
      workflow_purpose:
        description: 'ワークフローの目的'
        required: true
        type: string

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.GH_TOKEN_WORKFLOW }}
    
    - name: Setup MCP for Workflow Generation
      run: |
        # MCP設定の準備
        mkdir -p .claude
        echo '${{ secrets.MCP_CONFIG }}' | base64 -d > .claude/mcp-config.json
        
    - name: Generate Workflow
      uses: grll/claude-code-base-action@beta
      env:
        MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
      with:
        # OAuth認証の設定
        use_oauth: 'true'
        claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
        claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
        claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
        model: 'claude-3-5-sonnet-20241022'
        allowed_tools: 'Read,Write,mcp__github__*'
        prompt: |
          「${{ inputs.workflow_purpose }}」を実現するGitHub Actionsワークフローを生成してください。
          GitHub Actions MCPサーバーを使用して、既存のワークフローを参考にできます。
```

## OAuth 認証のメリット

Claude AI Max プランで OAuth 認証を使用することの利点：

1. **API キー不要**: `ANTHROPIC_API_KEY` の管理が不要
2. **セキュリティ向上**: トークンの自動更新とセッション管理
3. **レート制限の緩和**: Max プランの高い制限を活用
4. **統一された認証**: デスクトップアプリと同じ認証方式

## まとめ

MCP Manager を GitHub Actions と組み合わせることで：

1. **一貫性**: 開発環境と CI/CD 環境で同じ MCP 設定を使用
2. **セキュリティ**: GitHub Secrets で機密情報を安全に管理
3. **スケーラビリティ**: 複数プロジェクト・環境を効率的に管理
4. **自動化**: PR ごとに自動的に Claude Code 分析を実行
5. **拡張性**: 様々な MCP サーバーを組み合わせて高度な自動化を実現
6. **OAuth 統合**: Claude AI Max プランの機能をフル活用

これにより、チーム全体で Claude Code の機能を最大限活用できます。