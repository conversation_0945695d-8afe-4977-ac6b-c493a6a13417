# MCPサーバーインストールガイド

このドキュメントは、MCPサーバーを正しくインストールし、管理するための手順を記載しています。

## グローバルコマンドのセットアップ

初回のみ、以下の手順でグローバルコマンドをセットアップしてください：

### 1. スクリプトのインストール（初回のみ）
```bash
# スクリプトをグローバルに利用可能にする
cp ./install-mcp-servers.sh ~/.local/bin/mcp-install
chmod +x ~/.local/bin/mcp-install

# PATHに追加（~/.bashrcに既に追加済みの場合はスキップ）
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### 2. グローバルコマンドの使用
セットアップ後は、どのディレクトリからでも以下のコマンドが使用できます：

```bash
# デフォルト設定（~/.claude/.mcp.json）を使用
mcp-install

# カスタム設定ファイルを指定
mcp-install /path/to/custom-mcp-config.json

# エイリアスも利用可能
mcp-reinstall
```

## 重要な概念

### スコープについて
MCPサーバーには以下のスコープがあります：
- **local**: 現在のプロジェクトのみ
- **user**: 全プロジェクトで使用可能（グローバル）
- **project**: プロジェクト固有

**重要**: Lintツールなど、全プロジェクトで使用したいMCPサーバーは必ず`user`スコープでインストールしてください。

### 設定ファイルの場所
- **グローバル設定**: `~/.claude/.mcp.json`
- **プロジェクト設定**: `<project-root>/.claude/mcp-config.json`

## インストール方法

### 方法1: グローバルコマンドを使用した一括インストール（推奨）

```bash
# グローバル設定ファイルから一括インストール（デフォルト）
mcp-install

# カスタム設定ファイルを指定
mcp-install /path/to/custom-mcp-config.json
```

**注意**: 初めて使用する場合は、上記の「グローバルコマンドのセットアップ」を先に実行してください。

**特徴**:
- 既存のMCPサーバーを自動的に削除してから再インストール（設定変更に対応）
- 自動的に`-s user`オプションを付けてユーザースコープでインストール
- JSONファイルから設定を読み取り、環境変数も含めて正しく設定
- 成功/失敗のカウントを表示

### 方法2: mcp-managerを使用したインストール

```bash
# まずmcp-managerをインストール（初回のみ）
./install-mcp-manager.sh

# グローバル設定からインストール
mcp-manager install ~/.claude

# プロジェクト固有の設定からインストール
mcp-manager install
```

### 方法3: Claude CLIを直接使用

```bash
# ユーザースコープ（グローバル）でインストール
claude mcp add -s user <server-name> npx -- -y <package-name>

# 例：ESLintサーバーの追加
claude mcp add -s user eslint npx -- -y @eslint/mcp-server
```

## グローバル設定ファイルへの追加

新しいMCPサーバーを追加する場合は、`~/.claude/.mcp.json`を編集します：

```json
{
  "mcpServers": {
    "新しいサーバー名": {
      "command": "npx",
      "args": [
        "-y",
        "パッケージ名"
      ],
      "env": {
        "必要な環境変数": "値"
      }
    }
  }
}
```

## 利用可能なLintツールMCPサーバー

### 1. code-checker
- **パッケージ**: `mcp-server-code-checker-python`
- **機能**: Pythonコードの品質チェック（pylint、pytest）

### 2. eslint
- **パッケージ**: `@eslint/mcp-server`
- **機能**: JavaScript/TypeScriptのLintツール

### 3. code-review
- **パッケージ**: `mcp-code-review-server`
- **機能**: コードレビューとベストプラクティスの提案

## トラブルシューティング

### インストールが失敗する場合
1. MCPサーバーが既にインストールされていないか確認
   ```bash
   claude mcp list
   ```

2. 既存のサーバーを削除してから再インストール
   ```bash
   claude mcp remove <server-name>
   ```

### スコープを間違えた場合
ローカルスコープでインストールしてしまった場合は、削除してユーザースコープで再インストール：
```bash
# ローカルのものを削除
claude mcp remove <server-name>

# ユーザースコープで再インストール
claude mcp add -s user <server-name> npx -- -y <package-name>
```

## ベストプラクティス

1. **新しいMCPサーバーを追加する際の手順**:
   - `~/.claude/.mcp.json`に設定を追加
   - `./install-mcp-servers.sh`を実行
   - `claude mcp list`で確認

2. **プロジェクト固有のMCPサーバー**:
   - プロジェクトの`.claude/mcp-config.json`に設定
   - `mcp-manager install`でインストール

3. **定期的な確認**:
   - `claude mcp list`で現在のMCPサーバー一覧を確認
   - 不要なサーバーは`claude mcp remove`で削除

## 関連ファイル

- `/home/<USER>/projects/wordpress-appmart/install-mcp-servers.sh` - 一括インストールスクリプト
- `/home/<USER>/projects/wordpress-appmart/mcp-manager.sh` - MCPマネージャー
- `/home/<USER>/projects/wordpress-appmart/docs/mcp-github-actions-guide.md` - GitHub Actions統合ガイド
- `~/.claude/.mcp.json` - グローバルMCP設定ファイル