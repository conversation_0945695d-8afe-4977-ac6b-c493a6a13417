# Claude Code 使用方法

このプロジェクトでは、GitHub Issues のコメントから Claude Code を呼び出してタスクを実行できます。

## 利用可能なコマンド

### 🤖 @claude-task
**ファイルの作成・変更を伴うタスクの実行**

```
@claude-task
```

- 新機能の実装
- バグ修正
- コードのリファクタリング
- ファイルの作成・編集
- 自動的にブランチ作成とPull Request作成

**例:**
```
@claude-task

新しいログイン機能を実装してください。
- ユーザー認証フォーム
- セッション管理
- エラーハンドリング
```

### 💬 @claude-help
**質問や説明（読み取り専用）**

```
@claude-help
```

- コードの解説
- ファイル構造の確認
- 技術的な質問への回答
- プロジェクトに関する説明

**例:**
```
@claude-help

このプロジェクトのディレクトリ構造を教えてください。
WordPressのカスタムフィールドはどこで設定されていますか？
```

## 使用手順

### 1. Issue を作成
GitHub Issues で新しい Issue を作成するか、既存の Issue を使用します。

### 2. コマンドをコメント
Issue のコメント欄に上記のコマンドを投稿します。

### 3. 実行結果を確認
数分後、Claude Code が自動的にタスクを実行し、結果をコメントで返します。

### 4. Pull Request を確認（@claude-task の場合）
`@claude-task` の場合、実装が完了すると自動的に Pull Request が作成されます。

## 実行例

### バグ修正の例
```
Issue タイトル: ログイン後のリダイレクトが動作しない

@claude-task

ログイン成功後にダッシュボードにリダイレクトされない問題を修正してください。
現在は空白ページが表示されます。
```

### コード解説の例
```
Issue タイトル: ACF フィールドの使用方法を知りたい

@claude-help

このプロジェクトでAdvanced Custom Fields（ACF）がどのように使用されているか教えてください。
設定ファイルやテンプレートでの使用方法を知りたいです。
```

## 注意事項

### セキュリティ
- Claude Code は安全なコードのみを生成します
- セキュリティリスクのある変更は自動的に拒否されます

### 権限
- `@claude-task`: コードの変更権限が必要
- `@claude-help`: 読み取り専用、変更は行いません

### 制限
- 実行時間: 最大30分（@claude-task）/ 10分（@claude-help）
- ターン数: 最大20ターン（@claude-task）/ 5ターン（@claude-help）

## トラブルシューティング

### ワークフローが実行されない場合
1. GitHub Actions が有効になっているか確認
2. コマンドのスペルミスがないか確認
3. Issue のコメントに正確にコマンドが含まれているか確認

### 実行が失敗する場合
1. GitHub Actions のログを確認
2. エラーメッセージを確認
3. 必要に応じて Issue にエラー内容を追記

### Pull Request が作成されない場合
1. 変更が実際に行われたか確認
2. ブランチが正しく作成されているか確認
3. 権限の問題がないか確認

## サポート

問題や質問がある場合は、新しい Issue を作成して `@claude-help` で質問してください。

---

## 詳細ドキュメント

### 利用可能なツール（@claude-task）
- **ファイル操作**: Read, Write, Edit, MultiEdit
- **検索**: LS, Glob, Grep
- **実行**: Bash
- **ノートブック**: NotebookRead, NotebookEdit
- **タスク管理**: TodoRead, TodoWrite

### 利用可能なツール（@claude-help）
- **ファイル操作**: Read, LS
- **検索**: Bash, Glob, Grep
- **実行**: Bash（読み取り専用コマンドのみ）

### GitHub Actionsワークフロー
- `claude-simple-task.yml`: @claude-task 用のメインワークフロー
- `claude-quick-help.yml`: @claude-help 用のクイックヘルプワークフロー

---

🤖 **Claude Code** - AI-Powered Development Assistant