services:
  wordpress:
    build:
      context: ./dockerfile
      dockerfile: wpDockerfile
    image: appmart
    container_name: wordpress-appmart
    restart: 'no'
    ports:
      - 20085:80
    depends_on:
      - db
    volumes:
      # WordPress全体をボリュームマウント
      - wordpress_volume:/var/www/html
      # 開発対象のテーマのみバインドマウント（上書き）
      - ./mount_wordpress/wp-content/themes/appmart:/var/www/html/wp-content/themes/appmart
      # mu-plugins（Must Use Plugins）もバインドマウント
      - ./mount_wordpress/wp-content/mu-plugins:/var/www/html/wp-content/mu-plugins
      # 設定ファイル
      - ./conf/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
      - ./conf/apache2.conf:/etc/apache2/apache2.conf
      - ./conf/php.ini:/usr/local/etc/php/php.ini
      - ./log:/var/log
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: mysql
      WORDPRESS_DB_PASSWORD: Okiu15ot
      WORDPRESS_DB_NAME: wp_mysql
      WSL_IP: ${WSL_IP}
      # WordPress環境設定
      WP_ENV: ${WP_ENV:-development}
      WORDPRESS_DEBUG: ${WORDPRESS_DEBUG:-1}
    extra_hosts:
      - 'host.docker.internal:host-gateway'

  db:
    image: mysql:5.7
    container_name: appmart_db
    restart: 'no'
    environment:
      MYSQL_DATABASE: wp_mysql
      MYSQL_USER: mysql
      MYSQL_PASSWORD: Okiu15ot
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - 15086:3306
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  wordpress_volume:
    name: wordpress-appmart-volume
  mysql_data:
    name: wordpress-appmart-mysql

networks:
  appmart_network:
    driver: bridge
