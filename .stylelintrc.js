module.exports = {
  // extends: ['stylelint-config-standard', 'style-config-recess-order'],
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-recess-order',
    'stylelint-prettier/recommended',
  ],
  plugins: ['stylelint-scss', 'stylelint-selector-bem-pattern'],
  rules: {
    // ここに追加のルールを設定
    'scss/double-slash-comment-whitespace-inside': null,
    'scss/dollar-variable-pattern': null,
    'selector-class-pattern': [
      '^[a-z0-9\\-]+(__[a-z0-9\\-]+)?(--[a-z0-9\\-]+)?$',
      {
        resolveNestedSelectors: true,
      },
    ],
  },
  ignoreFiles: ['**/*.css'],
};
