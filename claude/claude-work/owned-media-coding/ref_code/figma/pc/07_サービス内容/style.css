.box {
  width: 1921px;
  height: 4198px;
}

.box .s {
  position: fixed;
  width: 1921px;
  height: 4198px;
  top: 0;
  left: 0;
}

.box .overlap-group {
  position: relative;
  height: 4198px;
}

.box .BG {
  position: absolute;
  width: 1921px;
  height: 4198px;
  top: 0;
  left: 0;
}

.box .div {
  position: absolute;
  width: 1920px;
  height: 4197px;
  top: 1px;
  left: 0;
  background-color: #f9f9f9;
}

.box .BG-2 {
  position: absolute;
  width: 1920px;
  height: 3581px;
  top: 617px;
  left: 1px;
  background-color: #d8ede7;
}

.box .mask-group {
  position: absolute;
  width: 1920px;
  height: 4198px;
  top: 0;
  left: 0;
}

.box .view {
  display: flex;
  flex-direction: column;
  width: 484px;
  align-items: center;
  gap: 8px;
  position: absolute;
  top: 180px;
  left: 718px;
}

.box .text-wrapper {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #3ab795;
  font-size: 80px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: normal;
  white-space: nowrap;
}

.box .frame {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  position: relative;
  flex: 0 0 auto;
}

.box .line {
  position: relative;
  width: 15px;
  height: 4px;
}

.box .text-wrapper-2 {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
  color: #3ab795;
  font-size: 38px;
  letter-spacing: 0.38px;
  font-weight: 500;
  text-align: center;
  line-height: normal;
}

.box .element {
  position: absolute;
  width: 1303px;
  height: 478px;
  top: 374px;
  left: 311px;
}

.box .overlap {
  position: relative;
  width: 1299px;
  height: 478px;
}

.box .rectangle {
  position: absolute;
  width: 1299px;
  height: 449px;
  top: 29px;
  left: 0;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 14px #43e2b899;
}

.box .number {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 71px;
  left: 108px;
}

.box .overlap-2 {
  width: 131px;
  left: 32px;
  position: absolute;
  height: 124px;
  top: 45px;
}

.box .text-wrapper-3 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "SF Pro Text-Medium", Helvetica;
  font-weight: 500;
  color: #7dc8b6;
  font-size: 100px;
  text-align: center;
  letter-spacing: 1.00px;
  line-height: normal;
  white-space: nowrap;
}

.box .text-wrapper-4 {
  position: absolute;
  top: 99px;
  left: 28px;
  font-family: "SF Pro Text-Medium", Helvetica;
  color: #7dc8b6;
  font-size: 21px;
  letter-spacing: 0.21px;
  white-space: nowrap;
  font-weight: 500;
  text-align: center;
  line-height: normal;
}

.box .img {
  position: absolute;
  width: 17px;
  height: 1px;
  top: 114px;
  left: 6px;
  object-fit: cover;
}

.box .line-2 {
  position: absolute;
  width: 17px;
  height: 1px;
  top: 114px;
  left: 106px;
  object-fit: cover;
}

.box .list {
  position: absolute;
  width: 194px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .overlap-group-2 {
  position: relative;
  height: 22px;
}

.box .line-3 {
  position: absolute;
  width: 172px;
  height: 3px;
  top: 10px;
  left: 11px;
}

.box .ellipse {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 0;
  background-color: #ffffff;
  border-radius: 11px;
  border: 3px solid;
  border-color: #7dc8b6;
}

.box .ellipse-2 {
  left: 28px;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .ellipse-3 {
  left: 57px;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .ellipse-4 {
  left: 86px;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .ellipse-5 {
  left: 115px;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .ellipse-6 {
  left: 143px;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .ellipse-7 {
  left: 172px;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .group {
  position: absolute;
  width: 873px;
  height: 1px;
  top: 279px;
  left: 316px;
}

.box .line-wrapper {
  position: relative;
  height: 7px;
  top: -7px;
  background-image: url(./img/line-17-1.svg);
  background-size: 100% 100%;
}

.box .line-4 {
  position: absolute;
  width: 83px;
  height: 7px;
  top: 0;
  left: 790px;
}

.box .p {
  position: absolute;
  width: 689px;
  height: 77px;
  top: 138px;
  left: 311px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
}

.box .span {
  color: #333333;
  letter-spacing: 0.20px;
}

.box .text-wrapper-5 {
  color: #fa6b58;
  font-size: 55px;
  letter-spacing: 0.30px;
}

.box .text-wrapper-6 {
  top: 304px;
  left: 109px;
  position: absolute;
  width: 1080px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .gdsagda {
  position: absolute;
  width: 365px;
  height: 254px;
  top: 0;
  left: 906px;
  object-fit: cover;
}

.box .overlap-wrapper {
  position: absolute;
  width: 1304px;
  height: 510px;
  top: 888px;
  left: 310px;
}

.box .overlap-3 {
  position: relative;
  width: 1300px;
  height: 510px;
}

.box .rectangle-2 {
  top: 61px;
  position: absolute;
  width: 1300px;
  height: 449px;
  left: 0;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 24px #00000040, 0px 0px 14px #43e2b899;
}

.box .number-2 {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 103px;
  left: 110px;
}

.box .overlap-4 {
  width: 117px;
  left: 38px;
  position: absolute;
  height: 124px;
  top: 45px;
}

.box .text-wrapper-7 {
  position: absolute;
  top: 0;
  left: 2px;
  font-family: "SF Pro Text-Medium", Helvetica;
  font-weight: 500;
  color: #7dc8b6;
  font-size: 100px;
  text-align: center;
  letter-spacing: 1.00px;
  line-height: normal;
  white-space: nowrap;
}

.box .text-wrapper-8 {
  position: absolute;
  top: 99px;
  left: 22px;
  font-family: "SF Pro Text-Medium", Helvetica;
  color: #7dc8b6;
  font-size: 21px;
  letter-spacing: 0.21px;
  white-space: nowrap;
  font-weight: 500;
  text-align: center;
  line-height: normal;
}

.box .line-5 {
  height: 2px;
  top: 113px;
  left: 0;
  position: absolute;
  width: 17px;
}

.box .line-6 {
  height: 2px;
  top: 113px;
  left: 100px;
  position: absolute;
  width: 17px;
}

.box .ellipse-8 {
  left: 0;
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  background-color: #d9d9d9;
  border-radius: 11px;
}

.box .ellipse-9 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 28px;
  background-color: #ffffff;
  border-radius: 11px;
  border: 2px solid;
  border-color: #7dc8b6;
}

.box .overlap-group-wrapper {
  top: 311px;
  left: 318px;
  position: absolute;
  width: 873px;
  height: 1px;
}

.box .search-console {
  position: absolute;
  width: 689px;
  height: 150px;
  top: 133px;
  left: 313px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
}

.box .text-wrapper-9 {
  position: absolute;
  width: 1080px;
  top: 336px;
  left: 111px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .fasfa {
  position: absolute;
  width: 343px;
  height: 296px;
  top: 0;
  left: 957px;
  object-fit: cover;
}

.box .div-wrapper {
  position: absolute;
  width: 1304px;
  height: 474px;
  top: 1464px;
  left: 310px;
}

.box .overlap-5 {
  position: relative;
  width: 1300px;
  height: 474px;
}

.box .rectangle-3 {
  top: 25px;
  position: absolute;
  width: 1300px;
  height: 449px;
  left: 0;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 24px #00000040, 0px 0px 14px #43e2b899;
}

.box .number-3 {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 67px;
  left: 109px;
}

.box .overlap-6 {
  width: 128px;
  left: 33px;
  position: absolute;
  height: 124px;
  top: 45px;
}

.box .text-wrapper-10 {
  position: absolute;
  top: 99px;
  left: 27px;
  font-family: "SF Pro Text-Medium", Helvetica;
  color: #7dc8b6;
  font-size: 21px;
  letter-spacing: 0.21px;
  white-space: nowrap;
  font-weight: 500;
  text-align: center;
  line-height: normal;
}

.box .line-7 {
  height: 1px;
  top: 114px;
  left: 5px;
  object-fit: cover;
  position: absolute;
  width: 17px;
}

.box .line-8 {
  height: 1px;
  top: 114px;
  left: 105px;
  object-fit: cover;
  position: absolute;
  width: 17px;
}

.box .ellipse-10 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 57px;
  background-color: #ffffff;
  border-radius: 11px;
  border: 2px solid;
  border-color: #7dc8b6;
}

.box .group-2 {
  top: 275px;
  left: 317px;
  position: absolute;
  width: 873px;
  height: 1px;
}

.box .img-wrapper {
  background-image: url(./img/line-17-6.svg);
  position: relative;
  height: 7px;
  top: -7px;
  background-size: 100% 100%;
}

.box .div-2 {
  position: absolute;
  width: 715px;
  height: 150px;
  top: 97px;
  left: 312px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
}

.box .text-wrapper-11 {
  position: absolute;
  width: 1080px;
  top: 300px;
  left: 110px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .gdsgs {
  position: absolute;
  width: 285px;
  height: 247px;
  top: 0;
  left: 1001px;
  object-fit: cover;
}

.box .element-2 {
  position: absolute;
  width: 1304px;
  height: 525px;
  top: 1953px;
  left: 310px;
}

.box .overlap-7 {
  position: relative;
  width: 1300px;
  height: 525px;
}

.box .rectangle-4 {
  top: 76px;
  position: absolute;
  width: 1300px;
  height: 449px;
  left: 0;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 24px #00000040, 0px 0px 14px #43e2b899;
}

.box .number-4 {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 118px;
  left: 109px;
}

.box .overlap-8 {
  position: absolute;
  width: 130px;
  height: 124px;
  top: 45px;
  left: 32px;
}

.box .ellipse-11 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 86px;
  background-color: #ffffff;
  border-radius: 11px;
  border: 2px solid;
  border-color: #7dc8b6;
}

.box .group-3 {
  top: 326px;
  left: 317px;
  position: absolute;
  width: 873px;
  height: 1px;
}

.box .div-3 {
  position: absolute;
  width: 689px;
  height: 77px;
  top: 185px;
  left: 312px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
}

.box .text-wrapper-12 {
  top: 351px;
  left: 110px;
  position: absolute;
  width: 1080px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .fsfaafa {
  position: absolute;
  width: 401px;
  height: 296px;
  top: 0;
  left: 870px;
  object-fit: cover;
}

.box .element-3 {
  position: absolute;
  width: 1304px;
  height: 492px;
  top: 2526px;
  left: 310px;
}

.box .overlap-9 {
  position: relative;
  width: 1300px;
  height: 492px;
}

.box .rectangle-5 {
  top: 43px;
  position: absolute;
  width: 1300px;
  height: 449px;
  left: 0;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 24px #00000040, 0px 0px 14px #43e2b899;
}

.box .number-5 {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 85px;
  left: 109px;
}

.box .overlap-10 {
  width: 132px;
  left: 31px;
  position: absolute;
  height: 124px;
  top: 45px;
}

.box .text-wrapper-13 {
  position: absolute;
  top: 99px;
  left: 29px;
  font-family: "SF Pro Text-Medium", Helvetica;
  color: #7dc8b6;
  font-size: 21px;
  letter-spacing: 0.21px;
  white-space: nowrap;
  font-weight: 500;
  text-align: center;
  line-height: normal;
}

.box .line-9 {
  height: 1px;
  top: 114px;
  left: 7px;
  object-fit: cover;
  position: absolute;
  width: 17px;
}

.box .line-10 {
  height: 1px;
  top: 114px;
  left: 107px;
  object-fit: cover;
  position: absolute;
  width: 17px;
}

.box .ellipse-12 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 115px;
  background-color: #ffffff;
  border-radius: 11px;
  border: 2px solid;
  border-color: #7dc8b6;
}

.box .group-4 {
  top: 293px;
  left: 317px;
  position: absolute;
  width: 873px;
  height: 1px;
}

.box .CV {
  height: 77px;
  top: 152px;
  font-size: 55px;
  letter-spacing: 0.55px;
  position: absolute;
  width: 689px;
  left: 312px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  line-height: 77px;
}

.box .text-wrapper-14 {
  color: #fa6b58;
  letter-spacing: 0.30px;
}

.box .text-wrapper-15 {
  color: #333333;
  font-size: 45px;
  letter-spacing: 0.20px;
}

.box .text-wrapper-16 {
  top: 318px;
  left: 110px;
  position: absolute;
  width: 1080px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .fasfa-x {
  position: absolute;
  width: 423px;
  height: 268px;
  top: 0;
  left: 845px;
  object-fit: cover;
}

.box .element-4 {
  position: absolute;
  width: 1304px;
  height: 466px;
  top: 3092px;
  left: 310px;
}

.box .overlap-11 {
  position: relative;
  width: 1300px;
  height: 466px;
}

.box .rectangle-6 {
  top: 17px;
  position: absolute;
  width: 1300px;
  height: 449px;
  left: 0;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 24px #00000040, 0px 0px 14px #43e2b899;
}

.box .number-6 {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 59px;
  left: 109px;
}

.box .ellipse-13 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 143px;
  background-color: #ffffff;
  border-radius: 11px;
  border: 2px solid;
  border-color: #7dc8b6;
}

.box .group-5 {
  top: 267px;
  left: 317px;
  position: absolute;
  width: 873px;
  height: 1px;
}

.box .CV-2 {
  height: 150px;
  top: 89px;
  font-size: 45px;
  letter-spacing: 0.45px;
  position: absolute;
  width: 689px;
  left: 312px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  line-height: 77px;
}

.box .text-wrapper-17 {
  position: absolute;
  width: 1080px;
  top: 292px;
  left: 110px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .gsafdga {
  position: absolute;
  width: 440px;
  height: 247px;
  top: 0;
  left: 829px;
  object-fit: cover;
}

.box .element-5 {
  position: absolute;
  width: 1304px;
  height: 449px;
  top: 3649px;
  left: 310px;
}

.box .overlap-12 {
  position: relative;
  width: 1300px;
  height: 449px;
  background-color: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 24px #00000040, 0px 0px 14px #43e2b899;
}

.box .number-7 {
  position: absolute;
  width: 198px;
  height: 169px;
  top: 42px;
  left: 109px;
}

.box .ellipse-14 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 0;
  left: 172px;
  background-color: #ffffff;
  border-radius: 11px;
  border: 2px solid;
  border-color: #7dc8b6;
}

.box .group-6 {
  top: 250px;
  left: 317px;
  position: absolute;
  width: 873px;
  height: 1px;
}

.box .overlap-13 {
  position: absolute;
  width: 952px;
  height: 223px;
  top: 7px;
  left: 312px;
}

.box .div-4 {
  position: absolute;
  width: 689px;
  height: 158px;
  top: 61px;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 55px;
  letter-spacing: 0.55px;
  line-height: 77px;
}

.box .element-x {
  position: absolute;
  width: 498px;
  height: 223px;
  top: 0;
  left: 454px;
  object-fit: cover;
}

.box .text-wrapper-18 {
  top: 275px;
  left: 110px;
  position: absolute;
  width: 1080px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
}

.box .group-7 {
  position: absolute;
  width: 140px;
  height: 2752px;
  top: 868px;
  left: 889px;
}
