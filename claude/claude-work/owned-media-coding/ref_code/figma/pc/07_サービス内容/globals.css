@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
@import url("https://fonts.googleapis.com/css?family=Noto+Sans+JP:500,700,400");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}
@font-face {
  font-family: "SF Pro Text-Medium";
  src: url("https://anima-uploads.s3.amazonaws.com/projects/6539d51e0dbdc65b57fa27ef/fonts/sf-pro-text-medium.otf")
    format("opentype");
}
