.box {
  width: 1929px;
  height: 1024px;
}

.box .FV {
  position: fixed;
  width: 1929px;
  height: 1024px;
  top: 0;
  left: 0;
}

.box .overlap {
  position: relative;
  width: 1922px;
  height: 1024px;
}

.box .BG {
  position: absolute;
  width: 1920px;
  height: 945px;
  top: 0;
  left: 2px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(227, 243, 255, 1) 33%,
    rgba(129, 204, 231, 1) 64%
  );
  opacity: 0.4;
}

.box .image {
  position: absolute;
  width: 1919px;
  height: 822px;
  top: 202px;
  left: 0;
}

.box .view {
  position: absolute;
  width: 705px;
  height: 684px;
  top: 59px;
  left: 268px;
  transform: rotate(-5.00deg);
}

.box .overlap-group {
  position: absolute;
  width: 708px;
  height: 543px;
  top: 141px;
  left: 0;
}

.box .group {
  position: absolute;
  width: 650px;
  height: 118px;
  top: 0;
  left: 53px;
}

.box .div {
  position: relative;
  width: 655px;
  height: 205px;
  top: -52px;
  left: -4px;
}

.box .union {
  position: absolute;
  width: 644px;
  height: 149px;
  top: 28px;
  left: 5px;
  transform: rotate(5.00deg);
}

.box .subtract {
  position: absolute;
  width: 195px;
  height: 114px;
  top: 47px;
  left: 454px;
  transform: rotate(5.00deg);
}

.box .text-wrapper {
  position: absolute;
  width: 406px;
  top: 70px;
  left: 45px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #ffffff;
  font-size: 50px;
  text-align: center;
  letter-spacing: -3.00px;
  line-height: normal;
}

.box .text-wrapper-2 {
  position: absolute;
  width: 167px;
  top: 70px;
  left: 468px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #333333;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.box .group-2 {
  position: absolute;
  width: 708px;
  height: 439px;
  top: 104px;
  left: 0;
}

.box .text-wrapper-3 {
  position: absolute;
  top: 284px;
  left: 45px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #333333;
  font-size: 129px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.box .overlap-2 {
  position: absolute;
  width: 702px;
  height: 284px;
  top: 0;
  left: 0;
}

.box .text-wrapper-4 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #fa6b58;
  font-size: 237px;
  letter-spacing: -40.29px;
  line-height: normal;
  white-space: nowrap;
}

.box .text-wrapper-5 {
  position: absolute;
  top: 50px;
  left: 400px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #333333;
  font-size: 161px;
  letter-spacing: -20.93px;
  line-height: normal;
  white-space: nowrap;
}

.box .group-3 {
  position: absolute;
  width: 658px;
  height: 110px;
  top: 0;
  left: 47px;
}

.box .group-4 {
  position: absolute;
  width: 486px;
  height: 110px;
  top: 0;
  left: 86px;
}

.box .text-wrapper-6 {
  position: absolute;
  top: 8px;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #19191a;
  font-size: 34px;
  text-align: center;
  letter-spacing: 0.34px;
  line-height: normal;
}

.box .group-5 {
  position: absolute;
  width: 137px;
  height: 5px;
  top: 0;
  left: 330px;
}

.box .ellipse {
  left: 0;
  position: absolute;
  width: 5px;
  height: 5px;
  top: 0;
  background-color: #333333;
  border-radius: 2.5px;
}

.box .ellipse-2 {
  left: 33px;
  position: absolute;
  width: 5px;
  height: 5px;
  top: 0;
  background-color: #333333;
  border-radius: 2.5px;
}

.box .ellipse-3 {
  left: 66px;
  position: absolute;
  width: 5px;
  height: 5px;
  top: 0;
  background-color: #333333;
  border-radius: 2.5px;
}

.box .ellipse-4 {
  left: 99px;
  position: absolute;
  width: 5px;
  height: 5px;
  top: 0;
  background-color: #333333;
  border-radius: 2.5px;
}

.box .ellipse-5 {
  left: 132px;
  position: absolute;
  width: 5px;
  height: 5px;
  top: 0;
  background-color: #333333;
  border-radius: 2.5px;
}

.box .text-wrapper-7 {
  position: absolute;
  top: 69px;
  left: 144px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #19191a;
  font-size: 34px;
  text-align: center;
  letter-spacing: 0.34px;
  line-height: normal;
}

.box .group-6 {
  position: absolute;
  width: 137px;
  height: 5px;
  top: 61px;
  left: 161px;
}

.box .line {
  width: 57px;
  height: 79px;
  top: 24px;
  left: -5px;
  position: absolute;
  transform: rotate(5.00deg);
}

.box .img {
  width: 43px;
  height: 87px;
  top: 20px;
  left: 614px;
  position: absolute;
  transform: rotate(5.00deg);
}

.box .form {
  position: absolute;
  width: 375px;
  height: 618px;
  top: 53px;
  left: 1440px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 0px 24px #00000040;
}

.box .group-7 {
  position: absolute;
  width: 335px;
  height: 68px;
  top: 20px;
  left: 20px;
}

.box .rectangle {
  position: absolute;
  width: 335px;
  height: 38px;
  top: 30px;
  left: 0;
  background-color: #f5f5f5;
  border-radius: 10px;
  border: 1px solid;
  border-color: #bebebe;
}

.box .group-8 {
  position: absolute;
  width: 140px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .overlap-group-wrapper {
  position: absolute;
  width: 42px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .div-wrapper {
  position: relative;
  width: 40px;
  height: 22px;
  background-color: #ce052c;
  border-radius: 10px;
}

.box .text-wrapper-8 {
  position: absolute;
  top: 5px;
  left: 8px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #ffffff;
  font-size: 12px;
  letter-spacing: 0.60px;
  line-height: 12px;
  white-space: nowrap;
}

.box .text-wrapper-9 {
  position: absolute;
  top: 4px;
  left: 50px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 14px;
  letter-spacing: 0.70px;
  line-height: 14px;
  white-space: nowrap;
}

.box .group-9 {
  position: absolute;
  width: 335px;
  height: 68px;
  top: 98px;
  left: 20px;
}

.box .group-10 {
  position: absolute;
  width: 81px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .group-11 {
  position: absolute;
  width: 335px;
  height: 68px;
  top: 176px;
  left: 20px;
}

.box .group-12 {
  position: absolute;
  width: 111px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .group-13 {
  position: absolute;
  width: 335px;
  height: 68px;
  top: 254px;
  left: 20px;
}

.box .group-14 {
  position: absolute;
  width: 155px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .group-15 {
  position: absolute;
  width: 339px;
  height: 88px;
  top: 510px;
  left: 20px;
}

.box .rectangle-2 {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 30px;
  left: 0;
  background-color: #ffffff;
  border-radius: 10px;
  border: 1px solid;
  border-color: #dddddd;
}

.box .text-wrapper-10 {
  position: absolute;
  top: 30px;
  left: 18px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 14px;
  letter-spacing: 0.70px;
  line-height: 14px;
  white-space: nowrap;
}

.box .p {
  position: absolute;
  width: 335px;
  top: 52px;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 12px;
  text-align: justify;
  letter-spacing: 0.60px;
  line-height: 18px;
}

.box .span {
  color: #666666;
  letter-spacing: 0.07px;
}

.box .text-wrapper-11 {
  color: #329bd2;
  letter-spacing: 0.07px;
  text-decoration: underline;
}

.box .group-16 {
  position: absolute;
  width: 184px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .group-17 {
  position: absolute;
  width: 335px;
  height: 68px;
  top: 432px;
  left: 20px;
}

.box .group-18 {
  position: absolute;
  width: 199px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .overlap-group-2 {
  position: relative;
  width: 40px;
  height: 22px;
  background-color: #777777;
  border-radius: 10px;
}

.box .group-19 {
  position: absolute;
  width: 266px;
  height: 90px;
  top: 332px;
  left: 20px;
}

.box .group-wrapper {
  position: absolute;
  width: 167px;
  height: 22px;
  top: 0;
  left: 0;
}

.box .group-20 {
  position: relative;
  width: 169px;
  height: 22px;
}

.box .text-wrapper-12 {
  position: absolute;
  top: 4px;
  left: 50px;
  font-family: "YuGothic-Bold", Helvetica;
  font-weight: 700;
  color: #333333;
  font-size: 14px;
  letter-spacing: 0.70px;
  line-height: 14px;
  white-space: nowrap;
}

.box .group-21 {
  position: absolute;
  width: 268px;
  height: 14px;
  top: 32px;
  left: 0;
}

.box .ellipse-6 {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 0;
  left: 0;
  background-color: #ffffff;
  border-radius: 7px;
  border: 1px solid;
  border-color: #dddddd;
}

.box .text-wrapper-13 {
  position: absolute;
  top: 0;
  left: 18px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 14px;
  letter-spacing: 0.70px;
  line-height: 14px;
  white-space: nowrap;
}

.box .group-22 {
  position: absolute;
  width: 196px;
  height: 14px;
  top: 54px;
  left: 0;
}

.box .group-23 {
  position: absolute;
  width: 64px;
  height: 14px;
  top: 76px;
  left: 0;
}
