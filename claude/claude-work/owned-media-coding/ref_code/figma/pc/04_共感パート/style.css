.box {
  position: relative;
  width: 1920px;
  height: 1476px;
}

.box .s {
  position: fixed;
  width: 1920px;
  height: 1476px;
  top: 0;
  left: 0;
  background-color: #f9f9f9;
}

.box .view {
  width: 1304px;
  height: 164px;
  top: 0;
  position: absolute;
  left: 309px;
}

.box .overlap-group {
  position: absolute;
  width: 1300px;
  height: 76px;
  top: 88px;
  left: 0;
}

.box .rectangle {
  width: 1300px;
  height: 39px;
  top: 37px;
  background-color: #b1e2d5;
  position: absolute;
  left: 0;
}

.box .text-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #333333;
  font-size: 56px;
  text-align: center;
  letter-spacing: -1.68px;
  line-height: normal;
  white-space: nowrap;
}

.box .overlap {
  position: absolute;
  width: 700px;
  height: 71px;
  top: 0;
  left: 300px;
}

.box .div {
  width: 700px;
  height: 39px;
  top: 32px;
  background-color: #b1e2d5;
  position: absolute;
  left: 0;
}

.box .text-wrapper-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #333333;
  font-size: 48px;
  text-align: center;
  letter-spacing: -1.44px;
  line-height: normal;
}

.box .overlap-wrapper {
  width: 1300px;
  height: 1106px;
  top: 270px;
  position: absolute;
  left: 309px;
}

.box .overlap-2 {
  position: relative;
  height: 1106px;
}

.box .BG-list {
  position: absolute;
  width: 1300px;
  height: 1106px;
  top: 0;
  left: 0;
  background-image: url(./img/subtract.svg);
  background-size: 100% 100%;
}

.box .subtract {
  position: absolute;
  width: 161px;
  height: 161px;
  top: 7px;
  left: 1134px;
}

.box .text {
  position: absolute;
  width: 1064px;
  height: 858px;
  top: 124px;
  left: 178px;
}

.box .p {
  position: absolute;
  width: 873px;
  top: 0;
  left: 4px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 46px;
  letter-spacing: 1.84px;
  line-height: 44px;
}

.box .span {
  font-weight: 900;
  color: #fa6b58;
  letter-spacing: 0.85px;
}

.box .text-wrapper-3 {
  font-weight: 700;
  color: #333333;
  letter-spacing: 0.85px;
}

.box .div-2 {
  position: absolute;
  width: 1005px;
  top: 456px;
  left: 1px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 46px;
  letter-spacing: 1.84px;
  line-height: 44px;
}

.box .div-3 {
  position: absolute;
  width: 955px;
  top: 152px;
  left: 2px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 46px;
  letter-spacing: 1.84px;
  line-height: 44px;
}

.box .div-4 {
  position: absolute;
  width: 1049px;
  top: 608px;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 46px;
  letter-spacing: 1.84px;
  line-height: 44px;
}

.box .CV {
  position: absolute;
  width: 825px;
  top: 304px;
  left: 5px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 46px;
  letter-spacing: 1.84px;
  line-height: 44px;
}

.box .div-5 {
  position: absolute;
  width: 1052px;
  top: 760px;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 46px;
  letter-spacing: 1.84px;
  line-height: 44px;
}

.box .line {
  top: 92px;
  left: 28px;
  position: absolute;
  width: 988px;
  height: 6px;
}

.box .img {
  top: 244px;
  left: 21px;
  position: absolute;
  width: 988px;
  height: 6px;
}

.box .line-2 {
  top: 396px;
  left: 21px;
  position: absolute;
  width: 988px;
  height: 6px;
}

.box .line-3 {
  top: 548px;
  left: 21px;
  position: absolute;
  width: 988px;
  height: 6px;
}

.box .line-4 {
  top: 700px;
  left: 21px;
  position: absolute;
  width: 988px;
  height: 6px;
}

.box .line-5 {
  top: 852px;
  left: 21px;
  position: absolute;
  width: 988px;
  height: 6px;
}

.box .icon {
  height: 847px;
  top: 85px;
  left: 78px;
  position: absolute;
  width: 86px;
}

.box .overlap-group-wrapper {
  height: 87px;
  top: 152px;
  left: 0;
  position: absolute;
  width: 86px;
}

.box .overlap-group-2 {
  position: relative;
  width: 93px;
  height: 94px;
  left: -7px;
}

.box .rectangle-2 {
  width: 72px;
  height: 72px;
  top: 22px;
  background-color: #ffffff;
  border-radius: 9px;
  border: 7px solid;
  border-color: #7dc8b6;
  position: absolute;
  left: 0;
}

.box .checkicon {
  position: absolute;
  width: 83px;
  height: 83px;
  top: 0;
  left: 10px;
  object-fit: cover;
}

.box .div-wrapper {
  height: 87px;
  top: 0;
  left: 0;
  position: absolute;
  width: 86px;
}

.box .icon-2 {
  height: 87px;
  top: 304px;
  left: 0;
  position: absolute;
  width: 86px;
}

.box .icon-3 {
  height: 87px;
  top: 456px;
  left: 0;
  position: absolute;
  width: 86px;
}

.box .icon-4 {
  height: 87px;
  top: 608px;
  left: 0;
  position: absolute;
  width: 86px;
}

.box .icon-5 {
  height: 87px;
  top: 760px;
  left: 0;
  position: absolute;
  width: 86px;
}
/* Original CSS code should be injected here */

.box {
  max-width: 100%;
  height: auto;
  overflow-x: hidden;
}

.box .s {
  position: relative;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  background-color: #f9f9f9;
}

.box .view {
  width: 100%;
  max-width: 1304px;
  margin: 0 auto;
  padding: 0 15px;
}

.box .overlap-group,
.box .overlap {
  position: relative;
  width: 100%;
}

.box .rectangle,
.box .div {
  width: 100%;
  height: auto;
  padding: 10px 0;
  background-color: #b1e2d5;
}

.box .text-wrapper,
.box .text-wrapper-2 {
  display: block;
  width: 100%;
  text-align: center;
}

.box .overlap-wrapper {
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 15px;
}

.box .BG-list {
  position: relative;
  width: 100%;
  height: auto;
  background-image: url(./img/subtract.svg);
  background-size: cover;
  background-position: center;
}

.box .subtract {
  position: absolute;
  top: 7px;
  right: 5px;
  width: 12%;
  height: auto;
}

.box .text {
  position: relative;
  width: 100%;
  max-width: 1064px;
  margin: 0 auto;
  padding: 124px 15px;
}

.box .text > * {
  margin-bottom: 30px;
}

.box .line,
.box .img,
.box .line-2,
.box .line-3,
.box .line-4,
.box .line-5 {
  display: block;
  width: 100%;
  height: 6px;
  margin: 20px 0;
}

.box .icon {
  position: absolute;
  left: 6%;
  top: 85px;
  display: flex;
  flex-direction: column;
  gap: 65px;
}

.box .overlap-group-2 {
  position: relative;
  width: 93px;
  height: 94px;
}

.box .rectangle-2 {
  position: absolute;
  width: 72px;
  height: 72px;
  top: 22px;
  left: 0;
  background-color: #ffffff;
  border-radius: 9px;
  border: 7px solid #7dc8b6;
}

.box .checkicon {
  position: absolute;
  width: 83px;
  height: 83px;
  top: 0;
  left: 10px;
  object-fit: cover;
}

@media (max-width: 768px) {
  .box .text-wrapper,
  .box .text-wrapper-2 {
    font-size: 36px;
  }

  .box .icon {
    display: none;
  }

  .box .text {
    padding: 60px 15px;
  }

  .box .text > * {
    font-size: 24px;
  }
}
