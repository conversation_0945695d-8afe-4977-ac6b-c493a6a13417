.box {
  width: 1920px;
  height: 4043px;
}

.box .s {
  position: fixed;
  width: 1920px;
  height: 4043px;
  top: 0;
  left: 0;
}

.box .overlap {
  position: relative;
  height: 4043px;
}

.box .view {
  position: absolute;
  width: 1920px;
  height: 4043px;
  top: 0;
  left: 0;
  background-color: #f9f9f9;
}

.box .vector {
  top: 531px;
  left: 273px;
  position: absolute;
  width: 410px;
  height: 413px;
}

.box .img {
  top: 1195px;
  left: 1213px;
  position: absolute;
  width: 410px;
  height: 413px;
}

.box .vector-2 {
  top: 1910px;
  left: 273px;
  position: absolute;
  width: 410px;
  height: 413px;
}

.box .vector-3 {
  top: 2555px;
  left: 1213px;
  position: absolute;
  width: 410px;
  height: 413px;
}

.box .vector-4 {
  top: 3270px;
  left: 273px;
  position: absolute;
  width: 410px;
  height: 413px;
}

.box .div {
  position: absolute;
  width: 772px;
  height: 324px;
  top: 180px;
  left: 621px;
}

.box .group {
  position: absolute;
  width: 553px;
  height: 87px;
  top: 110px;
  left: 78px;
  background-image: url(./img/union.svg);
  background-size: 100% 100%;
}

.box .overlap-group-wrapper {
  position: relative;
  width: 511px;
  height: 38px;
  top: 15px;
  left: 23px;
}

.box .overlap-group {
  position: relative;
  width: 507px;
  height: 38px;
}

.box .appmart {
  top: 0;
  font-weight: 700;
  color: #ffffff;
  font-size: 32px;
  letter-spacing: 0.32px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper {
  letter-spacing: 0.10px;
}

.box .span {
  font-size: 28px;
  letter-spacing: 0.08px;
}

.box .text-wrapper-2 {
  position: absolute;
  top: 4px;
  left: 169px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 28px;
  letter-spacing: 0.28px;
  line-height: normal;
}

.box .group-2 {
  position: absolute;
  width: 780px;
  height: 100px;
  top: 0;
  left: 0;
}

.box .text-wrapper-3 {
  top: 28px;
  left: 0;
  font-weight: 700;
  color: #333333;
  font-size: 55px;
  letter-spacing: 0.55px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .line {
  position: absolute;
  width: 27px;
  height: 42px;
  top: 43px;
  left: 290px;
}

.box .text-wrapper-4 {
  top: 28px;
  left: 327px;
  font-weight: 700;
  color: #333333;
  font-size: 55px;
  letter-spacing: 0.55px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-5 {
  top: 43px;
  left: 556px;
  font-weight: 700;
  color: #333333;
  font-size: 42px;
  letter-spacing: 0.42px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-6 {
  top: 0;
  left: 605px;
  font-weight: 900;
  color: #3ab795;
  font-size: 83px;
  letter-spacing: 0.83px;
  line-height: normal;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .group-3 {
  position: absolute;
  width: 640px;
  height: 127px;
  top: 197px;
  left: 59px;
}

.box .overlap-2 {
  position: absolute;
  width: 178px;
  height: 103px;
  top: 20px;
  left: 0;
}

.box .text-wrapper-7 {
  top: 0;
  left: 92px;
  font-weight: 500;
  color: #333333;
  font-size: 86px;
  text-align: center;
  letter-spacing: 0.86px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-8 {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(
    164deg,
    rgba(58, 183, 149, 1) 0%,
    rgba(127, 213, 191, 1) 100%
  );
  -webkit-background-clip: text !important;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  font-family: "SF Pro-Semibold", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 160px;
  text-align: center;
  letter-spacing: 1.60px;
  line-height: 80px;
  white-space: nowrap;
}

.box .p {
  top: 0;
  left: 178px;
  font-weight: 400;
  color: transparent;
  font-size: 75px;
  text-align: center;
  letter-spacing: -8.25px;
  line-height: normal;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-9 {
  font-weight: 500;
  color: #333333;
  letter-spacing: -6.19px;
}

.box .text-wrapper-10 {
  font-weight: 700;
  color: #3ab795;
  font-size: 106px;
  letter-spacing: -12.36px;
}

.box .element {
  position: absolute;
  width: 1304px;
  height: 567px;
  top: 624px;
  left: 309px;
}

.box .text-wrapper-11 {
  width: 643px;
  top: 388px;
  left: 652px;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 500;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .div-2 {
  width: 648px;
  top: 200px;
  left: 652px;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 400;
  color: transparent;
  font-size: 55px;
  letter-spacing: 0.55px;
  line-height: 77px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-12 {
  font-weight: 700;
  color: #fa6b58;
  letter-spacing: 0.30px;
}

.box .text-wrapper-13 {
  font-weight: 700;
  color: #333333;
  font-size: 45px;
  letter-spacing: 0.20px;
}

.box .text-wrapper-14 {
  font-weight: 900;
  color: #333333;
  font-size: 45px;
  letter-spacing: 0.20px;
}

.box .number {
  position: absolute;
  width: 211px;
  height: 191px;
  top: 7px;
  left: 652px;
}

.box .text-wrapper-15 {
  position: absolute;
  top: 0;
  left: 137px;
  font-family: "SF Pro Text-Light", Helvetica;
  font-weight: 300;
  color: #3ab795;
  font-size: 160px;
  text-align: center;
  letter-spacing: 1.60px;
  line-height: normal;
  white-space: nowrap;
}

.box .icon {
  position: absolute;
  width: 128px;
  height: 127px;
  top: 32px;
  left: 0;
}

.box .overlap-group-2 {
  position: relative;
  width: 126px;
  height: 127px;
}

.box .subtract {
  position: absolute;
  width: 118px;
  height: 127px;
  top: 0;
  left: 3px;
}

.box .text-wrapper-16 {
  position: absolute;
  top: 37px;
  left: 0;
  background: linear-gradient(
    164deg,
    rgba(58, 183, 149, 1) 0%,
    rgba(127, 213, 191, 1) 100%
  );
  -webkit-background-clip: text !important;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  font-family: "SF Pro Text-Medium", Helvetica;
  font-weight: 500;
  color: transparent;
  font-size: 49px;
  text-align: center;
  letter-spacing: 0.49px;
  line-height: normal;
  white-space: nowrap;
}

.box .images {
  left: 0;
  background-image: url(./img/subtract-9.svg);
  position: absolute;
  width: 567px;
  height: 567px;
  top: 0;
  background-size: 100% 100%;
}

.box .overlap-3 {
  position: relative;
  width: 535px;
  height: 511px;
  top: 39px;
  left: 16px;
}

.box .rectangle {
  position: absolute;
  width: 487px;
  height: 487px;
  top: 0;
  left: 24px;
  background-color: #ffffff;
  border-radius: 25px;
  box-shadow: 0px 0px 24px #5a869780;
}

.box .mask-group {
  position: absolute;
  width: 535px;
  height: 511px;
  top: 0;
  left: 0;
}

.box .element-2 {
  position: absolute;
  width: 1304px;
  height: 567px;
  top: 1312px;
  left: 309px;
}

.box .text-wrapper-17 {
  width: 643px;
  top: 388px;
  left: 0;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .div-3 {
  width: 648px;
  top: 200px;
  left: 0;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 400;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-18 {
  font-weight: 700;
  color: #333333;
  letter-spacing: 0.20px;
}

.box .text-wrapper-19 {
  font-weight: 900;
  color: #fa6b58;
  font-size: 55px;
  letter-spacing: 0.30px;
}

.box .overlap-wrapper {
  position: absolute;
  width: 223px;
  height: 191px;
  top: 7px;
  left: 0;
}

.box .overlap-4 {
  position: relative;
  width: 221px;
  height: 191px;
}

.box .text-wrapper-20 {
  position: absolute;
  top: 0;
  left: 125px;
  font-family: "SF Pro Text-Light", Helvetica;
  font-weight: 300;
  color: #3ab795;
  font-size: 160px;
  text-align: center;
  letter-spacing: 1.60px;
  line-height: normal;
  white-space: nowrap;
}

.box .div-wrapper {
  left: 733px;
  background-image: url(./img/subtract-9.svg);
  position: absolute;
  width: 567px;
  height: 567px;
  top: 0;
  background-size: 100% 100%;
}

.box .overlap-5 {
  position: relative;
  width: 487px;
  height: 487px;
  top: 39px;
  left: 40px;
  background-color: #ffffff;
  border-radius: 25px;
  box-shadow: 0px 0px 24px #5a869780;
  background-image: url(./img/mask-group-1.png);
  background-size: 100% 100%;
}

.box .element-3 {
  position: absolute;
  width: 1304px;
  height: 567px;
  top: 2000px;
  left: 309px;
}

.box .text-wrapper-21 {
  width: 643px;
  top: 388px;
  left: 652px;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 46px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .div-4 {
  width: 648px;
  top: 200px;
  left: 652px;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 700;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-22 {
  color: #333333;
  letter-spacing: 0.20px;
}

.box .text-wrapper-23 {
  color: #fa6b58;
  font-size: 55px;
  letter-spacing: 0.30px;
}

.box .number-2 {
  position: absolute;
  width: 225px;
  height: 191px;
  top: 7px;
  left: 652px;
}

.box .overlap-6 {
  position: relative;
  width: 223px;
  height: 191px;
}

.box .text-wrapper-24 {
  position: absolute;
  top: 0;
  left: 124px;
  font-family: "SF Pro Text-Light", Helvetica;
  font-weight: 300;
  color: #3ab795;
  font-size: 160px;
  text-align: center;
  letter-spacing: 1.60px;
  line-height: normal;
  white-space: nowrap;
}

.box .overlap-7 {
  background-image: url(./img/mask-group-2.png);
  position: relative;
  width: 487px;
  height: 487px;
  top: 39px;
  left: 40px;
  background-color: #ffffff;
  border-radius: 25px;
  box-shadow: 0px 0px 24px #5a869780;
  background-size: 100% 100%;
}

.box .element-4 {
  position: absolute;
  width: 1304px;
  height: 567px;
  top: 2688px;
  left: 309px;
}

.box .div-5 {
  width: 648px;
  top: 200px;
  left: 0;
  text-shadow: 0px 0px 9px #ffffff;
  font-weight: 700;
  color: transparent;
  font-size: 45px;
  letter-spacing: 0.45px;
  line-height: 77px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .number-3 {
  position: absolute;
  width: 227px;
  height: 191px;
  top: 7px;
  left: 0;
}

.box .overlap-8 {
  position: relative;
  width: 225px;
  height: 191px;
}

.box .text-wrapper-25 {
  position: absolute;
  top: 0;
  left: 122px;
  font-family: "SF Pro Text-Light", Helvetica;
  font-weight: 300;
  color: #3ab795;
  font-size: 160px;
  text-align: center;
  letter-spacing: 1.60px;
  line-height: normal;
  white-space: nowrap;
}

.box .overlap-9 {
  background-image: url(./img/mask-group-3.png);
  position: relative;
  width: 487px;
  height: 487px;
  top: 39px;
  left: 40px;
  background-color: #ffffff;
  border-radius: 25px;
  box-shadow: 0px 0px 24px #5a869780;
  background-size: 100% 100%;
}

.box .element-5 {
  position: absolute;
  width: 1304px;
  height: 567px;
  top: 3376px;
  left: 309px;
}

.box .text-wrapper-26 {
  color: #fa6b58;
  letter-spacing: 0.20px;
}

.box .overlap-10 {
  background-image: url(./img/mask-group-4.png);
  position: relative;
  width: 487px;
  height: 487px;
  top: 39px;
  left: 40px;
  background-color: #ffffff;
  border-radius: 25px;
  box-shadow: 0px 0px 24px #5a869780;
  background-size: 100% 100%;
}
