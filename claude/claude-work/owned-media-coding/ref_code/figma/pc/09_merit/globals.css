@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
@import url("https://fonts.googleapis.com/css?family=Noto+Sans+JP:900,400,500,700");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}
@font-face {
  font-family: "SF Pro-Semibold";
  src: url("https://anima-uploads.s3.amazonaws.com/projects/64f035e70ffb37c7c0f32912/fonts/sf-pro.ttf")
    format("truetype");
}

@font-face {
  font-family: "SF Pro Text-Light";
  src: url("https://anima-uploads.s3.amazonaws.com/projects/62e5bd2f729e81c0f2cb0ddb/fonts/sf-pro-text-light.ttf")
    format("truetype");
}

@font-face {
  font-family: "SF Pro Text-Medium";
  src: url("https://anima-uploads.s3.amazonaws.com/projects/6539d51e0dbdc65b57fa27ef/fonts/sf-pro-text-medium.otf")
    format("opentype");
}
