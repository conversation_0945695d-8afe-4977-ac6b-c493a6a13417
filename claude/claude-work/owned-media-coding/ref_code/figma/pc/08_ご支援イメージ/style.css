.box {
  position: relative;
  width: 1920px;
  height: 2643px;
}

.box .s {
  position: fixed;
  width: 1920px;
  height: 2643px;
  top: 0;
  left: 0;
  background-color: #f9f9f9;
}

.box .overlap {
  position: absolute;
  width: 1920px;
  height: 2233px;
  top: 410px;
  left: 0;
}

.box .rectangle {
  width: 1920px;
  height: 1894px;
  top: 339px;
  background-color: #b1e2d5;
  position: absolute;
  left: 0;
}

.box .element {
  position: absolute;
  width: 634px;
  height: 679px;
  top: 0;
  left: 310px;
}

.box .overlap-group {
  position: relative;
  width: 632px;
  height: 679px;
  background-color: #ffffff;
  border-radius: 43px;
  box-shadow: 0px 0px 14px #00000040;
}

.box .img {
  width: 613px;
  height: 336px;
  top: 335px;
  background-color: #e1ede8;
  position: absolute;
  left: 9px;
}

.box .div {
  position: relative;
  width: 580px;
  height: 304px;
  top: 17px;
  left: 15px;
}

.box .group {
  width: 455px;
  height: 263px;
  top: 0;
  left: 0;
  position: absolute;
  background-color: #ffffff;
  border-radius: 14px;
  border: 4px solid;
  border-color: #bcd3c9;
}

.box .element-2 {
  position: absolute;
  width: 435px;
  height: 245px;
  top: 2px;
  left: 5px;
  object-fit: cover;
}

.box .element-wrapper {
  width: 367px;
  height: 233px;
  top: 71px;
  left: 213px;
  position: absolute;
  background-color: #ffffff;
  border-radius: 14px;
  border: 4px solid;
  border-color: #bcd3c9;
}

.box .element-3 {
  position: absolute;
  width: 341px;
  height: 201px;
  top: 12px;
  left: 9px;
  object-fit: cover;
}

.box .view {
  position: absolute;
  width: 542px;
  height: 155px;
  top: 46px;
  left: 46px;
}

.box .div-wrapper {
  position: relative;
  width: 540px;
  height: 155px;
  background-color: #5f6061;
  border-radius: 13px;
}

.box .text-wrapper {
  height: 54px;
  top: 56px;
  left: 205px;
  font-weight: 700;
  color: #ffffff;
  font-size: 38px;
  letter-spacing: 1.52px;
  line-height: 54px;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .p {
  position: absolute;
  width: 540px;
  top: 219px;
  left: 46px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  text-align: center;
  letter-spacing: 0.24px;
  line-height: 44px;
}

.box .span {
  font-weight: 500;
  letter-spacing: 0.06px;
}

.box .text-wrapper-2 {
  font-weight: 700;
  font-size: 28px;
  letter-spacing: 0.08px;
}

.box .overlap-wrapper {
  position: absolute;
  width: 634px;
  height: 679px;
  top: 0;
  left: 977px;
}

.box .overlap-group-2 {
  position: relative;
  width: 576px;
  height: 303px;
  top: 18px;
  left: 20px;
}

.box .element-fotor-wrapper {
  width: 445px;
  height: 262px;
  top: 0;
  left: 0;
  position: absolute;
  background-color: #ffffff;
  border-radius: 14px;
  border: 4px solid;
  border-color: #bcd3c9;
}

.box .element-fotor {
  position: absolute;
  width: 428px;
  height: 243px;
  top: 1px;
  left: 0;
  object-fit: cover;
}

.box .img-wrapper {
  width: 376px;
  height: 234px;
  top: 69px;
  left: 200px;
  position: absolute;
  background-color: #ffffff;
  border-radius: 14px;
  border: 4px solid;
  border-color: #bcd3c9;
}

.box .element-4 {
  position: absolute;
  width: 346px;
  height: 202px;
  top: 14px;
  left: 12px;
  object-fit: cover;
}

.box .overlap-2 {
  position: relative;
  width: 540px;
  height: 155px;
  background-color: #5f5f60;
  border-radius: 13px;
}

.box .text-wrapper-3 {
  position: absolute;
  height: 54px;
  top: 56px;
  left: 79px;
  font-weight: 700;
  color: #ffffff;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 54px;
  white-space: nowrap;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .overlap-group-wrapper {
  position: absolute;
  width: 756px;
  height: 679px;
  top: 720px;
  left: 310px;
}

.box .overlap-3 {
  position: relative;
  width: 754px;
  height: 679px;
}

.box .rectangle-2 {
  width: 632px;
  height: 679px;
  top: 0;
  background-color: #ffffff;
  border-radius: 43px;
  box-shadow: 0px 0px 14px #00000040;
  position: absolute;
  left: 0;
}

.box .img-2 {
  width: 745px;
  height: 311px;
  top: 361px;
  position: absolute;
  left: 9px;
}

.box .overlap-group-3 {
  position: relative;
  height: 311px;
}

.box .rectangle-3 {
  width: 613px;
  height: 311px;
  top: 0;
  background-color: #e1ede8;
  border-radius: 10px 10px 36px 39px;
  position: absolute;
  left: 0;
}

.box .mask-group {
  position: absolute;
  width: 717px;
  height: 267px;
  top: 24px;
  left: 28px;
}

.box .text-wrapper-4 {
  position: absolute;
  height: 108px;
  top: 29px;
  left: 80px;
  font-weight: 700;
  color: #ffffff;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 54px;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .element-5 {
  position: absolute;
  width: 634px;
  height: 679px;
  top: 720px;
  left: 977px;
}

.box .img-3 {
  width: 613px;
  height: 311px;
  top: 361px;
  background-color: #e1ede8;
  position: absolute;
  left: 9px;
}

.box .element-6 {
  position: absolute;
  width: 503px;
  height: 285px;
  top: 14px;
  left: 55px;
  object-fit: cover;
}

.box .text-wrapper-5 {
  position: absolute;
  height: 108px;
  top: 29px;
  left: 98px;
  font-weight: 700;
  color: #ffffff;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 54px;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .element-7 {
  position: absolute;
  width: 634px;
  height: 679px;
  top: 1454px;
  left: 310px;
}

.box .element-8 {
  position: absolute;
  width: 573px;
  height: 268px;
  top: 30px;
  left: 21px;
  object-fit: cover;
}

.box .text-wrapper-6 {
  position: absolute;
  height: 108px;
  top: 29px;
  left: 16px;
  font-weight: 700;
  color: #ffffff;
  font-size: 34px;
  letter-spacing: 0;
  line-height: 54px;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .div-2 {
  position: absolute;
  width: 540px;
  top: 219px;
  left: 31px;
  font-weight: 400;
  color: #333333;
  font-size: 24px;
  letter-spacing: 0.24px;
  line-height: 44px;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .element-9 {
  position: absolute;
  width: 634px;
  height: 679px;
  top: 1454px;
  left: 977px;
}

.box .overlap-group-4 {
  position: relative;
  width: 585px;
  height: 281px;
  top: 17px;
  left: 19px;
}

.box .element-10 {
  position: absolute;
  width: 355px;
  height: 244px;
  top: 0;
  left: 0;
  object-fit: cover;
}

.box .element-11 {
  position: absolute;
  width: 315px;
  height: 208px;
  top: 73px;
  left: 270px;
  object-fit: cover;
}

.box .text-wrapper-7 {
  position: absolute;
  height: 108px;
  top: 29px;
  left: 61px;
  font-weight: 700;
  color: #ffffff;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 54px;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .view-2 {
  display: flex;
  flex-direction: column;
  width: 484px;
  align-items: center;
  gap: 8px;
  position: absolute;
  top: 180px;
  left: 718px;
}

.box .text-wrapper-8 {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  margin-left: -40.00px;
  margin-right: -40.00px;
  font-weight: 700;
  color: #3ab795;
  font-size: 80px;
  letter-spacing: 0.80px;
  line-height: normal;
  white-space: nowrap;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .frame {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  position: relative;
  flex: 0 0 auto;
}

.box .line {
  position: relative;
  width: 15px;
  height: 4px;
}

.box .text-wrapper-9 {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 500;
  color: #3ab795;
  font-size: 38px;
  text-align: center;
  letter-spacing: 0.38px;
  line-height: normal;
}
