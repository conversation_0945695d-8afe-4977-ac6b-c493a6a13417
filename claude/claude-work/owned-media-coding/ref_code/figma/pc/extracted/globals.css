@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
@import url("https://fonts.googleapis.com/css?family=Noto+Sans+JP:700,900,400");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}
@font-face {
  font-family: "YuGothic-Bold";
  src: url("https://anima-uploads.s3.amazonaws.com/projects/677f8158d2311af1a25c74a8/fonts/yu-gothic-bold.otf")
    format("opentype");
}
