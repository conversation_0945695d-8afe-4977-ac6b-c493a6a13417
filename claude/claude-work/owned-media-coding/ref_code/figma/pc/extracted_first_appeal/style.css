.box {
  width: 1920px;
  height: 773px;
}

.box .s {
  position: fixed;
  width: 1922px;
  height: 773px;
  top: 0;
  left: 0;
}

.box .overlap {
  position: relative;
  width: 1920px;
  height: 773px;
}

.box .rectangle {
  position: absolute;
  width: 1920px;
  height: 667px;
  top: 106px;
  left: 0;
  background-color: #b1e2d5;
}

.box .text-wrapper {
  top: 551px;
  left: 697px;
  color: #333333;
  font-size: 78px;
  letter-spacing: -2.34px;
  line-height: 110px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .group {
  position: absolute;
  width: 1209px;
  height: 341px;
  top: 205px;
  left: 376px;
}

.box .overlap-group {
  position: relative;
  width: 1215px;
  height: 341px;
}

.box .div {
  position: absolute;
  width: 1039px;
  height: 60px;
  top: 281px;
  left: 65px;
  background-color: #fa6b58;
}

.box .SEO {
  position: absolute;
  top: 203px;
  left: 79px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 38px;
  text-align: center;
  letter-spacing: -1.14px;
  line-height: 69px;
}

.box .span {
  color: #333333;
  letter-spacing: -0.43px;
}

.box .text-wrapper-2 {
  color: #ffffff;
  letter-spacing: -0.43px;
}

.box .group-2 {
  position: absolute;
  width: 1215px;
  height: 252px;
  top: 0;
  left: 0;
}

.box .overlap-2 {
  position: absolute;
  width: 741px;
  height: 252px;
  top: 0;
  left: 466px;
}

.box .graffiti-random {
  position: absolute;
  width: 421px;
  height: 181px;
  top: 71px;
  left: 155px;
  object-fit: cover;
}

.box .text-wrapper-3 {
  top: 64px;
  left: 661px;
  color: #333333;
  font-size: 80px;
  letter-spacing: -2.40px;
  line-height: 43px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .text-wrapper-4 {
  top: 32px;
  left: 0;
  transform: rotate(-5.00deg);
  color: #fa6b58;
  font-size: 121px;
  letter-spacing: -3.63px;
  line-height: 43px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .overlap-group-2 {
  position: absolute;
  width: 466px;
  height: 78px;
  top: 31px;
  left: 0;
}

.box .text-wrapper-5 {
  top: 14px;
  left: 1px;
  transform: rotate(-5.00deg);
  color: #333333;
  font-size: 100px;
  letter-spacing: -3.00px;
  line-height: 43px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .text-wrapper-6 {
  top: 35px;
  left: 308px;
  color: #333333;
  font-size: 80px;
  letter-spacing: -2.40px;
  line-height: 43px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .people {
  position: absolute;
  width: 1016px;
  height: 187px;
  top: 556px;
  left: 453px;
}

.box .ddsds {
  position: absolute;
  width: 51px;
  height: 182px;
  top: 5px;
  left: 830px;
  object-fit: cover;
}

.box .rfdsa {
  position: absolute;
  width: 47px;
  height: 179px;
  top: 5px;
  left: 0;
  object-fit: cover;
}

.box .gsd {
  position: absolute;
  width: 65px;
  height: 184px;
  top: 0;
  left: 951px;
  object-fit: cover;
}

.box .fs {
  position: absolute;
  width: 73px;
  height: 184px;
  top: 0;
  left: 113px;
  object-fit: cover;
}

.box .fafafa {
  position: absolute;
  width: 219px;
  height: 220px;
  top: 0;
  left: 906px;
  object-fit: cover;
}
/* Original CSS code should be injected here */

.box {
  max-width: 1920px;
  margin: 0 auto;
}

.box .s {
  position: relative;
  width: 100%;
  max-width: 1922px;
  height: 773px;
}

.box .overlap {
  position: relative;
  width: 100%;
  height: 100%;
}

.box .rectangle {
  position: absolute;
  width: 100%;
  height: calc(100% - 106px);
  top: 106px;
  left: 0;
  background-color: #b1e2d5;
}

.box .text-wrapper {
  position: absolute;
  top: 551px;
  left: 50%;
  transform: translateX(-50%);
  color: #333333;
  font-size: 78px;
  letter-spacing: -2.34px;
  line-height: 110px;
  font-family: "Noto Sans JP", Helvetica, sans-serif;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .group {
  position: absolute;
  width: 63%;
  max-width: 1209px;
  height: 341px;
  top: 205px;
  left: 50%;
  transform: translateX(-50%);
}

.box .overlap-group {
  position: relative;
  width: 100%;
  height: 100%;
}

.box .div {
  position: absolute;
  width: 85.5%;
  height: 60px;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fa6b58;
}

.box .SEO {
  position: absolute;
  top: 203px;
  left: 50%;
  transform: translateX(-50%);
  font-family: "Noto Sans JP", Helvetica, sans-serif;
  font-weight: 700;
  font-size: 38px;
  text-align: center;
  letter-spacing: -1.14px;
  line-height: 69px;
}

.box .span {
  color: #333333;
  letter-spacing: -0.43px;
}

.box .text-wrapper-2 {
  color: #ffffff;
  letter-spacing: -0.43px;
}

.box .group-2 {
  position: absolute;
  width: 100%;
  height: 252px;
  top: 0;
  left: 0;
}

.box .overlap-2 {
  position: absolute;
  width: 61%;
  height: 100%;
  top: 0;
  right: 0;
}

.box .graffiti-random {
  position: absolute;
  width: 56.8%;
  height: auto;
  bottom: 0;
  left: 20.9%;
  object-fit: cover;
}

.box .text-wrapper-3,
.box .text-wrapper-4,
.box .text-wrapper-5,
.box .text-wrapper-6 {
  position: absolute;
  font-family: "Noto Sans JP", Helvetica, sans-serif;
  font-weight: 900;
  text-align: center;
  white-space: nowrap;
}

.box .text-wrapper-3 {
  top: 64px;
  right: 0;
  color: #333333;
  font-size: 80px;
  letter-spacing: -2.40px;
  line-height: 43px;
}

.box .text-wrapper-4 {
  top: 32px;
  left: 0;
  transform: rotate(-5deg);
  color: #fa6b58;
  font-size: 121px;
  letter-spacing: -3.63px;
  line-height: 43px;
}

.box .overlap-group-2 {
  position: absolute;
  width: 38.4%;
  height: 78px;
  top: 31px;
  left: 0;
}

.box .text-wrapper-5 {
  top: 14px;
  left: 0.2%;
  transform: rotate(-5deg);
  color: #333333;
  font-size: 100px;
  letter-spacing: -3px;
  line-height: 43px;
}

.box .text-wrapper-6 {
  top: 35px;
  right: 0;
  color: #333333;
  font-size: 80px;
  letter-spacing: -2.40px;
  line-height: 43px;
}

.box .people {
  position: absolute;
  width: 52.9%;
  height: 187px;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
}

.box .ddsds,
.box .rfdsa,
.box .gsd,
.box .fs {
  position: absolute;
  height: auto;
  object-fit: cover;
}

.box .ddsds {
  width: 5%;
  top: 5px;
  right: 13.3%;
}

.box .rfdsa {
  width: 4.6%;
  top: 5px;
  left: 0;
}

.box .gsd {
  width: 6.4%;
  top: 0;
  right: 0;
}

.box .fs {
  width: 7.2%;
  top: 0;
  left: 11.1%;
}

.box .fafafa {
  position: absolute;
  width: 11.4%;
  height: auto;
  top: 0;
  right: 5.4%;
  object-fit: cover;
}

@media (max-width: 1200px) {
  .box .text-wrapper {
    font-size: 60px;
    line-height: 90px;
  }

  .box .SEO {
    font-size: 32px;
    line-height: 58px;
  }

  .box .text-wrapper-4 {
    font-size: 100px;
  }

  .box .text-wrapper-5 {
    font-size: 80px;
  }
}

@media (max-width: 768px) {
  .box .s {
    height: auto;
    padding-bottom: 50px;
  }

  .box .text-wrapper {
    position: relative;
    top: auto;
    margin-top: 30px;
    font-size: 48px;
    line-height: 70px;
  }

  .box .group {
    position: relative;
    top: auto;
    margin-top: 30px;
  }

  .box .SEO {
    font-size: 24px;
    line-height: 44px;
  }

  .box .text-wrapper-4 {
    font-size: 80px;
  }

  .box .text-wrapper-5 {
    font-size: 60px;
  }

  .box .people {
    position: relative;
    bottom: auto;
    margin-top: 30px;
  }
}
