.box {
  position: relative;
  width: 1920px;
  height: 416px;
}

.box .view {
  position: fixed;
  width: 1920px;
  height: 416px;
  top: 0;
  left: 0;
  background-color: #f9f9f9;
}

.box .btn {
  width: 1299px;
  top: 126px;
  left: 311px;
  position: absolute;
  height: 160px;
}

.box .overlap-group-wrapper {
  width: 630px;
  top: 0;
  left: 0;
  position: absolute;
  height: 160px;
}

.box .overlap-group {
  position: relative;
  width: 628px;
  height: 160px;
  background-color: #f9f9f9;
  border-radius: 190px;
  border: 9px solid;
  border-color: #fa6b58;
  box-shadow: 0px 9px 19px #52867821;
}

.box .text-wrapper {
  width: 545px;
  height: 111px;
  top: 15px;
  left: 21px;
  font-weight: 500;
  color: #fa6b58;
  letter-spacing: 0.38px;
  line-height: 59px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-size: 38px;
  text-align: center;
}

.box .union {
  position: absolute;
  width: 17px;
  height: 27px;
  top: 57px;
  left: 572px;
}

.box .overlap-wrapper {
  width: 630px;
  top: 0;
  left: 671px;
  position: absolute;
  height: 160px;
}

.box .overlap {
  position: relative;
  width: 628px;
  height: 160px;
  background-color: #fa6b58;
  border-radius: 190px;
  box-shadow: 0px 9px 19px #52867821;
}

.box .div {
  width: 545px;
  height: 111px;
  top: 24px;
  left: 30px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.38px;
  line-height: 59px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-size: 38px;
  text-align: center;
}

.box .img {
  position: absolute;
  width: 17px;
  height: 27px;
  top: 66px;
  left: 581px;
}

.box .group {
  position: absolute;
  width: 386px;
  height: 46px;
  top: 0;
  left: 768px;
}

.box .overlap-2 {
  position: relative;
  width: 384px;
  height: 46px;
}

.box .p {
  top: 0;
  left: 47px;
  font-weight: 700;
  color: transparent;
  letter-spacing: -1.14px;
  line-height: normal;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  font-size: 38px;
  text-align: center;
}

.box .span {
  color: #333333;
  letter-spacing: -0.43px;
}

.box .text-wrapper-2 {
  color: #fa6b58;
  letter-spacing: -0.43px;
}

.box .union-2 {
  position: absolute;
  width: 384px;
  height: 35px;
  top: 11px;
  left: 0;
}
/* Original CSS code should be injected here */

.box {
  max-width: 100%;
  height: auto;
  overflow: hidden;
}

.box .view {
  position: relative;
  max-width: 100%;
  height: auto;
  padding: 20px;
  background-color: #f9f9f9;
}

.box .btn {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.box .overlap-group-wrapper,
.box .overlap-wrapper {
  flex: 1;
  min-width: 300px;
  max-width: 630px;
}

.box .overlap-group,
.box .overlap {
  position: relative;
  width: 100%;
  padding: 20px;
  border-radius: 95px;
  text-align: center;
  transition: transform 0.3s ease;
}

.box .overlap-group:hover,
.box .overlap:hover {
  transform: translateY(-5px);
}

.box .overlap-group {
  background-color: #f9f9f9;
  border: 9px solid #fa6b58;
}

.box .overlap {
  background-color: #fa6b58;
}

.box .text-wrapper,
.box .div {
  font-family: "Noto Sans JP", Helvetica, sans-serif;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0.38px;
  margin-bottom: 10px;
}

.box .text-wrapper {
  color: #fa6b58;
}

.box .div {
  color: #ffffff;
  font-weight: 700;
}

.box .union,
.box .img {
  width: 17px;
  height: 27px;
  margin-left: 10px;
  vertical-align: middle;
}

.box .group {
  margin-bottom: 30px;
}

.box .overlap-2 {
  position: relative;
  text-align: center;
}

.box .p {
  font-family: "Noto Sans JP", Helvetica, sans-serif;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.43px;
  line-height: 1.2;
  margin-bottom: 10px;
}

.box .span {
  color: #333333;
}

.box .text-wrapper-2 {
  color: #fa6b58;
}

.box .union-2 {
  max-width: 100%;
  height: auto;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .box .text-wrapper,
  .box .div {
    font-size: 20px;
  }

  .box .p {
    font-size: 28px;
  }
}
