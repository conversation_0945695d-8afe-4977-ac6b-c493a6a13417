@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
@import url("https://fonts.googleapis.com/css?family=Noto+Sans+JP:500,700");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

/* Accessibility improvements */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for keyboard navigation */
a:focus,
button:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* Improve readability */
body {
  line-height: 1.5;
  color: #333;
}

/* Responsive font sizing */
html {
  font-size: 16px;
}

@media screen and (min-width: 768px) {
  html {
    font-size: 18px;
  }
}

@media screen and (min-width: 1024px) {
  html {
    font-size: 20px;
  }
}
