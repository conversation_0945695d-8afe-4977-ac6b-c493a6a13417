.box {
  width: 2975px;
  height: 284px;
}

.box .s {
  position: fixed;
  width: 2975px;
  height: 284px;
  top: 0;
  left: 0;
}

.box .overlap-group {
  position: relative;
  width: 1920px;
  height: 284px;
  left: 255px;
  background-color: #f9f9f9;
}

.box .image {
  width: 1044px;
  height: 50px;
  top: 80px;
  left: 0;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .img {
  width: 649px;
  height: 50px;
  top: 80px;
  left: 1271px;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .image-2 {
  width: 1338px;
  height: 52px;
  top: 152px;
  left: 17px;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .image-3 {
  width: 538px;
  height: 52px;
  top: 152px;
  left: 1382px;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .image-4 {
  width: 69px;
  height: 46px;
  top: 84px;
  left: 1126px;
  position: absolute;
  object-fit: cover;
}
/* Original CSS code should be injected here */

.box {
  max-width: 100%;
  overflow-x: hidden;
}

.box .s {
  position: relative;
  max-width: 100%;
  height: auto;
}

.box .overlap-group {
  position: relative;
  max-width: 100%;
  height: auto;
  margin: 0 auto;
  background-color: #f9f9f9;
  padding: 80px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.box .image,
.box .img,
.box .image-2,
.box .image-3,
.box .image-4 {
  position: static;
  max-width: 100%;
  height: auto;
  margin: 10px;
  object-fit: contain;
}

@media (max-width: 768px) {
  .box .overlap-group {
    padding: 40px 0;
  }

  .box .image,
  .box .img,
  .box .image-2,
  .box .image-3,
  .box .image-4 {
    margin: 5px;
  }
}
