.box {
  width: 1920px;
  height: 1952px;
}

.box .s {
  position: fixed;
  width: 1920px;
  height: 1952px;
  top: 0;
  left: 0;
}

.box .overlap {
  position: relative;
  height: 1952px;
}

.box .view {
  position: absolute;
  width: 1920px;
  height: 1952px;
  top: 0;
  left: 0;
}

.box .overlap-group {
  position: relative;
  height: 1914px;
  top: 38px;
}

.box .img {
  position: absolute;
  width: 1920px;
  height: 1863px;
  top: 51px;
  left: 0;
}

.box .arrow {
  position: absolute;
  width: 237px;
  height: 111px;
  top: 0;
  left: 841px;
}

.box .overlap-wrapper {
  position: absolute;
  width: 1368px;
  height: 1430px;
  top: 442px;
  left: 265px;
}

.box .div {
  position: relative;
  height: 1430px;
}

.box .hdsfgsds {
  position: absolute;
  width: 211px;
  height: 445px;
  top: 342px;
  left: 474px;
  object-fit: cover;
}

.box .view-2 {
  position: absolute;
  width: 354px;
  height: 350px;
  top: 636px;
  left: 594px;
  background-image: url(./img/fafda-4x-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .overlap-group-wrapper {
  position: absolute;
  width: 1368px;
  height: 1430px;
  top: 0;
  left: 0;
}

.box .overlap-2 {
  position: relative;
  width: 1370px;
  height: 1430px;
}

.box .group {
  position: absolute;
  width: 557px;
  height: 560px;
  top: 0;
  left: 30px;
}

.box .div-wrapper {
  position: relative;
  width: 597px;
  height: 602px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .p {
  width: 387px;
  height: 93px;
  top: 232px;
  left: 102px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 44px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper {
  font-weight: 900;
  color: #3c8b86;
}

.box .span {
  font-weight: 700;
  color: #5f6061;
  font-size: 32px;
}

.box .group-2 {
  position: absolute;
  width: 571px;
  height: 574px;
  top: 9px;
  left: 503px;
}

.box .PV-wrapper {
  position: relative;
  width: 611px;
  height: 616px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-1-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .PV {
  position: absolute;
  height: 91px;
  top: 231px;
  left: 131px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  text-align: center;
  letter-spacing: 0;
  line-height: 44px;
}

.box .group-3 {
  position: absolute;
  width: 451px;
  height: 395px;
  top: 430px;
  left: 919px;
}

.box .overlap-3 {
  position: relative;
  width: 491px;
  height: 437px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdafaf-2x-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .div-2 {
  width: 267px;
  height: 76px;
  top: 153px;
  left: 118px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 50px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .group-4 {
  position: absolute;
  width: 466px;
  height: 416px;
  top: 771px;
  left: 865px;
}

.box .SEO-wrapper {
  position: relative;
  width: 506px;
  height: 458px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdafaf-2x-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .SEO {
  position: absolute;
  height: 91px;
  top: 188px;
  left: 133px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  text-align: center;
  letter-spacing: 0;
  line-height: 44px;
}

.box .group-5 {
  position: absolute;
  width: 474px;
  height: 480px;
  top: 950px;
  left: 406px;
}

.box .overlap-4 {
  position: relative;
  width: 514px;
  height: 522px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-1-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .div-3 {
  height: 91px;
  top: 250px;
  left: 115px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 44px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .group-6 {
  position: absolute;
  width: 570px;
  height: 570px;
  top: 527px;
  left: 0;
}

.box .overlap-5 {
  position: relative;
  width: 610px;
  height: 612px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .div-4 {
  height: 107px;
  top: 265px;
  left: 188px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 38px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper-2 {
  font-weight: 900;
  color: #3c8b86;
  line-height: 76px;
}

.box .text-wrapper-3 {
  font-weight: 700;
  color: #5f6061;
  font-size: 32px;
  line-height: 44px;
}

.box .view-3 {
  position: absolute;
  width: 1308px;
  height: 282px;
  top: 164px;
  left: 307px;
}

.box .div-5 {
  top: 143px;
  left: 0;
  font-weight: 900;
  color: #333333;
  font-size: 60px;
  letter-spacing: -1.80px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper-4 {
  letter-spacing: -1.08px;
}

.box .text-wrapper-5 {
  font-size: 50px;
  letter-spacing: -0.75px;
}

.box .text-wrapper-6 {
  top: 36px;
  left: 530px;
  transform: rotate(-5.00deg);
  -webkit-text-stroke: 7px #333333;
  font-weight: 900;
  color: #f9f9f9;
  font-size: 186px;
  letter-spacing: 20.46px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .div-6 {
  top: 143px;
  left: 960px;
  font-weight: 400;
  color: #333333;
  font-size: 50px;
  letter-spacing: -1.50px;
  line-height: normal;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper-7 {
  font-weight: 700;
  letter-spacing: -0.75px;
}

.box .text-wrapper-8 {
  font-weight: 900;
  font-size: 60px;
  letter-spacing: -1.08px;
}

.box .group-7 {
  position: absolute;
  width: 101px;
  height: 111px;
  top: -4px;
  left: 956px;
}
.box {
  width: 1920px;
  height: 1952px;
}

.box .s {
  position: fixed;
  width: 1920px;
  height: 1952px;
  top: 0;
  left: 0;
}

.box .overlap {
  position: relative;
  height: 1952px;
}

.box .view {
  position: absolute;
  width: 1920px;
  height: 1952px;
  top: 0;
  left: 0;
}

.box .overlap-group {
  position: relative;
  height: 1914px;
  top: 38px;
}

.box .img {
  position: absolute;
  width: 1920px;
  height: 1863px;
  top: 51px;
  left: 0;
}

.box .arrow {
  position: absolute;
  width: 237px;
  height: 111px;
  top: 0;
  left: 841px;
}

.box .overlap-wrapper {
  position: absolute;
  width: 1368px;
  height: 1430px;
  top: 442px;
  left: 265px;
}

.box .div {
  position: relative;
  height: 1430px;
}

.box .hdsfgsds {
  position: absolute;
  width: 211px;
  height: 445px;
  top: 342px;
  left: 474px;
  object-fit: cover;
}

.box .view-2 {
  position: absolute;
  width: 354px;
  height: 350px;
  top: 636px;
  left: 594px;
  background-image: url(./img/fafda-4x-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .overlap-group-wrapper {
  position: absolute;
  width: 1368px;
  height: 1430px;
  top: 0;
  left: 0;
}

.box .overlap-2 {
  position: relative;
  width: 1370px;
  height: 1430px;
}

.box .group {
  position: absolute;
  width: 557px;
  height: 560px;
  top: 0;
  left: 30px;
}

.box .div-wrapper {
  position: relative;
  width: 597px;
  height: 602px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .p {
  width: 387px;
  height: 93px;
  top: 232px;
  left: 102px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 44px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper {
  font-weight: 900;
  color: #3c8b86;
}

.box .span {
  font-weight: 700;
  color: #5f6061;
  font-size: 32px;
}

.box .group-2 {
  position: absolute;
  width: 571px;
  height: 574px;
  top: 9px;
  left: 503px;
}

.box .PV-wrapper {
  position: relative;
  width: 611px;
  height: 616px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-1-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .PV {
  position: absolute;
  height: 91px;
  top: 231px;
  left: 131px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  text-align: center;
  letter-spacing: 0;
  line-height: 44px;
}

.box .group-3 {
  position: absolute;
  width: 451px;
  height: 395px;
  top: 430px;
  left: 919px;
}

.box .overlap-3 {
  position: relative;
  width: 491px;
  height: 437px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdafaf-2x-1.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .div-2 {
  width: 267px;
  height: 76px;
  top: 153px;
  left: 118px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 50px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .group-4 {
  position: absolute;
  width: 466px;
  height: 416px;
  top: 771px;
  left: 865px;
}

.box .SEO-wrapper {
  position: relative;
  width: 506px;
  height: 458px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdafaf-2x-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .SEO {
  position: absolute;
  height: 91px;
  top: 188px;
  left: 133px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  text-align: center;
  letter-spacing: 0;
  line-height: 44px;
}

.box .group-5 {
  position: absolute;
  width: 474px;
  height: 480px;
  top: 950px;
  left: 406px;
}

.box .overlap-4 {
  position: relative;
  width: 514px;
  height: 522px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-1-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .div-3 {
  height: 91px;
  top: 250px;
  left: 115px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 44px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .group-6 {
  position: absolute;
  width: 570px;
  height: 570px;
  top: 527px;
  left: 0;
}

.box .overlap-5 {
  position: relative;
  width: 610px;
  height: 612px;
  top: -10px;
  left: -21px;
  background-image: url(./img/fdasfas-2x-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.box .div-4 {
  height: 107px;
  top: 265px;
  left: 188px;
  font-weight: 400;
  color: transparent;
  font-size: 38px;
  letter-spacing: 0;
  line-height: 38px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper-2 {
  font-weight: 900;
  color: #3c8b86;
  line-height: 76px;
}

.box .text-wrapper-3 {
  font-weight: 700;
  color: #5f6061;
  font-size: 32px;
  line-height: 44px;
}

.box .view-3 {
  position: absolute;
  width: 1308px;
  height: 282px;
  top: 164px;
  left: 307px;
}

.box .div-5 {
  top: 143px;
  left: 0;
  font-weight: 900;
  color: #333333;
  font-size: 60px;
  letter-spacing: -1.80px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper-4 {
  letter-spacing: -1.08px;
}

.box .text-wrapper-5 {
  font-size: 50px;
  letter-spacing: -0.75px;
}

.box .text-wrapper-6 {
  top: 36px;
  left: 530px;
  transform: rotate(-5.00deg);
  -webkit-text-stroke: 7px #333333;
  font-weight: 900;
  color: #f9f9f9;
  font-size: 186px;
  letter-spacing: 20.46px;
  line-height: normal;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .div-6 {
  top: 143px;
  left: 960px;
  font-weight: 400;
  color: #333333;
  font-size: 50px;
  letter-spacing: -1.50px;
  line-height: normal;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
  text-align: center;
}

.box .text-wrapper-7 {
  font-weight: 700;
  letter-spacing: -0.75px;
}

.box .text-wrapper-8 {
  font-weight: 900;
  font-size: 60px;
  letter-spacing: -1.08px;
}

.box .group-7 {
  position: absolute;
  width: 101px;
  height: 111px;
  top: -4px;
  left: 956px;
}

@media (max-width: 1920px) {
  .box {
    width: 100%;
    height: auto;
  }

  .box .s {
    position: relative;
    width: 100%;
    height: auto;
  }

  .box .overlap {
    height: auto;
  }

  .box .view {
    position: relative;
    width: 100%;
    height: auto;
  }

  .box .overlap-group {
    height: auto;
    top: 0;
  }

  .box .img {
    width: 100%;
    height: auto;
    position: relative;
    top: 0;
  }

  .box .arrow {
    width: 12.34%;
    height: auto;
    left: 43.8%;
  }

  .box .overlap-wrapper {
    width: 71.25%;
    height: auto;
    left: 13.8%;
  }

  .box .div {
    height: auto;
  }

  .box .view-2 {
    width: 25.88%;
    height: 0;
    padding-bottom: 25.58%;
  }

  .box .view-3 {
    width: 68.13%;
    height: auto;
    left: 16%;
  }
}

@media (max-width: 768px) {
  .box .text-wrapper-6 {
    font-size: 120px;
  }

  .box .div-5,
  .box .div-6 {
    font-size: 40px;
  }

  .box .p,
  .box .PV,
  .box .div-2,
  .box .SEO,
  .box .div-3,
  .box .div-4 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .box .text-wrapper-6 {
    font-size: 80px;
  }

  .box .div-5,
  .div-6 {
    font-size: 30px;
  }

  .box .p,
  .box .PV,
  .box .div-2,
  .box .SEO,
  .box .div-3,
  .box .div-4 {
    font-size: 20px;
  }
}
