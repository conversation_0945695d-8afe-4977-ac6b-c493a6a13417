.box {
  width: 375px;
  height: 824px;
}

.box .s {
  position: fixed;
  width: 377px;
  height: 824px;
  top: 0;
  left: 0;
}

.box .overlap {
  position: relative;
  width: 379px;
  height: 824px;
}

.box .rectangle {
  position: absolute;
  width: 375px;
  height: 771px;
  top: 53px;
  left: 0;
  background-color: #b1e2d5;
}

.box .fafafa {
  position: absolute;
  width: 104px;
  height: 104px;
  top: 0;
  left: 148px;
  object-fit: cover;
}

.box .text-wrapper {
  top: 505px;
  left: 55px;
  font-weight: 900;
  color: #333333;
  font-size: 39px;
  text-align: center;
  letter-spacing: -1.17px;
  line-height: 45px;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .group {
  width: 379px;
  height: 350px;
  top: 130px;
  position: absolute;
  left: 0;
}

.box .overlap-group {
  position: absolute;
  width: 375px;
  height: 82px;
  top: 268px;
  left: 0;
  background-color: #fa6b58;
}

.box .SEO {
  position: absolute;
  width: 337px;
  top: 5px;
  left: 20px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 20px;
  text-align: center;
  letter-spacing: -0.60px;
  line-height: 35px;
}

.box .div {
  position: absolute;
  width: 347px;
  height: 250px;
  top: 0;
  left: 16px;
}

.box .text-wrapper-2 {
  top: 188px;
  left: 35px;
  font-weight: 700;
  color: #333333;
  font-size: 20px;
  text-align: center;
  letter-spacing: -0.60px;
  line-height: 31px;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .overlap-wrapper {
  width: 347px;
  height: 219px;
  top: 0;
  position: absolute;
  left: 0;
}

.box .overlap-2 {
  position: relative;
  width: 339px;
  height: 219px;
}

.box .overlap-group-2 {
  position: absolute;
  width: 339px;
  height: 167px;
  top: 52px;
  left: 0;
}

.box .graffiti-random {
  position: absolute;
  width: 319px;
  height: 136px;
  top: 31px;
  left: 15px;
  object-fit: cover;
}

.box .text-wrapper-3 {
  top: 13px;
  left: 1px;
  transform: rotate(-5.00deg);
  font-weight: 900;
  color: #fa6b58;
  font-size: 50px;
  letter-spacing: -1.50px;
  line-height: 43px;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-4 {
  top: 19px;
  left: 308px;
  font-weight: 900;
  color: #333333;
  font-size: 31px;
  text-align: center;
  letter-spacing: -0.93px;
  line-height: 32px;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .overlap-3 {
  position: absolute;
  width: 222px;
  height: 57px;
  top: 0;
  left: 0;
}

.box .text-wrapper-5 {
  top: 20px;
  left: 160px;
  font-weight: 900;
  color: #333333;
  font-size: 31px;
  text-align: center;
  letter-spacing: -0.93px;
  line-height: 32px;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .text-wrapper-6 {
  height: 43px;
  top: 7px;
  left: 2px;
  transform: rotate(-5.00deg);
  font-weight: 900;
  color: #333333;
  font-size: 50px;
  letter-spacing: -1.50px;
  line-height: 43px;
  white-space: nowrap;
  position: absolute;
  font-family: "Noto Sans JP", Helvetica;
}

.box .people {
  position: absolute;
  width: 249px;
  height: 170px;
  top: 574px;
  left: 53px;
}

.box .rfdsa {
  position: absolute;
  width: 43px;
  height: 164px;
  top: 6px;
  left: 0;
  object-fit: cover;
}

.box .fs {
  position: absolute;
  width: 67px;
  height: 168px;
  top: 2px;
  left: 54px;
  object-fit: cover;
}

.box .ddsds {
  position: absolute;
  width: 47px;
  height: 166px;
  top: 2px;
  left: 132px;
  object-fit: cover;
}

.box .gsd {
  position: absolute;
  width: 59px;
  height: 168px;
  top: 0;
  left: 190px;
  object-fit: cover;
}
