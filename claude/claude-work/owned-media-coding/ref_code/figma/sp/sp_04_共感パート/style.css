.box {
  position: relative;
  width: 375px;
  height: 1403px;
}

.box .s {
  position: fixed;
  width: 375px;
  height: 1403px;
  top: 0;
  left: 0;
  background-color: #f9f9f9;
}

.box .view {
  width: 339px;
  height: 106px;
  top: 0;
  position: absolute;
  left: 20px;
}

.box .overlap {
  position: absolute;
  width: 302px;
  height: 28px;
  top: 0;
  left: 16px;
  background-color: #b1e2d5;
}

.box .text-wrapper {
  position: absolute;
  top: 2px;
  left: 5px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #333333;
  font-size: 20px;
  text-align: center;
  letter-spacing: -0.60px;
  line-height: normal;
  white-space: nowrap;
}

.box .overlap-group {
  position: absolute;
  width: 335px;
  height: 66px;
  top: 40px;
  left: 0;
  background-color: #b1e2d5;
}

.box .div {
  position: absolute;
  width: 334px;
  top: 0;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #333333;
  font-size: 24px;
  text-align: center;
  letter-spacing: -0.72px;
  line-height: 33px;
}

.box .overlap-wrapper {
  width: 335px;
  height: 1182px;
  top: 141px;
  position: absolute;
  left: 20px;
}

.box .overlap-2 {
  position: relative;
  height: 1182px;
}

.box .BG-list {
  position: absolute;
  width: 335px;
  height: 1182px;
  top: 0;
  left: 0;
  background-image: url(./img/subtract.svg);
  background-size: 100% 100%;
}

.box .subtract {
  position: absolute;
  width: 31px;
  height: 31px;
  top: 1px;
  left: 304px;
}

.box .text {
  position: absolute;
  width: 282px;
  height: 1056px;
  top: 102px;
  left: 33px;
}

.box .p {
  position: absolute;
  height: 68px;
  top: 0;
  left: 28px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: 34px;
}

.box .span {
  font-weight: 900;
  color: #fa6b58;
  letter-spacing: 0.16px;
}

.box .text-wrapper-2 {
  font-weight: 700;
  color: #333333;
  letter-spacing: 0.16px;
}

.box .div-2 {
  position: absolute;
  height: 68px;
  top: 579px;
  left: 0;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: 34px;
}

.box .div-3 {
  position: absolute;
  height: 68px;
  top: 193px;
  left: 1px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: 34px;
}

.box .div-4 {
  position: absolute;
  height: 68px;
  top: 772px;
  left: 1px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: 34px;
}

.box .CV {
  position: absolute;
  height: 68px;
  top: 386px;
  left: 22px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: 34px;
}

.box .div-5 {
  position: absolute;
  height: 68px;
  top: 965px;
  left: 11px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.80px;
  line-height: 34px;
}

.box .line {
  top: 88px;
  left: 6px;
  position: absolute;
  width: 254px;
  height: 3px;
}

.box .img {
  top: 281px;
  left: 4px;
  position: absolute;
  width: 254px;
  height: 3px;
}

.box .line-2 {
  top: 474px;
  left: 4px;
  position: absolute;
  width: 254px;
  height: 3px;
}

.box .line-3 {
  top: 667px;
  left: 4px;
  position: absolute;
  width: 254px;
  height: 3px;
}

.box .line-4 {
  top: 860px;
  left: 4px;
  position: absolute;
  width: 254px;
  height: 3px;
}

.box .line-5 {
  top: 1053px;
  left: 4px;
  position: absolute;
  width: 254px;
  height: 3px;
}

.box .icon {
  height: 1024px;
  top: 20px;
  left: 139px;
  position: absolute;
  width: 59px;
}

.box .overlap-group-wrapper {
  height: 59px;
  top: 193px;
  left: 0;
  position: absolute;
  width: 59px;
}

.box .overlap-group-2 {
  position: relative;
  width: 62px;
  height: 62px;
  left: -3px;
}

.box .rectangle {
  position: absolute;
  width: 45px;
  height: 45px;
  top: 17px;
  left: 0;
  background-color: #ffffff;
  border-radius: 3px;
  border: 3px solid;
  border-color: #7dc8b6;
}

.box .checkicon {
  position: absolute;
  width: 57px;
  height: 57px;
  top: 0;
  left: 5px;
  object-fit: cover;
}

.box .div-wrapper {
  height: 59px;
  top: 0;
  left: 0;
  position: absolute;
  width: 59px;
}

.box .icon-2 {
  height: 59px;
  top: 386px;
  left: 0;
  position: absolute;
  width: 59px;
}

.box .icon-3 {
  height: 59px;
  top: 579px;
  left: 0;
  position: absolute;
  width: 59px;
}

.box .icon-4 {
  height: 59px;
  top: 772px;
  left: 0;
  position: absolute;
  width: 59px;
}

.box .icon-5 {
  height: 59px;
  top: 965px;
  left: 0;
  position: absolute;
  width: 59px;
}
