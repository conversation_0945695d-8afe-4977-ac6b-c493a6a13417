.box {
  width: 375px;
  height: 3859px;
}

.box .s {
  position: fixed;
  width: 375px;
  height: 3859px;
  top: 0;
  left: 0;
}

.box .frame {
  display: flex;
  flex-direction: column;
  width: 375px;
  align-items: center;
  gap: 80px;
  padding: 80px 19px;
  position: relative;
}

.box .BG {
  position: absolute;
  width: 375px;
  height: 3783px;
  top: 0;
  left: 0;
}

.box .BG-green-wrapper {
  display: flex;
  width: 375px;
  height: 3783px;
  align-items: flex-end;
  justify-content: center;
  gap: 10px;
  padding: 477px 0px 0px;
  position: relative;
  background-color: #f9f9f9;
}

.box .BG-green {
  position: relative;
  align-self: stretch;
  width: 375px;
  background-color: #b1e2d5;
}

.box .view {
  flex-direction: column;
  gap: 5px;
  display: inline-flex;
  align-items: center;
  position: relative;
  flex: 0 0 auto;
}

.box .text-wrapper {
  position: relative;
  width: 198px;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #3ab795;
  font-size: 28px;
  text-align: center;
  letter-spacing: 0.28px;
  line-height: normal;
}

.box .div {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  position: relative;
  flex: 0 0 auto;
}

.box .line {
  position: relative;
  width: 15px;
  height: 2px;
}

.box .text-wrapper-2 {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 500;
  color: #3ab795;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.20px;
  line-height: normal;
  white-space: nowrap;
}

.box .frame-2 {
  display: flex;
  flex-direction: column;
  width: 335px;
  align-items: flex-start;
  gap: 35px;
  position: relative;
  flex: 0 0 auto;
}

.box .element {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 35px;
  padding: 20px 8px 8px;
  position: relative;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
  background-color: #ffffff;
  border-radius: 20px;
  box-shadow: 0px 0px 14px #00000040;
}

.box .title-wrapper {
  min-width: 295px;
  min-height: 92px;
  justify-content: center;
  gap: 10px;
  padding: 12px 0px;
  background-color: #5f6061;
  border-radius: 13px;
  display: inline-flex;
  align-items: center;
  position: relative;
  flex: 0 0 auto;
}

.box .title {
  position: relative;
  width: 295px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0.88px;
  line-height: 35px;
}

.box .description {
  position: relative;
  width: 295px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 400;
  color: #333333;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: 38px;
}

.box .span {
  font-weight: 500;
}

.box .text-wrapper-3 {
  font-weight: 700;
  font-size: 22px;
}

.box .img {
  display: flex;
  flex-direction: column;
  width: 320px;
  height: 227px;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 13px;
  position: relative;
  margin-left: -0.50px;
  margin-right: -0.50px;
  background-color: #e1ede8;
  border-radius: 0px 0px 13px 13px;
}

.box .container {
  position: relative;
  width: 294.58px;
  height: 201.71px;
  margin-top: -0.35px;
  margin-bottom: -0.35px;
  margin-left: -0.29px;
  margin-right: -0.29px;
}

.box .overlap-group {
  position: relative;
  width: 295px;
  height: 202px;
}

.box .element-wrapper {
  position: absolute;
  width: 247px;
  height: 143px;
  top: 0;
  left: 0;
  background-color: #ffffff;
  border-radius: 14px;
  border: 4px solid;
  border-color: #bcd3c9;
}

.box .element-2 {
  position: absolute;
  width: 240px;
  height: 137px;
  top: -3px;
  left: -1px;
  object-fit: cover;
}

.box .img-wrapper {
  position: absolute;
  width: 200px;
  height: 127px;
  top: 75px;
  left: 95px;
  background-color: #ffffff;
  border-radius: 14px;
  border: 4px solid;
  border-color: #bcd3c9;
}

.box .element-3 {
  position: absolute;
  width: 189px;
  height: 113px;
  top: 3px;
  left: 1px;
  object-fit: cover;
}

.box .element-4 {
  position: absolute;
  width: 238px;
  height: 135px;
  top: -2px;
  left: 0;
}

.box .element-5 {
  position: absolute;
  width: 181px;
  height: 105px;
  top: 7px;
  left: 5px;
  object-fit: cover;
}

.box .title-2 {
  margin-top: -1.00px;
  position: relative;
  width: 295px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0.88px;
  line-height: 35px;
}

.box .container-2 {
  position: relative;
  flex: 1;
  align-self: stretch;
  width: 100%;
  flex-grow: 1;
  background-image: url(./img/01-1-2.png);
  background-size: 100% 100%;
}

.box .container-3 {
  position: relative;
  flex: 1;
  align-self: stretch;
  width: 100%;
  flex-grow: 1;
  background-image: url(./img/01-1-3.png);
  background-size: 100% 100%;
}

.box .container-4 {
  position: relative;
  flex: 1;
  align-self: stretch;
  width: 100%;
  flex-grow: 1;
  background-image: url(./img/01-1-4.png);
  background-size: cover;
  background-position: 50% 50%;
}
