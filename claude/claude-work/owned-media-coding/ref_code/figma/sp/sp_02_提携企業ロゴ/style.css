.box {
  position: relative;
  width: 2748px;
  height: 274px;
}

.box .s {
  position: fixed;
  width: 2748px;
  height: 274px;
  top: 0;
  left: 0;
}

.box .overlap-group {
  position: absolute;
  width: 3149px;
  height: 274px;
  top: 0;
  left: -1201px;
}

.box .rectangle {
  position: absolute;
  width: 1920px;
  height: 274px;
  top: 0;
  left: 1229px;
  background-color: #f9f9f9;
}

.box .image {
  width: 375px;
  height: 50px;
  top: 80px;
  left: 1909px;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .img {
  width: 1299px;
  height: 50px;
  top: 80px;
  left: 0;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .image-2 {
  width: 375px;
  height: 52px;
  top: 142px;
  left: 1909px;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .image-3 {
  width: 1338px;
  height: 52px;
  top: 152px;
  left: 111px;
  mix-blend-mode: multiply;
  position: absolute;
  object-fit: cover;
}

.box .image-4 {
  width: 69px;
  height: 46px;
  top: 84px;
  left: -1346px;
  position: absolute;
  object-fit: cover;
}
