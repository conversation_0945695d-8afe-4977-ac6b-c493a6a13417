# 技術仕様ガイドライン

## 作業環境
- **テンプレートファイル**: `mount_wordpress/wp-content/themes/appmart/s-owned-media.php`
- **SCSSファイル**: `mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss` (単一ファイルで完結)
- **画像保存場所**: `mount_wordpress/wp-content/themes/appmart/assets/images/s-owned/`
- **SCSSビルドコマンド**: `npm run sass:compile`
- **確認URL**: http://localhost:20085/new-owned-media/

## 命名規則
- BEM記法: `owned-media-[section]__[element]--[modifier]`
- 画像: `[セクション名]-[用途]-[番号].拡張子`

## HTML/PHP構造
```php
<section class="owned-media-[section-name]" id="owned-media-[section-name]">
  <div class="owned-media-[section-name]__container">
    <!-- コンテンツ -->
  </div>
</section>
```
- 画像パス: `<?php echo get_template_directory_uri(); ?>/assets/images/s-owned/`

## SCSS構造（単一ファイル）
```scss
// 変数定義
$owned-media-primary-color: #fa6b58;
$owned-media-bg-color: #b1e2d5;
$owned-media-text-color: #333;
$owned-media-white: #fff;
$owned-media-base-color: #f9f9f9;

// フォント設定
$owned-media-font-family: 'Noto Sans JP', helvetica, sans-serif;

// ミキシン
@mixin owned-media-container {
  width: 100%;
  max-width: 1340px; // 1300px + padding(20px × 2)
  padding: 0 20px;
  margin: 0 auto;
}

// セクションスタイル
.owned-media-[section] {
  &__container {
    @include owned-media-container;
  }
}
```

## 実装の基本方針
- **CSS優先**: 画像よりテキストやCSSでデザインを実現
- **レイアウト**: Flexbox/Gridを使用、position:absoluteは最小限
- **コンテナ幅**: 1300px固定（デザインは1920pxでも実装は1300px）
- **数値の正確性**: 
  - フォントサイズ: 提供されたpx値を正確に使用してclamp()を設定
  - 余白: padding/marginは提供された具体的な値を使用
  - 色: #fa6b58（プライマリ）、#333（テキスト）等、正確なカラーコードを使用
- **文字詰め**: 必要に応じてletter-spacingで調整（日本語は-0.03em程度が基準）

## レスポンシブ対応のルール
- PC版: `figma/pc/[番号_セクション名].zip`
- スマホ版: `figma/sp/sp_[番号_セクション名].zip`
- 基本的に画像は縮小対応、異なるレイアウトの場合のみ差し替え
- ブレークポイント: 1200px, 768px