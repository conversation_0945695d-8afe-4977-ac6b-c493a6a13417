# Figmaデザイン参照ガイドライン

## Figmaデザインファイル
- **PC版デザイン**: https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=0-1&p=f&m=dev
- **スマホ版デザイン**: https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=12-5529&m=dev

## 各セクションのFigma Node ID（PC版）
- FV（ファーストビュー）: `12-26`
- ファースト訴求: `12-263`
- 提携企業ロゴ: `12-281`
- CTAボタン: `12-288`
- 共感パート: `12-307`
- 発見パート: `12-354`
- 成果セクション: `12-1836`
- サービス内容: `12-4803`
- ご支援イメージ: `12-5015`
- 5つのメリット: `12-5095`
- ご支援体制: `12-5228`
- ご支援事例: `12-5308`
- 契約までの流れ: `12-5384`
- よくある質問: `12-5448`

## 各セクションのFigma Node ID（SP版）
- FV（ファーストビュー）: `12-5537`
- ファースト訴求: `12-5714`
- 提携企業ロゴ: `12-5733`
- CTAボタン: `12-5740`
- 共感パート: `12-5759`
- 発見パート: `12-5806`
- 成果セクション: `12-7281`
- サービス内容: `12-8801`
- ご支援イメージ: `12-8868`
- 5つのメリット: `12-8897`
- ご支援体制: `12-8966`
- ご支援事例: `12-9049`
- 契約までの流れ: `12-9128`
- よくある質問: `12-9196`

## MCPツールでの正しいアクセス方法
```
mcp__figma__get_figma_data(
  fileKey: "wTDUbl55LjN8R9NzeiZppa",  // URLから抽出
  nodeId: "12-288",                   // 各セクションのNode ID
  depth: 1                            // 階層の深さ（大きなファイルは浅めに）
)
```

## 取得できる主要な情報
- **レイアウト情報**: width, height, x, y座標
- **スタイル情報**: 
  - 色（fills, strokes）
  - タイポグラフィ（fontSize, fontWeight, lineHeight, letterSpacing）
  - エフェクト（boxShadow）
  - border-radius
- **構造情報**: 子要素の配置、グループ構成

## デザイン確認用画像対応表
```
for_check_img/
├─ pc/
│  ├─ FV.png                → 00_FV.zip
│  ├─ S_ファースト訴求.png      → 01_ファースト訴求.zip
│  ├─ S_提携企業ロゴ.png     → 02_提携企業ロゴ.zip
│  ├─ お申込みボタン.png       → 03_お申込みボタン.zip
│  ├─ S_共感パート.png        → 04_共感パート.zip
│  └─ ...(以下同様の対応)
└─ sp/
   └─ (スマホ版確認用画像)
```