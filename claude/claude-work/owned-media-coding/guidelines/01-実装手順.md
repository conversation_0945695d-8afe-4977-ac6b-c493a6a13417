# LP実装手順ガイドライン

## 実装タスクリスト

### 必須実装セクション（優先順に記載）

- [x] 00_FV.zip - ファーストビュー ✅ 完了
- [x] 01\_ファースト訴求.zip - ファースト訴求セクション ✅ 完了
- [x] 02\_提携企業ロゴ.zip - 提携企業ロゴセクション ✅ 完了
- [x] 03\_お申込みボタン.zip - CTAボタンセクション ✅ 完了
- [x] 04\_共感パート.zip - 共感パートセクション ✅ 完了
- [x] 05\_発見パート.zip - 発見パートセクション ✅ 完了
- [x] 06\_成果の出るオウンドメディア運用とは？.zip - 成果セクション ✅ 完了
- [x] 07\_サービス内容.zip - サービス内容セクション ✅ 完了
- [x] 08\_ご支援イメージ.zip - ご支援イメージセクション ✅ 完了
- [x] 09_5つのメリット.zip - 5つのメリットセクション ✅ 完了
- [ ] 10\_ご支援体制.zip - ご支援体制セクション 🔄 実装中（zipファイル解凍済み）
- [ ] 11\_ご支援事例.zip - ご支援事例セクション
- [ ] 12\_契約までの流れ.zip - 契約フローセクション
- [ ] 13\_よくある質問.zip - FAQセクション

## 各セクション実装時の必須作業

### 1. デザインファイル詳細分析

- PCデザイン: `figma/pc/[番号_セクション名].zip`を解凍
- スマホデザイン: `figma/sp/sp_[番号_セクション名].zip`を解凍
- 確認用画像: `for_check_img/pc/[セクション名].png`と照合
- **重要数値の抽出**: フォントサイズ、行間、余白、色コードをメモ

### 2. Figmaデザインとの照合（重要）

MCPツールでデザイン値を取得：

```
mcp__figma__get_figma_data(
  fileKey: "wTDUbl55LjN8R9NzeiZppa",
  nodeId: "[該当セクションのNode ID]",
  depth: 1
)
```

### 3. 実装前の準備

- 提供されたCSSから具体的な数値を抽出（px値、色、font-weight等）
- 画像アセットの確認と意味のある名前への変更
- セクション間の余白を確認（前後のセクションとの関係）

### 4. 精密な実装作業

- PHPテンプレート: セマンティックなHTML構造で実装
- SCSS実装時の注意点:
  - フォントサイズ: デザインの具体的なpx値を基準にclamp()を設定
  - 余白: デザインの正確な値を使用（概算値は避ける）
  - 色: 提供されたカラーコードを正確に使用
  - 行間: line-heightは提供された値を厳守
  - レイアウト: 要素間の正確な配置関係を再現
  - コンパイルは`npm run sass:compile` のみでよい

### 5. 実装後のFigmaデザイン検証

- 実装したCSS値とFigmaから取得した値を照合
- 特に重要な値（高さ、フォントサイズ、余白）の一致を確認
- レイアウトの崩れ、要素の配置ずれがないか確認

### 6. 視覚的検証

- SCSSコンパイル: `npm run sass:compile`
- ブラウザで確認: http://localhost:20085/new-owned-media/
- 確認用画像と並べて比較
- テキストの改行位置、要素間の余白、背景の処理を確認

## 実装完了セクションの詳細

### ✅ 完了済み（10セクション）

#### 00_FV.zip - ファーストビュー
- 実装ファイル: `s-owned-media.php` (1-140行目)
- スタイル: `owned-media.scss` (52-374行目)
- 特徴: グリッドレイアウト、装飾要素、レスポンシブ対応

#### 01_ファースト訴求.zip - ファースト訴求セクション
- 実装ファイル: `s-owned-media.php` (142-181行目)
- スタイル: `owned-media.scss` (376-652行目)
- 特徴: 人物イラスト、グラフィティ要素、回転要素

#### 02_提携企業ロゴ.zip - 提携企業ロゴセクション
- 実装ファイル: `s-owned-media.php` (183-205行目)
- スタイル: `owned-media.scss` (654-766行目)
- 特徴: スクロールアニメーション（SP版）、ロゴ配置

#### 03_お申込みボタン.zip - CTAボタンセクション
- 実装ファイル: `s-owned-media.php` (207-234行目)
- スタイル: `owned-media.scss` (768-936行目)
- 特徴: アウトライン・塗りつぶしボタン、SVG装飾線

#### 04_共感パート.zip - 共感パートセクション
- 実装ファイル: `s-owned-media.php` (236-264行目)
- スタイル: `owned-media.scss` (938-1170行目)
- 特徴: チェックリスト、装飾背景、カード型レイアウト

#### 05_発見パート.zip - 発見パートセクション
- 実装ファイル: `s-owned-media.php` (266-305行目)
- スタイル: `owned-media.scss` (1172-1496行目)
- 特徴: 中央人物、吹き出し配置、複雑なレイアウト

#### 06_成果の出るオウンドメディア運用とは？.zip - 成果セクション
- 実装ファイル: `s-owned-media.php` (307-346行目)
- スタイル: `owned-media.scss` (1498-1792行目)
- 特徴: 螺旋図形、重層レイアウト、装飾タイトル

#### 07_サービス内容.zip - サービス内容セクション
- 実装ファイル: `s-owned-media.php` (463-676行目)
- スタイル: `owned-media.scss` (1794-2343行目)
- 特徴: 7段階フロー、プログレスドット、アイコン表示

#### 08_ご支援イメージ.zip - ご支援イメージセクション
- 実装ファイル: `s-owned-media.php` (679-787行目)
- スタイル: `owned-media.scss` (2708-2930行目)
- 特徴: 6項目、画像表示、左右交互レイアウト

#### 09_5つのメリット.zip - 5つのメリットセクション
- 実装ファイル: `s-owned-media.php` (790-995行目)
- スタイル: `owned-media.scss` (2932-3449行目)
- 特徴: 複雑なヘッダー、装飾背景、Merit画像、左右交互

### 🔄 実装中

#### 10_ご支援体制.zip - ご支援体制セクション
- 進捗: zipファイル解凍済み、HTMLファイル確認済み
- 次の作業: 画像準備、PHPテンプレート実装、SCSS実装

### 📋 未実装（3セクション）
- 11_ご支援事例.zip - ご支援事例セクション
- 12_契約までの流れ.zip - 契約フローセクション  
- 13_よくある質問.zip - FAQセクション

## 技術的実装詳細

### ファイル構成
- メインPHPテンプレート: `mount_wordpress/wp-content/themes/appmart/s-owned-media.php`
- メインSCSSファイル: `mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss`
- 画像アセット: `mount_wordpress/wp-content/themes/appmart/assets/images/s-owned/`

### 命名規則
- BEM記法: `owned-media-[section]__[element]--[modifier]`
- 画像: `[セクション名]-[用途]-[番号].拡張子`

### コンパイル方法
```bash
cd /home/<USER>/projects/wordpress-appmart
npm run sass:compile
```

### 確認URL
http://localhost:20085/new-owned-media/
