# よくある実装ミスと対策

## 1. 要素のはみ出し問題
**問題**: リスト要素などがコンテナからはみ出す

**原因**: 
- 親要素に`overflow: hidden`が設定されていると、はみ出しが視覚的に隠れる
- max-widthとpaddingの組み合わせでコンテンツ幅が想定を超える

**検出方法**:
1. ブラウザの開発者ツールで要素の実際のサイズを確認
2. 親要素と子要素の幅を比較
3. セクション全体が見えるスクリーンショットで確認

**対策**:
- コンテナ幅とコンテンツ幅の関係を事前計算
- paddingは左右均等に設定（例: `padding: 124px 118px;`）

## 2. フォントサイズの不一致
**問題**: デザインとフォントサイズが異なる

**原因**:
- clamp()の使用で実際の値が変動
- デザインファイルの数値を見落とし

**対策**:
- PC版は固定値を使用
- レスポンシブ時のみclamp()を検討
- Figmaから正確な値を取得

## 3. 高さの不一致
**問題**: セクションの高さがデザインと異なる

**原因**:
- min-heightとheightの混同
- 余白の計算ミス

**対策**:
- デザインの高さを正確に設定
- padding/marginを含めた総高さを確認

## 4. 色の不一致
**問題**: 色がデザインと微妙に異なる

**原因**:
- カラーコードの転記ミス
- 透明度の見落とし

**対策**:
- Figmaから正確なカラーコードを取得
- rgba値の場合は透明度も確認

## 確認チェックリスト
- [ ] 要素がコンテナ内に収まっているか
- [ ] フォントサイズがデザインと一致しているか
- [ ] セクションの高さが正確か
- [ ] 色がデザインと完全に一致しているか
- [ ] レスポンシブ時の表示が崩れていないか