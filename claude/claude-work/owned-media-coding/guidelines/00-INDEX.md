# LPガイドライン INDEX

このディレクトリには、オウンドメディアLP実装のためのガイドラインが分割して格納されています。

## ガイドライン構成

### 1. [01-実装手順.md](./01-実装手順.md)
- タスクリスト（進捗管理）
- 各セクション実装時の必須作業フロー
- 実装の具体的な手順

### 2. [02-技術仕様.md](./02-技術仕様.md)
- 作業環境とファイルパス
- 命名規則（BEM記法）
- HTML/PHP/SCSS構造
- 実装の基本方針

### 3. [03-Figma参照.md](./03-Figma参照.md)
- FigmaデザインファイルのURL
- 各セクションのNode ID一覧
- MCPツールでのアクセス方法
- 確認用画像との対応表

### 4. [04-よくあるミスと対策.md](./04-よくあるミスと対策.md)
- 要素のはみ出し問題
- フォントサイズの不一致
- その他の実装ミスと対策
- 確認チェックリスト

## 作業時の参照順序

1. **新しいセクションを始める時**
   - 01-実装手順.md → タスクリストと作業フロー確認
   - 03-Figma参照.md → 該当セクションのNode ID確認

2. **実装中**
   - 02-技術仕様.md → 命名規則やSCSS構造の確認
   - 04-よくあるミスと対策.md → 問題が発生した時の対策確認

3. **実装後の確認**
   - 01-実装手順.md → 検証手順の確認
   - 04-よくあるミスと対策.md → チェックリストで最終確認