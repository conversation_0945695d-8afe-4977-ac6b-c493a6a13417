# LP作成ガイドライン

## Figmaデザインファイル
- **PC版デザイン**: https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=0-1&p=f&m=dev
- **スマホ版デザイン**: https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=12-5529&m=dev

### 各セクションのFigma Node ID（PC版）
- FV（ファーストビュー）: `12-26`
- ファースト訴求: `12-263`
- 提携企業ロゴ: `12-281`
- CTAボタン: `12-288`
- 共感パート: `12-307`
- 発見パート: `12-354`
- 成果セクション: `12-1836`
- サービス内容: `12-4803`
- ご支援イメージ: `12-5015`
- 5つのメリット: `12-5095`
- ご支援体制: `12-5228`
- ご支援事例: `12-5308`
- 契約までの流れ: `12-5384`
- よくある質問: `12-5448`

### 各セクションのFigma Node ID（SP版）
- FV（ファーストビュー）: `12-5537`
- ファースト訴求: `12-5714`
- 提携企業ロゴ: `12-5733`
- CTAボタン: `12-5740`
- 共感パート: `12-5759`
- 発見パート: `12-5806`
- 成果セクション: `12-7281`
- サービス内容: `12-8801`
- ご支援イメージ: `12-8868`
- 5つのメリット: `12-8897`
- ご支援体制: `12-8966`
- ご支援事例: `12-9049`
- 契約までの流れ: `12-9128`
- よくある質問: `12-9196`

### Figmaデザインの正しい参照方法

#### 1. MCPツールを使用したアクセス方法
```
mcp__figma__get_figma_data(
  fileKey: "wTDUbl55LjN8R9NzeiZppa",  // URLから抽出
  nodeId: "12-288",                   // 各セクションのNode ID
  depth: 1                            // 階層の深さ（大きなファイルは浅めに）
)
```

#### 2. URLからの情報抽出
- fileKey: URL内の `/design/` の後の部分（`wTDUbl55LjN8R9NzeiZppa`）
- nodeId: URL内の `node-id=` パラメータ、または上記リストから選択

#### 3. 効率的な参照のためのベストプラクティス
- **depthパラメータの活用**: ファイルが大きい場合は`depth: 1`または`2`を使用
- **特定セクションの指定**: 実装中のセクションのnodeIdを直接指定
- **段階的な確認**: まず浅い階層で全体構造を確認、その後詳細を取得

#### 4. 取得できる主要な情報
- **レイアウト情報**: width, height, x, y座標
- **スタイル情報**: 
  - 色（fills, strokes）
  - タイポグラフィ（fontSize, fontWeight, lineHeight, letterSpacing）
  - エフェクト（boxShadow）
  - border-radius
- **構造情報**: 子要素の配置、グループ構成

#### 5. 実装確認フロー
1. 実装するセクションのnodeIdを確認
2. MCPツールでデザインデータを取得
3. 実装したSCSS/CSSの値と照合
4. 差異があれば修正
5. PC版・SP版の両方を確認

## 実装タスクリスト
### 必須実装セクション（優先順に記載）
- [x] 00_FV.zip - ファーストビュー
- [x] 01_ファースト訴求.zip - ファースト訴求セクション
- [x] 02_提携企業ロゴ.zip - 提携企業ロゴセクション
- [x] 03_お申込みボタン.zip - CTAボタンセクション
- [x] 04_共感パート.zip - 共感パートセクション
- [x] 05_発見パート.zip - 発見パートセクション
- [x] 06_成果の出るオウンドメディア運用とは？.zip - 成果セクション
- [x] 07_サービス内容.zip - サービス内容セクション
- [x] 08_ご支援イメージ.zip - ご支援イメージセクション
- [x] 09_5つのメリット.zip - 5つのメリットセクション
- [ ] 10_ご支援体制.zip - ご支援体制セクション（実装中）
- [ ] 11_ご支援事例.zip - ご支援事例セクション
- [ ] 12_契約までの流れ.zip - 契約フローセクション
- [ ] 13_よくある質問.zip - FAQセクション

### 各セクション実装時の必須作業
1. **デザインファイル詳細分析**
   - PCデザイン: `figma/pc/[番号_セクション名].zip`を解凍
   - スマホデザイン: `figma/sp/sp_[番号_セクション名].zip`を解凍
   - 確認用画像: `for_check_img/pc/[セクション名].png`と照合
   - **重要数値の抽出**: フォントサイズ、行間、余白、色コードをメモ

2. **Figmaデザインとの照合（重要）**
   - **MCPツールでデザイン値を取得**:
     ```
     mcp__figma__get_figma_data(
       fileKey: "wTDUbl55LjN8R9NzeiZppa",
       nodeId: "[該当セクションのNode ID]",
       depth: 1
     )
     ```
   - **取得すべき主要データ**:
     - レイアウト: width, height, padding相当の位置情報
     - タイポグラフィ: fontSize, fontWeight, lineHeight, letterSpacing
     - カラー: fills（背景色）, strokes（枠線色）
     - エフェクト: boxShadow, borderRadius
   - **PC版・SP版の両方を確認**

3. **実装前の準備**
   - 提供されたCSSから具体的な数値を抽出（px値、色、font-weight等）
   - 画像アセットの確認と意味のある名前への変更
   - セクション間の余白を確認（前後のセクションとの関係）

4. **精密な実装作業**
   - PHPテンプレート: セマンティックなHTML構造で実装
   - SCSS実装時の注意点:
     - フォントサイズ: デザインの具体的なpx値を基準にclamp()を設定
     - 余白: デザインの正確な値を使用（概算値は避ける）
     - 色: 提供されたカラーコードを正確に使用
     - 行間: line-heightは提供された値を厳守
     - レイアウト: 要素間の正確な配置関係を再現
   - レスポンシブ対応: PC/SPそれぞれのデザインファイルの数値を正確に反映

5. **実装後のFigmaデザイン検証**
   - **数値の最終確認**:
     - 実装したCSS値とFigmaから取得した値を照合
     - 特に重要な値（高さ、フォントサイズ、余白）の一致を確認
   - **視覚的な確認**:
     - Figmaのスクリーンショットと実装結果を比較
     - レイアウトの崩れ、要素の配置ずれがないか確認

6. **視覚的検証**
   - SCSSコンパイル: `npm run sass:compile`
   - ブラウザで確認用画像と並べて比較
   - **Playwrightでの自動検証**:
     ```
     1. スクリーンショット取得:
        mcp__playwright__browser_navigate → http://localhost:20085/new-owned-media/
        mcp__playwright__browser_take_screenshot → 実装結果を撮影
     
     2. 確認用画像との比較:
        両画像を並べて表示し、差異を視覚的に確認
        特にレイアウト、フォントサイズ、余白の違いをチェック
     ```
   - 特に確認すべき点:
     - テキストの改行位置が一致しているか
     - 要素間の余白が正確か
     - ホバー効果やアニメーションの有無
     - 背景の処理（グラデーション、画像の配置）

5. **微調整**
   - 確認用画像との差異を特定し、具体的な数値で調整
   - デバイスフォントの差異を考慮した微調整
   - 必要に応じてletter-spacingで文字間を調整

## 概要
このガイドラインは、提供されたHTML/CSSデザインをWordPressテンプレート（PHP）に変換する際の作業手順書です。

## 作業環境
- **テンプレートファイル**: `mount_wordpress/wp-content/themes/appmart/s-owned-media.php`
- **SCSSファイル**: `mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss` (単一ファイルで完結)
- **画像保存場所**: `mount_wordpress/wp-content/themes/appmart/assets/images/s-owned/`
- **SCSSビルドコマンド**: `npm run sass:compile`

## 作業手順

### 1. 提供ファイルの取り扱い
- ZIPファイルは同名ディレクトリに展開（例: `00_FV.zip` → `figma/pc/00_FV/`）
- 意味のないクラス名や画像名は適切な名前に変換

### 2. レスポンシブ対応のルール
- PC版: `figma/pc/[番号_セクション名].zip`
- スマホ版: `figma/sp/sp_[番号_セクション名].zip`
- 基本的に画像は縮小対応、異なるレイアウトの場合のみ差し替え

### 3. 命名規則
- BEM記法: `owned-media-[section]__[element]--[modifier]`
- 画像: `[セクション名]-[用途]-[番号].拡張子`

### 4. HTML/PHP構造
```php
<section class="owned-media-[section-name]" id="owned-media-[section-name]">
  <div class="owned-media-[section-name]__container">
    <!-- コンテンツ -->
  </div>
</section>
```
- 画像パス: `<?php echo get_template_directory_uri(); ?>/assets/images/s-owned/`

### 5. SCSS構造（単一ファイル）
```scss
// 変数定義
$owned-media-primary-color: #fa6b58;

// ミキシン
@mixin owned-media-container {
  max-width: 1300px; // コンテナ幅固定
  margin: 0 auto;
  padding: 0 20px;
}

// セクションスタイル
.owned-media-[section] {
  &__container {
    @include owned-media-container;
  }
}
```



### 6. 実装の基本方針
- **CSS優先**: 画像よりテキストやCSSでデザインを実現
- **レイアウト**: Flexbox/Gridを使用、position:absoluteは最小限
- **コンテナ幅**: 1300px固定（デザインは1920pxでも実装は1300px）
- **数値の正確性**: 
  - フォントサイズ: 提供されたpx値を正確に使用してclamp()を設定
  - 余白: padding/marginは提供された具体的な値を使用
  - 色: #fa6b58（プライマリ）、#333（テキスト）等、正確なカラーコードを使用
- **フォント設定**: font-family: 'Noto Sans JP', helvetica, sans-serif;
- **文字詰め**: 必要に応じてletter-spacingで調整（日本語は-0.03em程度が基準）




## 確認方法
- **URL**: http://localhost:20085/new-owned-media/
- **確認ツール**: MCPのPlaywrightを使用した自動検証
- **確認手順**:
  1. Playwrightでページを開く
  2. 各セクションのスクリーンショットを取得
  3. 確認用画像と並べて視覚的に比較
  4. 差異がある場合は具体的な数値で調整
- **確認基準**: 確認用画像との一致（フォント、余白、色、配置）

### デザイン確認用画像対応表
```
for_check_img/
├─ pc/
│  ├─ FV.png                → 00_FV.zip
│  ├─ S_ファースト訴求.png      → 01_ファースト訴求.zip
│  ├─ S_提携企業ロゴ.png     → 02_提携企業ロゴ.zip
│  ├─ お申込みボタン.png       → 03_お申込みボタン.zip
│  ├─ S_共感パート.png        → 04_共感パート.zip
│  └─ ...(以下同様の対応)
└─ sp/
   └─ (スマホ版確認用画像)
```

## よくある実装ミスと対策

### 1. 要素のはみ出し問題
**問題**: リスト要素などがコンテナからはみ出す
**原因**: 
- 親要素に`overflow: hidden`が設定されていると、はみ出しが視覚的に隠れる
- max-widthとpaddingの組み合わせでコンテンツ幅が想定を超える

**検出方法**:
1. ブラウザの開発者ツールで要素の実際のサイズを確認
2. 親要素と子要素の幅を比較
3. セクション全体が見えるスクリーンショットで確認

**対策**:
- コンテナ幅とコンテンツ幅の関係を事前計算
- paddingは左右均等に設定（例: `padding: 124px 118px;`）