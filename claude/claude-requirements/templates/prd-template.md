# [機能名] 実装仕様書

## 1. 概要

### 目的
[この機能を実装する理由と解決する課題を記述]

### ゴール
- [ ] [達成すべき具体的な目標1]
- [ ] [達成すべき具体的な目標2]
- [ ] [達成すべき具体的な目標3]

## 2. 機能要件

### 入力
- [受け取るデータ/パラメータ]
- [ユーザーからの入力内容]

### 処理
1. [処理ステップ1]
2. [処理ステップ2]
3. [処理ステップ3]

### 出力
- [返すデータ/結果]
- [表示内容]

## 3. 技術仕様

### 対象ファイル
- 新規作成: 
  - `path/to/new/file.ext`
- 修正: 
  - `path/to/existing/file.ext`

### 実装詳細
[具体的な実装方法、使用する技術、アーキテクチャなど]

## 4. 制約事項
- [技術的制約]
- [ビジネス上の制約]
- [パフォーマンス要件]

## 5. 参考情報
- [参考にすべきファイルやドキュメント]
- [関連するIssueやPR]
- [外部リソースへのリンク]

## 6. 完了条件
- [ ] 機能が正常に動作する
- [ ] テストが通る
- [ ] ドキュメントが更新されている
- [ ] コードレビューが完了している