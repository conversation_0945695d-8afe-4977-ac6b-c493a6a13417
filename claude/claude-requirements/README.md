# Claude Requirements (PRD)

このディレクトリは、Claude Orchestrator Botに実装を依頼する際の要件定義書（PRD: Product Requirements Document）を管理します。

## ディレクトリ構造

```
claude-requirements/
├── templates/          # PRDテンプレート
│   └── prd-template.md # 標準テンプレート
└── active/            # アクティブなPRD
    └── issue-XX-prd.md # 各Issue用のPRD
```

## 使用方法

### 1. PRDの作成

1. `templates/prd-template.md`をコピーして`active/issue-XX-prd.md`を作成
2. Issue番号に合わせてファイル名を設定（例: `issue-43-prd.md`）
3. テンプレートに従って要件を記入

### 2. Issueでの参照

Issueのコメントに以下のように記載：

```markdown
@claude-task

要件定義書: `claude/claude-requirements/active/issue-43-prd.md`を参照してください。
```

### 3. Botの動作

Claude Orchestrator Botは：
1. Issueを分析する際に、指定されたPRDファイルを読み込みます
2. PRDの内容に基づいてタスクリストを作成します
3. 実装時にPRDの技術仕様を参照します

## PRD作成のベストプラクティス

1. **具体的に記述**: 曖昧な表現を避け、具体的な要件を記載
2. **技術仕様を明確に**: 対象ファイルや実装方法を明確に指定
3. **完了条件を定義**: 何をもって完了とするかを明確に定義
4. **参考情報を提供**: 関連ファイルや既存実装の参考箇所を記載

## 注意事項

- PRDファイルはgitで管理されるため、機密情報は含めないでください
- 実装完了後もPRDは保持し、将来の参考資料として活用します
- 大規模な機能の場合は、複数の小さなPRDに分割することを推奨します