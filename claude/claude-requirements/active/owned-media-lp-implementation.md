# オウンドメディアLP実装仕様書

## 1. 概要

### 目的
Figmaデザインを完全に再現したオウンドメディアサービスのランディングページ（LP）を作成する。PC版とスマホ版のレスポンシブ対応を行い、既存のWordPressテーマに統合する。

### ゴール
- [ ] Figmaデザイン（PC版・スマホ版）の完全再現
- [ ] 768px以下でのレスポンシブ切り替え実装
- [ ] 既存テーマとの整合性を保ちつつ独立したスタイル管理
- [ ] テキストベースの実装によるSEO最適化
- [ ] 外部フォームのiframe埋め込み対応

## 2. 機能要件

### 入力
- Figmaデザインファイル（PC版：node-id=2-2、スマホ版：node-id=2-5509）
- 既存WordPressテーマのヘッダー・フッター
- 外部フォームサービスのiframe埋め込みコード

### 処理
1. HTMLテンプレート（PHPファイル）の作成
2. SCSSファイルによるスタイル実装（BEM記法）
3. レスポンシブ対応の実装
4. 提携企業ロゴの無限スクロール実装（スマホ版のみ）
5. 外部フォームのiframeラッパー実装
6. 可能な限りテキストベースでの実装（画像化は最小限に）

### 出力
- 完成したLPページ（PC/スマホ対応）
- SEO最適化されたテキストベースのコンテンツ
- 適切なalt属性を持つ画像要素

## 3. 技術仕様

### 対象ファイル
- 新規作成: 
  - `mount_wordpress/wp-content/themes/appmart/s-owned-media.php`
  - `mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss`
  - `mount_wordpress/wp-content/themes/appmart/assets/css/_owned-media-variables.scss`
  - `mount_wordpress/wp-content/themes/appmart/assets/js/owned-media.js`
- 画像アセット:
  - `mount_wordpress/wp-content/themes/appmart/assets/images/owned-media/` 配下に配置

### 実装詳細

#### ページ構成
1. **ヘッダー** - 既存の`get_header()`を使用
2. **ファーストビュー（FV）**
   - キャッチコピー（HTMLテキスト）
   - メインイラスト
   - 外部フォームiframeラッパー
3. **ファースト訴求セクション**
   - 見出しテキスト
   - 説明文
   - イラスト画像
4. **提携企業ロゴセクション**
   - PC版：静的表示
   - スマホ版：無限横スクロール（CSS/JSアニメーション）
5. **CTAボタンセクション**
   - 共通CTAコンポーネント
6. **共感パートセクション**
   - チェックリスト形式のコンテンツ
7. **発見パートセクション**
   - 吹き出し形式のコンテンツ
8. **成果の出るオウンドメディア運用とは？セクション**
   - 概念図（画像またはSVG）
9. **サービス内容セクション**
   - 7つのサービス項目（番号付き）
10. **ご支援イメージセクション**
    - 6つのステップ
11. **5つのメリットセクション**
    - メリットリスト
12. **ご支援体制セクション**
    - チーム体制図
13. **ご支援事例セクション**
    - 事例カード
14. **契約までの流れセクション**
    - 5ステップのフロー
15. **よくあるご質問セクション**
    - アコーディオン形式（静的HTML）
16. **フッター** - 既存の`get_footer()`を使用

#### スタイル設計
- BEM記法の採用
- SCSSファイルは単体ファイルとして分離（他のSCSSファイルに依存しない）
- 独立した変数ファイル（`_owned-media-variables.scss`）
- メインスタイルファイル（`owned-media.scss`）は独立してコンパイル可能
- ブレークポイント：768px
- 表示切替クラス：`.owned-media__pc`、`.owned-media__sp`

#### 画像命名規則
- セクション名-要素名-[pc|sp].拡張子
- 例：`fv-main-visual-pc.png`、`fv-main-visual-sp.png`

## 4. 制約事項
- 既存のグローバルスタイルと干渉しないよう、すべてのスタイルは`.owned-media`プレフィックスを使用
- 画像はすべてalt属性必須
- テキストは可能な限りHTMLで実装（画像化は最小限）
- フォームは外部サービスのiframeのみ（内部実装なし）
- アニメーションは提携企業ロゴの無限スクロールのみ

## 5. 参考情報
- 既存サービスページテンプレート：`s-whitepaper.php`、`s-comonseo.php`など
- 既存のサービスページ用SCSS：`_service.scss`、`_service-mixin.scss`
- **PCデザイン**: https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=12-22&t=1uSNhVT1vWCRPKO4-0
- **スマホデザイン**: https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=12-5529&t=1uSNhVT1vWCRPKO4-0

## 6. 完了条件
- [ ] すべてのセクションがFigmaデザイン通りに実装されている
- [ ] PC版（768px以上）とスマホ版（768px以下）の表示が正しく切り替わる
- [ ] 提携企業ロゴの無限スクロールが動作する（スマホ版）
- [ ] すべての画像にalt属性が設定されている
- [ ] 外部フォームのiframeが正しく表示される
- [ ] 既存のヘッダー・フッターと統合されている
- [ ] ページ読み込み速度が適切である

## 7. 画像アセット状況

### 取得済み画像（実施済み）
Figmaデザインからの画像書き出しは完了し、**合計39ファイル**の画像アセットが `mount_wordpress/wp-content/themes/appmart/assets/images/s-owned/` ディレクトリに保存されています。

### 画像分類
- **ヘッダーロゴ**: header-logo-pc.png, header-logo-sp.png
- **FVセクション**: fv-illustration-pc/sp.png, fv-main-illustration-pc.svg, fv-illustration-detail-pc.svg
- **提携企業ロゴ**: partner-logo-1～3.png
- **サービスアイコン**: service-00～06-icon.png（7個）
- **ご支援イメージアイコン**: support-01～09-icon.png（9個）
- **メリットアイコン**: merit-01～05関連アイコン（8個）
- **契約フローアイコン**: flow-01～05-icon.png（5個）
- **ご支援体制図**: team-work-pc.svg
- **ご支援事例グラフ**: case-study-graph-pc.svg

### 実装時の画像使用方法
実装時は `mount_wordpress/wp-content/themes/appmart/assets/images/s-owned/` ディレクトリ内の画像を使用してください。各セクションに必要な画像は上記の分類を参考に適切なファイルを選択して使用します。

### 背景パターンや装飾要素
Figmaで使用されている背景パターンや装飾的要素は画像として取得されていないため、実装時はCSSで類似の視覚効果を再現します。

## 8. 実装時の注意事項

### セクションコメント
各セクションの開始位置にHTMLコメントで日本語のセクション名を記載：
```html
<!-- ファーストビュー -->
<section class="owned-media__fv">
...
</section>
```

### テキスト実装の原則
- 見出し、本文、リスト項目はHTMLテキストで実装
- 特殊なフォントや装飾が必要な場合のみ画像化を検討
- ボタンテキストもHTMLで実装
- 可能な限りテキストベースで実装し、SEO最適化を優先

### 画像の最適化
- 適切なフォーマット（WebP対応も検討）
- 適切なサイズでの書き出し
- 遅延読み込みの実装（loading="lazy"）

### アクセシビリティ
- セマンティックなHTML構造
- 適切な見出しレベル（h1〜h6）
- リンクとボタンの適切な使い分け

### デザイン確認方法
- PCデザイン（node-id=2-2）とスマホデザイン（node-id=2-5509）を完全に再現
- 取得済み画像アセットを使用してFigmaデザインを完全に再現
- PC版とスマホ版の表示切り替えを768pxブレークポイントで実装
- 既存サービスページのレイアウトパターンを参考に統一感を保つ