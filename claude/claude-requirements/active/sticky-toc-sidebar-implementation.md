# スティッキー目次サイドバー機能 実装仕様書

## 1. 概要

### 目的
ブログ（コラム）詳細ページにおいて、長い記事を読む際のナビゲーション性を向上させるため、スクロールに追従する目次をサイドバーに表示する機能を実装する。

### ゴール
- [ ] 記事本文の目次と既存サイドバーコンテンツの両方が画面上部に見切れたときに、サイドバーに追従型の目次を表示
- [ ] 現在表示中のセクションをハイライト表示
- [ ] クリックによるスムーススクロール実装（URLハッシュは変更しない）
- [ ] レスポンシブ対応（768px以上のみで動作）

## 2. 機能要件

### 入力
- TOCプラグインが生成する目次DOM（`#toc_container`）
- 記事本文内のH2見出し要素
- ユーザーのスクロール位置
- ユーザーの目次項目クリック

### 処理
1. **初期化処理**
   - TOCプラグインが生成する目次要素をラッパーで包む
   - H2見出しのみを抽出してサイドバー用目次を生成
   - サイドバーの既存コンテンツの下に配置

2. **スクロール監視処理**
   - 本文内目次の表示位置を監視
   - 既存サイドバーコンテンツの表示位置を監視
   - 両方が画面上部に見切れたらサイドバー目次を0.3秒のフェードインで表示
   - 現在のビューポートに表示されているH2セクションを判定
   - 該当セクションをハイライト表示

3. **クリック処理**
   - 目次項目クリック時に該当セクションへスムーススクロール
   - URLハッシュは変更しない（e.preventDefault()使用）

### 出力
- サイドバーに固定表示される目次
- 現在位置のハイライト表示
- スムーススクロール動作

## 3. 技術仕様

### 対象ファイル
- 新規作成: 
  - `mount_wordpress/wp-content/themes/appmart/assets/scss/components/_toc.scss`
  - `mount_wordpress/wp-content/themes/appmart/assets/js/sticky-toc.js`
- 修正: 
  - `mount_wordpress/wp-content/themes/appmart/single-blog.php`
  - `mount_wordpress/wp-content/themes/appmart/assets/scss/app.scss`（インポート追加）
  - `mount_wordpress/wp-content/themes/appmart/functions.php`（スクリプトエンキュー）

### 実装詳細

#### HTML構造（single-blog.php）
```html
<!-- 本文内目次のラッパー追加 -->
<div class="toc-wrapper">
  <!-- TOCプラグインが生成する目次 -->
  <div id="toc_container">...</div>
  <!-- 追加CTA要素用のプレースホルダー -->
  <div class="toc-cta"></div>
</div>
```

#### JavaScript実装（sticky-toc.js）
```javascript
// 主要な実装ポイント
1. DOMContentLoaded時に初期化
2. TOCプラグインの目次からH2のみを抽出
3. Intersection Observer APIを使用してセクション監視
4. スクロールイベントで本文目次と既存サイドバーの表示状態を監視
5. 両方が見切れた場合のみ表示（AND条件）
6. e.preventDefault()でURLハッシュ変更を防止
```

#### SCSS実装（_toc.scss）
```scss
// 主要なスタイリング
1. .sticky-toc（固定配置コンテナ）
2. .sticky-toc__list（目次本体）
3. .sticky-toc--visible（表示時、opacity: 0 → 1のフェードイン0.3秒）
4. .sticky-toc__item（目次項目）
5. .sticky-toc__item--active（アクティブ状態）
6. @media (min-width: 768px) でのレスポンシブ対応
```

### データ構造
```javascript
// サイドバー目次の構造
{
  id: "heading-id",
  text: "見出しテキスト",
  element: HTMLElement,
  offset: number
}
```

## 4. 制約事項
- TOCプラグインに依存（プラグインが無効の場合は動作しない）
- H2見出しのみ対象（H3以下は表示しない）
- 768px未満のデバイスでは無効
- JavaScriptが無効の環境では動作しない
- サイドバーの既存コンテンツのレイアウトを崩さない

## 5. 参考情報
- 現在のTOCプラグイン実装: `#toc_container`
- 既存のスムーススクロール実装: `main.js`（67-79行目）
- サイドバーテンプレート: `template-parts/sidebar.php`
- 既存の固定表示実装: `main.js`（209-231行目のバナー固定）

## 6. 完了条件
- [ ] 本文目次と既存サイドバーの両方が見切れたときにサイドバー目次が表示される
- [ ] 現在のセクションがハイライト表示される
- [ ] 目次クリックでスムーススクロールが動作する（URLハッシュは変更されない）
- [ ] 768px以上の画面幅でのみ動作する
- [ ] 既存の目次・サイドバーの表示を崩さない
- [ ] 0.3秒のフェードインアニメーションが動作する