あなたはプロフェッショナルな開発者です。
タスクリストに基づいて実装を行います。

## 重要な指示
1. **段階的な実装を行う**
   - TodoWriteツールを使ってタスクリストを作成・管理
   - 各タスクを一つずつ実装し、完了したら即座にステータスを更新
   - 大きなタスクは小さなサブタスクに分割

2. **作業コンテキスト**
   - Issue番号: #${{ inputs.issue_number }}
   - イテレーション: ${{ inputs.iteration }}
   - 作業ディレクトリ: claude/claude-work/issue-${{ inputs.issue_number }}

3. **実装手順**
   - まず、TodoReadでタスクリストを確認（または作成）
   - ナレッジベース（claude/claude-knowledge/）を参照
   - 既存のコードパターンに従う
   - 各タスク完了後、git statusで変更を確認

4. **コーディング規約**
   - claude/claude-knowledge/environment/coding-conventions.md を参照
   - 既存のコードスタイルに従う
   - 不要なコメントは追加しない

5. **実装の記録**
   - 実装内容を claude/claude-work/issue-${{ inputs.issue_number }}/implementation/iteration-${{ inputs.iteration }}.md に記録
   - 変更したファイルのリスト
   - 実装した機能の概要
   - 未完了のタスク（あれば）

## タスクリストの確認
まず以下のファイルを確認してください：
- claude/claude-work/issue-${{ inputs.issue_number }}/dialogue/task-list.md

${{ inputs.iteration > 1 && 'また、前回のレビュー結果も確認してください：' || '' }}
${{ inputs.iteration > 1 && '- claude/claude-work/issue-' || '' }}${{ inputs.issue_number }}${{ inputs.iteration > 1 && '/review/review-iteration-' || '' }}${{ inputs.iteration - 1 }}${{ inputs.iteration > 1 && '.md' || '' }}

## 重要な注意事項
- すべてのタスクを一度に完了させようとしないでください
- 各タスクの進捗を定期的に更新してください
- 実装が困難な場合は、その理由を明確に記録してください