# Claude Prompts

このディレクトリには、GitHub ActionsのワークフローでClaudeに渡すプロンプトを外部ファイルとして管理しています。

## 使用方法

### 1. プロンプトファイルの読み込み（推奨方法）

```yaml
- name: Load prompt
  id: prompt
  run: |
    PROMPT=$(cat .github/claude-prompts/analyze-env.md)
    echo "prompt<<EOF" >> $GITHUB_OUTPUT
    echo "$PROMPT" >> $GITHUB_OUTPUT
    echo "EOF" >> $GITHUB_OUTPUT

- name: <PERSON> <PERSON>
  uses: grll/claude-code-base-action@beta
  with:
    prompt: ${{ steps.prompt.outputs.prompt }}
```

### 2. 変数を含むプロンプトの場合

```yaml
- name: Load and process prompt
  id: prompt
  run: |
    # テンプレート変数を置換
    PROMPT=$(cat .github/claude-prompts/implement.md | \
      sed "s/{{ISSUE_NUMBER}}/${{ inputs.issue_number }}/g" | \
      sed "s/{{ITERATION}}/${{ inputs.iteration }}/g")
    echo "prompt<<EOF" >> $GITHUB_OUTPUT
    echo "$PROMPT" >> $GITHUB_OUTPUT
    echo "EOF" >> $GITHUB_OUTPUT
```

## ファイル一覧

- `analyze-env.md` - 環境分析フェーズのプロンプト
- `analyze-issue.md` - Issue分析フェーズのプロンプト
- `implement.md` - 実装フェーズのプロンプト
- `review.md` - レビューフェーズのプロンプト
- `knowledge-update.md` - ナレッジ更新フェーズのプロンプト

## メリット

1. **YAMLの構文エラー回避**: 特殊文字やインデントの問題を避けられる
2. **再利用性**: 同じプロンプトを複数のワークフローで使える
3. **保守性**: プロンプトの更新が容易
4. **バージョン管理**: プロンプトの変更履歴を追跡しやすい
5. **テスト可能性**: プロンプトを独立してテストできる