name: Claude Orchestrator タスク
description: Claude Orchestrator Botに実装を依頼するタスク
title: "[<PERSON>] "
labels: ["claude-task"]
body:
  - type: markdown
    attributes:
      value: |
        ## Claude Orchestrator Botによる自動実装

        このテンプレートは、Claude Orchestrator Botに実装を依頼する際に使用します。
        Issue作成後、本文に `@claude-task` とコメントすることでBotが起動します。

  - type: dropdown
    id: complexity
    attributes:
      label: タスクの複雑度
      description: 適切なAIモデルが自動選択されます
      options:
        - "標準的なタスク（デフォルト）"
        - "複雑なタスク（complex-task ラベル追加）"
        - "簡単なタスク（simple-task ラベル追加）"
    validations:
      required: true

  - type: dropdown
    id: model
    attributes:
      label: Claude モデル選択
      description: |
        使用するClaude モデルを選択してください
      options:
        - "claude-sonnet-4-20250514（推奨・デフォルト）"
        - "claude-opus-4-20250514（最高品質）"
        - "claude-3-5-haiku-20241022（高速・低コスト）"
      default: 0

  - type: input
    id: max_turns_env
    attributes:
      label: 環境分析フェーズ 最大ターン数
      description: "環境分析に使用する最大ターン数（デフォルト: 100）"
      placeholder: "100"

  - type: input
    id: max_turns_analysis
    attributes:
      label: Issue分析フェーズ 最大ターン数
      description: "Issue分析に使用する最大ターン数（デフォルト: 100）"
      placeholder: "100"

  - type: input
    id: max_turns_implement
    attributes:
      label: 実装フェーズ 最大ターン数
      description: "実装に使用する最大ターン数（デフォルト: 100）"
      placeholder: "100"

  - type: input
    id: max_turns_review
    attributes:
      label: レビューフェーズ 最大ターン数
      description: "レビューに使用する最大ターン数（デフォルト: 100）"
      placeholder: "100"

  - type: input
    id: iterations_limit
    attributes:
      label: コーダー・レビュアー対話回数制限
      description: "実装とレビューのイテレーション回数制限（デフォルト: 3）"
      placeholder: "3"

  - type: input
    id: related-issues
    attributes:
      label: 関連Issue番号
      description: |
        継続作業の場合、関連するIssue番号を入力してください（例: #53, #50）
        複数ある場合はカンマ区切りで入力
      placeholder: "#53, #50"
    validations:
      required: false

  - type: input
    id: prd-filename
    attributes:
      label: PRDファイル名
      description: |
        PRDファイル名を入力してください（拡張子は不要）
        デフォルトパス: `claude/claude-requirements/active/` に保存されます
      placeholder: issue-XX-feature-name
    validations:
      required: true

  - type: textarea
    id: summary
    attributes:
      label: 概要
      description: 実装したい機能の概要を簡潔に記載してください
    validations:
      required: true

  - type: textarea
    id: additional-info
    attributes:
      label: 補足情報
      description: |
        PRDに記載していない追加情報があれば記載してください
        例: 特定の技術的制約、参考リンク、関連Issue番号など
      value: |
        ## ⚠️ WordPress プロジェクトの制約事項

        - **データベースアクセス不可**: GitHub Actions環境ではWordPressデータベースにアクセスできません
        - **検証範囲**: コード品質、構文チェック、ファイル構造の確認のみ実行可能

        ## 追加の技術的制約・参考情報
        無し
