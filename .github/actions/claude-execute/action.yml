name: 'Execute <PERSON> with Auth Abstraction'
description: 'Execute <PERSON> Code with automatic auth selection'
inputs:
  auth_type:
    description: 'Authentication type (OAUTH or API_KEY)'
    required: true
  prompt:
    description: 'Prompt for <PERSON>'
    required: true
  model:
    description: 'Model to use'
    required: true
    default: 'claude-3-5-sonnet-20241022'
  allowed_tools:
    description: 'Allowed tools for <PERSON>'
    required: false
    default: 'Bash(git:*),View,GlobTool,GrepTool,BatchTool'
  max_turns:
    description: 'Maximum conversation turns'
    required: false
    default: ''
  timeout_minutes:
    description: 'Timeout in minutes'
    required: false
    default: '10'
  claude_access_token:
    description: '<PERSON> access token for <PERSON>A<PERSON>'
    required: false
  claude_expires_at:
    description: 'Claude token expiration timestamp'
    required: false
  anthropic_api_key:
    description: 'Anthropic API key'
    required: false

outputs:
  conclusion:
    description: 'Execution status'
    value: ${{ steps.oauth.outputs.conclusion || steps.apikey.outputs.conclusion }}
  execution_file:
    description: 'Execution log file path'
    value: ${{ steps.oauth.outputs.execution_file || steps.apikey.outputs.execution_file }}

runs:
  using: 'composite'
  steps:
    # OAuth認証での実行
    - name: Execute with <PERSON><PERSON><PERSON> (<PERSON>)
      id: oauth
      if: ${{ inputs.auth_type == 'OAUTH' }}
      uses: grll/claude-code-base-action@beta
      with:
        use_oauth: 'true'
        claude_access_token: ${{ inputs.claude_access_token }}
        claude_expires_at: ${{ inputs.claude_expires_at }}
        model: ${{ inputs.model }}
        prompt: ${{ inputs.prompt }}
        allowed_tools: ${{ inputs.allowed_tools }}
        max_turns: ${{ inputs.max_turns }}
        timeout_minutes: ${{ inputs.timeout_minutes }}
    
    # APIキー認証での実行
    - name: Execute with API Key (Anthropic)
      id: apikey
      if: ${{ inputs.auth_type == 'API_KEY' }}
      uses: anthropics/claude-code-base-action@beta
      with:
        anthropic_api_key: ${{ inputs.anthropic_api_key }}
        anthropic_model: ${{ inputs.model }}
        prompt: ${{ inputs.prompt }}
        allowed_tools: ${{ inputs.allowed_tools }}
        max_turns: ${{ inputs.max_turns }}
        timeout_minutes: ${{ inputs.timeout_minutes }}
