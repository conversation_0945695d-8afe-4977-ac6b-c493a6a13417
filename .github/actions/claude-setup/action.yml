name: 'Claude Setup Action'
description: '<PERSON>実行のための共通セットアップ'

inputs:
  issue_number:
    description: 'GitHub Issue番号'
    required: true
  phase:
    description: '実行フェーズ'
    required: true
  branch_name:
    description: '作業ブランチ名'
    required: false

outputs:
  issue_title:
    description: 'Issue タイトル'
    value: ${{ steps.issue-info.outputs.title }}
  issue_body:
    description: 'Issue 本文'
    value: ${{ steps.issue-info.outputs.body }}
  selected_model:
    description: '選択されたClaudeモデル'
    value: ${{ steps.model-select.outputs.model }}
  max_turns:
    description: '最大ターン数'
    value: ${{ steps.turns-config.outputs.max_turns }}

runs:
  using: "composite"
  steps:
    - name: Get Issue Information
      id: issue-info
      shell: bash
      run: |
        ISSUE_INFO=$(gh api repos/${{ github.repository }}/issues/${{ inputs.issue_number }})
        ISSUE_TITLE=$(echo "$ISSUE_INFO" | jq -r '.title')
        ISSUE_BODY=$(echo "$ISSUE_INFO" | jq -r '.body')
        
        echo "title=$ISSUE_TITLE" >> $GITHUB_OUTPUT
        echo "body<<EOF" >> $GITHUB_OUTPUT
        echo "$ISSUE_BODY" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
    - name: Select Model
      id: model-select
      shell: bash
      run: |
        ISSUE_BODY="${{ steps.issue-info.outputs.body }}"
        
        # デフォルトモデル
        selectedModel='claude-sonnet-4-20250514'
        
        # Issue本文からモデル設定を抽出
        if echo "$ISSUE_BODY" | grep -q "claude-opus-4-20250514"; then
          selectedModel='claude-opus-4-20250514'
        elif echo "$ISSUE_BODY" | grep -q "claude-3-5-haiku-20241022"; then
          selectedModel='claude-3-5-haiku-20241022'
        fi
        
        echo "model=$selectedModel" >> $GITHUB_OUTPUT
        
    - name: Configure Turns
      id: turns-config
      shell: bash
      run: |
        ISSUE_BODY="${{ steps.issue-info.outputs.body }}"
        PHASE="${{ inputs.phase }}"
        
        # フェーズごとのデフォルト値
        case $PHASE in
          "ANALYZE_ENV")
            DEFAULT_TURNS=100
            PATTERN="環境分析フェーズ 最大ターン数"
            ;;
          "ANALYZE")
            DEFAULT_TURNS=100
            PATTERN="Issue分析フェーズ 最大ターン数"
            ;;
          "IMPLEMENT")
            DEFAULT_TURNS=300
            PATTERN="実装フェーズ 最大ターン数"
            ;;
          "REVIEW")
            DEFAULT_TURNS=100
            PATTERN="レビューフェーズ 最大ターン数"
            ;;
          *)
            DEFAULT_TURNS=100
            ;;
        esac
        
        # Issue本文から値を抽出
        if [ -n "$PATTERN" ]; then
          max_turns=$(echo "$ISSUE_BODY" | grep -o "$PATTERN: [0-9]\+" | grep -o "[0-9]\+" | head -1)
          max_turns=${max_turns:-$DEFAULT_TURNS}
        else
          max_turns=$DEFAULT_TURNS
        fi
        
        echo "max_turns=$max_turns" >> $GITHUB_OUTPUT