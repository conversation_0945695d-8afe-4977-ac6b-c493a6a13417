name: <PERSON> Main

# メインワークフロー - 各フェーズのワークフローを順次呼び出す

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number
        description: 'Issue番号'

# 同一Issueの同時実行を防ぐ
concurrency:
  group: claude-orchestrator-${{ github.event.issue.number || github.event.inputs.issue_number }}
  cancel-in-progress: false

jobs:
  # トリガーチェック（issue_commentの場合のみ）
  trigger-check:
    if: |
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude-task')) ||
      github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
      issue_number: ${{ steps.check.outputs.issue_number }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github/scripts
            
      - name: Check trigger
        id: check
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          ISSUE_NUMBER: ${{ github.event.issue.number || github.event.inputs.issue_number }}
          REPOSITORY: ${{ github.repository }}
          EVENT_NAME: ${{ github.event_name }}
        run: bash .github/scripts/check-trigger.sh

  # 初期化フェーズ
  initialize:
    needs: trigger-check
    if: needs.trigger-check.outputs.should_run == 'true'
    uses: ./.github/workflows/claude-phase-initialize.yml
    with:
      issue_number: ${{ needs.trigger-check.outputs.issue_number }}
      trigger_type: ${{ github.event_name }}
    secrets: inherit

  # 環境分析フェーズ（初回のみ）
  analyze-env:
    needs: initialize
    if: needs.initialize.outputs.env_analysis_needed == 'true'
    uses: ./.github/workflows/claude-phase-analyze-env.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_env }}
    secrets: inherit

  # Issue分析フェーズ
  analyze-issue:
    needs: [initialize, analyze-env]
    if: |
      always() && 
      needs.initialize.result == 'success' &&
      (needs.analyze-env.result == 'success' || needs.analyze-env.result == 'skipped')
    uses: ./.github/workflows/claude-phase-analyze-issue.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_analysis }}
    secrets: inherit

  # 実装・レビューループ制御
  implementation-loop:
    needs: [initialize, analyze-issue]
    if: needs.analyze-issue.result == 'success'
    runs-on: ubuntu-latest
    outputs:
      continue_loop: ${{ steps.loop-control.outputs.continue }}
      current_iteration: ${{ steps.loop-control.outputs.iteration }}
    strategy:
      matrix:
        iteration: [1, 2, 3, 4, 5]  # 最大5回のイテレーション
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github/scripts
            
      - name: Loop Control
        id: loop-control
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          ITERATION: ${{ matrix.iteration }}
          MAX_ITERATIONS: ${{ needs.initialize.outputs.iterations_limit }}
          ISSUE_NUMBER: ${{ needs.initialize.outputs.issue_number }}
          BRANCH_NAME: ${{ needs.initialize.outputs.branch_name }}
        run: bash .github/scripts/implementation-loop-control.sh

  # 実装フェーズ
  implement:
    needs: [initialize, analyze-issue, implementation-loop]
    if: needs.implementation-loop.outputs.continue_loop == 'true'
    uses: ./.github/workflows/claude-phase-implement.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_implement }}
      iteration: ${{ needs.implementation-loop.outputs.current_iteration }}
    secrets: inherit

  # レビューフェーズ
  review:
    needs: [initialize, implement]
    if: needs.implement.result == 'success'
    uses: ./.github/workflows/claude-phase-review.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_review }}
      iteration: ${{ needs.implement.outputs.iteration }}
    secrets: inherit

  # ナレッジ更新フェーズ
  update-knowledge:
    needs: [initialize, review]
    if: |
      always() &&
      needs.initialize.result == 'success' &&
      (needs.review.result == 'success' || needs.review.result == 'skipped')
    uses: ./.github/workflows/claude-phase-knowledge.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_knowledge }}
    secrets: inherit

  # 最終処理
  finalize:
    needs: [initialize, update-knowledge]
    if: always() && needs.initialize.result == 'success'
    uses: ./.github/workflows/claude-phase-finalize.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      total_iterations: ${{ needs.review.outputs.total_iterations || '1' }}
    secrets: inherit