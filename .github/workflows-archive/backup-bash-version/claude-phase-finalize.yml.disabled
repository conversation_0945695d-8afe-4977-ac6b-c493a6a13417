name: <PERSON> Phase - Finalize

# 最終処理フェーズ - プルリクエスト作成と後処理

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: string
      branch_name:
        required: true
        type: string
      total_iterations:
        required: true
        type: string
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false

jobs:
  finalize:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: '最終処理Bot'
          SLACK_COLOR: '#607d8b'
          SLACK_TITLE: '🏁 最終処理Bot: 処理開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **フェーズ**: 最終処理
            **目的**: プルリクエスト作成と完了処理
        continue-on-error: true
        
      - name: Create Pull Request Summary
        id: pr-summary
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          ISSUE_NUMBER: ${{ inputs.issue_number }}
          REPOSITORY: ${{ github.repository }}
          TOTAL_ITERATIONS: ${{ inputs.total_iterations }}
          WORK_DIR: claude/claude-work/issue-${{ inputs.issue_number }}
        run: bash .github/scripts/create-pr-summary.sh
          
      - name: Create Pull Request
        id: create-pr
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          PR_TITLE="${{ steps.pr-summary.outputs.pr_title }}"
          
          # デバッグ情報
          echo "::debug::PR Title: $PR_TITLE"
          echo "::debug::Branch: ${{ inputs.branch_name }}"
          echo "::debug::PR body file exists: $(test -f /tmp/pr-body.md && echo 'yes' || echo 'no')"
          
          # PRを作成
          PR_URL=$(gh pr create \
            --title "$PR_TITLE" \
            --body-file /tmp/pr-body.md \
            --base develop \
            --head "${{ inputs.branch_name }}" \
            --label "claude-generated" \
            2>&1 || echo "")
          
          if [[ "$PR_URL" == *"github.com"* ]]; then
            echo "✅ プルリクエストを作成しました: $PR_URL"
            echo "pr_url=$PR_URL" >> $GITHUB_OUTPUT
            
            # PR番号を抽出
            PR_NUMBER=$(echo "$PR_URL" | grep -o '[0-9]*$')
            echo "pr_number=$PR_NUMBER" >> $GITHUB_OUTPUT
          else
            echo "❌ プルリクエストの作成に失敗しました"
            echo "Error output: $PR_URL"
            # 既存のPRがあるか確認
            EXISTING_PR=$(gh pr list --head "${{ inputs.branch_name }}" --json number,url --jq '.[0]' || echo "{}")
            if [ "$EXISTING_PR" != "{}" ]; then
              echo "::warning::既存のPRが見つかりました"
              PR_NUMBER=$(echo "$EXISTING_PR" | jq -r '.number')
              PR_URL=$(echo "$EXISTING_PR" | jq -r '.url')
              echo "pr_url=$PR_URL" >> $GITHUB_OUTPUT
              echo "pr_number=$PR_NUMBER" >> $GITHUB_OUTPUT
              echo "既存のPR: #$PR_NUMBER ($PR_URL)"
            else
              exit 1
            fi
          fi
          
      - name: Update Issue Status
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM="${{ inputs.issue_number }}"
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          
          # 最終状態を更新
          cat > "$WORK_DIR/state.json" << EOF
          {
            "issue_number": "${ISSUE_NUM}",
            "branch_name": "${{ inputs.branch_name }}",
            "phase": "COMPLETED",
            "iteration": ${{ inputs.total_iterations }},
            "status": "SUCCESS",
            "created_at": "$(jq -r '.created_at' "$WORK_DIR/state.json")",
            "completed_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
            "pr_number": "${{ steps.create-pr.outputs.pr_number }}",
            "pr_url": "${{ steps.create-pr.outputs.pr_url }}"
          }
          EOF
          
          # コミット
          git add .
          git commit -m "Finalize implementation for issue #${ISSUE_NUM}" || echo "No changes"
          git push
          
      - name: Update Issue Labels
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # processing ラベルを削除して done ラベルを追加
          gh issue edit ${{ inputs.issue_number }} \
            --remove-label "claude-processing" \
            --add-label "claude-done"
          
      - name: Post Completion Comment
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          PR_URL="${{ steps.create-pr.outputs.pr_url }}"
          
          COMMENT="## ✅ Claude Orchestrator 完了"$'\n\n'
          COMMENT="${COMMENT}実装が完了し、プルリクエストを作成しました。"$'\n\n'
          COMMENT="${COMMENT}**プルリクエスト**: $PR_URL"$'\n'
          COMMENT="${COMMENT}**総イテレーション数**: ${{ inputs.total_iterations }}"$'\n\n'
          COMMENT="${COMMENT}### 次のステップ"$'\n'
          COMMENT="${COMMENT}1. プルリクエストのレビューをお願いします"$'\n'
          COMMENT="${COMMENT}2. 必要に応じて追加の修正を行ってください"$'\n'
          COMMENT="${COMMENT}3. レビュー承認後、マージしてください"$'\n\n'
          COMMENT="${COMMENT}ご利用ありがとうございました！ 🎉"
          
          gh issue comment ${{ inputs.issue_number }} --body "$COMMENT"
          
      - name: Create Summary Report
        run: |
          ISSUE_NUM="${{ inputs.issue_number }}"
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          
          # サマリーレポートを作成
          cat > "$WORK_DIR/summary.md" << EOF
          # 実装サマリー - Issue #${ISSUE_NUM}
          
          ## 概要
          - **開始時刻**: $(jq -r '.created_at' "$WORK_DIR/state.json")
          - **完了時刻**: $(jq -r '.completed_at' "$WORK_DIR/state.json")
          - **総イテレーション数**: ${{ inputs.total_iterations }}
          - **プルリクエスト**: #${{ steps.create-pr.outputs.pr_number }}
          
          ## 実装プロセス
          1. 環境分析: 完了
          2. Issue分析: 完了
          3. 実装・レビューサイクル: ${{ inputs.total_iterations }}回
          4. ナレッジ更新: 完了
          5. PR作成: 完了
          
          ## 成果物
          - 実装コード（PR参照）
          - ナレッジベース更新
          - 実装記録（このディレクトリ内）
          EOF
          
          git add .
          git commit -m "Add summary report for issue #${ISSUE_NUM}" || echo "No changes"
          git push
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: '最終処理Bot'
          SLACK_COLOR: 'good'
          SLACK_TITLE: '🎉 全プロセス完了！'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **プルリクエスト**: ${{ steps.create-pr.outputs.pr_url || 'N/A' }}
            **総イテレーション数**: ${{ inputs.total_iterations }}
            
            実装が完了しました！レビューをお願いします。
        continue-on-error: true