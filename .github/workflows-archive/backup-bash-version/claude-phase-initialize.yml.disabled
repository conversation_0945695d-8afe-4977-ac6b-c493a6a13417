name: <PERSON> Phase - Initialize

# 初期化フェーズ - プロジェクト設定とブランチ作成

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: string
      trigger_type:
        required: false
        type: string
        default: 'workflow_dispatch'
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false
    outputs:
      issue_number:
        value: ${{ inputs.issue_number }}
      branch_name:
        value: ${{ jobs.initialize.outputs.branch_name }}
      selected_model:
        value: ${{ jobs.initialize.outputs.selected_model }}
      env_analysis_needed:
        value: ${{ jobs.initialize.outputs.env_analysis_needed }}
      max_turns_env:
        value: ${{ jobs.initialize.outputs.max_turns_env }}
      max_turns_analysis:
        value: ${{ jobs.initialize.outputs.max_turns_analysis }}
      max_turns_implement:
        value: ${{ jobs.initialize.outputs.max_turns_implement }}
      max_turns_review:
        value: ${{ jobs.initialize.outputs.max_turns_review }}
      max_turns_knowledge:
        value: ${{ jobs.initialize.outputs.max_turns_knowledge }}
      iterations_limit:
        value: ${{ jobs.initialize.outputs.iterations_limit }}

jobs:
  initialize:
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.setup.outputs.branch_name }}
      selected_model: ${{ steps.setup.outputs.selected_model }}
      env_analysis_needed: ${{ steps.check-env.outputs.needed }}
      max_turns_env: ${{ steps.setup.outputs.max_turns_env }}
      max_turns_analysis: ${{ steps.setup.outputs.max_turns_analysis }}
      max_turns_implement: ${{ steps.setup.outputs.max_turns_implement }}
      max_turns_review: ${{ steps.setup.outputs.max_turns_review }}
      max_turns_knowledge: ${{ steps.setup.outputs.max_turns_knowledge }}
      iterations_limit: ${{ steps.setup.outputs.iterations_limit }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}

      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # Issue情報を取得
          ISSUE_DATA=$(gh issue view ${{ inputs.issue_number }} --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          # 環境変数として出力（マルチライン対応）
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          # デバッグ出力
          echo "::debug::Issue Title: $ISSUE_TITLE"
          echo "::debug::Issue Body Length: $(echo "$ISSUE_BODY" | wc -c)"

      - name: Setup workflow parameters
        id: setup
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          ISSUE_NUMBER: ${{ inputs.issue_number }}
          REPOSITORY: ${{ github.repository }}
        run: bash .github/scripts/setup-workflow-parameters.sh

      - name: Create Labels
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # 必要なラベルを作成
          gh label create "claude-processing" \
            --description "Claude is currently processing this issue" \
            --color "1d76db" \
            2>/dev/null || true
          
          gh label create "claude-done" \
            --description "Claude has completed processing this issue" \
            --color "0e8a16" \
            2>/dev/null || true

      - name: Create Branch
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          BRANCH_NAME="${{ steps.setup.outputs.branch_name }}"
          
          # 既存のブランチを確認
          if git ls-remote --heads origin "$BRANCH_NAME" | grep -q "$BRANCH_NAME"; then
            echo "⚠️ ブランチ $BRANCH_NAME は既に存在します。番号を付けて再試行します。"
            COUNTER=2
            while git ls-remote --heads origin "${BRANCH_NAME}-${COUNTER}" | grep -q "${BRANCH_NAME}-${COUNTER}"; do
              COUNTER=$((COUNTER + 1))
            done
            BRANCH_NAME="${BRANCH_NAME}-${COUNTER}"
            echo "🌿 新しいブランチ名: $BRANCH_NAME"
            
            # 出力を更新
            echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT
          fi
          
          # ベースブランチの選択と作成
          if git ls-remote --heads origin claude-code | grep -q claude-code; then
            echo "🌿 claude-codeブランチから新しいブランチを作成します"
            git checkout -b $BRANCH_NAME origin/claude-code
          else
            echo "⚠️ claude-codeブランチが存在しません。developから作成します"
            git checkout -b $BRANCH_NAME origin/develop
          fi
          
          git push -u origin $BRANCH_NAME

      - name: Initialize Work Directory
        run: |
          ISSUE_NUM="${{ inputs.issue_number }}"
          BRANCH_NAME="${{ steps.setup.outputs.branch_name }}"
          
          # 作業ディレクトリ初期化
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          mkdir -p "$WORK_DIR/implementation"
          mkdir -p "$WORK_DIR/review"
          mkdir -p "$WORK_DIR/dialogue"
          
          # ナレッジベースディレクトリの初期化
          KNOWLEDGE_DIR="claude/claude-knowledge"
          mkdir -p "$KNOWLEDGE_DIR/environment"
          mkdir -p "$KNOWLEDGE_DIR/patterns"
          mkdir -p "$KNOWLEDGE_DIR/issues"
          mkdir -p "$KNOWLEDGE_DIR/history"
          
          # 初期状態ファイル
          cat > "$WORK_DIR/state.json" << EOF
          {
            "issue_number": "${ISSUE_NUM}",
            "branch_name": "${BRANCH_NAME}",
            "phase": "INIT",
            "iteration": 0,
            "status": "IN_PROGRESS",
            "created_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
          }
          EOF
          
          git add .
          git commit -m "Initialize sequential dialogue for issue #${ISSUE_NUM}" || echo "No changes to commit"
          git push

      - name: Add Processing Label
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          gh issue edit ${{ inputs.issue_number }} --add-label "claude-processing"

      - name: Check Environment Analysis Need
        id: check-env
        run: |
          if [ -f "claude/claude-knowledge/environment/.analysis-complete" ]; then
            echo "✅ 環境分析は完了済みです"
            echo "needed=false" >> $GITHUB_OUTPUT
          else
            echo "🔍 環境分析が必要です"
            echo "needed=true" >> $GITHUB_OUTPUT
          fi

      - name: Post Initial Comment
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          BRANCH_NAME="${{ steps.setup.outputs.branch_name }}"
          MODEL_NAME="${{ steps.setup.outputs.display_model_name }}"
          
          COMMENT="## 🚀 Claude Orchestrator 開始"$'\n\n'
          COMMENT="${COMMENT}**ブランチ**: \`$BRANCH_NAME\`"$'\n'
          COMMENT="${COMMENT}**モデル**: $MODEL_NAME"$'\n\n'
          COMMENT="${COMMENT}これから以下のプロセスを実行します："$'\n'
          COMMENT="${COMMENT}1. 🔍 環境分析（初回のみ）"$'\n'
          COMMENT="${COMMENT}2. 📋 Issue分析とタスクリスト作成"$'\n'
          COMMENT="${COMMENT}3. 💻 実装（開発者）"$'\n'
          COMMENT="${COMMENT}4. 📝 レビュー（レビュワー）"$'\n'
          COMMENT="${COMMENT}5. 🔄 レビュー結果に基づく修正（必要に応じて繰り返し）"$'\n'
          COMMENT="${COMMENT}6. 📚 ナレッジ更新"$'\n'
          COMMENT="${COMMENT}7. ✅ 完了"$'\n\n'
          COMMENT="${COMMENT}各フェーズは独立したワークフロー実行として、完全に分離された視点で実行されます。"
          
          gh issue comment ${{ inputs.issue_number }} --body "$COMMENT"