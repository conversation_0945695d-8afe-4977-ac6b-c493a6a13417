name: <PERSON> Phase - Knowledge Update

# ナレッジ更新フェーズ - 実装から得た知見の記録

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: string
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: string
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false

jobs:
  update-knowledge:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'ナレッジ管理Bot'
          SLACK_COLOR: '#9c27b0'
          SLACK_TITLE: '📚 ナレッジ管理Bot: 更新開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **フェーズ**: ナレッジ更新
            **役割**: ナレッジ管理者
            **目的**: 実装から得た知見の記録
        continue-on-error: true
        
      # プロンプトを外部ファイルから読み込む
      - name: Load prompt
        id: prompt
        run: |
          # 変数を置換しながらプロンプトを読み込む
          PROMPT=$(cat .github/claude-prompts/knowledge-update.md | \
            sed "s/{{ISSUE_NUMBER}}/${{ inputs.issue_number }}/g" | \
            sed "s/{{ITERATION}}/${{ inputs.iteration }}/g")
          echo "prompt<<EOF" >> $GITHUB_OUTPUT
          echo "$PROMPT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Execute Claude - Knowledge Update
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: 20
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: ${{ steps.prompt.outputs.prompt }}
            
      - name: Verify Knowledge Update
        run: |
          echo "📚 ナレッジ更新の確認..."
          
          # パターンファイルの確認
          PATTERN_FILE="claude/claude-knowledge/patterns/pattern-issue-${{ inputs.issue_number }}.md"
          if [ -f "$PATTERN_FILE" ]; then
            echo "✅ パターンファイルが作成されました"
            echo "内容:"
            head -20 "$PATTERN_FILE"
          else
            echo "⚠️ パターンファイルは作成されませんでした（パターンが見つからなかった可能性）"
          fi
          
          # Issue固有情報の確認
          ISSUE_FILE="claude/claude-knowledge/issues/issue-${{ inputs.issue_number }}.md"
          if [ -f "$ISSUE_FILE" ]; then
            echo "✅ Issue固有情報が記録されました"
          else
            echo "❌ Issue固有情報が記録されていません"
          fi
          
      - name: Commit and Push Results
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          git add .
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "Update knowledge base from issue #${{ inputs.issue_number }}"
            git push origin ${{ inputs.branch_name }}
          fi
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'ナレッジ管理Bot'
          SLACK_COLOR: ${{ steps.claude.outcome == 'success' && 'good' || 'danger' }}
          SLACK_TITLE: ${{ steps.claude.outcome == 'success' && '✅ ナレッジ更新完了' || '❌ ナレッジ更新失敗' }}
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **ステータス**: ${{ steps.claude.outcome }}
            **実行時間**: ${{ steps.claude.outputs.duration || 'N/A' }}
        continue-on-error: true