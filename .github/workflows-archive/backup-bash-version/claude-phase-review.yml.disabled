name: <PERSON> Phase - Review

# レビューフェーズ - 実装内容の品質確認

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: string
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: string
      iteration:
        required: true
        type: string
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false
    outputs:
      review_passed:
        value: ${{ jobs.review.outputs.passed }}
      total_iterations:
        value: ${{ inputs.iteration }}

jobs:
  review:
    runs-on: ubuntu-latest
    outputs:
      passed: ${{ steps.review-result.outputs.passed }}
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'レビュワーBot'
          SLACK_COLOR: '#ff9800'
          SLACK_TITLE: '📝 レビュワーBot: レビュー開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **イテレーション**: ${{ inputs.iteration }}
            **フェーズ**: レビュー
            **役割**: コードレビュワー
        continue-on-error: true
        
      - name: Prepare Review Context
        run: |
          ISSUE_NUM="${{ inputs.issue_number }}"
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          ITERATION="${{ inputs.iteration }}"
          
          # 実装内容を確認
          echo "📋 実装内容を確認..."
          if [ -f "$WORK_DIR/implementation/iteration-${ITERATION}.md" ]; then
            cat "$WORK_DIR/implementation/iteration-${ITERATION}.md"
          fi
          
          # 変更されたファイルを確認
          echo "📝 変更されたファイル:"
          git diff origin/develop --name-only
          
      # プロンプトを外部ファイルから読み込む
      - name: Load prompt
        id: prompt
        run: |
          # 変数を置換しながらプロンプトを読み込む
          PROMPT=$(cat .github/claude-prompts/review.md | \
            sed "s/{{ISSUE_NUMBER}}/${{ inputs.issue_number }}/g" | \
            sed "s/{{ITERATION}}/${{ inputs.iteration }}/g")
          echo "prompt<<EOF" >> $GITHUB_OUTPUT
          echo "$PROMPT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Execute Claude - Review
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: 20
          allowed_tools: 'Read,Bash,Glob,Grep,LS,Task,NotebookRead,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: ${{ steps.prompt.outputs.prompt }}
            
      - name: Parse Review Result
        id: review-result
        run: |
          WORK_DIR="claude/claude-work/issue-${{ inputs.issue_number }}"
          REVIEW_FILE="$WORK_DIR/review/review-iteration-${{ inputs.iteration }}.md"
          
          # レビュー結果を確認
          if [ -f "$REVIEW_FILE" ]; then
            if grep -q "## 総評" "$REVIEW_FILE" && grep -q "合格" "$REVIEW_FILE"; then
              echo "✅ レビュー合格"
              echo "passed=true" >> $GITHUB_OUTPUT
            else
              echo "⚠️ レビューで修正が必要"
              echo "passed=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "❌ レビュー結果ファイルが見つかりません"
            echo "passed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Commit Review Results
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          git add .
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "Add review results for iteration ${{ inputs.iteration }}"
            git push origin ${{ inputs.branch_name }}
          fi
          
      - name: Post Review Comment
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          REVIEW_FILE="claude/claude-work/issue-${{ inputs.issue_number }}/review/review-iteration-${{ inputs.iteration }}.md"
          
          if [ -f "$REVIEW_FILE" ]; then
            # レビュー結果の要約を作成
            COMMENT="## 📝 レビュー結果 (イテレーション ${{ inputs.iteration }})"$'\n\n'
            # 総評を抽出
            if grep -A 2 "## 総評" "$REVIEW_FILE" | grep -q "合格"; then
              COMMENT="${COMMENT}**結果**: ✅ 合格"$'\n\n'
            else
              COMMENT="${COMMENT}**結果**: ⚠️ 要修正"$'\n\n'
            fi
            
            # 良い点と改善点を抽出（最初の3項目まで）
            COMMENT="${COMMENT}$(grep -A 5 "## 良い点" "$REVIEW_FILE" | head -6 || echo "")"
            
            gh issue comment ${{ inputs.issue_number }} --body "$COMMENT"
          fi
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'レビュワーBot'
          SLACK_COLOR: ${{ steps.review-result.outputs.passed == 'true' && 'good' || 'warning' }}
          SLACK_TITLE: '📝 レビュー完了 (イテレーション ${{ inputs.iteration }})'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **結果**: ${{ steps.review-result.outputs.passed == 'true' && '✅ 合格' || '⚠️ 要修正' }}
            **ステータス**: ${{ steps.claude.outcome }}
        continue-on-error: true