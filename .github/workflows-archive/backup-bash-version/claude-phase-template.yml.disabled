name: <PERSON> Phase Template

# 再利用可能なワークフローテンプレート
on:
  workflow_call:
    inputs:
      phase:
        required: true
        type: string
      issue_number:
        required: true
        type: string
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: string
      prompt:
        required: true
        type: string
      allowed_tools:
        required: false
        type: string
        default: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
      timeout_minutes:
        required: false
        type: number
        default: 20
    secrets:
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      GH_TOKEN_WORKFLOW:
        required: true
      SLACK_WEBHOOK:
        required: false

jobs:
  execute:
    runs-on: ubuntu-latest
    outputs:
      execution_file: ${{ steps.claude.outputs.execution_file }}
      conclusion: ${{ steps.claude.outputs.conclusion }}
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Checkout Working Branch
        run: |
          git fetch origin
          git checkout ${{ inputs.branch_name }}
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Claude Bot - ${{ inputs.phase }}'
          SLACK_COLOR: '#2196F3'
          SLACK_TITLE: '🚀 ${{ inputs.phase }} フェーズ開始'
          SLACK_MESSAGE: |
            Issue: #${{ inputs.issue_number }}
            ブランチ: ${{ inputs.branch_name }}
            モデル: ${{ inputs.selected_model }}
        continue-on-error: true
          
      - name: Execute Claude
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          prompt: ${{ inputs.prompt }}
          allowed_tools: ${{ inputs.allowed_tools }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: ${{ inputs.timeout_minutes }}
          
      - name: Save Results
        if: always()
        run: |
          if [ -f "${{ steps.claude.outputs.execution_file }}" ]; then
            cp "${{ steps.claude.outputs.execution_file }}" "phase-${{ inputs.phase }}-execution.json"
            
            # アーティファクトとして保存
            mkdir -p artifacts
            cp "phase-${{ inputs.phase }}-execution.json" artifacts/
          fi
          
      - name: Upload Artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: claude-${{ inputs.phase }}-results
          path: artifacts/
          retention-days: 7