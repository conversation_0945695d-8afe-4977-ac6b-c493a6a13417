name: <PERSON> Phase - Implementation

# 実装フェーズ - タスクリストに基づく実装

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: string
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: string
      iteration:
        required: true
        type: string
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false
    outputs:
      iteration:
        value: ${{ inputs.iteration }}
      files_changed:
        value: ${{ jobs.implement.outputs.files_changed }}

jobs:
  implement:
    runs-on: ubuntu-latest
    outputs:
      files_changed: ${{ steps.changes.outputs.files }}
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: '開発者Bot'
          SLACK_COLOR: '#36a64f'
          SLACK_TITLE: '💻 開発者Bot: 実装開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **イテレーション**: ${{ inputs.iteration }}
            **フェーズ**: 実装
            **役割**: 開発者
        continue-on-error: true
        
      - name: Prepare Implementation Context
        id: context
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM="${{ inputs.issue_number }}"
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          
          # タスクリストを取得
          if [ -f "$WORK_DIR/dialogue/task-list.md" ]; then
            echo "📋 タスクリストを確認..."
            cat "$WORK_DIR/dialogue/task-list.md"
          fi
          
          # 前回のレビュー結果を確認（イテレーション2以降）
          ITERATION="${{ inputs.iteration }}"
          if [ "$ITERATION" -gt 1 ]; then
            PREV_ITERATION=$((ITERATION - 1))
            REVIEW_FILE="$WORK_DIR/review/review-iteration-${PREV_ITERATION}.md"
            if [ -f "$REVIEW_FILE" ]; then
              echo "📝 前回のレビュー結果を確認..."
              cat "$REVIEW_FILE"
            fi
          fi
          
      # プロンプトを外部ファイルから読み込む
      - name: Load prompt
        id: prompt
        run: |
          # 変数を置換しながらプロンプトを読み込む
          PROMPT=$(cat .github/claude-prompts/implement.md | \
            sed "s/{{ISSUE_NUMBER}}/${{ inputs.issue_number }}/g" | \
            sed "s/{{ITERATION}}/${{ inputs.iteration }}/g")
          echo "prompt<<EOF" >> $GITHUB_OUTPUT
          echo "$PROMPT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Execute Claude - Implementation
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: 30
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: ${{ steps.prompt.outputs.prompt }}
            
      - name: Check Changes
        id: changes
        run: |
          # 変更されたファイルを確認
          git status --porcelain > /tmp/changes.txt
          
          if [ -s /tmp/changes.txt ]; then
            echo "📝 変更されたファイル:"
            cat /tmp/changes.txt
            
            # 変更ファイルリストを出力に保存
            FILES=$(git status --porcelain | awk '{print $2}' | tr '\n' ' ')
            echo "files=$FILES" >> $GITHUB_OUTPUT
          else
            echo "⚠️ 変更されたファイルがありません"
            echo "files=" >> $GITHUB_OUTPUT
          fi
          
      - name: Commit and Push Changes
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # 変更をコミット
          git add .
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "Implement tasks for issue #${{ inputs.issue_number }} (iteration ${{ inputs.iteration }})"
            git push origin ${{ inputs.branch_name }}
          fi
          
      - name: Save Implementation Summary
        run: |
          WORK_DIR="claude/claude-work/issue-${{ inputs.issue_number }}"
          # implementationディレクトリを作成
          mkdir -p "$WORK_DIR/implementation"
          IMPL_FILE="$WORK_DIR/implementation/iteration-${{ inputs.iteration }}.md"
          
          # 実装サマリーを保存
          if [ -f "${{ steps.claude.outputs.execution_file }}" ]; then
            # Claudeの最後の応答を抽出
            jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' \
              "${{ steps.claude.outputs.execution_file }}" > "$IMPL_FILE" 2>/dev/null || echo "実装完了" > "$IMPL_FILE"
          fi
          
          # 変更をコミット
          git add .
          git commit -m "Save implementation summary for iteration ${{ inputs.iteration }}" || echo "No changes"
          git push
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: '開発者Bot'
          SLACK_COLOR: ${{ steps.claude.outcome == 'success' && 'good' || 'danger' }}
          SLACK_TITLE: ${{ steps.claude.outcome == 'success' && '✅ 実装完了' || '❌ 実装失敗' }} (イテレーション ${{ inputs.iteration }})
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **ステータス**: ${{ steps.claude.outcome }}
            **変更ファイル数**: ${{ steps.changes.outputs.files && '複数' || 'なし' }}
        continue-on-error: true