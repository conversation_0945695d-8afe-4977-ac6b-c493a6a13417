name: <PERSON> Phase - Analyze Issue

# Issue分析フェーズ - タスクリストの作成

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: string
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: string
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false

jobs:
  analyze-issue:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Git Config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Issue分析Bot'
          SLACK_COLOR: '#2196F3'
          SLACK_TITLE: '📋 Issue分析Bot: 分析開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **フェーズ**: Issue分析
            **役割**: タスク分析専門家
            **目的**: 実装タスクリストの作成
        continue-on-error: true
        
      # プロンプトを外部ファイルから読み込む
      - name: Load prompt
        id: prompt
        run: |
          # 変数を置換しながらプロンプトを読み込む
          PROMPT=$(cat .github/claude-prompts/analyze-issue.md | \
            sed "s/{{ISSUE_NUMBER}}/${{ inputs.issue_number }}/g")
          echo "prompt<<EOF" >> $GITHUB_OUTPUT
          echo "$PROMPT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Execute Claude - Issue Analysis
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: 20
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: ${{ steps.prompt.outputs.prompt }}
            
      - name: Verify Task List Creation
        env:
          ISSUE_NUMBER: ${{ inputs.issue_number }}
          WORK_DIR: claude/claude-work/issue-${{ inputs.issue_number }}
        run: bash .github/scripts/verify-task-list.sh
          
      - name: Commit and Push Results
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          git add .
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "Add issue analysis and task list for #${{ inputs.issue_number }}"
            git push origin ${{ inputs.branch_name }}
          fi
          
      - name: Post Task List Summary
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          ISSUE_NUMBER: ${{ inputs.issue_number }}
          COMMENT_TYPE: task-list
          TASK_FILE: claude/claude-work/issue-${{ inputs.issue_number }}/dialogue/task-list.md
        run: bash .github/scripts/post-issue-comment.sh
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Issue分析Bot'
          SLACK_COLOR: ${{ steps.claude.outcome == 'success' && 'good' || 'danger' }}
          SLACK_TITLE: ${{ steps.claude.outcome == 'success' && '✅ Issue分析完了' || '❌ Issue分析失敗' }}
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **ステータス**: ${{ steps.claude.outcome }}
            **実行時間**: ${{ steps.claude.outputs.duration || 'N/A' }}
        continue-on-error: true