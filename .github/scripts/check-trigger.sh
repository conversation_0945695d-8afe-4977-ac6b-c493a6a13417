#!/usr/bin/env bash
# check-trigger.sh - ワークフロートリガーのチェック
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 環境変数から値を取得（GitHub Actionsコンテキスト）
EVENT_NAME="${EVENT_NAME:-}"
ISSUE_NUMBER="${ISSUE_NUMBER:-}"
REPOSITORY="${GITHUB_REPOSITORY:-}"

# 必須変数のチェック
if [[ -z "$EVENT_NAME" ]]; then
    echo "::error::EVENT_NAME is required"
    exit 1
fi

# workflow_dispatch の場合
if [[ "$EVENT_NAME" == "workflow_dispatch" ]]; then
    echo "should_run=true" >> "$GITHUB_OUTPUT"
    echo "issue_number=$ISSUE_NUMBER" >> "$GITHUB_OUTPUT"
    echo "::notice::Triggered by workflow_dispatch for issue #$ISSUE_NUMBER"
    exit 0
fi

# issue_comment の場合
if [[ "$EVENT_NAME" == "issue_comment" ]]; then
    # Issue番号が必要
    if [[ -z "$ISSUE_NUMBER" ]]; then
        echo "::error::Issue number not found for issue_comment event"
        exit 1
    fi
    
    # GH_TOKEN が必要
    if [[ -z "${GH_TOKEN:-}" ]]; then
        echo "::error::GH_TOKEN is required for API calls"
        exit 1
    fi
    
    # Issue のラベルを取得
    echo "::notice::Checking labels for issue #$ISSUE_NUMBER"
    ISSUE_LABELS=$(gh api "repos/${REPOSITORY}/issues/${ISSUE_NUMBER}" --jq '.labels[].name' 2>/dev/null | tr '\n' ' ' || echo "")
    
    # claude-done ラベルがある場合はスキップ
    if echo "$ISSUE_LABELS" | grep -q "claude-done"; then
        echo "should_run=false" >> "$GITHUB_OUTPUT"
        echo "::notice::Issue #$ISSUE_NUMBER already processed (has claude-done label)"
    else
        echo "should_run=true" >> "$GITHUB_OUTPUT"
        echo "issue_number=$ISSUE_NUMBER" >> "$GITHUB_OUTPUT"
        echo "::notice::Processing issue #$ISSUE_NUMBER"
    fi
    exit 0
fi

# その他のイベント
echo "::warning::Unsupported event type: $EVENT_NAME"
echo "should_run=false" >> "$GITHUB_OUTPUT"
exit 0