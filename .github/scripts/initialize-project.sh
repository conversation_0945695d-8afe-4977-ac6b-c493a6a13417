#!/usr/bin/env bash
# initialize-project.sh - プロジェクトの初期化とブランチ作成
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 必須環境変数のチェック
: "${ISSUE_NUMBER:?Issue number is required}"
: "${REPOSITORY:?Repository is required}"
: "${GH_TOKEN:?GitHub token is required}"

# Issue情報を取得
echo "::notice::Fetching issue #$ISSUE_NUMBER information"
ISSUE_INFO=$(gh api "repos/${REPOSITORY}/issues/${ISSUE_NUMBER}")
ISSUE_TITLE=$(echo "$ISSUE_INFO" | jq -r '.title')
ISSUE_BODY=$(echo "$ISSUE_INFO" | jq -r '.body // ""')
ISSUE_LABELS=$(echo "$ISSUE_INFO" | jq -r '.labels[].name' | tr '\n' ' ')

# claude-done ラベルがある場合はスキップ
if echo "$ISSUE_LABELS" | grep -q "claude-done"; then
    echo "::warning::Issue already processed (has claude-done label)"
    echo "already_processed=true" >> "$GITHUB_OUTPUT"
    exit 0
fi

# モデルの選択
DEFAULT_MODEL="claude-3-5-sonnet-20241022"
SELECTED_MODEL="${SELECTED_MODEL:-$DEFAULT_MODEL}"

# Issue本文からモデル指定を抽出
if echo "$ISSUE_BODY" | grep -q "モデル:"; then
    MODEL_FROM_ISSUE=$(echo "$ISSUE_BODY" | grep "モデル:" | sed 's/.*モデル: *//' | cut -d' ' -f1)
    if [[ -n "$MODEL_FROM_ISSUE" ]]; then
        SELECTED_MODEL="$MODEL_FROM_ISSUE"
        echo "::notice::Using model from issue: $SELECTED_MODEL"
    fi
fi

# イテレーション制限の抽出
ITERATIONS_LIMIT=$(echo "$ISSUE_BODY" | grep -o "コーダー・レビュアー対話回数制限: [0-9]\+" | grep -o "[0-9]\+" | head -1)
ITERATIONS_LIMIT="${ITERATIONS_LIMIT:-3}"

# ブランチ名の生成
# 改行文字を含む可能性があるため、最初に改行をスペースに置換
TITLE_SINGLE_LINE=$(echo "$ISSUE_TITLE" | tr '\n' ' ' | tr '\r' ' ')
NORMALIZED_TITLE=$(echo "$TITLE_SINGLE_LINE" | \
    sed 's/[^a-zA-Z0-9 _-]//g' | \
    sed 's/[ _]/-/g' | \
    sed 's/--*/-/g' | \
    sed 's/^-//;s/-$//' | \
    cut -c1-50)

BRANCH_NAME="claude-task/${ISSUE_NUMBER}-${NORMALIZED_TITLE}"

# 出力
echo "issue_number=$ISSUE_NUMBER" >> "$GITHUB_OUTPUT"
echo "issue_title=$ISSUE_TITLE" >> "$GITHUB_OUTPUT"
echo "branch_name=$BRANCH_NAME" >> "$GITHUB_OUTPUT"
echo "selected_model=$SELECTED_MODEL" >> "$GITHUB_OUTPUT"
echo "iterations_limit=$ITERATIONS_LIMIT" >> "$GITHUB_OUTPUT"
echo "already_processed=false" >> "$GITHUB_OUTPUT"

# ブランチの作成
echo "::notice::Creating branch: $BRANCH_NAME"
git checkout -b "$BRANCH_NAME"

# 作業ディレクトリの作成
mkdir -p "claude/claude-work/issue-${ISSUE_NUMBER}/dialogue"

# Issue情報の保存
cat > "claude/claude-work/issue-${ISSUE_NUMBER}/issue-info.json" <<EOF
{
  "number": $ISSUE_NUMBER,
  "title": $(echo "$ISSUE_TITLE" | jq -R),
  "body": $(echo "$ISSUE_BODY" | jq -Rs),
  "labels": $(echo "$ISSUE_INFO" | jq '.labels'),
  "created_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "branch": "$BRANCH_NAME",
  "model": "$SELECTED_MODEL",
  "iterations_limit": $ITERATIONS_LIMIT
}
EOF

echo "::notice::Project initialized successfully"