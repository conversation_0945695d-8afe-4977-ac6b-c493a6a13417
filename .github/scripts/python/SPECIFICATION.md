# Claude Orchestrator Python版 仕様書

## 1. 概要

Claude OrchestratorのPython実装版は、既存のBashスクリプトベースの実装を置き換えることを目的としています。この実装により、より堅牢でメンテナンスしやすく、テスト可能なワークフローシステムを実現します。

## 2. システム構成

### 2.1 ディレクトリ構造

```
.github/
├── workflows/
│   ├── claude-orchestrator-main-python.yml      # メインワークフロー
│   ├── claude-phase-initialize-python.yml       # 初期化フェーズ
│   ├── claude-phase-finalize-python.yml         # 最終化フェーズ
│   └── claude-phase-*.yml                       # その他のフェーズ（未実装）
└── scripts/
    └── python/
        ├── orchestrator.py                       # メインCLIスクリプト
        ├── requirements.txt                      # 依存関係
        ├── core/                                 # コア機能
        │   ├── __init__.py
        │   └── base.py                          # 基本クラスとヘルパー
        ├── utils/                                # ユーティリティ
        │   ├── __init__.py
        │   ├── parameter_parser.py              # パラメータ解析
        │   ├── github_client.py                 # GitHub API操作
        │   └── pr_creator.py                    # PR作成ロジック
        └── phases/                               # ワークフローフェーズ
            ├── __init__.py
            ├── initialize.py                     # 初期化フェーズ
            └── finalize.py                       # 最終化フェーズ
```

### 2.2 主要コンポーネント

#### 2.2.1 Core Module (core/base.py)

**データクラス:**
- `WorkflowParameters`: ワークフローパラメータを保持
  - issue_number: Issue番号
  - issue_title: Issueタイトル
  - issue_body: Issue本文
  - branch_name: ブランチ名
  - model: 使用するClaudeモデル
  - max_turns_*: 各フェーズの最大ターン数
  - iterations_limit: イテレーション回数制限
  - prd_file_path: PRDファイルパス（オプション）
  - task_complexity: タスクの複雑度

- `IssueData`: GitHub Issue情報
  - number: Issue番号
  - title: タイトル
  - body: 本文
  - state: 状態
  - labels: ラベルリスト
  - created_at: 作成日時
  - updated_at: 更新日時

**ヘルパークラス:**
- `GitHubActionsHelper`: GitHub Actions固有の機能
  - set_output(): 出力設定
  - set_env(): 環境変数設定
  - log_*(): ログ出力（debug, notice, warning, error）

- `CommandRunner`: コマンド実行ラッパー
  - run(): コマンド実行
  - run_gh_cli(): GitHub CLIコマンド実行

- `BasePhase`: フェーズ基本クラス

#### 2.2.2 Utils Module

**parameter_parser.py:**
- `ParameterParser`: Issue本文からワークフローパラメータを抽出
  - モデル選択（claude-opus-4, claude-sonnet-4, claude-3-5-haiku, claude-3-5-sonnet）
  - ターン数制限の抽出
  - ブランチ名の生成（日本語対応）
  - PRDファイルパスの抽出
  - タスク複雑度の判定

**github_client.py:**
- `GitHubClient`: GitHub API操作のラッパー（GitHub CLIを使用）
  - get_issue(): Issue情報取得
  - create_issue_comment(): コメント作成
  - add/remove_issue_labels(): ラベル操作
  - create_branch(): ブランチ作成
  - create_pr(): プルリクエスト作成
  - setup_git_config(): Git設定
  
  注: PyGithubは将来の拡張のため依存関係に含まれていますが、現在の実装ではGitHub CLIを使用

**pr_creator.py:**
- `PRCreator`: プルリクエスト作成ロジック
  - create_pr_summary(): PR説明文生成
  - 実装内容のサマリー
  - 変更ファイルリスト
  - レビュー履歴
  - テスト結果（あれば）

#### 2.2.3 Phases Module

**initialize.py:**
- `InitializePhase`: 初期化フェーズの実装
  - 必要なラベルの作成
  - ブランチの作成
  - 作業ディレクトリの初期化
  - 環境分析の必要性チェック
  - 初期コメントの投稿

**finalize.py:**
- `FinalizePhase`: 最終化フェーズの実装
  - プルリクエストの作成
  - Issueの更新（ラベル、コメント）
  - サマリーレポートの生成

### 2.3 CLI コマンド

`orchestrator.py` は以下のコマンドを提供：

1. **check-trigger**: ワークフロートリガーのチェック
   - issue_commentイベント: @claude-taskメンションを確認
   - workflow_dispatchイベント: issue_number入力を確認

2. **parse-parameters**: Issueからパラメータ解析
   ```bash
   python orchestrator.py parse-parameters --issue-number 123
   ```

3. **initialize**: ワークフロー初期化
   ```bash
   python orchestrator.py initialize \
     --issue-number 123 \
     --issue-title "Title" \
     --issue-body "Body"
   ```

4. **finalize**: 最終化とPR作成
   ```bash
   python orchestrator.py finalize \
     --issue-number 123 \
     --branch-name "branch" \
     --total-iterations 3
   ```

5. **verify-task-list**: タスクリスト検証
   ```bash
   python orchestrator.py verify-task-list \
     --work-dir "claude/claude-work/issue-123"
   ```

6. **run-script**: 後方互換性のためのコマンド
   ```bash
   python orchestrator.py run-script \
     --script setup-workflow-parameters.sh
   ```

## 3. ワークフロー

### 3.1 メインワークフロー (claude-orchestrator-main-python.yml)

1. **trigger-check**: トリガーの確認
2. **initialize**: 環境の初期化
3. **analyze-env**: 環境分析（条件付き）
4. **analyze-issue**: Issue分析
5. **implementation-loop**: 実装ループ制御
6. **implement**: 実装フェーズ
7. **review**: レビューフェーズ
8. **update-knowledge**: ナレッジ更新
9. **finalize**: 最終化とPR作成

### 3.2 フェーズ間のデータフロー

各フェーズは以下の方法でデータを共有：
- GitHub Actions outputs
- Git リポジトリ内のファイル
- 環境変数

## 4. エラーハンドリング

### 4.1 エラーレベル

1. **WorkflowError**: ワークフロー固有のエラー
2. **subprocess.CalledProcessError**: コマンド実行エラー
3. **json.JSONDecodeError**: JSONパースエラー
4. **その他の例外**: 予期しないエラー

### 4.2 エラー時の動作

- ログ出力（logger.error）
- GitHub Actions エラー出力（::error::）
- 適切な終了コード（sys.exit(1)）
- Slackへの通知（設定されている場合）

## 5. 設定とカスタマイズ

### 5.1 環境変数

- `RUNNER_DEBUG`: デバッグモード（"1"で有効）
- `GH_TOKEN` / `GH_TOKEN_WORKFLOW`: GitHub トークン
- `GITHUB_OUTPUT`: 出力ファイルパス
- `GITHUB_ENV`: 環境変数ファイルパス
- `GITHUB_EVENT_NAME`: イベント名
- `GITHUB_EVENT_PATH`: イベントデータファイル
- `GITHUB_REPOSITORY`: リポジトリ名

### 5.2 デフォルト値

- デフォルトモデル: claude-3-5-sonnet-20241022
- デフォルト最大ターン数: 80
- デフォルトイテレーション制限: 10
- ブランチプレフィックス: claude-task/

## 6. セキュリティ考慮事項

1. **トークンの扱い**
   - GitHub トークンは環境変数経由で渡す
   - add_mask()でログからマスク

2. **入力検証**
   - Issue番号の型チェック
   - パラメータの妥当性検証

3. **コマンドインジェクション対策**
   - subprocess.runでリスト形式のコマンド使用
   - シェル展開を避ける

## 7. 実装状況

### 7.1 実装済み

- ✅ コア機能（base.py）
- ✅ パラメータ解析
- ✅ GitHub API操作
- ✅ 初期化フェーズ
- ✅ 最終化フェーズ
- ✅ PR作成機能
- ✅ CLI インターフェース

### 7.2 未実装

- ⏳ 環境分析フェーズ（ワークフローファイル含む）
- ⏳ Issue分析フェーズ（ワークフローファイル含む）
- ⏳ 実装フェーズ（ワークフローファイル含む）
- ⏳ レビューフェーズ（ワークフローファイル含む）
- ⏳ ナレッジ更新フェーズ（ワークフローファイル含む）
- ⏳ 単体テスト
- ⏳ 統合テスト

## 8. 移行計画

1. **Phase 1**: 基本機能の実装（完了）
2. **Phase 2**: 初期化とfinalizeの実装（完了）
3. **Phase 3**: テスト環境での動作確認
4. **Phase 4**: 残りのフェーズの実装
5. **Phase 5**: 本番環境への段階的移行

## 9. テスト戦略

### 9.1 単体テスト

各モジュールごとに単体テストを実装：
- パラメータ解析のテスト
- GitHub API操作のモック
- エラーケースのテスト

### 9.2 統合テスト

実際のGitHub環境でのテスト：
- テスト用Issueでの動作確認
- 各フェーズの連携確認
- エラーリカバリーのテスト

## 10. パフォーマンス最適化

1. **並列処理**
   - 可能な限りAPI呼び出しを並列化
   - ファイルI/Oの最適化

2. **キャッシュ**
   - pip依存関係のキャッシュ
   - Gitオブジェクトのキャッシュ

3. **リソース管理**
   - 一時ファイルの適切なクリーンアップ
   - メモリ使用量の監視

## 11. ログとモニタリング

### 11.1 ログレベル

- DEBUG: 詳細なデバッグ情報
- INFO: 通常の処理フロー
- WARNING: 警告（処理は継続）
- ERROR: エラー（処理失敗）

### 11.2 ログ出力先

- 標準出力（GitHub Actions ログ）
- GitHub Actions アノテーション
- Slack通知（オプション）

## 12. 実装検証

### 12.1 動作確認手順

1. **ローカルテスト**
   ```bash
   # CLIの動作確認
   python .github/scripts/python/orchestrator.py --help
   
   # 各コマンドのテスト
   python .github/scripts/python/orchestrator.py check-trigger
   ```

2. **GitHub Actions上でのテスト**
   - テスト用のIssueを作成
   - @claude-taskメンションでトリガー
   - 各フェーズの動作を確認

### 12.2 移行前チェックリスト

- [ ] すべてのPythonスクリプトのSyntaxチェック
- [ ] 依存関係のインストール確認
- [ ] 環境変数の互換性確認
- [ ] エラーハンドリングのテスト
- [ ] ログ出力の確認

## 13. 今後の拡張

1. **プラグインシステム**
   - カスタムフェーズの追加
   - 外部ツールとの連携

2. **設定ファイル**
   - YAML/JSONベースの設定
   - プロジェクト固有の設定

3. **メトリクス収集**
   - 実行時間の計測
   - 成功率の追跡
   - リソース使用量の監視