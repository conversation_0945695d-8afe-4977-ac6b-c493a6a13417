#!/usr/bin/env python3
"""Review Phase - Review implementation quality."""
import os
import sys
import logging
import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper
from utils.github_client import GitHubClient
from utils.datetime_helper import now_jst

logger = logging.getLogger(__name__)


class ReviewPhase(BasePhase):
    """Review phase implementation."""
    
    def __init__(self):
        super().__init__("review")
        self.github_client = GitHubClient()
        
    def run(self, params: WorkflowParameters, iteration: int = 1) -> Dict[str, Any]:
        """Run review phase."""
        self.log_phase_start()
        
        try:
            # Create work directories
            work_dir = self._create_work_directory(params.issue_number)
            review_dir = work_dir / "review"
            review_dir.mkdir(exist_ok=True)
            
            # Get implementation details
            implementation_details = self._get_implementation_details(work_dir, iteration)
            
            # Perform review
            review_result = self._perform_review(implementation_details, params, iteration)
            
            # Save review results
            review_file = review_dir / f"review-iteration-{iteration}.md"
            review_json = review_dir / f"iteration-{iteration}.json"
            timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
            
            review_content = f"""# レビュー結果 - イテレーション {iteration}

実行日時: {timestamp}
Issue: #{params.issue_number}
ブランチ: {params.branch_name}

## 総評
{review_result['overall_assessment']}

## 品質チェック結果
{review_result['quality_checks']}

## 改善点
{review_result['improvements']}

## 承認状況
{review_result['approval_status']}

## 次のステップ
{review_result['next_steps']}
"""
            
            review_file.write_text(review_content)
            
            # Save JSON for workflow processing
            json_data = {
                "passed": review_result['passed'],
                "iteration": iteration,
                "timestamp": timestamp,
                "feedback_items": review_result['feedback_items'],
                "score": review_result['score']
            }
            
            with open(review_json, 'w') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Created review files: {review_file}, {review_json}")
            
            # Update workflow state
            self._update_workflow_state(params.issue_number, f"review-{iteration}", {
                "completed": True,
                "timestamp": timestamp,
                "iteration": iteration,
                "review_file": str(review_file),
                "passed": review_result['passed'],
                "score": review_result['score']
            })
            
            # Post review comment
            status = "✅ 合格" if review_result['passed'] else "⚠️ 要修正"
            self._post_progress_comment(
                params.issue_number,
                f"📝 レビュー完了 (イテレーション {iteration})",
                f"コードレビューが完了しました。\\n\\n**結果**: {status}\\n**スコア**: {review_result['score']}/100\\n\\n{review_result['summary_comment']}"
            )
            
            self.log_phase_end(True)
            return {
                "success": True,
                "iteration": iteration,
                "review_file": str(review_file),
                "passed": review_result['passed'],
                "score": review_result['score'],
                "timestamp": timestamp
            }
            
        except Exception as e:
            logger.error(f"Review failed: {e}")
            self.github.log_error(f"Review failed: {e}")
            self.log_phase_end(False)
            raise

    def _get_implementation_details(self, work_dir: Path, iteration: int) -> Dict[str, Any]:
        """Get implementation details from files."""
        impl_dir = work_dir / "implementation"
        impl_file = impl_dir / f"iteration-{iteration}.md"
        
        if not impl_file.exists():
            logger.warning(f"Implementation file not found: {impl_file}")
            return {
                "content": "実装ファイルが見つかりませんでした",
                "files_changed": [],
                "tasks_completed": 0
            }
        
        content = impl_file.read_text()
        
        # Extract key information
        files_changed = []
        if "## 作成・変更されたファイル" in content:
            section_start = content.find("## 作成・変更されたファイル")
            section_end = content.find("##", section_start + 1)
            if section_end == -1:
                section_end = len(content)
            files_section = content[section_start:section_end]
            
            for line in files_section.split('\\n'):
                if line.strip() and not line.startswith('#'):
                    files_changed.append(line.strip())
        
        return {
            "content": content,
            "files_changed": files_changed,
            "tasks_completed": content.count("✅")
        }

    def _perform_review(self, implementation_details: Dict[str, Any], 
                       params: WorkflowParameters, iteration: int) -> Dict[str, Any]:
        """Perform actual code review."""
        
        score = 0
        feedback_items = []
        quality_checks = []
        improvements = []
        
        # Check if implementation was done
        if implementation_details['tasks_completed'] > 0:
            score += 40
            quality_checks.append("✅ タスクが実行されました")
        else:
            quality_checks.append("❌ 実行されたタスクがありません")
            feedback_items.append("タスクの実行を確認してください")
        
        # Check file creation
        if implementation_details['files_changed']:
            score += 30
            quality_checks.append(f"✅ {len(implementation_details['files_changed'])}個のファイルが作成/変更されました")
        else:
            quality_checks.append("⚠️ ファイルの変更が確認できませんでした")
            feedback_items.append("ファイルの作成または変更を確認してください")
        
        # Check content quality
        content = implementation_details['content']
        if len(content) > 100:
            score += 20
            quality_checks.append("✅ 詳細な実装記録があります")
        else:
            quality_checks.append("⚠️ 実装記録が簡潔すぎます")
            feedback_items.append("より詳細な実装記録を提供してください")
        
        # Check for error handling
        if "エラー" in content or "Exception" in content or "try" in content:
            score += 10
            quality_checks.append("✅ エラーハンドリングが考慮されています")
        else:
            improvements.append("エラーハンドリングの追加を検討してください")
        
        # Determine pass/fail
        passed = score >= 70
        
        # Generate assessments
        if passed:
            overall_assessment = "合格"
            approval_status = "✅ このイテレーションは承認されました"
            next_steps = "次のフェーズ（ナレッジ更新）に進んでください"
            summary_comment = "実装は基準を満たしています。"
        else:
            overall_assessment = "要修正"
            approval_status = "⚠️ 修正が必要です"
            next_steps = "指摘事項を修正して再度実装を行ってください"
            summary_comment = "いくつかの改善点があります。修正をお願いします。"
        
        # Format sections
        quality_checks_text = '\\n'.join([f"- {check}" for check in quality_checks])
        improvements_text = '\\n'.join([f"- {imp}" for imp in improvements]) if improvements else "特になし"
        
        return {
            'passed': passed,
            'score': score,
            'overall_assessment': overall_assessment,
            'quality_checks': quality_checks_text,
            'improvements': improvements_text,
            'approval_status': approval_status,
            'next_steps': next_steps,
            'summary_comment': summary_comment,
            'feedback_items': feedback_items
        }

    def process_review_result(self, review_file: Path) -> Dict[str, Any]:
        """Process review result and return status."""
        if not review_file.exists():
            return {"passed": False, "error": "Review file not found"}
            
        content = review_file.read_text()
        passed = '合格' in content and '## 総評' in content
        
        return {"passed": passed}

    def _create_work_directory(self, issue_number: int) -> Path:
        """Create work directory for the issue."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        work_dir.mkdir(parents=True, exist_ok=True)
        return work_dir

    def _update_workflow_state(self, issue_number: int, phase: str, data: Dict[str, Any]):
        """Update workflow state file."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        state_file = work_dir / "workflow-state.json"
        
        # Load existing state
        state = {}
        if state_file.exists():
            with open(state_file) as f:
                state = json.load(f)
        
        # Update state
        if "phases" not in state:
            state["phases"] = {}
        
        state["phases"][phase] = data
        state["last_updated"] = now_jst().isoformat()
        
        # Save state
        state_file.parent.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Updated workflow state for phase: {phase}")

    def _post_progress_comment(self, issue_number: int, title: str, message: str):
        """Post progress comment to issue."""
        try:
            comment = f"## {title}\\n\\n{message}\\n\\n---\\n*Automated by Claude Orchestrator*"
            self.github_client.create_issue_comment(issue_number, comment)
        except Exception as e:
            logger.warning(f"Failed to post progress comment: {e}")


def main():
    """CLI entry point for review."""
    import click
    
    @click.command()
    @click.option('--issue-number', required=True, type=int, help='GitHub issue number')
    @click.option('--branch-name', required=True, help='Branch name')
    @click.option('--model', required=True, help='Model to use')
    @click.option('--max-turns', required=True, type=int, help='Maximum turns')
    @click.option('--iteration', required=True, type=int, help='Iteration number')
    def review(issue_number, branch_name, model, max_turns, iteration):
        """Run review phase."""
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_review=max_turns
        )
        
        phase = ReviewPhase()
        result = phase.run(params, iteration)
        
        print(json.dumps(result, indent=2))
        
    review()


if __name__ == '__main__':
    main()