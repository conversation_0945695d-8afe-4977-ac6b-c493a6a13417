"""Initialize phase for <PERSON>."""
import os
from pathlib import Path
from typing import Dict, Any
from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper
from utils.github_client import GitHubClient
from utils.workflow_state import WorkflowState
from utils.datetime_helper import now_jst, parse_iso_datetime

class InitializePhase(BasePhase):
    """Initialize the workflow environment."""
    
    # Required labels
    REQUIRED_LABELS = [
        ("claude-processing", "<PERSON> is currently working on this issue", "0E8A16"),
        ("claude-done", "<PERSON> has completed work on this issue", "22863A"),
        ("claude-error", "<PERSON> encountered an error", "B60205"),
        ("claude-generated", "Code generated by <PERSON>", "7057FF")
    ]
    
    def __init__(self):
        super().__init__("Initialize")
        self.github_client = GitHubClient()
        
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        """Run the initialization phase."""
        self.log_phase_start()
        
        try:
            # Setup git configuration
            self.github_client.setup_git_config()
            
            # Create required labels
            self._create_labels()
            
            # Create branch
            self._create_branch(params)
            
            # Initialize work directory
            work_dir = self._initialize_work_directory(params)
            
            # Add processing label
            self.github_client.add_issue_labels(params.issue_number, ["claude-processing"])
            
            # Check if environment analysis is needed
            env_analysis_needed = self._check_env_analysis_need(params)
            
            # Post initial comment
            self._post_initial_comment(params)
            
            # Set outputs
            self._set_outputs(params, env_analysis_needed)
            
            self.log_phase_end(success=True)
            
            return {
                "success": True,
                "branch_name": params.branch_name,
                "work_dir": str(work_dir),
                "env_analysis_needed": env_analysis_needed
            }
            
        except Exception as e:
            self.logger.error(f"Initialize phase failed: {e}")
            self.log_phase_end(success=False)
            raise
    
    def _create_labels(self) -> None:
        """Create required labels if they don't exist."""
        for name, description, color in self.REQUIRED_LABELS:
            if not self.github_client.check_label_exists(name):
                self.github_client.create_label(name, description, color)
                self.logger.info(f"Created label: {name}")
    
    def _create_branch(self, params: WorkflowParameters) -> None:
        """Create the working branch."""
        if self.github_client.branch_exists(params.branch_name):
            self.logger.warning(f"Branch {params.branch_name} already exists")
            # Checkout existing branch
            self.runner.run(["git", "fetch", "origin", params.branch_name])
            self.runner.run(["git", "checkout", params.branch_name])
        else:
            self.github_client.create_branch(params.branch_name)
            self.logger.info(f"Created branch: {params.branch_name}")
    
    def _initialize_work_directory(self, params: WorkflowParameters) -> Path:
        """Initialize the work directory structure."""
        work_dir = Path(f"claude/claude-work/issue-{params.issue_number}")
        
        # Create directory structure
        directories = [
            work_dir,
            work_dir / "dialogue",
            work_dir / "implementation",
            work_dir / "review"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        # Initialize workflow state
        state = WorkflowState(params.issue_number)
        state.initialize_state(params.__dict__)
        
        # Commit initialization
        self.github_client.commit_and_push(
            f"Initialize work directory for issue #{params.issue_number}"
        )
        
        return work_dir
    
    def _check_env_analysis_need(self, params: WorkflowParameters) -> bool:
        """Check if environment analysis is needed."""
        knowledge_dir = Path("claude/claude-knowledge/environment")
        
        # Check for essential knowledge files
        required_files = [
            "tech-stack.md",
            "project-structure.md",
            "coding-conventions.md"
        ]
        
        for file_name in required_files:
            if not (knowledge_dir / file_name).exists():
                self.logger.info(f"Environment analysis needed: {file_name} not found")
                return True
        
        # Check for last-updated.json
        last_updated_file = knowledge_dir / "last-updated.json"
        if last_updated_file.exists():
            import json
            from datetime import datetime, timedelta
            
            try:
                data = json.loads(last_updated_file.read_text())
                last_updated_str = data.get("last_updated") or data.get("timestamp", "")
                if last_updated_str:
                    last_updated = datetime.fromisoformat(last_updated_str.replace('Z', '+00:00'))
                    
                    # Re-analyze if older than 30 days
                    if datetime.utcnow() - last_updated > timedelta(days=30):
                        self.logger.info("Environment analysis needed: knowledge is outdated")
                        return True
            except Exception as e:
                self.logger.warning(f"Failed to parse last-updated.json: {e}")
                # If we can't parse it, assume we need to re-analyze
                return True
        
        self.logger.info("Environment analysis not needed")
        return False
    
    def _post_initial_comment(self, params: WorkflowParameters) -> None:
        """Post initial comment to the issue."""
        comment = f"""## 🚀 Claude Orchestrator 開始

issue #{params.issue_number} の作業を開始します。

**ブランチ**: `{params.branch_name}`
**モデル**: {params.model}

### 📋 ワークフロー設定
- 環境分析フェーズ 最大ターン数: {params.max_turns_env}
- Issue分析フェーズ 最大ターン数: {params.max_turns_analysis}
- 実装フェーズ 最大ターン数: {params.max_turns_implement}
- レビューフェーズ 最大ターン数: {params.max_turns_review}
- コーダー・レビュアー対話回数制限: {params.iterations_limit}

処理の進行状況は随時更新されます。
"""
        
        if params.prd_file_path:
            comment += f"\n**PRDファイル**: `{params.prd_file_path}`\n"
            
        self.github_client.create_issue_comment(params.issue_number, comment)
    
    def _set_outputs(self, params: WorkflowParameters, env_analysis_needed: bool) -> None:
        """Set GitHub Actions outputs."""
        outputs = {
            "branch_name": params.branch_name,
            "env_analysis_needed": str(env_analysis_needed).lower(),
            "work_dir": f"claude/claude-work/issue-{params.issue_number}"
        }
        
        for key, value in outputs.items():
            self.github.set_output(key, value)