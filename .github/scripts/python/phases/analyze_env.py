#!/usr/bin/env python3
"""Environment Analysis Phase - Analyze project structure and tech stack."""
import os
import sys
import logging
import json
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper, CommandRunner
from utils.github_client import GitHubClient
from utils.datetime_helper import now_jst

logger = logging.getLogger(__name__)


class AnalyzeEnvPhase(BasePhase):
    """Environment analysis phase implementation."""
    
    def __init__(self):
        super().__init__("analyze-env")
        self.github_client = GitHubClient()
        
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        """Run environment analysis phase."""
        self.log_phase_start()
        
        try:
            # Create work directories
            work_dir = self._create_work_directory(params.issue_number)
            dialogue_dir = work_dir / "dialogue"
            dialogue_dir.mkdir(exist_ok=True)
            
            # Perform actual environment analysis
            env_analysis = self._perform_environment_analysis()
            
            # Save environment analysis results
            env_file = dialogue_dir / "env-analysis.md"
            timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
            
            env_content = f"""# 環境分析結果

生成日時: {timestamp}
Issue: #{params.issue_number}

## プロジェクト概要
WordPress開発プロジェクト - Docker化されたローカル開発環境

{env_analysis}

## 分析完了
環境分析が完了しました。次のフェーズ（Issue分析）に進むことができます。
"""
            
            env_file.write_text(env_content)
            logger.info(f"Created environment analysis file: {env_file}")
            
            # Update workflow state
            self._update_workflow_state(params.issue_number, "analyze-env", {
                "completed": True,
                "timestamp": timestamp,
                "env_file": str(env_file)
            })
            
            # Post progress comment
            self._post_progress_comment(
                params.issue_number,
                "🔍 環境分析フェーズ完了",
                f"プロジェクト構造と技術スタックの分析が完了しました。\\n\\n主要な技術スタック：\\n- WordPress + Docker\\n- PHP 8.x\\n- Apache/Nginx\\n- MySQL/MariaDB\\n\\n詳細は作業ディレクトリの env-analysis.md を参照してください。"
            )
            
            self.log_phase_end(True)
            return {
                "success": True,
                "env_file": str(env_file),
                "timestamp": timestamp
            }
            
        except Exception as e:
            logger.error(f"Environment analysis failed: {e}")
            self.github.log_error(f"Environment analysis failed: {e}")
            self.log_phase_end(False)
            raise
    
    def _perform_environment_analysis(self) -> str:
        """Perform actual environment analysis."""
        analysis_parts = []
        
        # Check project structure
        try:
            analysis_parts.append("## プロジェクト構造")
            
            # Check for key files
            key_files = [
                "docker-compose.yml",
                "package.json", 
                "composer.json",
                "wp-config.php",
                "README.md"
            ]
            
            found_files = []
            for file in key_files:
                if Path(file).exists():
                    found_files.append(f"✅ {file}")
                else:
                    found_files.append(f"❌ {file}")
            
            analysis_parts.append("### 主要ファイル")
            analysis_parts.extend(found_files)
            
        except Exception as e:
            logger.warning(f"Failed to analyze project structure: {e}")
            analysis_parts.append("⚠️ プロジェクト構造の分析でエラーが発生しました")
        
        # Check Docker setup
        try:
            analysis_parts.append("\\n## Docker環境")
            
            if Path("docker-compose.yml").exists():
                analysis_parts.append("✅ Docker Compose設定ファイルあり")
                
                # Try to get docker info
                try:
                    result = CommandRunner.run(["docker", "--version"], check=False)
                    if result.returncode == 0:
                        analysis_parts.append(f"✅ Docker: {result.stdout.strip()}")
                    else:
                        analysis_parts.append("⚠️ Docker未インストールまたは実行不可")
                except:
                    analysis_parts.append("⚠️ Docker状態確認不可")
            else:
                analysis_parts.append("❌ Docker Compose設定なし")
                
        except Exception as e:
            logger.warning(f"Failed to analyze Docker setup: {e}")
            analysis_parts.append("⚠️ Docker環境の分析でエラーが発生しました")
        
        # Check dependencies
        try:
            analysis_parts.append("\\n## 依存関係")
            
            # Node.js dependencies
            if Path("package.json").exists():
                analysis_parts.append("✅ Node.js package.json あり")
                if Path("node_modules").exists():
                    analysis_parts.append("✅ node_modules インストール済み")
                else:
                    analysis_parts.append("⚠️ node_modules 未インストール")
            
            # PHP dependencies
            if Path("composer.json").exists():
                analysis_parts.append("✅ PHP composer.json あり")
                if Path("vendor").exists():
                    analysis_parts.append("✅ vendor ディレクトリあり")
                else:
                    analysis_parts.append("⚠️ vendor ディレクトリなし")
                    
        except Exception as e:
            logger.warning(f"Failed to analyze dependencies: {e}")
            analysis_parts.append("⚠️ 依存関係の分析でエラーが発生しました")
        
        return "\\n".join(analysis_parts)

    def _create_work_directory(self, issue_number: int) -> Path:
        """Create work directory for the issue."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        work_dir.mkdir(parents=True, exist_ok=True)
        return work_dir

    def _update_workflow_state(self, issue_number: int, phase: str, data: Dict[str, Any]):
        """Update workflow state file."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        state_file = work_dir / "workflow-state.json"
        
        # Load existing state
        state = {}
        if state_file.exists():
            with open(state_file) as f:
                state = json.load(f)
        
        # Update state
        if "phases" not in state:
            state["phases"] = {}
        
        state["phases"][phase] = data
        state["last_updated"] = now_jst().isoformat()
        
        # Save state
        state_file.parent.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Updated workflow state for phase: {phase}")

    def _post_progress_comment(self, issue_number: int, title: str, message: str):
        """Post progress comment to issue."""
        try:
            comment = f"## {title}\\n\\n{message}\\n\\n---\\n*Automated by Claude Orchestrator*"
            self.github_client.create_issue_comment(issue_number, comment)
        except Exception as e:
            logger.warning(f"Failed to post progress comment: {e}")


def main():
    """CLI entry point for environment analysis."""
    import click
    
    @click.command()
    @click.option('--issue-number', required=True, type=int, help='GitHub issue number')
    @click.option('--branch-name', required=True, help='Branch name')
    @click.option('--model', required=True, help='Model to use')
    @click.option('--max-turns', required=True, type=int, help='Maximum turns')
    def analyze_env(issue_number, branch_name, model, max_turns):
        """Run environment analysis phase."""
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_env=max_turns
        )
        
        phase = AnalyzeEnvPhase()
        result = phase.run(params)
        
        print(json.dumps(result, indent=2))
        
    analyze_env()


if __name__ == '__main__':
    main()