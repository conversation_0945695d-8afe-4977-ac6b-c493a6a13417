"""Finalize phase for <PERSON>."""
import os
import json
from pathlib import Path
from typing import Dict, Any
from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper
from utils.github_client import G<PERSON>H<PERSON><PERSON>lient
from utils.pr_creator import PRCreator


class FinalizePhase(BasePhase):
    """Finalize the workflow and create PR."""
    
    def __init__(self):
        super().__init__("Finalize")
        self.github_client = GitHubClient()
        
    def run(self, issue_number: int, branch_name: str, total_iterations: int) -> Dict[str, Any]:
        """Run the finalization phase."""
        self.log_phase_start()
        
        try:
            # Create PR
            pr_creator = PRCreator(
                issue_number=issue_number,
                repository=os.environ.get("GITHUB_REPOSITORY", ""),
                total_iterations=total_iterations
            )
            
            pr_data = pr_creator.create_pull_request(branch_name)
            
            # Update issue
            self._update_issue(issue_number, pr_data)
            
            # Create summary report
            summary = self._create_summary_report(issue_number, pr_data, total_iterations)
            
            self.log_phase_end(success=True)
            
            return {
                "success": True,
                "pr_number": pr_data["number"],
                "pr_url": pr_data["url"],
                "summary": summary
            }
            
        except Exception as e:
            self.logger.error(f"Finalize phase failed: {e}")
            self.log_phase_end(success=False)
            raise
    
    def _update_issue(self, issue_number: int, pr_data: Dict[str, Any]) -> None:
        """Update issue with completion status."""
        # Remove processing label and add done label
        self.github_client.remove_issue_labels(issue_number, ["claude-processing"])
        self.github_client.add_issue_labels(issue_number, ["claude-done"])
        
        # Post completion comment
        comment = f"""## ✅ Claude Orchestrator 完了

実装が完了し、プルリクエストを作成しました。

**プルリクエスト**: #{pr_data['number']}
**URL**: {pr_data['url']}

### 次のステップ
1. プルリクエストのレビュー
2. 必要に応じて修正
3. マージ

ご確認をお願いします。"""
        
        self.github_client.create_issue_comment(issue_number, comment)
    
    def _create_summary_report(self, issue_number: int, pr_data: Dict[str, Any], 
                              total_iterations: int) -> str:
        """Create summary report of the workflow execution."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        summary_file = work_dir / "summary.md"
        
        # Collect statistics
        stats = self._collect_statistics(work_dir, total_iterations)
        
        summary = f"""# Claude Orchestrator Summary - Issue #{issue_number}

## Overview
- **Pull Request**: #{pr_data['number']}
- **Total Iterations**: {total_iterations}
- **Files Changed**: {stats['files_changed']}
- **Lines Added**: {stats['lines_added']}
- **Lines Removed**: {stats['lines_removed']}

## Implementation Summary
{stats['implementation_summary']}

## Review Summary
{stats['review_summary']}

## Knowledge Updates
{stats['knowledge_updates']}

---
Generated by Claude Orchestrator
"""
        
        summary_file.write_text(summary)
        
        # Commit summary
        self.github_client.commit_and_push(
            f"Add workflow summary for issue #{issue_number}"
        )
        
        return summary
    
    def _collect_statistics(self, work_dir: Path, total_iterations: int) -> Dict[str, Any]:
        """Collect workflow execution statistics."""
        stats = {
            "files_changed": 0,
            "lines_added": 0,
            "lines_removed": 0,
            "implementation_summary": "",
            "review_summary": "",
            "knowledge_updates": ""
        }
        
        # Get git statistics
        result = self.runner.run([
            "git", "diff", "--shortstat", "origin/develop"
        ], check=False)
        
        if result.stdout:
            import re
            # Parse: "X files changed, Y insertions(+), Z deletions(-)"
            match = re.search(r'(\d+) files? changed', result.stdout)
            if match:
                stats["files_changed"] = int(match.group(1))
            
            match = re.search(r'(\d+) insertions?\(\+\)', result.stdout)
            if match:
                stats["lines_added"] = int(match.group(1))
                
            match = re.search(r'(\d+) deletions?\(-\)', result.stdout)
            if match:
                stats["lines_removed"] = int(match.group(1))
        
        # Summarize implementations
        impl_summaries = []
        for i in range(1, total_iterations + 1):
            impl_file = work_dir / "implementation" / f"iteration-{i}.md"
            if impl_file.exists():
                # Get first line as summary
                first_line = impl_file.read_text().split('\n')[0]
                impl_summaries.append(f"- Iteration {i}: {first_line}")
        
        stats["implementation_summary"] = '\n'.join(impl_summaries) if impl_summaries else "No implementation summaries found"
        
        # Summarize reviews
        review_summaries = []
        for i in range(1, total_iterations + 1):
            review_file = work_dir / "review" / f"iteration-{i}.json"
            if review_file.exists():
                try:
                    review_data = json.loads(review_file.read_text())
                    status = "Passed" if review_data.get('passed', False) else "Failed"
                    review_summaries.append(f"- Iteration {i}: {status}")
                except:
                    pass
        
        stats["review_summary"] = '\n'.join(review_summaries) if review_summaries else "No review summaries found"
        
        # Check for knowledge updates
        knowledge_file = work_dir / "knowledge-updates.md"
        if knowledge_file.exists():
            stats["knowledge_updates"] = knowledge_file.read_text()[:500] + "..."
        else:
            stats["knowledge_updates"] = "No knowledge updates recorded"
        
        return stats