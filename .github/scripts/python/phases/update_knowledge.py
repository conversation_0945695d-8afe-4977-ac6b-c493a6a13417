#!/usr/bin/env python3
"""Knowledge Update Phase - Update knowledge base with learnings."""
import os
import sys
import logging
import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper
from utils.github_client import GitHubClient
from utils.datetime_helper import now_jst

logger = logging.getLogger(__name__)


class UpdateKnowledgePhase(BasePhase):
    """Knowledge update phase implementation."""
    
    def __init__(self):
        super().__init__("update-knowledge")
        self.github_client = GitHubClient()
        
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        """Run knowledge update phase."""
        self.log_phase_start()
        
        try:
            # Create knowledge directories
            knowledge_base_dir = Path("claude/claude-knowledge")
            patterns_dir = knowledge_base_dir / "patterns"
            issues_dir = knowledge_base_dir / "issues"
            
            for directory in [knowledge_base_dir, patterns_dir, issues_dir]:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Initialize knowledge files if they don't exist
            self._initialize_knowledge_files(knowledge_base_dir)
            
            # Get workflow data
            work_dir = Path(f"claude/claude-work/issue-{params.issue_number}")
            workflow_data = self._extract_workflow_data(work_dir)
            
            # Update knowledge base
            knowledge_updates = self._update_knowledge_base(
                knowledge_base_dir, workflow_data, params
            )
            
            # Create issue-specific documentation
            issue_file = issues_dir / f"issue-{params.issue_number}.md"
            timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
            
            issue_content = f"""# Issue #{params.issue_number} 実装記録

更新日時: {timestamp}
タイトル: {workflow_data.get('title', 'N/A')}

## 概要
{workflow_data.get('summary', 'Issue実装の記録')}

## 実装内容
{workflow_data.get('implementation_summary', '実装が完了しました')}

## 学習した知見
{knowledge_updates.get('learnings', '特記事項なし')}

## 技術的考慮事項
{workflow_data.get('technical_notes', '技術的な知見を記録')}

## パターン化された要素
{knowledge_updates.get('patterns', '再利用可能なパターンを特定')}
"""
            
            issue_file.write_text(issue_content)
            logger.info(f"Created issue documentation: {issue_file}")
            
            # Update patterns if applicable
            pattern_file = None
            if knowledge_updates.get('create_pattern'):
                pattern_file = self._create_pattern_file(
                    patterns_dir, workflow_data, params
                )
            
            # Update workflow state
            self._update_workflow_state(params.issue_number, "update-knowledge", {
                "completed": True,
                "timestamp": timestamp,
                "issue_file": str(issue_file),
                "pattern_file": str(pattern_file) if pattern_file else None,
                "learnings_count": len(knowledge_updates.get('learnings_list', []))
            })
            
            # Post progress comment
            learning_count = len(knowledge_updates.get('learnings_list', []))
            pattern_msg = f"新しいパターンファイルを作成しました: {pattern_file.name}" if pattern_file else "既存パターンに統合しました"
            
            self._post_progress_comment(
                params.issue_number,
                "📚 ナレッジ更新完了",
                f"ワークフローから得られた知見をナレッジベースに反映しました。\\n\\n**学習項目**: {learning_count}個\\n**パターン**: {pattern_msg}\\n\\nナレッジベースが更新され、今後の開発に活用できます。"
            )
            
            self.log_phase_end(True)
            return {
                "success": True,
                "issue_file": str(issue_file),
                "pattern_file": str(pattern_file) if pattern_file else None,
                "learnings_count": learning_count,
                "timestamp": timestamp
            }
            
        except Exception as e:
            logger.error(f"Knowledge update failed: {e}")
            self.github.log_error(f"Knowledge update failed: {e}")
            self.log_phase_end(False)
            raise

    def _initialize_knowledge_files(self, knowledge_base_dir: Path):
        """Initialize knowledge base files if they don't exist."""
        files_to_create = {
            "implementation-patterns.md": """# 実装パターン

このファイルには、過去の実装から学んだ再利用可能なパターンを記録します。

## パターン一覧

### ファイル作成パターン
- Markdownファイル: ドキュメント作成時
- PHPファイル: WordPress機能実装時
- JavaScriptファイル: フロントエンド機能時

### エラーハンドリングパターン
- try-catch構文の適切な使用
- ログ出力の標準化
- ユーザーフレンドリーなエラーメッセージ

### テストパターン
- 単体テストの構造
- 統合テストの実装
- E2Eテストのシナリオ

## 更新履歴
- 初版作成
""",
            "troubleshooting.md": """# トラブルシューティング

このファイルには、過去に発生した問題とその解決策を記録します。

## よくある問題

### ワークフロー関連
- 依存関係のエラー
- 権限の問題
- タイムアウトエラー

### 実装関連
- コンフリクトの解決
- パフォーマンス問題
- セキュリティ考慮事項

## 解決策のパターン
- 問題の特定方法
- 調査手順
- 解決手順
- 予防策

## 更新履歴
- 初版作成
""",
            "best-practices.md": """# ベストプラクティス

このファイルには、効果的な開発・実装のベストプラクティスを記録します。

## コーディング規約
- 命名規則
- コメントの書き方
- ファイル構造

## ワークフロー
- ブランチ戦略
- コミットメッセージ
- レビュープロセス

## ドキュメント
- README.mdの構造
- APIドキュメント
- 実装ガイド

## 更新履歴
- 初版作成
"""
        }
        
        for filename, content in files_to_create.items():
            file_path = knowledge_base_dir / filename
            if not file_path.exists():
                file_path.write_text(content)
                logger.info(f"Created knowledge file: {filename}")

    def _extract_workflow_data(self, work_dir: Path) -> Dict[str, Any]:
        """Extract data from workflow execution."""
        data = {
            "title": "Unknown",
            "summary": "ワークフロー実行記録",
            "implementation_summary": "",
            "technical_notes": "",
            "tasks_completed": 0,
            "files_created": [],
            "errors_encountered": []
        }
        
        try:
            # Extract from task list
            task_file = work_dir / "dialogue" / "task-list.md"
            if task_file.exists():
                task_content = task_file.read_text()
                data["title"] = self._extract_title_from_task_file(task_content)
                data["summary"] = self._extract_summary_from_task_file(task_content)
                data["tasks_completed"] = task_content.count("✅")
            
            # Extract from implementation files
            impl_dir = work_dir / "implementation"
            if impl_dir.exists():
                impl_summary = []
                files_created = []
                
                for impl_file in impl_dir.glob("iteration-*.md"):
                    if impl_file.exists():
                        content = impl_file.read_text()
                        impl_summary.append(self._extract_implementation_summary(content))
                        files_created.extend(self._extract_files_from_implementation(content))
                
                data["implementation_summary"] = "\\n".join(impl_summary)
                data["files_created"] = files_created
            
            # Extract technical notes
            data["technical_notes"] = self._generate_technical_notes(data)
            
        except Exception as e:
            logger.warning(f"Failed to extract workflow data: {e}")
        
        return data

    def _extract_title_from_task_file(self, content: str) -> str:
        """Extract title from task file."""
        lines = content.split('\\n')
        for line in lines:
            if line.startswith('タイトル:'):
                return line.replace('タイトル:', '').strip()
        return "Unknown Task"

    def _extract_summary_from_task_file(self, content: str) -> str:
        """Extract summary from task file."""
        if "## 概要" in content:
            start = content.find("## 概要")
            end = content.find("##", start + 1)
            if end == -1:
                end = len(content)
            section = content[start:end]
            lines = section.split('\\n')[1:]  # Skip the header
            return ' '.join([line.strip() for line in lines if line.strip()])
        return "タスクリストから抽出された概要"

    def _extract_implementation_summary(self, content: str) -> str:
        """Extract implementation summary from content."""
        if "## 実装内容" in content:
            start = content.find("## 実装内容")
            end = content.find("##", start + 1)
            if end == -1:
                end = len(content)
            section = content[start:end]
            lines = section.split('\\n')[1:]  # Skip the header
            return ' '.join([line.strip() for line in lines[:3] if line.strip()])  # First 3 lines
        return "実装が完了しました"

    def _extract_files_from_implementation(self, content: str) -> List[str]:
        """Extract created files from implementation content."""
        files = []
        if "## 作成・変更されたファイル" in content:
            start = content.find("## 作成・変更されたファイル")
            end = content.find("##", start + 1)
            if end == -1:
                end = len(content)
            section = content[start:end]
            
            for line in section.split('\\n'):
                line = line.strip()
                if line and not line.startswith('#') and '.' in line:
                    files.append(line)
        return files

    def _generate_technical_notes(self, data: Dict[str, Any]) -> str:
        """Generate technical notes based on workflow data."""
        notes = []
        
        if data["files_created"]:
            file_types = set()
            for file in data["files_created"]:
                if '.' in file:
                    ext = file.split('.')[-1].lower()
                    file_types.add(ext)
            
            if file_types:
                notes.append(f"作成されたファイルタイプ: {', '.join(file_types)}")
        
        if data["tasks_completed"] > 0:
            notes.append(f"完了タスク数: {data['tasks_completed']}個")
        
        notes.append("実装はPython版ワークフローで自動実行されました")
        
        return '\\n'.join([f"- {note}" for note in notes])

    def _update_knowledge_base(self, knowledge_base_dir: Path, 
                             workflow_data: Dict[str, Any], 
                             params: WorkflowParameters) -> Dict[str, Any]:
        """Update knowledge base with new learnings."""
        
        learnings_list = []
        
        # Analyze if this creates a new pattern
        create_pattern = self._should_create_pattern(workflow_data)
        
        # Generate learnings
        if workflow_data["files_created"]:
            learnings_list.append(f"ファイル作成パターンの実践: {len(workflow_data['files_created'])}個のファイル")
        
        if workflow_data["tasks_completed"] > 3:
            learnings_list.append("複数タスクの効率的な実行")
        
        learnings_list.append("Python版ワークフローでの自動実装")
        learnings_list.append("GitHub Issues連携による進捗管理")
        
        return {
            "create_pattern": create_pattern,
            "learnings": '\\n'.join([f"- {learning}" for learning in learnings_list]),
            "learnings_list": learnings_list,
            "patterns": "ファイル作成、タスク実行、進捗報告のパターンを確認"
        }

    def _should_create_pattern(self, workflow_data: Dict[str, Any]) -> bool:
        """Determine if this workflow should create a new pattern."""
        # Create pattern if multiple files were created or complex implementation
        return (
            len(workflow_data.get("files_created", [])) >= 2 or
            workflow_data.get("tasks_completed", 0) >= 3
        )

    def _create_pattern_file(self, patterns_dir: Path, 
                           workflow_data: Dict[str, Any], 
                           params: WorkflowParameters) -> Path:
        """Create a new pattern file."""
        pattern_file = patterns_dir / f"pattern-issue-{params.issue_number}.md"
        timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
        
        content = f"""# パターン: {workflow_data.get('title', 'Issue実装')}

作成日時: {timestamp}
元Issue: #{params.issue_number}

## パターン概要
{workflow_data.get('summary', 'このパターンはIssue実装から抽出されました')}

## 実装手順
{self._generate_pattern_steps(workflow_data)}

## 作成されるファイル
{self._generate_file_patterns(workflow_data)}

## 適用条件
{self._generate_application_conditions(workflow_data)}

## 注意事項
- このパターンはPython版ワークフローで実行されました
- 類似のタスクに適用可能です
- 必要に応じてカスタマイズしてください

## 関連Issue
- #{params.issue_number}
"""
        
        pattern_file.write_text(content)
        logger.info(f"Created pattern file: {pattern_file}")
        return pattern_file

    def _generate_pattern_steps(self, workflow_data: Dict[str, Any]) -> str:
        """Generate pattern steps from workflow data."""
        steps = [
            "1. 要件の分析と理解",
            "2. タスクリストの作成",
            "3. 段階的な実装",
            "4. ファイルの作成・更新",
            "5. 実装内容の検証",
            "6. ドキュメントの更新"
        ]
        
        if workflow_data.get("files_created"):
            steps.append("7. 作成されたファイルの確認")
        
        return '\\n'.join(steps)

    def _generate_file_patterns(self, workflow_data: Dict[str, Any]) -> str:
        """Generate file creation patterns."""
        files = workflow_data.get("files_created", [])
        if not files:
            return "- 作成されたファイルなし"
        
        patterns = []
        for file in files[:5]:  # Limit to 5 files
            patterns.append(f"- {file}")
        
        return '\\n'.join(patterns)

    def _generate_application_conditions(self, workflow_data: Dict[str, Any]) -> str:
        """Generate conditions for applying this pattern."""
        conditions = [
            "- 類似の機能実装が必要な場合",
            "- 同様のファイル構造が求められる場合"
        ]
        
        if workflow_data.get("tasks_completed", 0) > 3:
            conditions.append("- 複数のタスクを順次実行する必要がある場合")
        
        return '\\n'.join(conditions)

    def _update_workflow_state(self, issue_number: int, phase: str, data: Dict[str, Any]):
        """Update workflow state file."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        state_file = work_dir / "workflow-state.json"
        
        # Load existing state
        state = {}
        if state_file.exists():
            with open(state_file) as f:
                state = json.load(f)
        
        # Update state
        if "phases" not in state:
            state["phases"] = {}
        
        state["phases"][phase] = data
        state["last_updated"] = now_jst().isoformat()
        
        # Save state
        state_file.parent.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Updated workflow state for phase: {phase}")

    def _post_progress_comment(self, issue_number: int, title: str, message: str):
        """Post progress comment to issue."""
        try:
            comment = f"## {title}\\n\\n{message}\\n\\n---\\n*Automated by Claude Orchestrator*"
            self.github_client.create_issue_comment(issue_number, comment)
        except Exception as e:
            logger.warning(f"Failed to post progress comment: {e}")


def main():
    """CLI entry point for knowledge update."""
    import click
    
    @click.command()
    @click.option('--issue-number', required=True, type=int, help='GitHub issue number')
    @click.option('--branch-name', required=True, help='Branch name')
    @click.option('--model', required=True, help='Model to use')
    @click.option('--max-turns', required=True, type=int, help='Maximum turns')
    def update_knowledge(issue_number, branch_name, model, max_turns):
        """Run knowledge update phase."""
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_review=max_turns
        )
        
        phase = UpdateKnowledgePhase()
        result = phase.run(params)
        
        print(json.dumps(result, indent=2))
        
    update_knowledge()


if __name__ == '__main__':
    main()