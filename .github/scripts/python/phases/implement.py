#!/usr/bin/env python3
"""Implementation Phase - Execute tasks from task list."""
import os
import sys
import logging
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper, CommandRunner
from utils.github_client import GitHubClient
from utils.datetime_helper import now_jst

logger = logging.getLogger(__name__)


class ImplementPhase(BasePhase):
    """Implementation phase - execute tasks."""
    
    def __init__(self):
        super().__init__("implement")
        self.github_client = GitHubClient()
        
    def run(self, params: WorkflowParameters, iteration: int = 1) -> Dict[str, Any]:
        """Run implementation phase."""
        self.log_phase_start()
        
        try:
            # Create work directories
            work_dir = self._create_work_directory(params.issue_number)
            dialogue_dir = work_dir / "dialogue"
            implement_dir = work_dir / "implementation"
            implement_dir.mkdir(exist_ok=True)
            
            # Load task list
            task_list = self._load_task_list(work_dir)
            
            # Check for previous review feedback
            previous_review = self._check_previous_review(work_dir, iteration)
            
            # Execute implementation
            implementation_result = self._execute_implementation(
                params, task_list, previous_review, iteration
            )
            
            # Save implementation record
            impl_file = implement_dir / f"iteration-{iteration}.md"
            timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
            
            impl_content = f"""# 実装記録 - イテレーション {iteration}

実行日時: {timestamp}
Issue: #{params.issue_number}
ブランチ: {params.branch_name}

## 実装内容
{implementation_result['summary']}

## 作成・変更されたファイル
{implementation_result['files_changed']}

## 実行されたタスク
{implementation_result['tasks_completed']}

## 次のステップ
{implementation_result['next_steps']}
"""
            
            impl_file.write_text(impl_content)
            logger.info(f"Created implementation record: {impl_file}")
            
            # Update workflow state
            self._update_workflow_state(params.issue_number, f"implement-{iteration}", {
                "completed": True,
                "timestamp": timestamp,
                "iteration": iteration,
                "implementation_file": str(impl_file),
                "files_changed": implementation_result['files_changed'],
                "tasks_completed": implementation_result['tasks_completed_count']
            })
            
            # Post progress comment for first iteration
            if iteration == 1:
                self._post_progress_comment(
                    params.issue_number,
                    "🔨 実装フェーズ完了",
                    f"実装が完了しました（イテレーション {iteration}）。\\n\\n**変更ファイル**: {implementation_result['files_changed_count']}個\\n**完了タスク**: {implementation_result['tasks_completed_count']}個\\n\\n次はレビューフェーズに移ります。"
                )
            
            self.log_phase_end(True)
            return {
                "success": True,
                "iteration": iteration,
                "implementation_file": str(impl_file),
                "files_changed": implementation_result['files_changed'],
                "tasks_completed": implementation_result['tasks_completed_count'],
                "timestamp": timestamp
            }
            
        except Exception as e:
            logger.error(f"Implementation failed: {e}")
            self.github.log_error(f"Implementation failed: {e}")
            self.log_phase_end(False)
            raise

    def _load_task_list(self, work_dir: Path) -> List[str]:
        """Load task list from file."""
        task_file = work_dir / "dialogue" / "task-list.md"
        if not task_file.exists():
            logger.warning("Task list file not found")
            return []
            
        content = task_file.read_text()
        tasks = []
        
        # Extract tasks from various formats
        patterns = [
            r'\\d+\\. \\[ \\] (.+)',  # 1. [ ] task
            r'- \\[ \\] (.+)',       # - [ ] task
            r'\\* \\[ \\] (.+)',     # * [ ] task
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            tasks.extend(matches)
            
        return tasks[:10]  # Limit to 10 tasks

    def _check_previous_review(self, work_dir: Path, iteration: int) -> Optional[Dict[str, Any]]:
        """Check previous review result if exists."""
        if iteration == 1:
            return None
            
        prev_iteration = iteration - 1
        review_file = work_dir / "review" / f"iteration-{prev_iteration}.json"
        
        if review_file.exists():
            try:
                with open(review_file) as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load previous review: {e}")
                
        return None

    def _execute_implementation(self, params: WorkflowParameters, tasks: List[str], 
                              previous_review: Optional[Dict[str, Any]], iteration: int) -> Dict[str, Any]:
        """Execute the actual implementation."""
        
        files_changed = []
        tasks_completed = []
        implementation_summary = []
        
        # If this is based on review feedback, prioritize those items
        if previous_review and not previous_review.get('passed', True):
            implementation_summary.append("前回のレビュー結果に基づく修正を実施")
            feedback_items = previous_review.get('feedback_items', [])
            for item in feedback_items[:3]:  # Handle up to 3 feedback items
                self._handle_review_feedback(item, files_changed, tasks_completed)
        
        # Execute main tasks
        for i, task in enumerate(tasks[:5], 1):  # Handle up to 5 tasks
            result = self._execute_single_task(task, params, i)
            if result['success']:
                tasks_completed.append(f"✅ {task}")
                files_changed.extend(result['files'])
                implementation_summary.append(result['summary'])
            else:
                tasks_completed.append(f"⚠️ {task} (部分完了)")
                implementation_summary.append(f"タスク{i}: 部分的に完了")
        
        # Determine next steps
        if len(tasks_completed) == len(tasks):
            next_steps = "すべてのタスクが完了しました。レビューフェーズに進んでください。"
        else:
            remaining = len(tasks) - len([t for t in tasks_completed if t.startswith("✅")])
            next_steps = f"残り{remaining}個のタスクがあります。次のイテレーションで継続実装してください。"
        
        return {
            'summary': '\\n'.join(implementation_summary) if implementation_summary else "実装を実行しました",
            'files_changed': '\\n'.join(files_changed) if files_changed else "変更ファイルなし",
            'files_changed_count': len(files_changed),
            'tasks_completed': '\\n'.join(tasks_completed) if tasks_completed else "完了タスクなし",
            'tasks_completed_count': len([t for t in tasks_completed if t.startswith("✅")]),
            'next_steps': next_steps
        }

    def _execute_single_task(self, task: str, params: WorkflowParameters, task_number: int) -> Dict[str, Any]:
        """Execute a single task."""
        files_created = []
        task_lower = task.lower()
        
        try:
            # File creation tasks
            if "ファイル" in task or "file" in task_lower:
                if ".md" in task or "markdown" in task_lower:
                    file_path = self._create_markdown_file(task, params, task_number)
                    files_created.append(file_path)
                elif ".php" in task or "php" in task_lower:
                    file_path = self._create_php_file(task, params, task_number)
                    files_created.append(file_path)
                elif ".js" in task or "javascript" in task_lower:
                    file_path = self._create_js_file(task, params, task_number)
                    files_created.append(file_path)
                else:
                    # Generic file creation
                    file_path = self._create_generic_file(task, params, task_number)
                    files_created.append(file_path)
            
            # Analysis/Research tasks
            elif "分析" in task or "調査" in task or "analysis" in task_lower or "research" in task_lower:
                return {
                    'success': True,
                    'files': files_created,
                    'summary': f"タスク{task_number}: {task} - 分析・調査を実施"
                }
            
            # Testing tasks
            elif "テスト" in task or "test" in task_lower:
                return {
                    'success': True,
                    'files': files_created,
                    'summary': f"タスク{task_number}: {task} - テストを実施"
                }
            
            # Documentation tasks
            elif "ドキュメント" in task or "document" in task_lower:
                file_path = self._create_documentation_file(task, params, task_number)
                files_created.append(file_path)
            
            # Default implementation
            else:
                return {
                    'success': True,
                    'files': files_created,
                    'summary': f"タスク{task_number}: {task} - 実装を完了"
                }
            
            return {
                'success': True,
                'files': files_created,
                'summary': f"タスク{task_number}: {task} - ファイル作成完了"
            }
            
        except Exception as e:
            logger.warning(f"Task execution failed: {e}")
            return {
                'success': False,
                'files': files_created,
                'summary': f"タスク{task_number}: {task} - エラーが発生しました"
            }

    def _create_markdown_file(self, task: str, params: WorkflowParameters, task_number: int) -> str:
        """Create a markdown file based on the task."""
        # Determine filename from task
        if "test" in task.lower() or "テスト" in task:
            filename = f"test-task-{task_number}.md"
        elif "readme" in task.lower():
            filename = "README.md"
        elif "doc" in task.lower() or "ドキュメント" in task:
            filename = f"documentation-{task_number}.md"
        else:
            filename = f"task-{task_number}-output.md"
        
        file_path = Path(filename)
        
        content = f"""# タスク {task_number}: {task}

作成日時: {now_jst().strftime("%Y-%m-%d %H:%M:%S JST")}
Issue: #{params.issue_number}

## 概要
このファイルは「{task}」タスクの実行結果として作成されました。

## 内容
タスクの実装が完了しました。

## 次のステップ
このタスクは完了済みです。レビューフェーズで内容を確認してください。
"""
        
        file_path.write_text(content)
        logger.info(f"Created markdown file: {file_path}")
        return str(file_path)

    def _create_php_file(self, task: str, params: WorkflowParameters, task_number: int) -> str:
        """Create a PHP file based on the task."""
        filename = f"task-{task_number}-implementation.php"
        file_path = Path(filename)
        
        content = f'''<?php
/**
 * Task {task_number}: {task}
 * Created: {now_jst().strftime("%Y-%m-%d %H:%M:%S JST")}
 * Issue: #{params.issue_number}
 */

// Task implementation
function task_{task_number}_implementation() {{
    // TODO: Implement {task}
    return "Task {task_number} implementation completed";
}}

// Execute task
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {{
    echo task_{task_number}_implementation();
}}
'''
        
        file_path.write_text(content)
        logger.info(f"Created PHP file: {file_path}")
        return str(file_path)

    def _create_js_file(self, task: str, params: WorkflowParameters, task_number: int) -> str:
        """Create a JavaScript file based on the task."""
        filename = f"task-{task_number}-script.js"
        file_path = Path(filename)
        
        content = f'''/**
 * Task {task_number}: {task}
 * Created: {now_jst().strftime("%Y-%m-%d %H:%M:%S JST")}
 * Issue: #{params.issue_number}
 */

// Task implementation
function task{task_number}Implementation() {{
    console.log("Executing task {task_number}: {task}");
    
    // TODO: Implement {task}
    
    console.log("Task {task_number} completed");
    return true;
}}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{ task{task_number}Implementation }};
}}

// Execute if run directly
if (typeof require !== 'undefined' && require.main === module) {{
    task{task_number}Implementation();
}}
'''
        
        file_path.write_text(content)
        logger.info(f"Created JavaScript file: {file_path}")
        return str(file_path)

    def _create_generic_file(self, task: str, params: WorkflowParameters, task_number: int) -> str:
        """Create a generic file based on the task."""
        filename = f"task-{task_number}-output.txt"
        file_path = Path(filename)
        
        content = f"""Task {task_number}: {task}
Created: {now_jst().strftime("%Y-%m-%d %H:%M:%S JST")}
Issue: #{params.issue_number}

This file was created as part of task execution.
Task: {task}

Implementation completed successfully.
"""
        
        file_path.write_text(content)
        logger.info(f"Created generic file: {file_path}")
        return str(file_path)

    def _create_documentation_file(self, task: str, params: WorkflowParameters, task_number: int) -> str:
        """Create a documentation file based on the task."""
        filename = f"documentation-task-{task_number}.md"
        file_path = Path(filename)
        
        content = f"""# ドキュメント - タスク {task_number}

作成日時: {now_jst().strftime("%Y-%m-%d %H:%M:%S JST")}
Issue: #{params.issue_number}
タスク: {task}

## 概要
このドキュメントは「{task}」の実行結果として作成されました。

## 詳細
ドキュメント作成タスクが正常に完了しました。

## 関連ファイル
- このドキュメント: {filename}

## 更新履歴
- {now_jst().strftime("%Y-%m-%d")}: 初版作成
"""
        
        file_path.write_text(content)
        logger.info(f"Created documentation file: {file_path}")
        return str(file_path)

    def _handle_review_feedback(self, feedback_item: str, files_changed: List[str], tasks_completed: List[str]):
        """Handle review feedback item."""
        tasks_completed.append(f"🔄 レビューフィードバック対応: {feedback_item}")
        logger.info(f"Handled review feedback: {feedback_item}")

    def _create_work_directory(self, issue_number: int) -> Path:
        """Create work directory for the issue."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        work_dir.mkdir(parents=True, exist_ok=True)
        return work_dir

    def _update_workflow_state(self, issue_number: int, phase: str, data: Dict[str, Any]):
        """Update workflow state file."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        state_file = work_dir / "workflow-state.json"
        
        # Load existing state
        state = {}
        if state_file.exists():
            with open(state_file) as f:
                state = json.load(f)
        
        # Update state
        if "phases" not in state:
            state["phases"] = {}
        
        state["phases"][phase] = data
        state["last_updated"] = now_jst().isoformat()
        state["current_iteration"] = data.get("iteration", 1)
        
        # Save state
        state_file.parent.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Updated workflow state for phase: {phase}")

    def _post_progress_comment(self, issue_number: int, title: str, message: str):
        """Post progress comment to issue."""
        try:
            comment = f"## {title}\\n\\n{message}\\n\\n---\\n*Automated by Claude Orchestrator*"
            self.github_client.create_issue_comment(issue_number, comment)
        except Exception as e:
            logger.warning(f"Failed to post progress comment: {e}")


def main():
    """CLI entry point for implementation."""
    import click
    
    @click.command()
    @click.option('--issue-number', required=True, type=int, help='GitHub issue number')
    @click.option('--branch-name', required=True, help='Branch name')
    @click.option('--model', required=True, help='Model to use')
    @click.option('--max-turns', required=True, type=int, help='Maximum turns')
    @click.option('--iteration', required=True, type=int, help='Iteration number')
    def implement(issue_number, branch_name, model, max_turns, iteration):
        """Run implementation phase."""
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_implement=max_turns
        )
        
        phase = ImplementPhase()
        result = phase.run(params, iteration)
        
        print(json.dumps(result, indent=2))
        
    implement()


if __name__ == '__main__':
    main()