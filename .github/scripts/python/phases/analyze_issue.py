#!/usr/bin/env python3
"""Issue Analysis Phase - Analyze issue and create task list."""
import os
import sys
import logging
import json
import re
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.base import BasePhase, WorkflowParameters, GitHubActionsHelper
from utils.github_client import GitHubClient
from utils.datetime_helper import now_jst

logger = logging.getLogger(__name__)


class AnalyzeIssuePhase(BasePhase):
    """Issue analysis phase implementation."""
    
    def __init__(self):
        super().__init__("analyze-issue")
        self.github_client = GitHubClient()
        
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        """Run issue analysis phase."""
        self.log_phase_start()
        
        try:
            # Create work directories
            work_dir = self._create_work_directory(params.issue_number)
            dialogue_dir = work_dir / "dialogue"
            dialogue_dir.mkdir(exist_ok=True)
            
            # Get issue details
            issue_data = self._get_issue_details(params.issue_number)
            
            # Analyze issue and create task list
            task_list = self._analyze_issue_and_create_tasks(issue_data)
            
            # Save task list
            task_file = dialogue_dir / "task-list.md"
            timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
            
            task_content = f"""# タスクリスト - Issue #{params.issue_number}

生成日時: {timestamp}
タイトル: {issue_data.get('title', 'N/A')}

## 概要
{self._extract_issue_summary(issue_data)}

## タスク一覧
{task_list}

## 技術的考慮事項
{self._extract_technical_considerations(issue_data)}

## 依存関係
{self._analyze_task_dependencies(task_list)}
"""
            
            task_file.write_text(task_content)
            logger.info(f"Created task list file: {task_file}")
            
            # Update workflow state
            self._update_workflow_state(params.issue_number, "analyze-issue", {
                "completed": True,
                "timestamp": timestamp,
                "task_file": str(task_file),
                "task_count": self._count_tasks(task_content)
            })
            
            # Post progress comment
            task_count = self._count_tasks(task_content)
            self._post_progress_comment(
                params.issue_number,
                "📋 Issue分析フェーズ完了",
                f"Issue分析が完了し、{task_count}個のタスクを特定しました。\\n\\n次は実装フェーズに移ります。詳細なタスクリストは作業ディレクトリの task-list.md を参照してください。"
            )
            
            self.log_phase_end(True)
            return {
                "success": True,
                "task_file": str(task_file),
                "task_count": task_count,
                "timestamp": timestamp
            }
            
        except Exception as e:
            logger.error(f"Issue analysis failed: {e}")
            self.github.log_error(f"Issue analysis failed: {e}")
            self.log_phase_end(False)
            raise

    def _get_issue_details(self, issue_number: int) -> Dict[str, Any]:
        """Get issue details from GitHub."""
        try:
            return self.github_client.get_issue(issue_number)
        except Exception as e:
            logger.warning(f"Failed to get issue details: {e}")
            return {
                "title": f"Issue #{issue_number}",
                "body": "Issue詳細の取得に失敗しました",
                "labels": []
            }

    def _analyze_issue_and_create_tasks(self, issue_data: Dict[str, Any]) -> str:
        """Analyze issue content and create task list."""
        tasks = []
        issue_body = issue_data.get('body', '')
        issue_title = issue_data.get('title', '')
        
        # Extract existing tasks from issue body
        existing_tasks = self._extract_tasks_from_text(issue_body)
        if existing_tasks:
            tasks.extend(existing_tasks)
        else:
            # Generate tasks based on issue content
            tasks = self._generate_tasks_from_content(issue_title, issue_body)
        
        # Format tasks as markdown
        task_list = []
        for i, task in enumerate(tasks, 1):
            task_list.append(f"{i}. [ ] {task}")
            
        return "\\n".join(task_list)

    def _extract_tasks_from_text(self, text: str) -> List[str]:
        """Extract existing task items from text."""
        tasks = []
        
        # Look for various task formats
        patterns = [
            r'- \[ \] (.+)',  # - [ ] task
            r'- (.+)',        # - task  
            r'\* (.+)',       # * task
            r'\d+\. (.+)',    # 1. task
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            tasks.extend(matches)
            
        # Clean up tasks
        cleaned_tasks = []
        for task in tasks:
            task = task.strip()
            if task and len(task) > 3:  # Filter out very short items
                cleaned_tasks.append(task)
                
        return cleaned_tasks[:10]  # Limit to 10 tasks

    def _generate_tasks_from_content(self, title: str, body: str) -> List[str]:
        """Generate tasks based on issue title and body."""
        tasks = []
        
        # Basic implementation tasks based on common patterns
        if "実装" in title or "implementation" in title.lower():
            tasks.append("要件の詳細分析")
            tasks.append("実装方針の決定")
            tasks.append("コードの実装")
            tasks.append("テストの作成")
            tasks.append("動作確認")
            
        elif "修正" in title or "fix" in title.lower() or "bug" in title.lower():
            tasks.append("問題の詳細調査")
            tasks.append("根本原因の特定")
            tasks.append("修正の実装")
            tasks.append("修正の検証")
            tasks.append("リグレッションテスト")
            
        elif "テスト" in title or "test" in title.lower():
            tasks.append("テスト環境の準備")
            tasks.append("テストケースの作成")
            tasks.append("テストの実行")
            tasks.append("結果の検証")
            tasks.append("レポートの作成")
            
        else:
            # Generic tasks
            tasks.append("要件の確認")
            tasks.append("技術調査")
            tasks.append("実装")
            tasks.append("テスト")
            tasks.append("ドキュメント更新")
        
        # Add file-specific task if mentioned in body
        if ".md" in body or "markdown" in body.lower():
            tasks.append("Markdownファイルの作成/更新")
        if ".php" in body or "php" in body.lower():
            tasks.append("PHPファイルの作成/更新")
        if ".js" in body or "javascript" in body.lower():
            tasks.append("JavaScriptファイルの作成/更新")
            
        return tasks

    def _extract_issue_summary(self, issue_data: Dict[str, Any]) -> str:
        """Extract summary from issue."""
        body = issue_data.get('body', '')
        title = issue_data.get('title', '')
        
        # Extract first paragraph as summary
        lines = body.split('\\n')
        summary_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('-') and not line.startswith('*'):
                summary_lines.append(line)
                if len(summary_lines) >= 3:  # Limit to 3 lines
                    break
                    
        if summary_lines:
            return ' '.join(summary_lines)
        else:
            return f"Issue: {title}"

    def _extract_technical_considerations(self, issue_data: Dict[str, Any]) -> str:
        """Extract technical considerations from issue."""
        body = issue_data.get('body', '')
        considerations = []
        
        # Look for technical keywords
        tech_keywords = ['api', 'database', 'docker', 'php', 'javascript', 'css', 'html', 'wordpress', 'mysql']
        found_tech = []
        
        for keyword in tech_keywords:
            if keyword.lower() in body.lower():
                found_tech.append(keyword.upper())
                
        if found_tech:
            considerations.append(f"関連技術: {', '.join(found_tech)}")
            
        # Add generic considerations
        considerations.extend([
            "既存コードとの互換性を確保",
            "パフォーマンスへの影響を考慮",
            "セキュリティ要件の確認"
        ])
        
        return '\\n'.join([f"- {item}" for item in considerations])

    def _analyze_task_dependencies(self, task_list: str) -> str:
        """Analyze task dependencies."""
        tasks = task_list.split('\\n')
        if len(tasks) <= 1:
            return "- 依存関係なし"
            
        dependencies = []
        dependencies.append(f"- タスク1は他のすべてのタスクの前提条件")
        
        if len(tasks) >= 3:
            dependencies.append(f"- タスク2,3は並行して実施可能")
            
        if len(tasks) >= 4:
            dependencies.append(f"- タスク4はタスク3の完了後に実施")
            
        if len(tasks) >= 5:
            dependencies.append(f"- 最終タスクは他のすべてのタスクの完了後に実施")
            
        return '\\n'.join(dependencies)

    def _count_tasks(self, content: str) -> int:
        """Count tasks in content."""
        return content.count('- [ ]') + content.count('- [x]') + len(re.findall(r'\\d+\\. \\[ \\]', content))

    def verify_task_list(self, work_dir: Path) -> Dict[str, Any]:
        """Verify task list was created properly."""
        task_file = work_dir / "dialogue" / "task-list.md"
        
        if not task_file.exists():
            raise FileNotFoundError("Task list file not found")
            
        # Count tasks
        content = task_file.read_text()
        task_count = self._count_tasks(content)
        
        self.github.log_notice(f"Found {task_count} tasks")
        self.github.set_output('task_count', str(task_count))
        
        if task_count == 0:
            raise ValueError("No tasks found in task list")
            
        return {
            "task_count": task_count,
            "task_file": str(task_file)
        }

    def _create_work_directory(self, issue_number: int) -> Path:
        """Create work directory for the issue."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        work_dir.mkdir(parents=True, exist_ok=True)
        return work_dir

    def _update_workflow_state(self, issue_number: int, phase: str, data: Dict[str, Any]):
        """Update workflow state file."""
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        state_file = work_dir / "workflow-state.json"
        
        # Load existing state
        state = {}
        if state_file.exists():
            with open(state_file) as f:
                state = json.load(f)
        
        # Update state
        if "phases" not in state:
            state["phases"] = {}
        
        state["phases"][phase] = data
        state["last_updated"] = now_jst().isoformat()
        
        # Save state
        state_file.parent.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Updated workflow state for phase: {phase}")

    def _post_progress_comment(self, issue_number: int, title: str, message: str):
        """Post progress comment to issue."""
        try:
            comment = f"## {title}\\n\\n{message}\\n\\n---\\n*Automated by Claude Orchestrator*"
            self.github_client.create_issue_comment(issue_number, comment)
        except Exception as e:
            logger.warning(f"Failed to post progress comment: {e}")


def main():
    """CLI entry point for issue analysis."""
    import click
    
    @click.command()
    @click.option('--issue-number', required=True, type=int, help='GitHub issue number')
    @click.option('--branch-name', required=True, help='Branch name')
    @click.option('--model', required=True, help='Model to use')
    @click.option('--max-turns', required=True, type=int, help='Maximum turns')
    def analyze_issue(issue_number, branch_name, model, max_turns):
        """Run issue analysis phase."""
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_analysis=max_turns
        )
        
        phase = AnalyzeIssuePhase()
        result = phase.run(params)
        
        print(json.dumps(result, indent=2))
        
    analyze_issue()


if __name__ == '__main__':
    main()