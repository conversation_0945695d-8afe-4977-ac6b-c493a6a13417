"""State updater mixin for phases to update workflow state."""
from typing import Optional
from utils.workflow_state import WorkflowState


class StateUpdaterMixin:
    """Mixin to add state update capability to phases."""
    
    def update_workflow_state(self, issue_number: int, phase_name: str) -> None:
        """Update workflow state after phase completion."""
        state = WorkflowState(issue_number)
        state.update_phase_completion(phase_name)
    
    def update_iteration_state(self, issue_number: int, iteration: int) -> None:
        """Update current iteration in workflow state."""
        state = WorkflowState(issue_number)
        state.update_current_iteration(iteration)