"""Base classes and utilities for Claude Orchestrator."""
import os
import sys
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class WorkflowParameters:
    """Workflow parameters extracted from issue."""
    issue_number: int
    issue_title: str
    issue_body: str
    branch_name: str
    model: str = "claude-3-5-sonnet-20241022"
    max_turns_env: int = 80
    max_turns_analysis: int = 80
    max_turns_implement: int = 80
    max_turns_review: int = 80
    iterations_limit: int = 10
    prd_file_path: Optional[str] = None
    task_complexity: str = "standard"
    labels: List[str] = field(default_factory=list)


@dataclass
class IssueData:
    """GitHub issue data structure."""
    number: int
    title: str
    body: str
    state: str
    labels: List[str]
    created_at: str
    updated_at: str


class GitHubActionsHelper:
    """Helper class for GitHub Actions specific functionality."""
    
    @staticmethod
    def set_output(name: str, value: str) -> None:
        """Set GitHub Actions output."""
        output_file = os.environ.get("GITHUB_OUTPUT")
        if output_file:
            with open(output_file, "a") as f:
                # Handle multiline values
                if "\n" in value:
                    f.write(f"{name}<<EOF\n{value}\nEOF\n")
                else:
                    f.write(f"{name}={value}\n")
        else:
            print(f"::set-output name={name}::{value}")
    
    @staticmethod
    def set_env(name: str, value: str) -> None:
        """Set GitHub Actions environment variable."""
        env_file = os.environ.get("GITHUB_ENV")
        if env_file:
            with open(env_file, "a") as f:
                # Handle multiline values
                if "\n" in value:
                    f.write(f"{name}<<EOF\n{value}\nEOF\n")
                else:
                    f.write(f"{name}={value}\n")
        else:
            print(f"::set-env name={name}::{value}")
    
    @staticmethod
    def add_mask(value: str) -> None:
        """Mask a value in logs."""
        print(f"::add-mask::{value}")
    
    @staticmethod
    def log_debug(message: str) -> None:
        """Log debug message."""
        print(f"::debug::{message}")
    
    @staticmethod
    def log_notice(message: str) -> None:
        """Log notice message."""
        print(f"::notice::{message}")
    
    @staticmethod
    def log_warning(message: str) -> None:
        """Log warning message."""
        print(f"::warning::{message}")
    
    @staticmethod
    def log_error(message: str) -> None:
        """Log error message."""
        print(f"::error::{message}")
    
    @staticmethod
    def start_group(title: str) -> None:
        """Start a log group."""
        print(f"::group::{title}")
    
    @staticmethod
    def end_group() -> None:
        """End a log group."""
        print("::endgroup::")


class CommandRunner:
    """Safe command execution wrapper."""
    
    @staticmethod
    def run(cmd: List[str], check: bool = True, capture_output: bool = True, 
            text: bool = True, **kwargs) -> subprocess.CompletedProcess:
        """Run a command safely with logging."""
        logger.debug(f"Running command: {' '.join(cmd)}")
        try:
            result = subprocess.run(
                cmd, 
                check=check, 
                capture_output=capture_output,
                text=text,
                **kwargs
            )
            if result.stdout:
                logger.debug(f"Command output: {result.stdout}")
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {e}")
            if e.stdout:
                logger.error(f"stdout: {e.stdout}")
            if e.stderr:
                logger.error(f"stderr: {e.stderr}")
            raise
    
    @staticmethod
    def run_gh_cli(args: List[str], **kwargs) -> Dict[str, Any]:
        """Run GitHub CLI command and return JSON output."""
        cmd = ["gh"] + args + ["--json"]
        result = CommandRunner.run(cmd, **kwargs)
        try:
            return json.loads(result.stdout) if result.stdout else {}
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON output: {e}")
            logger.error(f"Raw output: {result.stdout}")
            return {}


class WorkflowError(Exception):
    """Custom exception for workflow errors."""
    pass


class BasePhase:
    """Base class for workflow phases."""
    
    def __init__(self, phase_name: str):
        self.phase_name = phase_name
        self.logger = logging.getLogger(f"{__name__}.{phase_name}")
        self.github = GitHubActionsHelper()
        self.runner = CommandRunner()
        
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        """Run the phase. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement run()")
    
    def log_phase_start(self) -> None:
        """Log phase start."""
        self.logger.info(f"Starting {self.phase_name} phase")
        self.github.log_notice(f"Starting {self.phase_name} phase")
        
    def log_phase_end(self, success: bool = True) -> None:
        """Log phase end."""
        status = "completed successfully" if success else "failed"
        self.logger.info(f"{self.phase_name} phase {status}")
        if success:
            self.github.log_notice(f"{self.phase_name} phase {status}")
        else:
            self.github.log_error(f"{self.phase_name} phase {status}")