# Claude Orchestrator 再開・修正機能 使用ガイド

## 概要
Claude Orchestratorは、作業の途中から再開したり、完了した作業を修正したりする機能をサポートしています。

## 使用方法

### 1. 作業の再開（Resume）
ワークフローが途中で停止した場合、以下のコメントで再開できます：

```
@claude-task resume
```

**使用例：**
- エラーで中断した場合
- タイムアウトした場合
- 手動で停止した後に再開したい場合

### 2. 作業の修正（Revise）
PR作成後に追加の修正が必要な場合：

```
@claude-task revise
追加で以下の修正をお願いします：
- ボタンの色を青に変更
- エラーメッセージを日本語に修正
```

### 3. 作業の継続（Continue）
最後の状態から自動的に継続：

```
@claude-task continue
```

## 動作の仕組み

### 状態の保存
各フェーズが完了するたびに、以下の情報が保存されます：
- 完了したフェーズ
- 現在のイテレーション番号
- ワークフローパラメータ
- ブランチ名
- PR情報（作成済みの場合）

保存場所：`claude/claude-work/issue-{番号}/state/workflow-state.json`

### 再開時の動作
1. 保存された状態を読み込み
2. 次に実行すべきフェーズを判定
3. 必要なパラメータを復元
4. 中断した箇所から処理を再開

### 修正モードの動作
1. 既存のPRブランチをチェックアウト
2. 修正内容に基づいて変更を実施
3. 同一PRに変更をプッシュ
4. レビュー結果を更新

## 状態確認方法

現在の作業状態を確認するには：

```bash
# ローカルで実行
python .github/scripts/python/orchestrator.py load-state --issue-number 123
```

出力例：
```
## Workflow State Summary

**Issue**: #123
**Branch**: claude-task/issue-123
**Mode**: resume
**Current Phase**: implement
**Current Iteration**: 2

### Phases Completed:
- ✅ initialize
- ✅ analyze-env
- ✅ analyze-issue

### Parameters:
- Model: claude-3-5-sonnet-20241022
- Iterations Limit: 10

### PR Status:
- Created: No
- PR Number: N/A

### Timestamps:
- Created: 2024-06-10T10:00:00Z
- Last Update: 2024-06-10T11:30:00Z
```

## トラブルシューティング

### 状態ファイルが見つからない場合
- 初回実行時は `@claude-task` のみでスタート
- 再開機能は2回目以降の実行で利用可能

### 再開できない場合
- ブランチが削除されていないか確認
- 権限エラーがないか確認
- ワークフローログでエラーを確認

### 修正モードでPRが見つからない場合
- PRが既にマージ/クローズされていないか確認
- 正しいIssue番号を指定しているか確認

## 注意事項

1. **同時実行の防止**
   - 同一Issueに対する複数の同時実行は防止されます
   - 実行中の場合は完了を待ってから再実行してください

2. **状態の整合性**
   - 手動でブランチやファイルを変更した場合、状態が不整合になる可能性があります
   - その場合は新規で `@claude-task` を実行してください

3. **リトライ制限**
   - 同じエラーで繰り返し失敗する場合は、Issue内容を見直してください
   - 必要に応じてパラメータを調整してください