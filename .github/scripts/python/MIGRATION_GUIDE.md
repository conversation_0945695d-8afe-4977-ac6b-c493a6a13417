# Claude Orchestrator Python版 移行ガイド

## 現在の作業状況

### 完了済みタスク

1. **ブランチ作成**
   - `feature/python-workflow-migration` ブランチで作業中
   - developブランチから分岐

2. **基本構造の実装**
   - Pythonパッケージ構造の設計と実装
   - コアモジュール（base.py）の作成
   - CLIインターフェース（orchestrator.py）の実装

3. **機能実装**
   - トリガーチェック機能
   - パラメータ解析（Issueからの情報抽出）
   - 初期化フェーズ
   - 最終化フェーズとPR作成
   - GitHub API操作ラッパー

4. **ワークフローファイル**
   - メインワークフロー（claude-orchestrator-main-python.yml）
   - 初期化フェーズ（claude-phase-initialize-python.yml）
   - 最終化フェーズ（claude-phase-finalize-python.yml）

5. **ドキュメント**
   - README.md
   - SPECIFICATION.md（仕様書）
   - MIGRATION_GUIDE.md（このファイル）

### 未実装タスク

1. **残りのフェーズ**
   - 環境分析フェーズ（analyze-env）
   - Issue分析フェーズ（analyze-issue）
   - 実装フェーズ（implement）
   - レビューフェーズ（review）
   - ナレッジ更新フェーズ（update-knowledge）

2. **テスト**
   - 単体テストの作成
   - 統合テストの実施
   - エラーケースのテスト

3. **本番移行**
   - 動作確認
   - 段階的な切り替え

## 次の作業手順

### 1. 未実装フェーズの移植

各フェーズの既存Bashスクリプトを参考にPython実装を作成：

#### 環境分析フェーズ（analyze-env）
```python
# phases/analyze_env.py
class AnalyzeEnvPhase(BasePhase):
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        # 環境分析の実装
        pass
```

対応するワークフロー：
```yaml
# claude-phase-analyze-env-python.yml
```

#### Issue分析フェーズ（analyze-issue）
```python
# phases/analyze_issue.py
class AnalyzeIssuePhase(BasePhase):
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        # Issue分析とタスクリスト作成
        pass
```

既存のverify-task-list.shの機能も統合

#### 実装フェーズ（implement）
```python
# phases/implement.py
class ImplementPhase(BasePhase):
    def run(self, params: WorkflowParameters, iteration: int) -> Dict[str, Any]:
        # タスクの実装
        pass
```

イテレーション管理を含む

#### レビューフェーズ（review）
```python
# phases/review.py
class ReviewPhase(BasePhase):
    def run(self, params: WorkflowParameters, iteration: int) -> Dict[str, Any]:
        # コードレビュー
        pass
```

#### ナレッジ更新フェーズ（update-knowledge）
```python
# phases/update_knowledge.py
class UpdateKnowledgePhase(BasePhase):
    def run(self, params: WorkflowParameters) -> Dict[str, Any]:
        # ナレッジベースの更新
        pass
```

### 2. orchestrator.pyへのコマンド追加

```python
@cli.command()
@click.option('--issue-number', required=True, type=int)
@click.option('--branch-name', required=True)
@click.option('--model', required=True)
@click.option('--max-turns', required=True, type=int)
def analyze_env(issue_number, branch_name, model, max_turns):
    """Run environment analysis phase."""
    # 実装

@cli.command()
@click.option('--issue-number', required=True, type=int)
@click.option('--branch-name', required=True)
@click.option('--model', required=True)
@click.option('--max-turns', required=True, type=int)
def analyze_issue(issue_number, branch_name, model, max_turns):
    """Run issue analysis phase."""
    # 実装

# 他のフェーズも同様に追加
```

### 3. テストの実装

```python
# tests/test_parameter_parser.py
import pytest
from utils.parameter_parser import ParameterParser

def test_extract_model():
    parser = ParameterParser()
    # テストケース
```

### 4. 動作確認手順

1. **ローカルテスト**
   ```bash
   # Python環境のセットアップ
   python -m venv venv
   source venv/bin/activate
   pip install -r .github/scripts/python/requirements.txt
   
   # 各コマンドのテスト
   python .github/scripts/python/orchestrator.py --help
   ```

2. **GitHub Actions でのテスト**
   - テスト用のIssueを作成
   - Python版ワークフローを手動実行
   - 各フェーズの動作確認

3. **段階的移行**
   - 最初は特定のIssueでのみPython版を使用
   - 問題がなければ徐々に適用範囲を拡大

## 既存実装との互換性

### 1. ファイル構造の互換性

Python版は既存のディレクトリ構造を維持：
- `claude/claude-work/issue-{number}/`
- `claude/claude-knowledge/`

### 2. Git操作の互換性

同じGit設定とブランチ命名規則を使用：
- ユーザー名: "Claude Orchestrator"
- メールアドレス: "claude-orchestrator@github-actions"
- ブランチ名: `claude-task/{issue_number}-{normalized_title}`

### 3. GitHub Actions出力の互換性

同じ出力変数名を使用：
- branch_name
- selected_model
- max_turns_*
- iterations_limit

## トラブルシューティング

### 問題: ModuleNotFoundError

```bash
# sys.pathの確認
python -c "import sys; print(sys.path)"

# 正しいディレクトリから実行
cd /path/to/repository
python .github/scripts/python/orchestrator.py
```

### 問題: GitHub API認証エラー

```bash
# トークンの確認
echo $GH_TOKEN

# GitHub CLIの認証状態確認
gh auth status
```

### 問題: Git操作エラー

```bash
# Git設定の確認
git config user.name
git config user.email

# リモートの確認
git remote -v
```

## 参考資料

1. **既存のBashスクリプト**
   - `.github/scripts/*.sh`
   - 各スクリプトのロジックを参考に

2. **既存のワークフローファイル**
   - `.github/workflows/claude-*.yml`
   - ジョブの依存関係と入出力を確認

3. **Claude Orchestratorドキュメント**
   - `claude-orchestrator-troubleshooting-guide.md`
   - エラーパターンと解決策

## 連絡事項

- 作業ブランチ: `feature/python-workflow-migration`
- 主要な変更はすべてコミット済み
- 次の作業者は残りのフェーズの実装から開始してください