"""DateTime helper utilities for JST handling."""
from datetime import datetime, timezone, timedelta
from typing import Optional

# JST timezone
JST = timezone(timedelta(hours=9))


def now_jst() -> datetime:
    """Get current datetime in JST."""
    return datetime.now(JST)


def utc_to_jst(dt: datetime) -> datetime:
    """Convert UTC datetime to JST."""
    if dt.tzinfo is None:
        # Assume UTC if no timezone info
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(JST)


def parse_iso_datetime(iso_str: str) -> Optional[datetime]:
    """Parse ISO format datetime string and convert to JST."""
    try:
        # Handle Z suffix
        if iso_str.endswith('Z'):
            iso_str = iso_str[:-1] + '+00:00'
        
        dt = datetime.fromisoformat(iso_str)
        
        # If no timezone info, assume UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
            
        return dt.astimezone(JST)
    except (ValueError, AttributeError):
        return None


def format_jst(dt: Optional[datetime] = None) -> str:
    """Format datetime in JST ISO format."""
    if dt is None:
        dt = now_jst()
    elif dt.tzinfo is None:
        dt = dt.replace(tzinfo=JST)
    else:
        dt = dt.astimezone(JST)
    
    return dt.isoformat()


def format_jst_readable(dt: Optional[datetime] = None) -> str:
    """Format datetime in readable JST format."""
    if dt is None:
        dt = now_jst()
    elif dt.tzinfo is None:
        dt = dt.replace(tzinfo=JST)
    else:
        dt = dt.astimezone(JST)
    
    return dt.strftime("%Y-%m-%d %H:%M:%S JST")