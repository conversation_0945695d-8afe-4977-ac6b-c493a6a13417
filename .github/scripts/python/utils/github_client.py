"""GitHub API client wrapper using GitHub CLI."""
import os
import json
import logging
from typing import Dict, Any, List, Optional
from core.base import CommandRunner, WorkflowError

logger = logging.getLogger(__name__)


class GitHubClient:
    """GitHub API client using gh CLI."""
    
    def __init__(self):
        self.runner = CommandRunner()
        self.repo = os.environ.get("GITHUB_REPOSITORY", "")
        
    def get_issue(self, issue_number: int) -> Dict[str, Any]:
        """Get issue details."""
        logger.info(f"Fetching issue #{issue_number}")
        result = self.runner.run([
            "gh", "issue", "view", str(issue_number),
            "--json", "number,title,body,state,labels,createdAt,updatedAt"
        ])
        
        try:
            data = json.loads(result.stdout)
            # Flatten labels to just names
            data['labels'] = [label['name'] for label in data.get('labels', [])]
            return data
        except json.JSONDecodeError:
            raise WorkflowError(f"Failed to parse issue data for #{issue_number}")
    
    def create_issue_comment(self, issue_number: int, body: str) -> None:
        """Create a comment on an issue."""
        logger.info(f"Creating comment on issue #{issue_number}")
        self.runner.run([
            "gh", "issue", "comment", str(issue_number),
            "--body", body
        ])
    
    def add_issue_labels(self, issue_number: int, labels: List[str]) -> None:
        """Add labels to an issue."""
        if not labels:
            return
            
        logger.info(f"Adding labels to issue #{issue_number}: {labels}")
        cmd = ["gh", "issue", "edit", str(issue_number)]
        for label in labels:
            cmd.extend(["--add-label", label])
        self.runner.run(cmd)
    
    def remove_issue_labels(self, issue_number: int, labels: List[str]) -> None:
        """Remove labels from an issue."""
        if not labels:
            return
            
        logger.info(f"Removing labels from issue #{issue_number}: {labels}")
        cmd = ["gh", "issue", "edit", str(issue_number)]
        for label in labels:
            cmd.extend(["--remove-label", label])
        self.runner.run(cmd)
    
    def setup_git_auth(self) -> None:
        """Setup git authentication for GitHub Actions."""
        token = os.environ.get("GH_TOKEN")
        if not token:
            raise WorkflowError("GH_TOKEN environment variable not set")
        
        repo = os.environ.get("GITHUB_REPOSITORY", "")
        if not repo:
            raise WorkflowError("GITHUB_REPOSITORY environment variable not set")
        
        # Configure git with authenticated URL
        auth_url = f"https://x-access-token:{token}@github.com/{repo}.git"
        self.runner.run(["git", "remote", "set-url", "origin", auth_url])
        logger.info("Git authentication configured")
    
    def create_branch(self, branch_name: str, base: str = "develop") -> None:
        """Create a new branch."""
        logger.info(f"Creating branch: {branch_name} from {base}")
        
        # Setup authentication for git operations
        self.setup_git_auth()
        
        # Fetch latest changes
        self.runner.run(["git", "fetch", "origin", base])
        
        # Create and push branch
        self.runner.run(["git", "checkout", "-b", branch_name, f"origin/{base}"])
        self.runner.run(["git", "push", "-u", "origin", branch_name])
    
    def branch_exists(self, branch_name: str) -> bool:
        """Check if a branch exists."""
        result = self.runner.run(
            ["git", "ls-remote", "--heads", "origin", branch_name],
            check=False
        )
        return bool(result.stdout.strip())
    
    def get_pr_for_branch(self, branch_name: str) -> Optional[Dict[str, Any]]:
        """Get PR for a specific branch."""
        result = self.runner.run([
            "gh", "pr", "list",
            "--head", branch_name,
            "--json", "number,title,url,state",
            "--jq", ".[0]"
        ], check=False)
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                return json.loads(result.stdout)
            except json.JSONDecodeError:
                return None
        return None
    
    def create_pr(self, title: str, body: str, base: str, head: str, 
                  labels: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create a pull request."""
        logger.info(f"Creating PR: {title}")
        
        # Save body to temporary file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(body)
            body_file = f.name
        
        try:
            cmd = [
                "gh", "pr", "create",
                "--title", title,
                "--body-file", body_file,
                "--base", base,
                "--head", head
            ]
            
            if labels:
                for label in labels:
                    cmd.extend(["--label", label])
            
            result = self.runner.run(cmd)
            pr_url = result.stdout.strip()
            
            # Extract PR number from URL
            pr_number = pr_url.split('/')[-1]
            
            return {
                "number": int(pr_number),
                "url": pr_url
            }
        finally:
            # Clean up temporary file
            os.unlink(body_file)
    
    def check_label_exists(self, label_name: str) -> bool:
        """Check if a label exists in the repository."""
        result = self.runner.run([
            "gh", "label", "list",
            "--search", label_name,
            "--json", "name",
            "--jq", f'.[] | select(.name == "{label_name}") | .name'
        ], check=False)
        
        return result.stdout.strip() == label_name
    
    def create_label(self, name: str, description: str = "", color: str = "") -> None:
        """Create a new label."""
        logger.info(f"Creating label: {name}")
        cmd = ["gh", "label", "create", name]
        
        if description:
            cmd.extend(["--description", description])
        if color:
            cmd.extend(["--color", color])
            
        self.runner.run(cmd)
    
    def setup_git_config(self) -> None:
        """Setup git configuration for the workflow."""
        logger.info("Setting up git configuration")
        
        # Set user name and email
        self.runner.run(["git", "config", "user.name", "Claude Orchestrator"])
        self.runner.run(["git", "config", "user.email", "claude-orchestrator@github-actions"])
        
        # Set push default
        self.runner.run(["git", "config", "push.default", "current"])
    
    def commit_and_push(self, message: str, push: bool = True) -> bool:
        """Commit changes and optionally push."""
        # Check if there are changes
        result = self.runner.run(["git", "status", "--porcelain"], check=False)
        if not result.stdout.strip():
            logger.info("No changes to commit")
            return False
        
        # Add all changes
        self.runner.run(["git", "add", "-A"])
        
        # Commit
        self.runner.run(["git", "commit", "-m", message])
        
        # Push if requested
        if push:
            # Setup authentication for git operations
            self.setup_git_auth()
            self.runner.run(["git", "push"])
            
        return True