"""Create pull request summary and PR."""
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from core.base import Git<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommandRunner
from .github_client import GitHubClient

logger = logging.getLogger(__name__)


class PRCreator:
    """Create pull request with summary."""
    
    def __init__(self, issue_number: int, repository: str, total_iterations: int):
        self.issue_number = issue_number
        self.repository = repository
        self.total_iterations = total_iterations
        self.work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        self.github = GitHubActionsHelper()
        self.github_client = GitHubClient()
        self.runner = CommandRunner()
        
    def create_pr_summary(self) -> Dict[str, str]:
        """Create PR summary and return title and body."""
        logger.info(f"Creating PR summary for issue #{self.issue_number}")
        
        # Get issue information
        issue_data = self.github_client.get_issue(self.issue_number)
        issue_title = issue_data['title']
        
        # Create PR body
        pr_body = self._create_pr_body(issue_title)
        
        # Create PR title
        pr_title = f"fix: {issue_title} (#{self.issue_number})"
        
        # Save PR body to file
        pr_body_file = Path("/tmp/pr-body.md")
        pr_body_file.write_text(pr_body)
        
        # Set GitHub Actions outputs
        self.github.set_output("pr_title", pr_title)
        self.github.set_output("pr_body", pr_body)
        
        self.github.log_notice("Pull request summary created")
        
        return {
            "title": pr_title,
            "body": pr_body,
            "body_file": str(pr_body_file)
        }
    
    def _create_pr_body(self, issue_title: str) -> str:
        """Create PR body content."""
        sections = []
        
        # Header
        sections.append(f"""## 概要

Issue #{self.issue_number} の実装です。

## 実装内容""")
        
        # Add implementation content for each iteration
        for i in range(1, self.total_iterations + 1):
            impl_file = self.work_dir / "implementation" / f"iteration-{i}.md"
            if impl_file.exists():
                sections.append(f"\n### イテレーション {i}\n")
                # Get first 20 lines of implementation summary
                lines = impl_file.read_text().split('\n')[:20]
                sections.append('\n'.join(lines))
        
        # Add changed files
        sections.append("\n## 変更ファイル")
        changed_files = self._get_changed_files()
        for status, file_path in changed_files:
            if status == 'A':
                sections.append(f"- ➕ {file_path}")
            elif status == 'M':
                sections.append(f"- ✏️ {file_path}")
            elif status == 'D':
                sections.append(f"- ❌ {file_path}")
        
        # Add test results if available
        sections.append("\n## テスト")
        test_results_file = self.work_dir / "test-results.md"
        if test_results_file.exists():
            sections.append(test_results_file.read_text())
        else:
            sections.append("テスト結果なし")
        
        # Add review history
        sections.append("""
## レビュー履歴

| イテレーション | 結果 | レビュワー |
|--------------|------|-----------|""")
        
        for i in range(1, self.total_iterations + 1):
            review_file = self.work_dir / "review" / f"iteration-{i}.json"
            if review_file.exists():
                try:
                    review_data = json.loads(review_file.read_text())
                    result = "✅ 合格" if review_data.get('passed', False) else "⚠️ 要修正"
                    sections.append(f"| {i} | {result} | Claude |")
                except:
                    pass
        
        # Add related information
        current_branch = self._get_current_branch()
        sections.append(f"""
## 関連情報

- Fixes #{self.issue_number}
- ブランチ: `{current_branch}`
- 総イテレーション数: {self.total_iterations}

---
*このPRは Claude Orchestrator により自動生成されました*""")
        
        return '\n'.join(sections)
    
    def _get_changed_files(self) -> List[tuple]:
        """Get list of changed files."""
        result = self.runner.run([
            "git", "diff", "origin/develop", "--name-status"
        ], check=False)
        
        changed_files = []
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = line.split('\t', 1)
                    if len(parts) == 2:
                        changed_files.append((parts[0], parts[1]))
        
        return changed_files
    
    def _get_current_branch(self) -> str:
        """Get current git branch name."""
        result = self.runner.run(["git", "branch", "--show-current"])
        return result.stdout.strip()
    
    def create_pull_request(self, branch_name: str, base: str = "develop") -> Dict[str, Any]:
        """Create the actual pull request."""
        pr_summary = self.create_pr_summary()
        
        # Check if PR already exists
        existing_pr = self.github_client.get_pr_for_branch(branch_name)
        if existing_pr:
            self.github.log_warning("PR already exists for this branch")
            return existing_pr
        
        # Create new PR
        try:
            pr_data = self.github_client.create_pr(
                title=pr_summary["title"],
                body=pr_summary["body"],
                base=base,
                head=branch_name,
                labels=["claude-generated"]
            )
            
            self.github.log_notice(f"Created PR #{pr_data['number']}: {pr_data['url']}")
            return pr_data
            
        except Exception as e:
            logger.error(f"Failed to create PR: {e}")
            raise