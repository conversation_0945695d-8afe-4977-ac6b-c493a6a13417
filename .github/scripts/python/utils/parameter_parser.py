"""Parse workflow parameters from GitHub issue."""
import re
import logging
from typing import Dict, <PERSON><PERSON>, Tuple
from core.base import WorkflowParameters, IssueData, GitHubActionsHelper

logger = logging.getLogger(__name__)


class ParameterParser:
    """Parse workflow parameters from issue body."""
    
    # Model mapping
    MODEL_MAPPING = {
        "claude-opus-4-20250514": "Claude Opus 4",
        "claude-sonnet-4-20250514": "Claude Sonnet 4", 
        "claude-3-5-haiku-20241022": "Claude 3.5 Haiku",
        "claude-3-5-sonnet-20241022": "Claude 3.5 Sonnet"
    }
    
    # Default values
    DEFAULT_MODEL = "claude-3-5-sonnet-20241022"
    DEFAULT_MAX_TURNS = 80
    DEFAULT_ITERATIONS_LIMIT = 10
    
    def __init__(self):
        self.github = GitHubActionsHelper()
        
    def parse(self, issue_data: IssueData) -> WorkflowParameters:
        """Parse workflow parameters from issue data."""
        logger.info(f"Parsing parameters for issue #{issue_data.number}")
        
        # Extract model
        model, display_name = self._extract_model(issue_data.body)
        
        # Extract turn limits
        turns = self._extract_turn_limits(issue_data.body)
        
        # Extract iterations limit
        iterations_limit = self._extract_iterations_limit(issue_data.body)
        
        # Extract PRD file path
        prd_file_path = self._extract_prd_path(issue_data.body)
        
        # Extract task complexity
        task_complexity = self._extract_task_complexity(issue_data.body)
        
        # Generate branch name
        branch_name = self._generate_branch_name(issue_data.number, issue_data.title)
        
        params = WorkflowParameters(
            issue_number=issue_data.number,
            issue_title=issue_data.title,
            issue_body=issue_data.body,
            branch_name=branch_name,
            model=model,
            max_turns_env=turns['env'],
            max_turns_analysis=turns['analysis'],
            max_turns_implement=turns['implement'],
            max_turns_review=turns['review'],
            iterations_limit=iterations_limit,
            prd_file_path=prd_file_path,
            task_complexity=task_complexity,
            labels=issue_data.labels
        )
        
        # Log parsed parameters
        self._log_parameters(params, display_name)
        
        # Set GitHub Actions outputs
        self._set_outputs(params, display_name)
        
        return params
    
    def _extract_model(self, issue_body: str) -> Tuple[str, str]:
        """Extract model from issue body."""
        for model_id, display_name in self.MODEL_MAPPING.items():
            if model_id in issue_body:
                self.github.log_notice(f"Selected model: {display_name}")
                return model_id, display_name
        
        # Default model
        self.github.log_notice(f"Using default model: {self.MODEL_MAPPING[self.DEFAULT_MODEL]}")
        return self.DEFAULT_MODEL, self.MODEL_MAPPING[self.DEFAULT_MODEL]
    
    def _extract_turn_limits(self, issue_body: str) -> Dict[str, int]:
        """Extract turn limits for each phase."""
        patterns = {
            'env': r"環境分析フェーズ 最大ターン数:\s*(\d+)",
            'analysis': r"Issue分析フェーズ 最大ターン数:\s*(\d+)",
            'implement': r"実装フェーズ 最大ターン数:\s*(\d+)",
            'review': r"レビューフェーズ 最大ターン数:\s*(\d+)"
        }
        
        turns = {}
        for phase, pattern in patterns.items():
            match = re.search(pattern, issue_body)
            turns[phase] = int(match.group(1)) if match else self.DEFAULT_MAX_TURNS
            
        return turns
    
    def _extract_iterations_limit(self, issue_body: str) -> int:
        """Extract iterations limit."""
        match = re.search(r"コーダー・レビュアー対話回数制限:\s*(\d+)", issue_body)
        return int(match.group(1)) if match else self.DEFAULT_ITERATIONS_LIMIT
    
    def _extract_prd_path(self, issue_body: str) -> Optional[str]:
        """Extract PRD file path from issue body."""
        # Look for PRD file path pattern
        match = re.search(r"PRDファイル:\s*`?([^\s`]+\.md)`?", issue_body)
        if match:
            return match.group(1)
        
        # Alternative pattern
        match = re.search(r"/claude/claude-requirements/active/([^\s]+\.md)", issue_body)
        if match:
            return f"/claude/claude-requirements/active/{match.group(1)}"
            
        return None
    
    def _extract_task_complexity(self, issue_body: str) -> str:
        """Extract task complexity."""
        if "複雑なタスク" in issue_body:
            return "complex"
        elif "簡単なタスク" in issue_body:
            return "simple"
        return "standard"
    
    def _generate_branch_name(self, issue_number: int, issue_title: str) -> str:
        """Generate branch name from issue number and title."""
        # Remove newlines and normalize whitespace
        title = issue_title.replace('\n', ' ').replace('\r', ' ').strip()
        
        # Remove non-alphanumeric characters (except spaces, underscores, hyphens)
        normalized = re.sub(r'[^a-zA-Z0-9 _-]', '', title)
        
        # Replace spaces and underscores with hyphens
        normalized = re.sub(r'[ _]+', '-', normalized)
        
        # Remove multiple consecutive hyphens
        normalized = re.sub(r'-+', '-', normalized)
        
        # Remove leading/trailing hyphens
        normalized = normalized.strip('-')
        
        # Limit length
        normalized = normalized[:50]
        
        branch_name = f"claude-task/{issue_number}-{normalized}"
        logger.debug(f"Generated branch name: {branch_name}")
        
        return branch_name
    
    def _log_parameters(self, params: WorkflowParameters, display_name: str) -> None:
        """Log parsed parameters."""
        self.github.log_notice(f"Branch name: {params.branch_name}")
        self.github.log_notice(f"Model: {display_name} ({params.model})")
        self.github.log_notice(f"Iterations limit: {params.iterations_limit}")
        self.github.log_notice(
            f"Max turns - Env: {params.max_turns_env}, "
            f"Analysis: {params.max_turns_analysis}, "
            f"Implement: {params.max_turns_implement}, "
            f"Review: {params.max_turns_review}"
        )
        if params.prd_file_path:
            self.github.log_notice(f"PRD file: {params.prd_file_path}")
    
    def _set_outputs(self, params: WorkflowParameters, display_name: str) -> None:
        """Set GitHub Actions outputs."""
        outputs = {
            "branch_name": params.branch_name,
            "selected_model": params.model,
            "display_model_name": display_name,
            "max_turns_env": str(params.max_turns_env),
            "max_turns_analysis": str(params.max_turns_analysis),
            "max_turns_implement": str(params.max_turns_implement),
            "max_turns_review": str(params.max_turns_review),
            "max_turns_knowledge": str(params.max_turns_review),  # Use same as review
            "iterations_limit": str(params.iterations_limit)
        }
        
        if params.prd_file_path:
            outputs["prd_file_path"] = params.prd_file_path
            
        for key, value in outputs.items():
            self.github.set_output(key, value)