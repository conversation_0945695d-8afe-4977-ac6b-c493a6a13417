"""Workflow state management for resume/revise functionality."""
import os
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from .datetime_helper import now_jst, format_jst

logger = logging.getLogger(__name__)


class WorkflowState:
    """Manage workflow execution state for resume capability."""
    
    def __init__(self, issue_number: int):
        self.issue_number = issue_number
        self.work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        self.state_dir = self.work_dir / "state"
        self.state_file = self.state_dir / "workflow-state.json"
        
    def initialize_state(self, parameters: Dict[str, Any]) -> None:
        """Initialize a new workflow state."""
        logger.info(f"Initializing workflow state for issue #{self.issue_number}")
        
        state = {
            "issue_number": self.issue_number,
            "branch_name": parameters.get("branch_name"),
            "current_phase": "initialize",
            "current_iteration": 0,
            "phases_completed": [],
            "phases_skipped": [],
            "last_successful_step": None,
            "parameters": {
                "selected_model": parameters.get("model"),
                "max_turns_env": parameters.get("max_turns_env"),
                "max_turns_analysis": parameters.get("max_turns_analysis"),
                "max_turns_implement": parameters.get("max_turns_implement"),
                "max_turns_review": parameters.get("max_turns_review"),
                "iterations_limit": parameters.get("iterations_limit"),
                "prd_file_path": parameters.get("prd_file_path"),
                "task_complexity": parameters.get("task_complexity"),
            },
            "pr_created": False,
            "pr_number": None,
            "created_at": format_jst(),
            "last_update": format_jst(),
            "workflow_mode": "new",
            "revision_history": []
        }
        
        self.save_state(state)
    
    def save_state(self, state: Dict[str, Any]) -> None:
        """Save workflow state to file."""
        self.state_dir.mkdir(parents=True, exist_ok=True)
        state["last_update"] = format_jst()
        
        with open(self.state_file, 'w') as f:
            json.dump(state, f, indent=2)
        
        logger.info(f"Saved workflow state to {self.state_file}")
    
    def load_state(self) -> Optional[Dict[str, Any]]:
        """Load workflow state from file."""
        if not self.state_file.exists():
            logger.warning(f"State file not found: {self.state_file}")
            return None
            
        try:
            with open(self.state_file, 'r') as f:
                state = json.load(f)
            logger.info(f"Loaded workflow state from {self.state_file}")
            return state
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse state file: {e}")
            return None
    
    def update_phase_completion(self, phase: str) -> None:
        """Mark a phase as completed."""
        state = self.load_state()
        if not state:
            logger.error("No state found to update")
            return
            
        completed = state.get("phases_completed", [])
        if phase not in completed:
            completed.append(phase)
            logger.info(f"Marked phase '{phase}' as completed")
        
        state["phases_completed"] = completed
        state["current_phase"] = phase
        state["last_successful_step"] = phase
        
        self.save_state(state)
    
    def update_current_iteration(self, iteration: int) -> None:
        """Update current implementation iteration."""
        state = self.load_state()
        if not state:
            return
            
        state["current_iteration"] = iteration
        state["last_successful_step"] = f"implement-iteration-{iteration}"
        
        self.save_state(state)
    
    def mark_pr_created(self, pr_number: int) -> None:
        """Mark that a PR has been created."""
        state = self.load_state()
        if not state:
            return
            
        state["pr_created"] = True
        state["pr_number"] = pr_number
        
        self.save_state(state)
    
    def add_revision(self, revision_request: str) -> None:
        """Add a revision request to history."""
        state = self.load_state()
        if not state:
            return
            
        revision = {
            "request": revision_request,
            "timestamp": format_jst(),
            "pr_number": state.get("pr_number")
        }
        
        state.setdefault("revision_history", []).append(revision)
        state["workflow_mode"] = "revise"
        
        self.save_state(state)
    
    def get_resume_point(self) -> Dict[str, Any]:
        """Determine where to resume the workflow."""
        state = self.load_state()
        if not state:
            return {"can_resume": False, "reason": "No state file found"}
            
        # Check if PR was already created
        if state.get("pr_created"):
            return {
                "can_resume": True,
                "mode": "revise",
                "pr_number": state.get("pr_number"),
                "branch_name": state.get("branch_name")
            }
        
        # Determine next phase
        phases_order = [
            "initialize",
            "analyze-env",
            "analyze-issue", 
            "implement",
            "review",
            "update-knowledge",
            "finalize"
        ]
        
        completed = state.get("phases_completed", [])
        current_phase = state.get("current_phase")
        
        # Find next phase to execute
        next_phase = None
        for phase in phases_order:
            if phase not in completed:
                next_phase = phase
                break
        
        return {
            "can_resume": True,
            "mode": "resume",
            "next_phase": next_phase,
            "current_iteration": state.get("current_iteration", 0),
            "branch_name": state.get("branch_name"),
            "parameters": state.get("parameters", {})
        }
    
    def should_skip_phase(self, phase: str) -> bool:
        """Check if a phase should be skipped based on state."""
        state = self.load_state()
        if not state:
            return False
            
        # Phase already completed
        if phase in state.get("phases_completed", []):
            return True
            
        # Special handling for env analysis
        if phase == "analyze-env" and phase in state.get("phases_skipped", []):
            return True
            
        return False
    
    def get_workflow_summary(self) -> str:
        """Get a human-readable summary of workflow state."""
        state = self.load_state()
        if not state:
            return "No workflow state found"
            
        summary = f"""## Workflow State Summary

**Issue**: #{state.get('issue_number')}
**Branch**: {state.get('branch_name')}
**Mode**: {state.get('workflow_mode', 'unknown')}
**Current Phase**: {state.get('current_phase')}
**Current Iteration**: {state.get('current_iteration', 0)}

### Phases Completed:
{chr(10).join(f"- ✅ {phase}" for phase in state.get('phases_completed', []))}

### Parameters:
- Model: {state['parameters'].get('selected_model')}
- Iterations Limit: {state['parameters'].get('iterations_limit')}

### PR Status:
- Created: {'Yes' if state.get('pr_created') else 'No'}
- PR Number: {state.get('pr_number', 'N/A')}

### Timestamps:
- Created: {state.get('created_at')}
- Last Update: {state.get('last_update')}
"""
        
        if state.get("revision_history"):
            summary += "\n### Revision History:\n"
            for rev in state["revision_history"]:
                summary += f"- {rev['timestamp']}: {rev['request'][:100]}...\n"
                
        return summary