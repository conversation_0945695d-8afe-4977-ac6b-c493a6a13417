#!/usr/bin/env python3
"""Main orchestrator script for Claude Orchestrator workflow."""
import os
import sys
import click
import logging
import json
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from core.base import IssueData, WorkflowError, GitHubActionsHelper, WorkflowParameters
from utils.parameter_parser import ParameterParser
from utils.github_client import GitHubClient
from utils.workflow_state import WorkflowState
from phases.initialize import InitializePhase

# Configure logging
logging.basicConfig(
    level=logging.DEBUG if os.environ.get("RUNNER_DEBUG") == "1" else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
def cli():
    """Claude Orchestrator CLI."""
    pass


@cli.command('parse-parameters')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
def parse_parameters(issue_number):
    """Parse workflow parameters from issue."""
    try:
        github_client = GitHubClient()
        github = GitHubActionsHelper()
        
        # Fetch issue data
        issue_json = github_client.get_issue(issue_number)
        issue_data = IssueData(
            number=issue_json['number'],
            title=issue_json['title'],
            body=issue_json.get('body', ''),
            state=issue_json['state'],
            labels=issue_json['labels'],
            created_at=issue_json['createdAt'],
            updated_at=issue_json['updatedAt']
        )
        
        # Parse parameters
        parser = ParameterParser()
        params = parser.parse(issue_data)
        
        logger.info("Successfully parsed workflow parameters")
        
    except Exception as e:
        logger.error(f"Failed to parse parameters: {e}")
        github.log_error(f"Failed to parse parameters: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--issue-title', required=True, help='Issue title')
@click.option('--issue-body', default='', help='Issue body')
def initialize(issue_number, issue_title, issue_body):
    """Initialize the workflow environment."""
    try:
        github = GitHubActionsHelper()
        
        # Create issue data
        issue_data = IssueData(
            number=issue_number,
            title=issue_title,
            body=issue_body,
            state='open',
            labels=[],
            created_at='',
            updated_at=''
        )
        
        # Parse parameters
        parser = ParameterParser()
        params = parser.parse(issue_data)
        
        # Run initialization
        phase = InitializePhase()
        result = phase.run(params)
        
        logger.info(f"Initialization completed: {result}")
        
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        github.log_error(f"Initialization failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--script', required=True, help='Script name to run')
@click.option('--phase', help='Phase name')
@click.pass_context
def run_script(ctx, script, phase):
    """Run a specific script (for backward compatibility)."""
    try:
        github = GitHubActionsHelper()
        
        # Map script names to commands
        script_mapping = {
            'setup-workflow-parameters.sh': 'parse-parameters',
            'check-trigger.sh': 'check-trigger',
            'create-pr-summary.sh': 'create-pr-summary',
            'verify-task-list.sh': 'verify-task-list'
        }
        
        if script in script_mapping:
            # Get the corresponding command
            command_name = script_mapping[script]
            
            # Get environment variables as parameters
            params = {}
            if command_name == 'parse-parameters':
                params['issue_number'] = int(os.environ.get('ISSUE_NUMBER', 0))
            
            # Invoke the command
            ctx.invoke(cli.commands[command_name], **params)
        else:
            logger.error(f"Unknown script: {script}")
            github.log_error(f"Unknown script: {script}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Script execution failed: {e}")
        github.log_error(f"Script execution failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--total-iterations', required=True, type=int, help='Total iterations')
def finalize(issue_number, branch_name, total_iterations):
    """Finalize the workflow and create PR."""
    try:
        from phases.finalize import FinalizePhase
        
        phase = FinalizePhase()
        result = phase.run(issue_number, branch_name, total_iterations)
        
        logger.info(f"Finalization completed: {result}")
        
    except Exception as e:
        logger.error(f"Finalization failed: {e}")
        GitHubActionsHelper().log_error(f"Finalization failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--work-dir', required=True, help='Work directory path')
def verify_task_list(work_dir):
    """Verify task list was created properly."""
    try:
        from pathlib import Path
        import json
        
        github = GitHubActionsHelper()
        task_file = Path(work_dir) / "dialogue" / "task-list.md"
        
        if not task_file.exists():
            github.log_error("Task list file not found")
            sys.exit(1)
            
        # Count tasks
        content = task_file.read_text()
        task_count = content.count('- [ ]') + content.count('- [x]')
        
        github.log_notice(f"Found {task_count} tasks")
        github.set_output('task_count', str(task_count))
        
        if task_count == 0:
            github.log_error("No tasks found in task list")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Task list verification failed: {e}")
        github.log_error(f"Task list verification failed: {e}")
        sys.exit(1)


@cli.command('check-trigger')
def check_trigger():
    """Check workflow trigger and extract issue number."""
    try:
        github = GitHubActionsHelper()
        
        # Get event data
        event_name = os.environ.get('GITHUB_EVENT_NAME', '')
        
        if event_name == 'issue_comment':
            # Get issue number from event
            import json
            event_path = os.environ.get('GITHUB_EVENT_PATH', '')
            if event_path and os.path.exists(event_path):
                with open(event_path) as f:
                    event_data = json.load(f)
                
                issue_number = event_data.get('issue', {}).get('number')
                comment_body = event_data.get('comment', {}).get('body', '')
                
                # Check for @claude-task mention
                if '@claude-task' in comment_body and issue_number:
                    github.set_output('should_run', 'true')
                    github.set_output('issue_number', str(issue_number))
                    
                    # Determine workflow mode
                    workflow_mode = 'new'  # default
                    if '@claude-task resume' in comment_body:
                        workflow_mode = 'resume'
                    elif '@claude-task revise' in comment_body:
                        workflow_mode = 'revise'
                    elif '@claude-task continue' in comment_body:
                        workflow_mode = 'continue'
                    
                    github.set_output('workflow_mode', workflow_mode)
                    github.log_notice(f"Processing issue #{issue_number} in {workflow_mode} mode")
                else:
                    github.set_output('should_run', 'false')
                    github.log_notice("No @claude-task mention found")
            else:
                github.set_output('should_run', 'false')
                github.log_error("Event data not found")
                
        elif event_name == 'workflow_dispatch':
            # Get issue number from input
            issue_number = os.environ.get('ISSUE_NUMBER', '')
            if issue_number:
                github.set_output('should_run', 'true') 
                github.set_output('issue_number', issue_number)
                github.set_output('workflow_mode', 'new')  # workflow_dispatch is always new
                github.log_notice(f"Processing issue #{issue_number}")
            else:
                github.set_output('should_run', 'false')
                github.log_error("Issue number not provided")
        else:
            github.set_output('should_run', 'false')
            github.log_warning(f"Unsupported event: {event_name}")
            
    except Exception as e:
        logger.error(f"Trigger check failed: {e}")
        github.log_error(f"Trigger check failed: {e}")
        github.set_output('should_run', 'false')
        sys.exit(1)


@cli.command('load-state')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
def load_state(issue_number):
    """Load and display workflow state."""
    try:
        state = WorkflowState(issue_number)
        summary = state.get_workflow_summary()
        
        print(summary)
        
        # Also output resume point for GitHub Actions
        github = GitHubActionsHelper()
        resume_info = state.get_resume_point()
        
        if resume_info.get('can_resume'):
            github.set_output('can_resume', 'true')
            github.set_output('resume_mode', resume_info.get('mode', 'resume'))
            github.set_output('next_phase', resume_info.get('next_phase', ''))
            github.set_output('current_iteration', str(resume_info.get('current_iteration', 0)))
            github.set_output('branch_name', resume_info.get('branch_name', ''))
            
            # Output parameters
            params = resume_info.get('parameters', {})
            for key, value in params.items():
                if value is not None:
                    github.set_output(key, str(value))
        else:
            github.set_output('can_resume', 'false')
            github.log_warning(resume_info.get('reason', 'Unknown reason'))
            
    except Exception as e:
        logger.error(f"Failed to load state: {e}")
        github.log_error(f"Failed to load state: {e}")
        sys.exit(1)


@cli.command('update-state')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--phase', required=True, help='Phase name')
def update_state(issue_number, phase):
    """Update workflow state for a completed phase."""
    try:
        state = WorkflowState(issue_number)
        state.update_phase_completion(phase)
        
        github = GitHubActionsHelper()
        github.log_notice(f"Updated state: phase '{phase}' completed")
        
    except Exception as e:
        logger.error(f"Failed to update state: {e}")
        github.log_error(f"Failed to update state: {e}")
        sys.exit(1)


@cli.command('check-iteration')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--iteration', required=True, type=int, help='Current iteration')
@click.option('--iterations-limit', required=True, type=int, help='Maximum iterations')
def check_iteration(issue_number, iteration, iterations_limit):
    """Check if implementation iteration should continue."""
    try:
        github = GitHubActionsHelper()
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        
        logger.info(f"Checking iteration {iteration} of max {iterations_limit}")
        
        # Check iteration limit
        if iteration > iterations_limit:
            logger.info(f"Iteration {iteration} exceeds limit")
            github.set_output(f"continue_{iteration}", "false")
            return
        
        # First iteration always runs
        if iteration == 1:
            logger.info("Starting first iteration")
            github.set_output(f"continue_{iteration}", "true")
            return
        
        # Check previous review result
        prev_iteration = iteration - 1
        review_file = work_dir / "review" / f"iteration-{prev_iteration}.json"
        
        if review_file.exists():
            try:
                import json
                with open(review_file) as f:
                    review_data = json.load(f)
                    
                review_passed = review_data.get('passed', False)
                if review_passed:
                    logger.info(f"Previous review passed, stopping iterations")
                    github.set_output(f"continue_{iteration}", "false")
                else:
                    logger.info(f"Continuing to iteration {iteration}")
                    github.set_output(f"continue_{iteration}", "true")
            except Exception as e:
                logger.warning(f"Failed to parse review file: {e}")
                github.set_output(f"continue_{iteration}", "false")
        else:
            logger.warning(f"Review file not found for iteration {prev_iteration}")
            github.set_output(f"continue_{iteration}", "false")
            
    except Exception as e:
        logger.error(f"Iteration check failed: {e}")
        github.log_error(f"Iteration check failed: {e}")
        github.set_output(f"continue_{iteration}", "false")
        sys.exit(1)


@cli.command()
@click.option('--prompt-file', required=True, help='Path to prompt file')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
def load_prompt(prompt_file, issue_number):
    """Load and process prompt template."""
    try:
        from datetime import datetime
        from utils.datetime_helper import now_jst, format_jst_readable
        
        github = GitHubActionsHelper()
        
        # Read prompt file
        prompt_path = Path(prompt_file)
        if not prompt_path.exists():
            raise FileNotFoundError(f"Prompt file not found: {prompt_file}")
            
        prompt_content = prompt_path.read_text()
        
        # Replace variables
        replacements = {
            '${{ inputs.issue_number }}': str(issue_number),
            '$(date +%Y-%m-%d)': now_jst().strftime('%Y-%m-%d'),
            '$(date +"%Y-%m-%d %H:%M:%S JST")': format_jst_readable()
        }
        
        for old, new in replacements.items():
            prompt_content = prompt_content.replace(old, new)
            
        # Output prompt
        github.set_output('prompt', prompt_content)
        
    except Exception as e:
        logger.error(f"Failed to load prompt: {e}")
        github.log_error(f"Failed to load prompt: {e}")
        sys.exit(1)


@cli.command('setup-git-config')
def setup_git_config():
    """Setup git configuration."""
    try:
        github_client = GitHubClient()
        github_client.setup_git_config()
        
        logger.info("Git configuration setup completed")
        
    except Exception as e:
        logger.error(f"Failed to setup git config: {e}")
        GitHubActionsHelper().log_error(f"Failed to setup git config: {e}")
        sys.exit(1)


@cli.command('commit-and-push')
@click.option('--message', required=True, help='Commit message')
def commit_and_push(message):
    """Commit and push changes."""
    try:
        github_client = GitHubClient()
        github_client.commit_and_push(message)
        
        logger.info(f"Committed and pushed: {message}")
        
    except Exception as e:
        logger.error(f"Failed to commit and push: {e}")
        GitHubActionsHelper().log_error(f"Failed to commit and push: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--total-iterations', required=True, type=int, help='Total iterations')
def create_pr(issue_number, branch_name, total_iterations):
    """Create pull request and update outputs."""
    try:
        from utils.pr_creator import PRCreator
        github = GitHubActionsHelper()
        
        creator = PRCreator(
            issue_number=issue_number,
            repository=os.environ.get('GITHUB_REPOSITORY', ''),
            total_iterations=total_iterations
        )
        
        pr_data = creator.create_pull_request(branch_name)
        
        github.set_output('pr_url', pr_data['url'])
        github.set_output('pr_number', str(pr_data['number']))
        
        logger.info(f"Created PR: {pr_data['url']}")
        
    except Exception as e:
        logger.error(f"Failed to create PR: {e}")
        github.log_error(f"Failed to create PR: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--remove-labels', default='', help='Comma-separated labels to remove')
@click.option('--add-labels', default='', help='Comma-separated labels to add')
def update_issue_status(issue_number, remove_labels, add_labels):
    """Update issue labels."""
    try:
        github_client = GitHubClient()
        
        if remove_labels:
            labels_to_remove = [l.strip() for l in remove_labels.split(',') if l.strip()]
            github_client.remove_issue_labels(issue_number, labels_to_remove)
            
        if add_labels:
            labels_to_add = [l.strip() for l in add_labels.split(',') if l.strip()]
            github_client.add_issue_labels(issue_number, labels_to_add)
            
        logger.info(f"Updated issue #{issue_number} labels")
        
    except Exception as e:
        logger.error(f"Failed to update issue status: {e}")
        GitHubActionsHelper().log_error(f"Failed to update issue status: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--pr-number', required=True, type=int, help='PR number')
@click.option('--pr-url', required=True, help='PR URL')
@click.option('--total-iterations', required=True, type=int, help='Total iterations')
def post_completion_comment(issue_number, pr_number, pr_url, total_iterations):
    """Post completion comment to issue."""
    try:
        github_client = GitHubClient()
        
        comment = f"""## ✅ Claude Orchestrator 完了

実装が完了し、プルリクエストを作成しました。

**プルリクエスト**: #{pr_number}
**URL**: {pr_url}
**総イテレーション数**: {total_iterations}

### 次のステップ
1. プルリクエストのレビュー
2. 必要に応じて修正
3. マージ

ご確認をお願いします。"""
        
        github_client.create_issue_comment(issue_number, comment)
        logger.info(f"Posted completion comment to issue #{issue_number}")
        
    except Exception as e:
        logger.error(f"Failed to post completion comment: {e}")
        GitHubActionsHelper().log_error(f"Failed to post completion comment: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--iteration', required=True, type=int, help='Review iteration')
@click.option('--review-file', required=True, help='Path to review file')
def post_review_comment(issue_number, iteration, review_file):
    """Post review result comment to issue."""
    try:
        import re
        from pathlib import Path
        
        github_client = GitHubClient()
        
        # Read review file
        review_path = Path(review_file)
        if not review_path.exists():
            raise FileNotFoundError(f"Review file not found: {review_file}")
            
        content = review_path.read_text()
        
        # 総評を確認
        passed = '合格' in content and '## 総評' in content
        
        # コメント作成
        comment = f'## 📝 レビュー結果 (イテレーション {iteration})\n\n'
        comment += f'**結果**: {"✅ 合格" if passed else "⚠️ 要修正"}\n\n'
        
        # 良い点を抽出
        good_points = re.search(r'## 良い点\n((?:- .*\n)*)', content)
        if good_points:
            comment += '### 良い点\n' + good_points.group(1)[:500] + '\n'
        
        # 改善点を抽出
        improvements = re.search(r'## 改善が必要な点\n((?:- .*\n)*)', content)
        if improvements and improvements.group(1).strip():
            comment += '### 改善が必要な点\n' + improvements.group(1)[:500] + '\n'
        
        comment += '\n---\n*このコメントは自動生成されています*'
        
        github_client.create_issue_comment(issue_number, comment)
        logger.info(f"Posted review comment for iteration {iteration}")
        
    except FileNotFoundError:
        logger.error(f'Review file not found: {review_file}')
        GitHubActionsHelper().log_error(f"Review file not found: {review_file}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to post review comment: {e}")
        GitHubActionsHelper().log_error(f"Failed to post review comment: {e}")
        sys.exit(1)


@cli.command('process-claude-result')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--phase', required=True, help='Phase name')
@click.option('--execution-file', required=True, help='Claude execution file path')
@click.option('--conclusion', required=True, help='Claude conclusion')
def process_claude_result(issue_number, phase, execution_file, conclusion):
    """Process Claude execution result and update workflow state."""
    try:
        github_client = GitHubClient()
        github = GitHubActionsHelper()
        
        # ワークフロー状態を更新
        from utils.workflow_state import WorkflowState
        from utils.datetime_helper import now_jst
        
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        state_file = work_dir / "workflow-state.json"
        
        # 既存の状態を読み込み
        state = {}
        if state_file.exists():
            with open(state_file) as f:
                state = json.load(f)
        
        # 状態を更新
        if "phases" not in state:
            state["phases"] = {}
        
        timestamp = now_jst().strftime("%Y-%m-%d %H:%M:%S JST")
        state["phases"][phase] = {
            "completed": True,
            "timestamp": timestamp,
            "execution_file": execution_file,
            "conclusion": conclusion
        }
        state["last_updated"] = now_jst().isoformat()
        
        # 状態ファイルを保存
        work_dir.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
        
        # Issueコメントを投稿
        phase_names = {
            "analyze-env": "🔍 環境分析フェーズ完了",
            "analyze-issue": "📋 Issue分析フェーズ完了",
            "implement": "🔨 実装フェーズ完了",
            "review": "📝 レビューフェーズ完了",
            "update-knowledge": "📚 ナレッジ更新フェーズ完了"
        }
        
        title = phase_names.get(phase, f"{phase}フェーズ完了")
        comment = f"""## {title}

Claude AIによる処理が完了しました。

**実行時刻**: {timestamp}
**結論**: {conclusion[:500]}...

詳細は実行ログを参照してください。

---
*Automated by Claude Orchestrator*"""

        github_client.create_issue_comment(issue_number, comment)
        logger.info(f"Processed Claude result for phase: {phase}")
        
    except Exception as e:
        logger.error(f"Failed to process Claude result: {e}")
        GitHubActionsHelper().log_error(f"Failed to process Claude result: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--task-file', required=True, help='Path to task file')
def post_task_summary(issue_number, task_file):
    """Post task list summary to issue."""
    try:
        from pathlib import Path
        
        github_client = GitHubClient()
        github = GitHubActionsHelper()
        
        # Read task file
        task_path = Path(task_file)
        if not task_path.exists():
            raise FileNotFoundError(f"Task file not found: {task_file}")
            
        task_content = task_path.read_text()
        
        # Count tasks
        total_tasks = task_content.count('- [ ]') + task_content.count('- [x]')
        completed_tasks = task_content.count('- [x]')
        
        # Extract first few tasks
        lines = task_content.split('\n')
        task_lines = [line for line in lines if line.strip().startswith('- [')]
        preview_tasks = '\n'.join(task_lines[:5])
        
        if len(task_lines) > 5:
            preview_tasks += f"\n... 他 {len(task_lines) - 5} タスク"
            
        comment = f"""## 📋 タスクリスト作成完了

**総タスク数**: {total_tasks}
**完了タスク**: {completed_tasks}

### タスクプレビュー:
{preview_tasks}

詳細は実装フェーズで確認されます。"""
        
        github_client.create_issue_comment(issue_number, comment)
        github.set_output('task_count', str(total_tasks))
        
        logger.info(f"Posted task summary with {total_tasks} tasks")
        
    except Exception as e:
        logger.error(f"Failed to post task summary: {e}")
        github.log_error(f"Failed to post task summary: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--model', required=True, help='Model to use')
@click.option('--max-turns', required=True, type=int, help='Maximum turns')
def analyze_env(issue_number, branch_name, model, max_turns):
    """Run environment analysis phase."""
    try:
        from phases.analyze_env import AnalyzeEnvPhase
        
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_env=max_turns
        )
        
        phase = AnalyzeEnvPhase()
        result = phase.run(params)
        
        logger.info(f"Environment analysis completed: {result}")
        
    except Exception as e:
        logger.error(f"Environment analysis failed: {e}")
        GitHubActionsHelper().log_error(f"Environment analysis failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--model', required=True, help='Model to use')
@click.option('--max-turns', required=True, type=int, help='Maximum turns')
def analyze_issue(issue_number, branch_name, model, max_turns):
    """Run issue analysis phase."""
    try:
        from phases.analyze_issue import AnalyzeIssuePhase
        
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_analysis=max_turns
        )
        
        phase = AnalyzeIssuePhase()
        result = phase.run(params)
        
        logger.info(f"Issue analysis completed: {result}")
        
    except Exception as e:
        logger.error(f"Issue analysis failed: {e}")
        GitHubActionsHelper().log_error(f"Issue analysis failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--model', required=True, help='Model to use')
@click.option('--max-turns', required=True, type=int, help='Maximum turns')
@click.option('--iteration', required=True, type=int, help='Iteration number')
def implement(issue_number, branch_name, model, max_turns, iteration):
    """Run implementation phase."""
    try:
        from phases.implement import ImplementPhase
        
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_implement=max_turns
        )
        
        phase = ImplementPhase()
        result = phase.run(params, iteration)
        
        logger.info(f"Implementation completed: {result}")
        
    except Exception as e:
        logger.error(f"Implementation failed: {e}")
        GitHubActionsHelper().log_error(f"Implementation failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--model', required=True, help='Model to use')
@click.option('--max-turns', required=True, type=int, help='Maximum turns')
@click.option('--iteration', required=True, type=int, help='Iteration number')
def review(issue_number, branch_name, model, max_turns, iteration):
    """Run review phase."""
    try:
        from phases.review import ReviewPhase
        
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_review=max_turns
        )
        
        phase = ReviewPhase()
        result = phase.run(params, iteration)
        
        logger.info(f"Review completed: {result}")
        
    except Exception as e:
        logger.error(f"Review failed: {e}")
        GitHubActionsHelper().log_error(f"Review failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
@click.option('--branch-name', required=True, help='Branch name')
@click.option('--model', required=True, help='Model to use')
@click.option('--max-turns', required=True, type=int, help='Maximum turns')
def update_knowledge(issue_number, branch_name, model, max_turns):
    """Run knowledge update phase."""
    try:
        from phases.update_knowledge import UpdateKnowledgePhase
        
        params = WorkflowParameters(
            issue_number=issue_number,
            issue_title="",
            issue_body="",
            branch_name=branch_name,
            model=model,
            max_turns_review=max_turns
        )
        
        phase = UpdateKnowledgePhase()
        result = phase.run(params)
        
        logger.info(f"Knowledge update completed: {result}")
        
    except Exception as e:
        logger.error(f"Knowledge update failed: {e}")
        GitHubActionsHelper().log_error(f"Knowledge update failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--review-file', required=True, help='Path to review file')
def process_review_result(review_file):
    """Process review result from Claude Code."""
    try:
        from phases.review import ReviewPhase
        from pathlib import Path
        
        phase = ReviewPhase()
        result = phase.process_review_result(Path(review_file))
        
        logger.info(f"Review processing completed: {result}")
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        logger.error(f"Review processing failed: {e}")
        GitHubActionsHelper().log_error(f"Review processing failed: {e}")
        sys.exit(1)


@cli.command('check-implementation-needed')
@click.option('--issue-number', required=True, type=int, help='GitHub issue number')
def check_implementation_needed(issue_number):
    """Check if implementation is needed based on issue analysis."""
    try:
        github = GitHubActionsHelper()
        work_dir = Path(f"claude/claude-work/issue-{issue_number}")
        
        # Check if task list exists from analyze-issue phase
        task_list_file = work_dir / "dialogue" / "task-list.md"
        
        if task_list_file.exists():
            logger.info("Task list found - implementation needed")
            github.set_output('should_implement', 'true')
        else:
            logger.info("No task list found - skipping implementation")
            github.set_output('should_implement', 'false')
            
    except Exception as e:
        logger.error(f"Failed to check implementation needed: {e}")
        github.log_error(f"Failed to check implementation needed: {e}")
        # Default to implementing if we can't determine
        github.set_output('should_implement', 'true')


if __name__ == '__main__':
    cli()