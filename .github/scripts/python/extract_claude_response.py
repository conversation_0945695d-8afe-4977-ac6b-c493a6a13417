#!/usr/bin/env python3
"""Extract <PERSON>'s last response from execution file."""
import json
import click
import sys
from pathlib import Path


@click.command()
@click.option('--execution-file', required=True, help='Path to Claude execution file')
@click.option('--output-file', required=True, help='Path to output file')
def extract_response(execution_file, output_file):
    """Extract <PERSON>'s last response from execution file."""
    try:
        with open(execution_file, 'r') as f:
            data = json.load(f)
        
        # Find the last assistant message
        last_message = None
        for item in reversed(data):
            if item.get('type') == 'assistant':
                for content in item['message']['content']:
                    if content.get('type') == 'text':
                        last_message = content['text']
                        break
                if last_message:
                    break
        
        # Write to output file
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if last_message:
            output_path.write_text(last_message)
        else:
            output_path.write_text('実装完了')
            
    except Exception as e:
        print(f'Error processing execution file: {e}')
        # Write default message on error
        Path(output_file).write_text('実装完了')
        sys.exit(1)


if __name__ == '__main__':
    extract_response()