# Claude Orchestrator Python Implementation

## 概要

Claude OrchestratorのPython実装版です。既存のBashスクリプトベースの実装をPythonで書き直し、以下の改善を実現しています：

- より堅牢なエラーハンドリング
- 型安全性とデータ構造の明確化
- デバッグとメンテナンスの容易さ
- テスト可能な構造

## ディレクトリ構造

```
.github/scripts/python/
├── core/               # コア機能とベースクラス
│   ├── __init__.py
│   └── base.py        # 基本クラスとユーティリティ
├── utils/             # 共通ユーティリティ
│   ├── __init__.py
│   ├── parameter_parser.py  # パラメータ解析
│   ├── github_client.py     # GitHub API操作
│   └── pr_creator.py        # PR作成ロジック
├── phases/            # ワークフローフェーズ
│   ├── __init__.py
│   ├── initialize.py  # 初期化フェーズ
│   └── finalize.py    # 最終化フェーズ
├── orchestrator.py    # メインCLIスクリプト
├── requirements.txt   # Python依存関係
└── README.md         # このファイル
```

## 使用方法

### 1. 依存関係のインストール

```bash
pip install -r .github/scripts/python/requirements.txt
```

### 2. CLI コマンド

#### トリガーチェック
```bash
python orchestrator.py check-trigger
```

#### パラメータ解析
```bash
python orchestrator.py parse-parameters --issue-number 123
```

#### 初期化
```bash
python orchestrator.py initialize \
  --issue-number 123 \
  --issue-title "Test Issue" \
  --issue-body "Issue description"
```

#### 最終化とPR作成
```bash
python orchestrator.py finalize \
  --issue-number 123 \
  --branch-name "claude-task/123-test" \
  --total-iterations 3
```

#### タスクリスト検証
```bash
python orchestrator.py verify-task-list \
  --work-dir "claude/claude-work/issue-123"
```

### 3. 後方互換性

既存のBashスクリプトとの互換性のため、以下のコマンドも使用可能：

```bash
python orchestrator.py run-script --script setup-workflow-parameters.sh
```

## 主な改善点

### 1. エラーハンドリング

```python
try:
    result = github_client.get_issue(issue_number)
except WorkflowError as e:
    logger.error(f"Failed to get issue: {e}")
    # 適切なエラー処理
```

### 2. 型安全性

```python
@dataclass
class WorkflowParameters:
    issue_number: int
    issue_title: str
    issue_body: str
    branch_name: str
    model: str = "claude-3-5-sonnet-20241022"
    # ... その他のパラメータ
```

### 3. 構造化されたログ

```python
logger = logging.getLogger(__name__)
logger.info(f"Processing issue #{issue_number}")
logger.debug(f"Parameters: {params}")
```

### 4. テスト可能な設計

各フェーズとユーティリティは独立したモジュールとして実装されており、単体テストが容易です。

## ワークフローファイルの更新

Python版を使用するには、ワークフローファイルを以下のように更新します：

```yaml
- name: Setup Python
  uses: actions/setup-python@v5
  with:
    python-version: '3.11'

- name: Install dependencies
  run: |
    pip install -r .github/scripts/python/requirements.txt

- name: Run Python script
  run: |
    python .github/scripts/python/orchestrator.py [command] [options]
```

## 移行状況

### 完了済み
- ✅ トリガーチェック
- ✅ パラメータ解析
- ✅ 初期化フェーズ
- ✅ PR作成とfinalize
- ✅ GitHub API操作

### 未実装
- ⏳ 環境分析フェーズ
- ⏳ Issue分析フェーズ
- ⏳ 実装フェーズ
- ⏳ レビューフェーズ
- ⏳ ナレッジ更新フェーズ

## デバッグ

デバッグモードを有効にするには：

```bash
export RUNNER_DEBUG=1
python orchestrator.py [command]
```

## トラブルシューティング

### 1. ModuleNotFoundError

```bash
# sys.pathに親ディレクトリが追加されていることを確認
sys.path.insert(0, str(Path(__file__).parent))
```

### 2. GitHub API エラー

```bash
# GH_TOKENが設定されていることを確認
export GH_TOKEN=your_token
```

### 3. Git操作エラー

```bash
# Gitの設定が正しいことを確認
git config user.name "Claude Orchestrator"
git config user.email "claude-orchestrator@github-actions"
```

## 今後の計画

1. 残りのフェーズの実装
2. 単体テストの追加
3. エラーリカバリーの強化
4. パフォーマンスの最適化
5. より詳細なログとメトリクス