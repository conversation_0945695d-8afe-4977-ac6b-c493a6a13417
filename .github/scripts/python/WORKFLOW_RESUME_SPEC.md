# ワークフロー再開・修正機能 仕様書

## 概要
Claude Orchestratorに、作業の途中からの再開や、完了した作業の修正を可能にする機能を追加します。

## ユースケース

### 1. 作業の続きから実行
- ワークフローが途中で停止した場合の再開
- 特定のフェーズから作業を再開
- レビューで修正が必要になった場合の継続作業

### 2. 実施済み作業の修正
- PR作成後に追加の修正が必要になった場合
- レビューコメントへの対応
- バグ修正や機能追加

## 実装方針

### 1. コメントによるトリガー
```
@claude-task resume       # 中断した作業を再開
@claude-task revise       # 実施済み作業を修正
@claude-task continue     # 最後の状態から継続
```

### 2. 作業状態の永続化
```yaml
# claude/claude-work/issue-{number}/state/workflow-state.json
{
  "issue_number": 123,
  "branch_name": "claude-task/issue-123",
  "current_phase": "implement",
  "current_iteration": 2,
  "phases_completed": ["initialize", "analyze-env", "analyze-issue"],
  "last_successful_step": "implement-iteration-1",
  "parameters": {
    "selected_model": "claude-3-5-sonnet-20241022",
    "max_turns_implement": 80,
    "iterations_limit": 10
  },
  "pr_created": false,
  "pr_number": null
}
```

### 3. パラメータ解析の拡張
```python
# parameter_parser.py に追加
def parse_resume_parameters(self, issue_body: str) -> Dict[str, Any]:
    """再開/修正モードのパラメータを解析"""
    params = {}
    
    # 再開モードの検出
    if "@claude-task resume" in issue_body:
        params["mode"] = "resume"
        params["resume_from"] = self._extract_resume_point(issue_body)
    elif "@claude-task revise" in issue_body:
        params["mode"] = "revise"
        params["revision_request"] = self._extract_revision_details(issue_body)
    elif "@claude-task continue" in issue_body:
        params["mode"] = "continue"
    
    return params
```

### 4. メインワークフローの修正
```yaml
# claude-orchestrator-main-python.yml
jobs:
  trigger-check:
    outputs:
      workflow_mode: ${{ steps.check.outputs.workflow_mode }}
      resume_from_phase: ${{ steps.check.outputs.resume_from_phase }}
      
  # 既存のジョブに条件を追加
  initialize:
    if: |
      needs.trigger-check.outputs.should_run == 'true' &&
      (needs.trigger-check.outputs.workflow_mode == 'new' ||
       needs.trigger-check.outputs.resume_from_phase == 'initialize')
```

### 5. 状態管理クラスの追加
```python
# utils/workflow_state.py
class WorkflowState:
    """ワークフロー状態の管理"""
    
    def __init__(self, issue_number: int):
        self.issue_number = issue_number
        self.state_file = f"claude/claude-work/issue-{issue_number}/state/workflow-state.json"
    
    def save_state(self, state: Dict[str, Any]) -> None:
        """状態を保存"""
        os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
        with open(self.state_file, 'w') as f:
            json.dump(state, f, indent=2)
    
    def load_state(self) -> Optional[Dict[str, Any]]:
        """状態を読み込み"""
        if os.path.exists(self.state_file):
            with open(self.state_file, 'r') as f:
                return json.load(f)
        return None
    
    def update_phase_completion(self, phase: str) -> None:
        """フェーズ完了を記録"""
        state = self.load_state() or {}
        completed = state.get("phases_completed", [])
        if phase not in completed:
            completed.append(phase)
        state["phases_completed"] = completed
        state["last_update"] = datetime.utcnow().isoformat()
        self.save_state(state)
```

### 6. 各フェーズでの状態保存
```python
# 各フェーズのワークフローに追加
- name: Save workflow state
  run: |
    python -c "
import sys
sys.path.insert(0, '.github/scripts/python')
from utils.workflow_state import WorkflowState

state = WorkflowState(${{ inputs.issue_number }})
state.update_phase_completion('analyze-env')
"
```

## 実装の優先順位

1. **Phase 1: 基本的な再開機能**
   - workflow_state.pyの実装
   - orchestrator.pyへのresumeコマンド追加
   - メインワークフローの条件分岐

2. **Phase 2: 修正モード**
   - reviseコマンドの実装
   - PR更新機能
   - 修正内容の追跡

3. **Phase 3: 高度な機能**
   - 特定のステップからの再開
   - 部分的な再実行
   - 進捗の可視化

## セキュリティ考慮事項
- 状態ファイルへのアクセス制限
- 再開時の権限チェック
- 不正な状態遷移の防止