#!/usr/bin/env bash
# check-oauth-token.sh - OAuthトークンの有効期限チェック
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 環境変数から有効期限を取得
EXPIRES_AT="${CLAUDE_EXPIRES_AT:-}"
CURRENT_TIME_MS=$(date +%s000)  # 現在時刻をミリ秒に変換

# 有効期限が設定されていない場合
if [[ -z "$EXPIRES_AT" ]]; then
    echo "::warning::CLAUDE_EXPIRES_AT is not set"
    echo "expires_at=" >> "$GITHUB_OUTPUT"
    echo "expiry_date=Not Set" >> "$GITHUB_OUTPUT"
    echo "remaining_seconds=0" >> "$GITHUB_OUTPUT"
    echo "remaining_days=0" >> "$GITHUB_OUTPUT"
    echo "remaining_hours=0" >> "$GITHUB_OUTPUT"
    echo "remaining_minutes=0" >> "$GITHUB_OUTPUT"
    echo "token_status=not_set" >> "$GITHUB_OUTPUT"
    exit 0
fi

# ミリ秒単位での残り時間を計算
REMAINING_MS=$((EXPIRES_AT - CURRENT_TIME_MS))
REMAINING_SEC=$((REMAINING_MS / 1000))
DAYS=$((REMAINING_SEC / 86400))
HOURS=$(((REMAINING_SEC % 86400) / 3600))
MINUTES=$(((REMAINING_SEC % 3600) / 60))

# 有効期限を人間が読める形式に変換（JST）
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    EXPIRY_DATE=$(TZ='Asia/Tokyo' date -r $((EXPIRES_AT / 1000)) '+%Y-%m-%d %H:%M:%S JST')
else
    # Linux (GitHub Actions)
    EXPIRY_DATE=$(TZ='Asia/Tokyo' date -d "@$((EXPIRES_AT / 1000))" '+%Y-%m-%d %H:%M:%S JST')
fi

# GitHub Actions出力
echo "expires_at=$EXPIRES_AT" >> "$GITHUB_OUTPUT"
echo "expiry_date=$EXPIRY_DATE" >> "$GITHUB_OUTPUT"
echo "remaining_seconds=$REMAINING_SEC" >> "$GITHUB_OUTPUT"
echo "remaining_days=$DAYS" >> "$GITHUB_OUTPUT"
echo "remaining_hours=$HOURS" >> "$GITHUB_OUTPUT"
echo "remaining_minutes=$MINUTES" >> "$GITHUB_OUTPUT"

# トークンの状態を判定
if [[ $REMAINING_SEC -le 0 ]]; then
    echo "::error::OAuth token has expired!"
    echo "token_status=expired" >> "$GITHUB_OUTPUT"
elif [[ $REMAINING_SEC -le 86400 ]]; then
    echo "::warning::OAuth token expires in less than 24 hours!"
    echo "token_status=expiring_soon" >> "$GITHUB_OUTPUT"
else
    echo "::notice::OAuth token is valid until $EXPIRY_DATE ($DAYS days, $HOURS hours)"
    echo "token_status=valid" >> "$GITHUB_OUTPUT"
fi