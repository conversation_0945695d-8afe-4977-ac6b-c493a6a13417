#!/usr/bin/env bash
# verify-task-list.sh - タスクリストの作成確認
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 必須環境変数
: "${ISSUE_NUMBER:?Issue number is required}"

WORK_DIR="claude/claude-work/issue-${ISSUE_NUMBER}"
TASK_FILE="$WORK_DIR/dialogue/task-list.md"

echo "::notice::Verifying task list creation"

if [[ -f "$TASK_FILE" ]]; then
    echo "✅ Task list created: $TASK_FILE"
    echo "task_list_exists=true" >> "$GITHUB_OUTPUT"
    
    # タスク数をカウント
    TASK_COUNT=$(grep -c "^\s*[0-9]\+\.\s*\[\s*\]" "$TASK_FILE" || echo "0")
    echo "task_count=$TASK_COUNT" >> "$GITHUB_OUTPUT"
    echo "::notice::Found $TASK_COUNT tasks"
    
    # タスクリストの内容を表示（最初の10行）
    echo "=== Task list preview ==="
    head -10 "$TASK_FILE"
    
    # タスクが0個の場合はエラー
    if [[ "$TASK_COUNT" -eq 0 ]]; then
        echo "::error::No tasks found in task list"
        exit 1
    fi
else
    echo "❌ Task list not found at: $TASK_FILE"
    echo "task_list_exists=false" >> "$GITHUB_OUTPUT"
    echo "task_count=0" >> "$GITHUB_OUTPUT"
    echo "::error::Task list was not created"
    
    # TodoWriteツールの使用を確認
    echo "Checking if TodoWrite was used..."
    if [[ -d "$WORK_DIR" ]]; then
        echo "Work directory contents:"
        ls -la "$WORK_DIR"
    fi
    
    exit 1
fi