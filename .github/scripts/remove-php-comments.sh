#!/bin/bash

# PHPコメント削除スクリプト
# WordPressコアファイルは除外し、テーマとプラグインのPHPファイルのみ処理

# 処理対象ディレクトリ
DEPLOY_DIR="./deploy-files"

# 除外パターン
EXCLUDE_PATTERNS=(
    "*/wp-admin/*"
    "*/wp-includes/*"
    "*/vendor/*"
    "*/node_modules/*"
)

# 一時ファイル用のディレクトリ
TEMP_DIR=$(mktemp -d)

echo "PHPコメント削除処理を開始します..."

# テーマのPHPファイルのみを検索（プラグインは除外）
find "$DEPLOY_DIR/wp-content/themes" -name "*.php" -type f | while read -r file; do
    # 除外パターンのチェック
    skip=false
    for pattern in "${EXCLUDE_PATTERNS[@]}"; do
        if [[ "$file" == $pattern ]]; then
            skip=true
            break
        fi
    done
    
    if [ "$skip" = true ]; then
        continue
    fi
    
    # PHPコメントを削除（php -wコマンドを使用）
    echo "処理中: $file"
    php -w "$file" > "$TEMP_DIR/temp.php" 2>/dev/null
    
    if [ $? -eq 0 ] && [ -s "$TEMP_DIR/temp.php" ]; then
        # 元のファイルと置き換え
        mv "$TEMP_DIR/temp.php" "$file"
        
        # ファイルの先頭に改行を追加（php -wは改行を削除するため）
        if ! head -n 1 "$file" | grep -q "^<?php"; then
            echo -e "<?php\n$(cat "$file")" > "$file"
        fi
    else
        echo "警告: $file の処理に失敗しました"
    fi
done

# 一時ディレクトリを削除
rm -rf "$TEMP_DIR"

echo "PHPコメント削除処理が完了しました"