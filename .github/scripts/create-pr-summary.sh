#!/usr/bin/env bash
# create-pr-summary.sh - Pull Request サマリーの作成
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 必須環境変数
: "${ISSUE_NUMBER:?Issue number is required}"
: "${REPOSITORY:?Repository is required}"
: "${GH_TOKEN:?GitHub token is required}"
: "${TOTAL_ITERATIONS:?Total iterations is required}"

WORK_DIR="claude/claude-work/issue-${ISSUE_NUMBER}"

# Issue情報を取得
echo "::notice::Fetching issue #$ISSUE_NUMBER information"
ISSUE_INFO=$(gh api "repos/${REPOSITORY}/issues/${ISSUE_NUMBER}")
ISSUE_TITLE=$(echo "$ISSUE_INFO" | jq -r '.title')

# PRの説明を作成
PR_BODY="## 概要

Issue #${ISSUE_NUMBER} の実装です。

## 実装内容
"

# 各イテレーションの実装内容を追加
for i in $(seq 1 "$TOTAL_ITERATIONS"); do
    if [[ -f "$WORK_DIR/implementation/iteration-${i}.md" ]]; then
        PR_BODY="${PR_BODY}
### イテレーション ${i}
"
        # 実装内容の最初の部分を抽出
        PR_BODY="${PR_BODY}$(head -20 "$WORK_DIR/implementation/iteration-${i}.md" || echo "実装内容なし")"
    fi
done

# 変更ファイルリストを追加
PR_BODY="${PR_BODY}

## 変更ファイル
"

# git diffの結果を処理
while IFS=$'\t' read -r status file; do
    case $status in
        A) PR_BODY="${PR_BODY}"$'\n'"- ➕ ${file}";;
        M) PR_BODY="${PR_BODY}"$'\n'"- ✏️ ${file}";;
        D) PR_BODY="${PR_BODY}"$'\n'"- ❌ ${file}";;
    esac
done < <(git diff origin/develop --name-status)

# テスト結果を追加（あれば）
PR_BODY="${PR_BODY}

## テスト
"

if [[ -f "$WORK_DIR/test-results.md" ]]; then
    PR_BODY="${PR_BODY}$(cat "$WORK_DIR/test-results.md")"
else
    PR_BODY="${PR_BODY}テスト結果なし"
fi

# レビュー履歴を追加
PR_BODY="${PR_BODY}

## レビュー履歴

| イテレーション | 結果 | レビュワー |
|--------------|------|-----------|"

for i in $(seq 1 "$TOTAL_ITERATIONS"); do
    if [[ -f "$WORK_DIR/review/review-iteration-${i}.md" ]]; then
        # レビュー結果から合格/不合格を判定
        if grep -q "## 総評" "$WORK_DIR/review/review-iteration-${i}.md" && \
           grep -A 2 "## 総評" "$WORK_DIR/review/review-iteration-${i}.md" | grep -q "合格"; then
            RESULT="✅ 合格"
        else
            RESULT="⚠️ 要修正"
        fi
        PR_BODY="${PR_BODY}
| ${i} | $RESULT | Claude |"
    fi
done

# 関連情報を追加
PR_BODY="${PR_BODY}

## 関連情報

- Fixes #${ISSUE_NUMBER}
- ブランチ: \`$(git branch --show-current)\`
- 総イテレーション数: $TOTAL_ITERATIONS

---
*このPRは Claude Orchestrator により自動生成されました*"

# PR本文をファイルに保存
echo "$PR_BODY" > /tmp/pr-body.md

# GitHub Actions出力（改行をエスケープ）
{
    echo "pr_title<<EOF"
    echo "fix: ${ISSUE_TITLE} (#${ISSUE_NUMBER})"
    echo "EOF"
    echo "pr_body<<EOF"
    echo "$PR_BODY"
    echo "EOF"
} >> "$GITHUB_OUTPUT"

echo "::notice::Pull request summary created"