#!/usr/bin/env bash
# implementation-loop-control.sh - 実装・レビューループの制御
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 必須環境変数
: "${ITERATION:?Current iteration is required}"
: "${MAX_ITERATIONS:?Max iterations is required}"

echo "::notice::Checking iteration $ITERATION of max $MAX_ITERATIONS"

# イテレーション制限のチェック
if [[ $ITERATION -le $MAX_ITERATIONS ]]; then
    # TODO: 前回のレビュー結果を確認する機能を追加
    # 現在は簡略化されているため、常にcontinue=true
    
    if [[ $ITERATION -gt 1 ]]; then
        # 前回のレビューが合格したかチェック（将来的に実装）
        echo "::notice::Continuing to iteration $ITERATION"
        echo "continue=true" >> "$GITHUB_OUTPUT"
    else
        echo "::notice::Starting first iteration"
        echo "continue=true" >> "$GITHUB_OUTPUT"
    fi
    echo "iteration=$ITERATION" >> "$GITHUB_OUTPUT"
else
    echo "::notice::Reached maximum iterations ($MAX_ITERATIONS)"
    echo "continue=false" >> "$GITHUB_OUTPUT"
fi