#!/usr/bin/env bash
# setup-workflow-parameters.sh - ワークフローパラメータの設定
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# より詳細なエラーハンドリング
trap 'echo "Error at line $LINENO: $BASH_COMMAND"' ERR

# 必須環境変数
: "${ISSUE_NUMBER:?Issue number is required}"
: "${ISSUE_TITLE:?Issue title is required}"
: "${ISSUE_BODY:-}"  # Issue本文は空の場合もある

# デフォルト値
DEFAULT_MODEL='claude-3-5-sonnet-20241022'
DEFAULT_MODEL_NAME='Claude 3.5 Sonnet'

# モデルの選択
selectedModel="$DEFAULT_MODEL"
displayModelName="$DEFAULT_MODEL_NAME"

# Issue本文からモデル設定を抽出（日本語の説明を含む形式にも対応）
if echo "$ISSUE_BODY" | grep -E -q "claude-opus-4-20250514"; then
    selectedModel='claude-opus-4-20250514'
    displayModelName='Claude Opus 4'
    echo "::notice::Selected model: Claude Opus 4"
elif echo "$ISSUE_BODY" | grep -E -q "claude-sonnet-4-20250514"; then
    selectedModel='claude-sonnet-4-20250514'
    displayModelName='Claude Sonnet 4'
    echo "::notice::Selected model: Claude Sonnet 4"
elif echo "$ISSUE_BODY" | grep -E -q "claude-3-5-haiku-20241022"; then
    selectedModel='claude-3-5-haiku-20241022'
    displayModelName='Claude 3.5 Haiku'
    echo "::notice::Selected model: Claude 3.5 Haiku"
else
    echo "::notice::Using default model: $displayModelName"
fi

# ターン数設定の抽出（デフォルト値）
max_turns_env=$(echo "$ISSUE_BODY" | grep -o "環境分析フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1 || true)
max_turns_env=${max_turns_env:-80}

max_turns_analysis=$(echo "$ISSUE_BODY" | grep -o "Issue分析フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1 || true)
max_turns_analysis=${max_turns_analysis:-80}

max_turns_implement=$(echo "$ISSUE_BODY" | grep -o "実装フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1 || true)
max_turns_implement=${max_turns_implement:-80}

max_turns_review=$(echo "$ISSUE_BODY" | grep -o "レビューフェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1 || true)
max_turns_review=${max_turns_review:-80}

max_turns_knowledge=80  # 固定値

# イテレーション制限
iterations_limit=$(echo "$ISSUE_BODY" | grep -o "コーダー・レビュアー対話回数制限: [0-9]\+" | grep -o "[0-9]\+" | head -1 || true)
iterations_limit=${iterations_limit:-10}

# ブランチ名生成（日本語文字を含む特殊文字を除去）
echo "::debug::ISSUE_TITLE before normalization: '$ISSUE_TITLE'"

# 改行文字を含む可能性があるため、最初に改行をスペースに置換
TITLE_SINGLE_LINE=$(echo "$ISSUE_TITLE" | tr '\n' ' ' | tr '\r' ' ')
echo "::debug::TITLE_SINGLE_LINE: '$TITLE_SINGLE_LINE'"

NORMALIZED_TITLE=$(echo "$TITLE_SINGLE_LINE" | \
    sed 's/[^a-zA-Z0-9 _-]//g' | \
    sed 's/[ _]/-/g' | \
    sed 's/--*/-/g' | \
    sed 's/^-//;s/-$//' | \
    cut -c1-50)
echo "::debug::NORMALIZED_TITLE after processing: '$NORMALIZED_TITLE'"

BRANCH_NAME="claude-task/${ISSUE_NUMBER}-${NORMALIZED_TITLE}"
echo "::debug::BRANCH_NAME: '$BRANCH_NAME'"

# GitHub Actions出力
if [ -z "${GITHUB_OUTPUT:-}" ]; then
  echo "::error::GITHUB_OUTPUT environment variable is not set"
  exit 1
fi

echo "::debug::Writing to GITHUB_OUTPUT file: $GITHUB_OUTPUT"

# 各変数を個別に出力（エラーが発生した場合に特定しやすくするため）
echo "branch_name=$BRANCH_NAME" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write branch_name"; exit 1; }
echo "selected_model=$selectedModel" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write selected_model"; exit 1; }
echo "display_model_name=$displayModelName" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write display_model_name"; exit 1; }
echo "max_turns_env=$max_turns_env" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write max_turns_env"; exit 1; }
echo "max_turns_analysis=$max_turns_analysis" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write max_turns_analysis"; exit 1; }
echo "max_turns_implement=$max_turns_implement" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write max_turns_implement"; exit 1; }
echo "max_turns_review=$max_turns_review" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write max_turns_review"; exit 1; }
echo "max_turns_knowledge=$max_turns_knowledge" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write max_turns_knowledge"; exit 1; }
echo "iterations_limit=$iterations_limit" >> "$GITHUB_OUTPUT" || { echo "::error::Failed to write iterations_limit"; exit 1; }

# デバッグ出力
echo "::notice::Branch name: $BRANCH_NAME"
echo "::notice::Model: $displayModelName ($selectedModel)"
echo "::notice::Iterations limit: $iterations_limit"
echo "::notice::Max turns - Env: $max_turns_env, Analysis: $max_turns_analysis, Implement: $max_turns_implement, Review: $max_turns_review"