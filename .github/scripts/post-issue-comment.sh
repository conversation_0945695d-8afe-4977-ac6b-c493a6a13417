#!/usr/bin/env bash
# post-issue-comment.sh - Issue にコメントを投稿
set -euo pipefail

# デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 必須環境変数
: "${ISSUE_NUMBER:?Issue number is required}"
: "${COMMENT_TYPE:?Comment type is required}"
: "${GH_TOKEN:?GitHub token is required}"

# オプション環境変数
WORK_DIR="claude/claude-work/issue-${ISSUE_NUMBER}"
ITERATION="${ITERATION:-}"
TASK_COUNT="${TASK_COUNT:-0}"

case "$COMMENT_TYPE" in
    "task-list")
        TASK_FILE="$WORK_DIR/dialogue/task-list.md"
        if [[ -f "$TASK_FILE" ]]; then
            # タスクリストの要約を作成
            COMMENT="## 📋 タスクリスト作成完了"$'\n\n'
            COMMENT="${COMMENT}**タスク数**: $TASK_COUNT"$'\n\n'
            COMMENT="${COMMENT}### 主なタスク:"$'\n'
            
            # 最初の5つのタスクを抽出
            while IFS= read -r line; do
                COMMENT="${COMMENT}${line}"$'\n'
            done < <(grep "^\s*[0-9]\+\.\s*\[\s*\]" "$TASK_FILE" | head -5)
            
            if [[ $TASK_COUNT -gt 5 ]]; then
                COMMENT="${COMMENT}... 他 $((TASK_COUNT - 5)) タスク"$'\n'
            fi
            
            gh issue comment "$ISSUE_NUMBER" --body "$COMMENT"
        fi
        ;;
        
    "review-result")
        REVIEW_FILE="$WORK_DIR/review/review-iteration-${ITERATION}.md"
        if [[ -f "$REVIEW_FILE" ]]; then
            # レビュー結果の要約を作成
            COMMENT="## 📝 レビュー結果 (イテレーション ${ITERATION})"$'\n\n'
            
            # 総評を抽出
            if grep -A 2 "## 総評" "$REVIEW_FILE" | grep -q "合格"; then
                COMMENT="${COMMENT}**結果**: ✅ 合格"$'\n\n'
            else
                COMMENT="${COMMENT}**結果**: ⚠️ 要修正"$'\n\n'
            fi
            
            # 良い点と改善点を抽出（最初の3項目まで）
            if grep -q "## 良い点" "$REVIEW_FILE"; then
                COMMENT="${COMMENT}### 良い点"$'\n'
                grep -A 5 "## 良い点" "$REVIEW_FILE" | head -6 | tail -5 | while read -r line; do
                    [[ -n "$line" ]] && COMMENT="${COMMENT}${line}"$'\n'
                done
            fi
            
            gh issue comment "$ISSUE_NUMBER" --body "$COMMENT"
        fi
        ;;
        
    *)
        echo "::error::Unknown comment type: $COMMENT_TYPE"
        exit 1
        ;;
esac

echo "::notice::Comment posted to issue #$ISSUE_NUMBER"