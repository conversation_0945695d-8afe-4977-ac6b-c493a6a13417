# GitHub Actions Scripts

このディレクトリには、GitHub Actionsワークフローで使用するシェルスクリプトを格納します。

## 目的

- YAMLファイル内の複雑なスクリプトを外部化
- 構文エラーのリスク削減
- スクリプトの再利用性向上
- テストしやすさの向上

## ディレクトリ構造

```
.github/
├── workflows/         # ワークフローファイル
├── claude-prompts/    # Claudeプロンプト
└── scripts/          # シェルスクリプト
    ├── check-trigger.sh
    ├── check-oauth-token.sh
    ├── initialize-project.sh
    └── ...
```

## 使用方法

### 1. 基本的な使用方法

```yaml
- name: Check trigger
  id: check
  run: |
    bash .github/scripts/check-trigger.sh
```

### 2. 引数を渡す場合

```yaml
- name: Check trigger
  id: check
  run: |
    bash .github/scripts/check-trigger.sh \
      "${{ github.event_name }}" \
      "${{ github.event.issue.number }}" \
      "${{ github.event.inputs.issue_number }}"
```

### 3. 環境変数を使用する場合

```yaml
- name: Check trigger
  id: check
  env:
    EVENT_NAME: ${{ github.event_name }}
    ISSUE_NUMBER: ${{ github.event.issue.number }}
    INPUT_ISSUE_NUMBER: ${{ github.event.inputs.issue_number }}
  run: |
    bash .github/scripts/check-trigger.sh
```

## スクリプト作成のガイドライン

### 1. シバン行
```bash
#!/usr/bin/env bash
```

### 2. エラーハンドリング
```bash
set -euo pipefail
```

### 3. 環境変数のデフォルト値
```bash
EVENT_NAME="${EVENT_NAME:-workflow_dispatch}"
ISSUE_NUMBER="${ISSUE_NUMBER:-}"
```

### 4. GitHub Actions出力
```bash
echo "key=value" >> $GITHUB_OUTPUT
```

### 5. デバッグ情報
```bash
if [[ "${RUNNER_DEBUG:-0}" == "1" ]]; then
  set -x
fi
```

## メリット

1. **構文エラーの削減**: YAMLのインデント問題を回避
2. **テスト可能**: スクリプトを独立してテスト可能
3. **再利用性**: 複数のワークフローで共有可能
4. **バージョン管理**: スクリプトの変更履歴を追跡
5. **IDE サポート**: シェルスクリプトの構文ハイライトとリンティング

## 例: check-trigger.sh

```bash
#!/usr/bin/env bash
set -euo pipefail

# GitHub Actions デバッグモード
[[ "${RUNNER_DEBUG:-0}" == "1" ]] && set -x

# 環境変数またはパラメータから値を取得
EVENT_NAME="${1:-${EVENT_NAME:-}}"
ISSUE_NUMBER="${2:-${ISSUE_NUMBER:-}}"
INPUT_ISSUE_NUMBER="${3:-${INPUT_ISSUE_NUMBER:-}}"

# メイン処理
if [[ "$EVENT_NAME" == "workflow_dispatch" ]]; then
    echo "should_run=true" >> "$GITHUB_OUTPUT"
    echo "issue_number=$INPUT_ISSUE_NUMBER" >> "$GITHUB_OUTPUT"
else
    # Issue番号を取得
    if [[ -z "$ISSUE_NUMBER" ]]; then
        echo "::error::Issue number not found"
        exit 1
    fi
    
    # ラベルをチェック
    ISSUE_LABELS=$(gh api "repos/${GITHUB_REPOSITORY}/issues/${ISSUE_NUMBER}" --jq '.labels[].name' | tr '\n' ' ')
    
    if echo "$ISSUE_LABELS" | grep -q "claude-done"; then
        echo "should_run=false" >> "$GITHUB_OUTPUT"
        echo "::notice::Issue already processed (has claude-done label)"
    else
        echo "should_run=true" >> "$GITHUB_OUTPUT"
        echo "issue_number=${ISSUE_NUMBER}" >> "$GITHUB_OUTPUT"
    fi
fi
```