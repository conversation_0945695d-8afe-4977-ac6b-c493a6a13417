name: Example - Refactored with External Scripts

on:
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number

jobs:
  example:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      # 外部スクリプトを使用したトリガーチェック
      - name: Check trigger
        id: check
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          EVENT_NAME: ${{ github.event_name }}
          ISSUE_NUMBER: ${{ github.event.issue.number }}
          INPUT_ISSUE_NUMBER: ${{ github.event.inputs.issue_number }}
        run: bash .github/scripts/check-trigger.sh
        
      # 外部スクリプトを使用したOAuthトークンチェック
      - name: Check OAuth token
        id: oauth-check
        env:
          CLAUDE_EXPIRES_AT: ${{ secrets.CLAUDE_EXPIRES_AT }}
        run: bash .github/scripts/check-oauth-token.sh
        
      # 結果の使用
      - name: Display results
        run: |
          echo "Should run: ${{ steps.check.outputs.should_run }}"
          echo "Issue number: ${{ steps.check.outputs.issue_number }}"
          echo "Token status: ${{ steps.oauth-check.outputs.token_status }}"
          echo "Expiry date: ${{ steps.oauth-check.outputs.expiry_date }}"