name: <PERSON> Orchestrator Python

# Python版メインワークフロー - 各フェーズのワークフローを順次呼び出す

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number
        description: 'Issue番号'

# 同一Issueの同時実行を防ぐ
concurrency:
  group: claude-orchestrator-${{ github.event.issue.number || github.event.inputs.issue_number }}
  cancel-in-progress: false

jobs:
  # トリガーチェック（issue_commentの場合のみ）
  trigger-check:
    if: |
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude-task')) ||
      github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
      issue_number: ${{ steps.check.outputs.issue_number }}
      workflow_mode: ${{ steps.check.outputs.workflow_mode }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github/scripts
            
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
            
      - name: Check trigger
        id: check
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          GITHUB_EVENT_NAME: ${{ github.event_name }}
          GITHUB_EVENT_PATH: ${{ github.event_path }}
          ISSUE_NUMBER: ${{ github.event.inputs.issue_number }}
        run: python .github/scripts/python/orchestrator.py check-trigger

  # Load existing state for resume/revise modes
  load-state:
    needs: trigger-check
    if: |
      needs.trigger-check.outputs.should_run == 'true' &&
      needs.trigger-check.outputs.workflow_mode != 'new'
    runs-on: ubuntu-latest
    outputs:
      can_resume: ${{ steps.load.outputs.can_resume }}
      resume_mode: ${{ steps.load.outputs.resume_mode }}
      next_phase: ${{ steps.load.outputs.next_phase }}
      current_iteration: ${{ steps.load.outputs.current_iteration }}
      branch_name: ${{ steps.load.outputs.branch_name }}
      selected_model: ${{ steps.load.outputs.selected_model }}
      max_turns_env: ${{ steps.load.outputs.max_turns_env }}
      max_turns_analysis: ${{ steps.load.outputs.max_turns_analysis }}
      max_turns_implement: ${{ steps.load.outputs.max_turns_implement }}
      max_turns_review: ${{ steps.load.outputs.max_turns_review }}
      iterations_limit: ${{ steps.load.outputs.iterations_limit }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github/scripts
            claude/claude-work
            
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
          
      - name: Load workflow state
        id: load
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py load-state \
            --issue-number ${{ needs.trigger-check.outputs.issue_number }}

  # Phase 1: Initialize
  initialize:
    needs: trigger-check
    if: |
      needs.trigger-check.outputs.should_run == 'true' &&
      (needs.trigger-check.outputs.workflow_mode == 'new' ||
       needs.trigger-check.outputs.workflow_mode == 'resume')
    uses: ./.github/workflows/claude-phase-initialize-python.yml
    with:
      issue_number: ${{ needs.trigger-check.outputs.issue_number }}
    secrets: inherit

  # Phase 2: Environment Analysis (条件付き)
  analyze-env:
    needs: initialize
    if: |
      needs.initialize.result == 'success' &&
      needs.initialize.outputs.env_analysis_needed == 'true'
    uses: ./.github/workflows/claude-phase-analyze-env-python.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_env || 80 }}
    secrets: inherit

  # Phase 3: Issue Analysis
  analyze-issue:
    needs: [initialize, analyze-env]
    if: |
      always() &&
      needs.initialize.result == 'success' &&
      (needs.analyze-env.result == 'success' || needs.analyze-env.result == 'skipped')
    uses: ./.github/workflows/claude-phase-analyze-issue-python.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_analysis || 80 }}
    secrets: inherit

  # Implementation Controller - シンプル版
  implementation-controller:
    needs: analyze-issue
    if: needs.analyze-issue.result == 'success'
    runs-on: ubuntu-latest
    outputs:
      should_implement: ${{ steps.check.outputs.should_implement }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ needs.initialize.outputs.branch_name }}
          sparse-checkout: |
            claude/claude-work
            
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
          
      - name: Check if implementation needed
        id: check
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py check-implementation-needed \
            --issue-number ${{ needs.initialize.outputs.issue_number }}

  # Phase 4: Implementation - シンプル版
  implement:
    needs: [initialize, analyze-issue, implementation-controller]
    if: needs.implementation-controller.outputs.should_implement == 'true'
    uses: ./.github/workflows/claude-phase-implement-python.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_implement || 80 }}
      iteration: 1
      should_run: 'true'
    secrets: inherit

  # Phase 5: Review - シンプル版
  review:
    needs: [initialize, implement]
    if: always() && needs.implement.result == 'success'
    uses: ./.github/workflows/claude-phase-review-python.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_review || 80 }}
      iteration: 1
      iterations_limit: ${{ needs.initialize.outputs.iterations_limit || 3 }}
    secrets: inherit

  # Phase 6: Knowledge Update
  update-knowledge:
    needs: [initialize, implement]
    if: |
      always() &&
      needs.implement.result == 'success'
    uses: ./.github/workflows/claude-phase-update-knowledge-python.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      selected_model: ${{ needs.initialize.outputs.selected_model }}
      max_turns: ${{ needs.initialize.outputs.max_turns_review || 80 }}
    secrets: inherit

  # Phase 7: Finalize
  finalize:
    needs: [initialize, implement, review, update-knowledge]
    if: |
      always() &&
      needs.initialize.result == 'success' &&
      needs.implement.result == 'success'
    uses: ./.github/workflows/claude-phase-finalize-python.yml
    with:
      issue_number: ${{ needs.initialize.outputs.issue_number }}
      branch_name: ${{ needs.initialize.outputs.branch_name }}
      total_iterations: ${{ needs.review.outputs.total_iterations || 1 }}
    secrets: inherit