name: <PERSON> Phase - Finalize (Python)

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: number
      branch_name:
        required: true
        type: string
      total_iterations:
        required: true
        type: number

jobs:
  finalize:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
          
      - name: Setup Git Config
        run: |
          git config user.name "Claude Orchestrator"
          git config user.email "claude-orchestrator@github-actions"
          git config push.default current
          
      - name: Notify Start
        uses: rtCamp/action-slack-notify@v2
        if: ${{ vars.USE_SLACK == 'true' }}
        env:
          SLACK_USERNAME: Claude Orchestrator
          SLACK_ICON: https://ui-avatars.com/api/?name=CO&size=48&background=007bff&color=fff&rounded=true
          SLACK_TITLE: Finalize Phase Started
          SLACK_MESSAGE: |
            Issue: #${{ inputs.issue_number }}
            Branch: ${{ inputs.branch_name }}
            Total Iterations: ${{ inputs.total_iterations }}
          SLACK_COLOR: 007bff
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          
      - name: Create Pull Request
        id: create-pr
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          GITHUB_REPOSITORY: ${{ github.repository }}
        run: |
          python .github/scripts/python/orchestrator.py create-pr \
            --issue-number ${{ inputs.issue_number }} \
            --branch-name ${{ inputs.branch_name }} \
            --total-iterations ${{ inputs.total_iterations }}
          
      - name: Update Issue Status
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py update-issue-status \
            --issue-number ${{ inputs.issue_number }} \
            --remove-labels "claude-processing" \
            --add-labels "claude-done"
          
      - name: Post Completion Comment
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py post-completion-comment \
            --issue-number ${{ inputs.issue_number }} \
            --pr-number ${{ steps.create-pr.outputs.pr_number }} \
            --pr-url ${{ steps.create-pr.outputs.pr_url }} \
            --total-iterations ${{ inputs.total_iterations }}
          
      - name: Create Summary Report  
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py finalize \
            --issue-number ${{ inputs.issue_number }} \
            --branch-name ${{ inputs.branch_name }} \
            --total-iterations ${{ inputs.total_iterations }}
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_USERNAME: Claude Orchestrator
          SLACK_ICON: https://ui-avatars.com/api/?name=CO&size=48&background=28a745&color=fff&rounded=true
          SLACK_TITLE: Workflow Completed
          SLACK_MESSAGE: |
            Issue: #${{ inputs.issue_number }}
            PR: #${{ steps.create-pr.outputs.pr_number }}
            URL: ${{ steps.create-pr.outputs.pr_url }}
            Status: ${{ job.status }}
          SLACK_COLOR: ${{ job.status == 'success' && '28a745' || 'dc3545' }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}