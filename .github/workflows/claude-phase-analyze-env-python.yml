name: <PERSON> Phase - Analyze Environment (Python)

# 環境分析フェーズ - プロジェクト構造と技術スタックの分析

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: number
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: number
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false

jobs:
  analyze-environment:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
          
      - name: Setup Git Config
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py setup-git-config
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: '環境分析Bot'
          SLACK_COLOR: '#2196F3'
          SLACK_TITLE: '🔍 環境分析Bot: 分析開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **フェーズ**: 環境分析
            **役割**: 環境分析専門家
            **目的**: プロジェクト構造と技術スタックの分析
        continue-on-error: true
        
      # プロンプトを外部ファイルから読み込む
      - name: Load prompt
        id: prompt
        run: |
          PROMPT=$(cat .github/claude-prompts/analyze-env.md)
          echo "prompt<<EOF" >> $GITHUB_OUTPUT
          echo "$PROMPT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Execute Claude - Environment Analysis
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: 20
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: ${{ steps.prompt.outputs.prompt }}
          
      - name: Debug Claude Results
        if: always()
        run: |
          echo "=== Environment Analysis Debug ==="
          echo "Execution file: ${{ steps.claude.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.claude.outputs.conclusion }}"
          echo "Model used: ${{ steps.claude.outputs.model }}"
          
          # ファイル作成の確認
          echo "=== File Creation Check ==="
          if [ -d "claude/claude-knowledge/environment" ]; then
            echo "✅ Environment knowledge directory exists"
            ls -la claude/claude-knowledge/environment/
          else
            echo "❌ Environment knowledge directory not found"
          fi
          
      - name: Process Claude Results
        if: steps.claude.outcome == 'success'
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # Python実装でワークフロー状態を更新し、Issueコメントを投稿
          python .github/scripts/python/orchestrator.py process-claude-result \
            --issue-number ${{ inputs.issue_number }} \
            --phase analyze-env \
            --execution-file "${{ steps.claude.outputs.execution_file }}" \
            --conclusion "${{ steps.claude.outputs.conclusion }}"
          
      - name: Commit and Push Results
        if: always()
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py commit-and-push \
            --message "Add environment analysis results for issue #${{ inputs.issue_number }}"
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: '環境分析Bot'
          SLACK_COLOR: ${{ steps.claude.outcome == 'success' && 'good' || 'danger' }}
          SLACK_TITLE: ${{ steps.claude.outcome == 'success' && '✅ 環境分析完了' || '❌ 環境分析失敗' }}
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **ステータス**: ${{ steps.claude.outcome }}
            **実行時間**: ${{ steps.claude.outputs.duration || 'N/A' }}
            
            作成されたファイル:
            - project-structure.md
            - tech-stack.md
            - coding-conventions.md
        continue-on-error: true