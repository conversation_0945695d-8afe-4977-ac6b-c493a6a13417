name: Claude Orchestrator Minimal Test

# 最小限のテスト - trigger-checkのみテスト

on:
  issue_comment:
    types: [created]

jobs:
  # トリガーチェックのみ実行
  trigger-check:
    if: contains(github.event.comment.body, '@claude-minimal')
    runs-on: ubuntu-latest
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
      issue_number: ${{ steps.check.outputs.issue_number }}
      workflow_mode: ${{ steps.check.outputs.workflow_mode }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github/scripts
            
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
            
      - name: Check trigger
        id: check
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          GITHUB_EVENT_NAME: ${{ github.event_name }}
          GITHUB_EVENT_PATH: ${{ github.event_path }}
        run: python .github/scripts/python/orchestrator.py check-trigger
        
      - name: Display results
        run: |
          echo "should_run: ${{ steps.check.outputs.should_run }}"
          echo "issue_number: ${{ steps.check.outputs.issue_number }}"
          echo "workflow_mode: ${{ steps.check.outputs.workflow_mode }}"