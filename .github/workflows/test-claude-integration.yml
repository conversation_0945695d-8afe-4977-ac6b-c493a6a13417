name: Test Claude Integration

# Claude API連携の動作テスト

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number
        description: 'Issue番号'

jobs:
  test-claude-api:
    if: contains(github.event.comment.body, '@test-claude-api') || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
        
      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM=${{ github.event.issue.number || github.event.inputs.issue_number }}
          ISSUE_DATA=$(gh issue view $ISSUE_NUM --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_NUMBER=$ISSUE_NUM" >> $GITHUB_ENV
          
      - name: Test Claude API Connection
        id: claude-test
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: 'claude-3-5-sonnet-20241022'
          max_turns: 5
          timeout_minutes: 10
          allowed_tools: 'Read,LS,Bash'
          prompt: |
            あなたはプロジェクト環境分析の専門家です。
            
            ## 現在のタスク
            このプロジェクトの基本的な環境分析を行ってください。
            
            ## 実行してほしいこと
            1. 現在のディレクトリを確認してください
            2. 主要なファイル（package.json, composer.json, docker-compose.yml等）の存在を確認してください
            3. プロジェクトの種類を判定してください（WordPress, Laravel, Node.js等）
            
            ## 重要
            - ファイルの作成や変更は行わないでください
            - 環境の確認のみを行ってください
            - 簡潔に結果を報告してください
            
            Issue情報:
            - タイトル: ${{ env.ISSUE_TITLE }}
            - 内容: ${{ env.ISSUE_BODY }}
            
            それでは環境分析を開始してください。
            
      - name: Comment Result to Issue
        if: always()
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          if [[ "${{ steps.claude-test.outcome }}" == "success" ]]; then
            RESULT="✅ Claude API連携テスト成功"
            CONCLUSION="${{ steps.claude-test.outputs.conclusion || 'completed' }}"
          else
            RESULT="❌ Claude API連携テスト失敗"
            CONCLUSION="failed"
          fi
          
          COMMENT_BODY="## Claude API連携テスト結果

          $RESULT

          **実行時刻**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          **ステータス**: ${{ steps.claude-test.outcome }}
          **結論**: $CONCLUSION

          ### テスト内容
          - OAuth認証による Claude API 接続
          - 基本的な環境分析プロンプト実行
          - 権限設定の動作確認

          ### 詳細
          ワークフロー実行: [GitHub Actions](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          ---
          🤖 Claude Code 統合テスト"
          
          echo "$COMMENT_BODY" > comment.md
          gh issue comment ${{ env.ISSUE_NUMBER }} --body-file comment.md
          
      - name: Display Integration Test Results
        run: |
          echo "=== Claude Integration Test Results ==="
          echo "Claude API Test: ${{ steps.claude-test.outcome }}"
          echo "Issue Number: ${{ env.ISSUE_NUMBER }}"
          echo "Issue Title: ${{ env.ISSUE_TITLE }}"
          
          if [[ "${{ steps.claude-test.outcome }}" == "success" ]]; then
            echo "✅ Claude OAuth認証 - 成功"
            echo "✅ Claude API呼び出し - 成功"
            echo "✅ プロンプト実行 - 成功"
            echo ""
            echo "🎉 Claude Code 統合が正常に動作しています！"
            echo "ユーザーは @claude-task コメントで実際にタスクを依頼できる状態です。"
          else
            echo "❌ Claude API連携に問題があります"
            echo "認証設定やプロンプトを確認してください"
          fi