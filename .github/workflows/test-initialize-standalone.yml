name: Test Initialize Standalone

# initializeフェーズを単独でテスト

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number
        description: 'Issue番号'

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  test-initialize:
    if: contains(github.event.comment.body, '@test-init') || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.params.outputs.branch_name }}
      selected_model: ${{ steps.params.outputs.selected_model }}
      display_model_name: ${{ steps.params.outputs.display_model_name }}
      max_turns_env: ${{ steps.params.outputs.max_turns_env }}
      max_turns_analysis: ${{ steps.params.outputs.max_turns_analysis }}
      max_turns_implement: ${{ steps.params.outputs.max_turns_implement }}
      max_turns_review: ${{ steps.params.outputs.max_turns_review }}
      iterations_limit: ${{ steps.params.outputs.iterations_limit }}
      env_analysis_needed: ${{ steps.init.outputs.env_analysis_needed }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
        
      - name: Setup Git Config
        run: |
          git config user.name "Claude Orchestrator"
          git config user.email "claude-orchestrator@github-actions"
          git config push.default current
          
      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM=${{ github.event.issue.number || github.event.inputs.issue_number }}
          ISSUE_DATA=$(gh issue view $ISSUE_NUM --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_NUMBER=$ISSUE_NUM" >> $GITHUB_ENV
          
      - name: Setup workflow parameters
        id: params
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          echo "Testing parameter parsing..."
          python .github/scripts/python/orchestrator.py parse-parameters \
            --issue-number ${{ env.ISSUE_NUMBER }}
          
      - name: Initialize workflow
        id: init
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          echo "Testing initialize command..."
          python .github/scripts/python/orchestrator.py initialize \
            --issue-number "${{ env.ISSUE_NUMBER }}" \
            --issue-title "${{ env.ISSUE_TITLE }}" \
            --issue-body "${{ env.ISSUE_BODY }}"
            
      - name: Display Results
        run: |
          echo "=== Initialize Test Results ==="
          echo "Branch: ${{ steps.params.outputs.branch_name }}"
          echo "Model: ${{ steps.params.outputs.selected_model }}"
          echo "Max turns env: ${{ steps.params.outputs.max_turns_env }}"
          echo "Max turns analysis: ${{ steps.params.outputs.max_turns_analysis }}"
          echo "Env analysis needed: ${{ steps.init.outputs.env_analysis_needed }}"