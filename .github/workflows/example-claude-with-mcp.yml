name: <PERSON> with MCP Example

# MCPサーバーを使用したClaude Codeの実行例

on:
  workflow_dispatch:
    inputs:
      task_description:
        description: '実行するタスクの説明'
        required: true
        type: string
        default: 'リポジトリのワークフロー一覧を表示'

jobs:
  claude-with-mcp:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install MCP Manager
        run: |
          # MCP Managerスクリプトが存在する場合は使用
          if [ -f "./install-mcp-manager.sh" ]; then
            chmod +x ./install-mcp-manager.sh
            ./install-mcp-manager.sh
          else
            echo "MCP Manager script not found, skipping installation"
          fi
          
      - name: Setup MCP Configuration
        run: |
          # .claudeディレクトリを作成
          mkdir -p .claude
          
          # MCP設定ファイルを作成
          cat > .claude/mcp-config.json << 'EOF'
          {
            "mcpServers": {
              "github": {
                "command": "npx",
                "args": [
                  "-y",
                  "@modelcontextprotocol/server-github"
                ],
                "env": {
                  "GITHUB_TOKEN": "${{ secrets.GH_TOKEN_WORKFLOW }}"
                }
              },
              "filesystem": {
                "command": "npx",
                "args": [
                  "-y",
                  "@modelcontextprotocol/server-filesystem",
                  "${{ github.workspace }}"
                ]
              }
            }
          }
          EOF
          
          echo "MCP configuration created at .claude/mcp-config.json"
          
      - name: Install MCP Servers
        run: |
          # GitHub MCPサーバーのインストール
          npm install -g @modelcontextprotocol/server-github
          
          # ファイルシステムMCPサーバーのインストール
          npm install -g @modelcontextprotocol/server-filesystem
          
          # インストール確認
          echo "Installed MCP servers:"
          npm list -g --depth=0 | grep @modelcontextprotocol
          
      - name: Execute Claude with MCP
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          # MCPサーバー用の環境変数
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        with:
          # OAuth認証を使用
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          
          # モデルとターン数の設定
          model: 'claude-3-5-sonnet-20241022'
          max_turns: 20
          timeout_minutes: 10
          
          # MCPサーバーが提供するツールを含める
          # mcp__で始まるツール名はMCPサーバー提供のツール
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,WebFetch,TodoRead,TodoWrite,mcp__*'
          
          # タスクの指示
          prompt: |
            タスク: ${{ inputs.task_description }}
            
            利用可能なMCPツール:
            - mcp__github__*: GitHub APIの操作（リポジトリ、ファイル、Issue、PR管理）
            - mcp__filesystem__*: ファイルシステムへのアクセス
            
            以下のようなGitHub関連のツールが使用できます:
            - リポジトリの検索・作成
            - ファイルの作成・更新・取得
            - Issue/PRの作成・更新・検索
            - ブランチの作成・管理
            - コミットの一覧取得
            
            注意: MCPツールの正確な名前は環境により異なる場合があります。
            利用可能なツールを確認してから使用してください。
            
      - name: Display Results
        if: always()
        run: |
          echo "=== Claude Execution Results ==="
          echo "Status: ${{ steps.claude.outcome }}"
          echo "Model: ${{ steps.claude.outputs.model || 'N/A' }}"
          echo "Duration: ${{ steps.claude.outputs.duration || 'N/A' }}"
          echo "Conclusion: ${{ steps.claude.outputs.conclusion || 'N/A' }}"
          
      - name: Commit Changes (if any)
        if: steps.claude.outcome == 'success'
        run: |
          # 変更があればコミット
          if [ -n "$(git status --porcelain)" ]; then
            git config user.name "github-actions[bot]"
            git config user.email "github-actions[bot]@users.noreply.github.com"
            git add -A
            git commit -m "Apply changes from Claude with MCP - ${{ inputs.task_description }}"
            git push
          else
            echo "No changes to commit"
          fi