name: <PERSON> Phase - Review (Python)

# レビューフェーズ - 実装内容の品質確認

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: number
      branch_name:
        required: true
        type: string
      selected_model:
        required: true
        type: string
      max_turns:
        required: true
        type: number
      iteration:
        required: true
        type: number
      iterations_limit:
        required: true
        type: number
    secrets:
      GH_TOKEN_WORKFLOW:
        required: true
      CLAUDE_ACCESS_TOKEN:
        required: true
      CLAUDE_REFRESH_TOKEN:
        required: true
      CLAUDE_EXPIRES_AT:
        required: true
      SLACK_WEBHOOK:
        required: false
    outputs:
      review_passed:
        value: ${{ jobs.review.outputs.passed }}
      total_iterations:
        value: ${{ inputs.iteration }}

jobs:
  review:
    runs-on: ubuntu-latest
    if: inputs.iteration <= inputs.iterations_limit
    outputs:
      passed: ${{ steps.review-result.outputs.passed }}
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_WORKFLOW }}
          
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
          
      - name: Setup Git Config
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py setup-git-config
          
      - name: Notify Start
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'レビュワーBot'
          SLACK_COLOR: '#ff9800'
          SLACK_TITLE: '📝 レビュワーBot: レビュー開始'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **イテレーション**: ${{ inputs.iteration }}
            **フェーズ**: レビュー
            **役割**: コードレビュワー
        continue-on-error: true
        
      # レビューフェーズの準備
      - name: Prepare Review Phase
        id: prepare
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py review \
            --issue-number ${{ inputs.issue_number }} \
            --branch-name ${{ inputs.branch_name }} \
            --model ${{ inputs.selected_model }} \
            --max-turns ${{ inputs.max_turns }} \
            --iteration ${{ inputs.iteration }}
            
      - name: Prepare Review Context
        run: |
          ISSUE_NUM="${{ inputs.issue_number }}"
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          ITERATION="${{ inputs.iteration }}"
          
          # 実装内容を確認
          echo "📋 実装内容を確認..."
          if [ -f "$WORK_DIR/implementation/iteration-${ITERATION}.md" ]; then
            cat "$WORK_DIR/implementation/iteration-${ITERATION}.md"
          fi
          
          # 変更されたファイルを確認
          echo "📝 変更されたファイル:"
          git diff origin/develop --name-only
          
      # プロンプトを外部ファイルから読み込む
      - name: Load prompt
        id: prompt
        run: |
          # 変数を置換しながらプロンプトを読み込む
          PROMPT=$(cat .github/claude-prompts/review.md | \
            sed "s/\${{ inputs.issue_number }}/${{ inputs.issue_number }}/g" | \
            sed "s/\${{ inputs.iteration }}/${{ inputs.iteration }}/g")
          echo "prompt<<EOF" >> $GITHUB_OUTPUT
          echo "$PROMPT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Execute Claude - Review
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: ${{ inputs.selected_model }}
          max_turns: ${{ inputs.max_turns }}
          timeout_minutes: 20
          allowed_tools: 'Read,Bash,Glob,Grep,LS,Task,NotebookRead,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: ${{ steps.prompt.outputs.prompt }}
            
      - name: Parse Review Result
        id: review-result
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py process-review-result \
            --review-file "claude/claude-work/issue-${{ inputs.issue_number }}/review/review-iteration-${{ inputs.iteration }}.md"
          
      - name: Commit Review Results
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py commit-and-push \
            --message "Add review results for iteration ${{ inputs.iteration }}"
          
      - name: Post Review Comment
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py post-review-comment \
            --issue-number ${{ inputs.issue_number }} \
            --iteration ${{ inputs.iteration }} \
            --review-file "claude/claude-work/issue-${{ inputs.issue_number }}/review/review-iteration-${{ inputs.iteration }}.md"
          
      - name: Notify Completion
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'レビュワーBot'
          SLACK_COLOR: ${{ steps.review-result.outputs.passed == 'true' && 'good' || 'warning' }}
          SLACK_TITLE: '📝 レビュー完了 (イテレーション ${{ inputs.iteration }})'
          SLACK_MESSAGE: |
            **Issue**: #${{ inputs.issue_number }}
            **結果**: ${{ steps.review-result.outputs.passed == 'true' && '✅ 合格' || '⚠️ 要修正' }}
            **ステータス**: ${{ steps.claude.outcome }}
        continue-on-error: true