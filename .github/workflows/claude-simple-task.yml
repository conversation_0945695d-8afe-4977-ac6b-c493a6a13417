name: <PERSON> Task

# ユーザーが実際に使用できるシンプルなClaude Codeワークフロー

on:
  issue_comment:
    types: [created]

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  claude-task:
    if: contains(github.event.comment.body, '@claude-task')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Setup Git Config
        run: |
          git config user.name "<PERSON> Orchestrator"
          git config user.email "claude-orchestrator@github-actions"
          git config push.default current
          
      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM=${{ github.event.issue.number }}
          ISSUE_DATA=$(gh issue view $ISSUE_NUM --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_NUMBER=$ISSUE_NUM" >> $GITHUB_ENV
          
          # ブランチ名を生成
          BRANCH_NAME="claude-task-${ISSUE_NUM}-$(date +%s)"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          
      - name: Create Task Branch
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # 認証設定
          git remote set-url origin https://x-access-token:${{ secrets.GH_TOKEN_WORKFLOW }}@github.com/${{ github.repository }}.git
          
          # ブランチ作成とプッシュ
          git checkout -b ${{ env.BRANCH_NAME }}
          git push -u origin ${{ env.BRANCH_NAME }}
          
      - name: Execute Claude Task
        id: claude-execution
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: 'claude-3-5-sonnet-20241022'
          max_turns: 20
          timeout_minutes: 30
          allowed_tools: 'Read,Write,Edit,MultiEdit,LS,Bash,Glob,Grep,NotebookRead,NotebookEdit,TodoRead,TodoWrite'
          prompt: |
            あなたはプロフェッショナルなソフトウェア開発者です。

            ## タスク情報
            - **Issue番号**: #${{ env.ISSUE_NUMBER }}
            - **タイトル**: ${{ env.ISSUE_TITLE }}
            - **詳細**: ${{ env.ISSUE_BODY }}

            ## 現在の状況
            - 作業ブランチ: `${{ env.BRANCH_NAME }}`
            - ベースブランチ: `develop`

            ## 指示
            上記のIssueの内容を理解し、適切にタスクを実行してください。

            ### 重要な注意事項
            1. **コードの品質**: 既存のコードスタイルに従って実装してください
            2. **セキュリティ**: セキュリティリスクのあるコードは書かないでください
            3. **テスト**: 必要に応じてテストも実装してください
            4. **ドキュメント**: 重要な変更には適切なコメントを追加してください
            5. **Git操作**: 作業完了後、変更をコミットしてください

            ### 作業の流れ
            1. 現在のプロジェクト構造を理解する
            2. Issueの要求事項を分析する  
            3. 必要なファイルを作成・修正する
            4. 動作確認を行う
            5. 変更をコミットする

            それでは、タスクを開始してください。
            
      - name: Create Pull Request
        if: steps.claude-execution.outcome == 'success'
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # 変更があるかチェック
          if git diff --quiet HEAD origin/develop; then
            echo "変更がないため、PRは作成しません"
            exit 0
          fi
          
          # PR作成
          PR_BODY="## Claude Codeによる自動実装

          ### 元Issue
          - **Issue**: #${{ env.ISSUE_NUMBER }}
          - **タイトル**: ${{ env.ISSUE_TITLE }}

          ### 実装内容
          Claude Codeが自動的に以下のタスクを実行しました：
          ${{ env.ISSUE_BODY }}

          ### 実行時刻
          $(date -u '+%Y-%m-%d %H:%M:%S UTC')

          ### 詳細
          - **ワークフロー実行**: [GitHub Actions](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
          - **実行結果**: ${{ steps.claude-execution.outcome }}

          ---
          🤖 Claude Code によって自動生成されました"
          
          echo "$PR_BODY" > pr_body.md
          
          PR_URL=$(gh pr create \
            --title "Claude Task: ${{ env.ISSUE_TITLE }}" \
            --body-file pr_body.md \
            --base develop \
            --head ${{ env.BRANCH_NAME }} \
            --label "claude-generated")
          
          echo "PR_URL=$PR_URL" >> $GITHUB_ENV
          
      - name: Comment Result to Issue
        if: always()
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          if [[ "${{ steps.claude-execution.outcome }}" == "success" ]]; then
            RESULT="✅ Claude タスク実行完了"
            DETAILS="タスクが正常に実行され、Pull Requestが作成されました。"
            if [[ -n "${{ env.PR_URL }}" ]]; then
              DETAILS="$DETAILS

              **作成されたPR**: ${{ env.PR_URL }}"
            fi
          else
            RESULT="❌ Claude タスク実行失敗"
            DETAILS="タスクの実行中にエラーが発生しました。ワークフローログを確認してください。"
          fi
          
          COMMENT_BODY="## $RESULT

          $DETAILS

          ### 実行詳細
          - **ブランチ**: \`${{ env.BRANCH_NAME }}\`
          - **実行時刻**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          - **ステータス**: ${{ steps.claude-execution.outcome }}

          ### ワークフロー詳細
          [GitHub Actions実行詳細](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          ---
          🤖 Claude Code"
          
          echo "$COMMENT_BODY" > comment.md
          gh issue comment ${{ env.ISSUE_NUMBER }} --body-file comment.md