name: <PERSON> Help

# 質問や簡単なタスク用のクイックヘルプワークフロー

on:
  issue_comment:
    types: [created]

permissions:
  contents: read
  issues: write

jobs:
  claude-help:
    if: contains(github.event.comment.body, '@claude-help')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM=${{ github.event.issue.number }}
          ISSUE_DATA=$(gh issue view $ISSUE_NUM --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_NUMBER=$ISSUE_NUM" >> $GITHUB_ENV
          
      - name: Claude Quick Help
        id: claude-help
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: 'claude-3-5-sonnet-20241022'
          max_turns: 5
          timeout_minutes: 10
          allowed_tools: 'Read,LS,Bash,Glob,Grep'
          prompt: |
            あなたはプロジェクトのアシスタントです。

            ## 質問・リクエスト
            - **Issue番号**: #${{ env.ISSUE_NUMBER }}
            - **タイトル**: ${{ env.ISSUE_TITLE }}
            - **詳細**: ${{ env.ISSUE_BODY }}

            ## 指示
            上記の質問やリクエストに対して、簡潔で有用な回答を提供してください。

            ### 対応できること
            - コードの説明や理解
            - ファイル構造の確認
            - 簡単なコード例の提供
            - プロジェクトに関する質問への回答
            - 技術的なアドバイス

            ### 制約
            - ファイルの変更や作成は行いません
            - 読み取り専用の操作のみ実行します
            - 簡潔で分かりやすい回答を心がけてください

            回答は日本語で提供してください。
            
      - name: Comment Response to Issue
        if: always()
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          if [[ "${{ steps.claude-help.outcome }}" == "success" ]]; then
            RESULT="✅ Claude クイックヘルプ完了"
            DETAILS="質問にお答えしました。上記のClaude Codeの実行結果をご確認ください。"
          else
            RESULT="❌ Claude クイックヘルプ失敗"
            DETAILS="回答の生成中にエラーが発生しました。ワークフローログを確認してください。"
          fi
          
          COMMENT_BODY="## $RESULT

          $DETAILS

          ### 実行詳細
          - **実行時刻**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          - **ステータス**: ${{ steps.claude-help.outcome }}

          ### その他のコマンド
          - \`@claude-task\` - ファイルの変更を伴うタスクの実行
          - \`@claude-help\` - 質問や説明（読み取り専用）

          ### ワークフロー詳細
          [GitHub Actions実行詳細](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          ---
          🤖 Claude Code Quick Help"
          
          echo "$COMMENT_BODY" > comment.md
          gh issue comment ${{ env.ISSUE_NUMBER }} --body-file comment.md