name: Test <PERSON> Simple

on:
  workflow_dispatch:

jobs:
  test-simple:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Run Claude Simple Test
        id: claude
        uses: grll/claude-code-base-action@beta
        with:
          use_oauth: "true"
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          
          prompt: "Say hello in Japanese"
          model: "claude-opus-4-20250514"
          max_turns: "1"
      
      - name: Show outputs
        if: always()
        run: |
          echo "=== Claude Outputs ==="
          echo "execution_file: ${{ steps.claude.outputs.execution_file }}"
          echo "conclusion: ${{ steps.claude.outputs.conclusion }}"
          echo "model: ${{ steps.claude.outputs.model }}"
          
          echo "=== Check /tmp ==="
          ls -la /tmp/*.json 2>/dev/null || echo "No JSON files in /tmp"
          
          echo "=== Check execution file ==="
          if [ -f "${{ steps.claude.outputs.execution_file }}" ]; then
            echo "File exists. Content:"
            cat "${{ steps.claude.outputs.execution_file }}"
          elif [ -f "/tmp/claude-execution-output.json" ]; then
            echo "Found at default location. Content:"
            cat "/tmp/claude-execution-output.json"
          else
            echo "No execution file found"
          fi