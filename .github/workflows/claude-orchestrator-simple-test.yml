name: <PERSON> Simple Test

# シンプルなテスト版 - initializeフェーズのみ実行

on:
  issue_comment:
    types: [created]

jobs:
  # トリガーチェック
  trigger-check:
    if: contains(github.event.comment.body, '@claude-test')
    runs-on: ubuntu-latest
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
      issue_number: ${{ steps.check.outputs.issue_number }}
    steps:
      - name: Check trigger
        id: check
        run: |
          echo "should_run=true" >> $GITHUB_OUTPUT
          echo "issue_number=${{ github.event.issue.number }}" >> $GITHUB_OUTPUT
          echo "Processing issue #${{ github.event.issue.number }}"

  # 環境分析のみ実行
  analyze-env:
    needs: trigger-check
    if: needs.trigger-check.outputs.should_run == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Execute Claude - Environment Analysis
        id: claude
        uses: grll/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        with:
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          model: 'claude-3-5-sonnet-20241022'
          max_turns: 10
          timeout_minutes: 20
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          prompt: |
            あなたはプロジェクト環境分析の専門家です。
            プロジェクト全体の構造と特性を分析し、効率的な開発のための基盤情報を提供します。
            
            ## 重要な前提情報
            - 現在の作業ディレクトリは GitHub Actions のワークスペースです
            - リポジトリのルートディレクトリで作業します
            - 最初に必ず pwd コマンドで現在地を確認してください
            
            ## 実行内容
            1. プロジェクト構造の分析
            2. 技術スタックの特定
            3. コーディング規約の確認
            4. 環境分析結果をファイルに保存
            
            ## 具体的な手順
            1. まず現在地と基本的なファイル構成を確認してください
            2. claude/claude-knowledge/environment/ ディレクトリを作成してください
            3. project-structure.md に分析結果を保存してください
            
            それでは分析を開始してください。
      
      - name: Debug Results
        if: always()
        run: |
          echo "Claude execution completed"
          echo "Outcome: ${{ steps.claude.outcome }}"
          echo "Conclusion: ${{ steps.claude.outputs.conclusion }}"