name: <PERSON> Phase - Initialize (Python)

on:
  workflow_call:
    inputs:
      issue_number:
        required: true
        type: number
    outputs:
      issue_number:
        value: ${{ inputs.issue_number }}
      branch_name:
        value: ${{ jobs.initialize.outputs.branch_name }}
      selected_model:
        value: ${{ jobs.initialize.outputs.selected_model }}
      display_model_name:
        value: ${{ jobs.initialize.outputs.display_model_name }}
      max_turns_env:
        value: ${{ jobs.initialize.outputs.max_turns_env }}
      max_turns_analysis:
        value: ${{ jobs.initialize.outputs.max_turns_analysis }}
      max_turns_implement:
        value: ${{ jobs.initialize.outputs.max_turns_implement }}
      max_turns_review:
        value: ${{ jobs.initialize.outputs.max_turns_review }}
      iterations_limit:
        value: ${{ jobs.initialize.outputs.iterations_limit }}
      env_analysis_needed:
        value: ${{ jobs.initialize.outputs.env_analysis_needed }}

jobs:
  initialize:
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.params.outputs.branch_name }}
      selected_model: ${{ steps.params.outputs.selected_model }}
      display_model_name: ${{ steps.params.outputs.display_model_name }}
      max_turns_env: ${{ steps.params.outputs.max_turns_env }}
      max_turns_analysis: ${{ steps.params.outputs.max_turns_analysis }}
      max_turns_implement: ${{ steps.params.outputs.max_turns_implement }}
      max_turns_review: ${{ steps.params.outputs.max_turns_review }}
      iterations_limit: ${{ steps.params.outputs.iterations_limit }}
      env_analysis_needed: ${{ steps.init.outputs.env_analysis_needed }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
        
      - name: Setup Git Config
        run: |
          git config user.name "Claude Orchestrator"
          git config user.email "claude-orchestrator@github-actions"
          git config push.default current
          
      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_DATA=$(gh issue view ${{ inputs.issue_number }} --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          # Set environment variables for next steps
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
      - name: Setup workflow parameters
        id: params
        env:
          ISSUE_NUMBER: ${{ inputs.issue_number }}
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          python .github/scripts/python/orchestrator.py parse-parameters \
            --issue-number ${{ inputs.issue_number }}
          
      - name: Create Labels
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          # Labels are created in the initialize phase
          echo "Labels will be created by the initialize command"
          
      - name: Initialize workflow
        id: init
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          ISSUE_NUMBER: ${{ inputs.issue_number }}
          ISSUE_TITLE: ${{ env.ISSUE_TITLE }}
          ISSUE_BODY: ${{ env.ISSUE_BODY }}
        run: |
          python .github/scripts/python/orchestrator.py initialize \
            --issue-number "$ISSUE_NUMBER" \
            --issue-title "$ISSUE_TITLE" \
            --issue-body "$ISSUE_BODY"