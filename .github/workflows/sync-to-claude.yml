name: Sync to <PERSON>-<PERSON> Branch

on:
  push:
    branches:
      - develop
    paths-ignore:
      - '.github/workflows/sync-to-claude.yml'  # 無限ループを防ぐ

jobs:
  sync:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
      # リポジトリのチェックアウト
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      
      # Git設定
      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
      
      # claude-baseブランチの作成または更新
      - name: Create or update claude-base branch
        run: |
          # claude-baseブランチが存在するか確認
          if git show-ref --verify --quiet refs/heads/claude-base; then
            echo "📌 Claude-base branch exists, updating..."
            git checkout claude-base
            git merge develop --no-edit --strategy-option=theirs
          else
            echo "🌱 Creating claude-base branch..."
            git checkout -b claude-base
          fi
          
          # 変更をプッシュ
          git push origin claude-base --force-with-lease
          
          echo "✅ Successfully synced develop to claude-base branch"
      
      # Slack通知（同期完了）
      - name: Notify Sync Complete
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Sync Bot'
          SLACK_COLOR: '#0099cc'
          SLACK_TITLE: '🔄 claude-baseブランチ同期完了'
          SLACK_MESSAGE: |
            develop → claude-base の同期が完了しました
            コミット: ${{ github.event.head_commit.id }}
            メッセージ: ${{ github.event.head_commit.message }}
            🔗 [GitHub Actions](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
      
      # 同期完了の通知
      - name: Add sync status comment
        if: github.event.head_commit
        uses: actions/github-script@v7
        with:
          script: |
            const commit = context.payload.head_commit;
            const message = `🔄 Synced to claude-base branch\n\nCommit: ${commit.id}\nMessage: ${commit.message}`;
            
            console.log(message);