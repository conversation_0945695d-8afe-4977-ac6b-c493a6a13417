name: Test Claude O<PERSON>uth Authentication

on:
  workflow_dispatch:
    inputs:
      test_prompt:
        description: 'Test prompt for <PERSON>'
        required: false
        default: 'List files in the current directory'

jobs:
  test-oauth:
    name: Test OAuth Authentication
    runs-on: ubuntu-latest

    steps:
      # リポジトリのチェックアウト
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # OAuth認証テスト開始通知
      - name: Notify test start
        uses: rtCamp/action-slack-notify@v2
        if: success()
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Claude OAuth Test'
          SLACK_COLOR: '#36a64f'
          SLACK_TITLE: '🔐 OAuth認証テスト開始'
          SLACK_MESSAGE: |
            👤 **実行者:** ${{ github.actor }}
            🎯 **テストプロンプト:** ${{ github.event.inputs.test_prompt }}
            🤖 **使用モデル:** claude-opus-4-20250514
            🔗 **ワークフロー:** ${{ github.workflow }}
            🎭 **ジョブID:** ${{ github.run_id }}

            🔍 **OAuth設定確認中...**

            🔗 [GitHub Actions](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
        continue-on-error: true

      # シークレットのソース判定
      - name: Determine secret source
        id: secret-source
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          echo "Checking secret source..."

          if [ -n "${{ secrets.CLAUDE_ACCESS_TOKEN }}" ]; then
            # アクセストークンの最初の10文字でハッシュを作成（セキュリティのため）
            TOKEN_HASH=$(echo "${{ secrets.CLAUDE_ACCESS_TOKEN }}" | head -c 10 | sha256sum | head -c 8)
            echo "secret_exists=true" >> $GITHUB_OUTPUT
            echo "token_hash=$TOKEN_HASH" >> $GITHUB_OUTPUT

            # リポジトリとOrganization情報を取得
            REPO_NAME="${{ github.repository }}"
            REPO_OWNER="${{ github.repository_owner }}"

            # リポジトリシークレットの存在を確認（GitHub CLIを使用）
            REPO_SECRETS=$(gh api repos/$REPO_NAME/actions/secrets --jq '.secrets[].name' 2>/dev/null || echo "")

            if echo "$REPO_SECRETS" | grep -q "CLAUDE_ACCESS_TOKEN"; then
              echo "secret_source=Repository Secret ($REPO_NAME)" >> $GITHUB_OUTPUT
              echo "::notice::Using Repository Secret"
            else
              # リポジトリシークレットにない場合はOrganizationシークレットと推定
              echo "secret_source=Organization Secret ($REPO_OWNER)" >> $GITHUB_OUTPUT
              echo "::notice::Using Organization Secret (inferred)"
            fi

            # 追加情報
            echo "repository=$REPO_NAME" >> $GITHUB_OUTPUT
            echo "organization=$REPO_OWNER" >> $GITHUB_OUTPUT
          else
            echo "secret_exists=false" >> $GITHUB_OUTPUT
            echo "secret_source=Not Found" >> $GITHUB_OUTPUT
            echo "::error::CLAUDE_ACCESS_TOKEN not found"
          fi

      # OAuthトークンの有効期限確認
      - name: Check OAuth token expiry
        id: check-token
        run: |
          echo "Checking OAuth token expiry..."
          EXPIRES_AT="${{ secrets.CLAUDE_EXPIRES_AT }}"
          CURRENT_TIME_MS=$(date +%s000)  # 現在時刻をミリ秒に変換

          if [ -n "$EXPIRES_AT" ]; then
            # ミリ秒単位での残り時間を計算
            REMAINING_MS=$((EXPIRES_AT - CURRENT_TIME_MS))
            REMAINING_SEC=$((REMAINING_MS / 1000))
            DAYS=$((REMAINING_SEC / 86400))
            HOURS=$(((REMAINING_SEC % 86400) / 3600))
            MINUTES=$(((REMAINING_SEC % 3600) / 60))

            # 有効期限を人間が読める形式に変換（JST）
            if [[ "$OSTYPE" == "darwin"* ]]; then
              # macOS
              EXPIRY_DATE=$(TZ='Asia/Tokyo' date -r $((EXPIRES_AT / 1000)) '+%Y-%m-%d %H:%M:%S JST')
            else
              # Linux
              EXPIRY_DATE=$(TZ='Asia/Tokyo' date -d "@$((EXPIRES_AT / 1000))" '+%Y-%m-%d %H:%M:%S JST')
            fi

            echo "expires_at=$EXPIRES_AT" >> $GITHUB_OUTPUT
            echo "expiry_date=$EXPIRY_DATE" >> $GITHUB_OUTPUT
            echo "remaining_seconds=$REMAINING_SEC" >> $GITHUB_OUTPUT
            echo "remaining_days=$DAYS" >> $GITHUB_OUTPUT
            echo "remaining_hours=$HOURS" >> $GITHUB_OUTPUT
            echo "remaining_minutes=$MINUTES" >> $GITHUB_OUTPUT

            if [ $REMAINING_SEC -le 0 ]; then
              echo "::error::OAuth token has expired!"
              echo "token_status=expired" >> $GITHUB_OUTPUT
            elif [ $REMAINING_SEC -le 86400 ]; then
              echo "::warning::OAuth token expires in less than 24 hours!"
              echo "token_status=expiring_soon" >> $GITHUB_OUTPUT
            else
              echo "::notice::OAuth token is valid until $EXPIRY_DATE ($DAYS days, $HOURS hours)"
              echo "token_status=valid" >> $GITHUB_OUTPUT
            fi
          else
            echo "::error::CLAUDE_EXPIRES_AT is not set"
            echo "token_status=not_set" >> $GITHUB_OUTPUT
          fi

      # Claude Code Base Action実行（OAuth認証）
      - name: Run Claude with OAuth
        id: claude
        uses: grll/claude-code-base-action@beta
        with:
          # OAuth認証設定（必須）
          use_oauth: "true"
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}

          # プロンプト（シンプルなテスト用）
          prompt: |
            ${{ github.event.inputs.test_prompt }}

            これはOAuth認証のテストです。簡潔に返答してください。

          # モデル設定（Opus 4モデルを使用）
          model: "claude-opus-4-20250514"

          # ツール制限（安全なツールのみ）
          allowed_tools: "View,GlobTool,Bash(ls:*),Bash(echo:*)"
          disallowed_tools: "Write,Edit,MultiEdit,Bash(rm:*),Bash(sudo:*)"

          # 実行制限
          max_turns: "5"
          timeout_minutes: "5"

        continue-on-error: true

      # Claude実行後のデバッグ情報
      - name: Debug Claude outputs
        if: always()
        run: |
          echo "=== Claude Action Outputs Debug ==="
          echo "Execution file: ${{ steps.claude.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.claude.outputs.conclusion }}"
          echo "Model used: ${{ steps.claude.outputs.model }}"

          # Claudeのステップ結果を確認
          echo "=== Claude Step Outcome ==="
          echo "Outcome: ${{ steps.claude.outcome }}"
          echo "Conclusion: ${{ steps.claude.conclusion }}"

          # /tmp配下のすべてのファイルを表示（サイズと共に）
          echo "=== All files in /tmp ==="
          ls -la /tmp/ | head -30

          # /tmp配下のclaude関連ファイルを探す
          echo "=== Searching for Claude execution files ==="
          find /tmp -name "*claude*" -type f 2>/dev/null | head -20
          find /tmp -name "*.json" -type f 2>/dev/null | head -20

          # 実行ファイルが存在するか確認
          if [ -f "${{ steps.claude.outputs.execution_file }}" ]; then
            echo "✅ Execution file exists"
            echo "File size: $(ls -lh "${{ steps.claude.outputs.execution_file }}" | awk '{print $5}')"
            echo "File type: $(file "${{ steps.claude.outputs.execution_file }}")"

            # ファイルが有効なJSONか確認
            if jq . "${{ steps.claude.outputs.execution_file }}" > /dev/null 2>&1; then
              echo "✅ Valid JSON file"
            else
              echo "❌ Invalid JSON file - showing raw content:"
              head -n 100 "${{ steps.claude.outputs.execution_file }}"
            fi
          else
            echo "❌ Execution file not found at: ${{ steps.claude.outputs.execution_file }}"

            # 代替パスを確認
            if [ -f "/tmp/claude-execution-output.json" ]; then
              echo "Found alternative file at /tmp/claude-execution-output.json"
              echo "execution_file=/tmp/claude-execution-output.json" >> $GITHUB_OUTPUT
            fi
          fi

      # 実行結果の保存と表示
      - name: Save and display execution results
        id: claude-results
        if: always()
        run: |
          # 実行ファイルのパスを確認
          EXEC_FILE="${{ steps.claude.outputs.execution_file }}"

          # 実行ファイルが指定されていない場合のデフォルトパス
          if [ -z "$EXEC_FILE" ]; then
            echo "⚠️ Execution file output is empty, checking default location"
            EXEC_FILE="/tmp/claude-execution-output.json"
          fi

          echo "Checking execution file at: $EXEC_FILE"

          if [ -f "$EXEC_FILE" ]; then
            cp "$EXEC_FILE" oauth-test-execution.json
            echo "✅ Execution log saved to oauth-test-execution.json"

            # ファイルの構造を確認
            echo "=== Debug: File structure analysis ==="
            echo "File size: $(ls -lh "$EXEC_FILE" | awk '{print $5}')"

            # まず生のファイル内容を確認（最初の数行）
            echo "=== Raw file content (first 10 lines) ==="
            head -n 10 "$EXEC_FILE"
            echo "=== End of raw content ==="

            # JSONの構造を確認（配列か、オブジェクトか）
            if jq -e 'type == "array"' "$EXEC_FILE" > /dev/null 2>&1; then
              echo "✅ File is JSON array"
              TOTAL_ITEMS=$(jq 'length' "$EXEC_FILE")
              echo "Total items in array: $TOTAL_ITEMS"

              # 配列が空の場合
              if [ "$TOTAL_ITEMS" -eq 0 ]; then
                echo "⚠️ JSON array is empty!"
                ASSISTANT_COUNT=0
                CLAUDE_RESPONSE="配列が空です。Claudeが実行されませんでした。"
              else
                # 各アイテムの詳細を表示（最初の3つ）
                echo "=== First 3 items in array ==="
                jq '.[:3]' "$EXEC_FILE" 2>/dev/null || echo "Could not display items"

                # 各アイテムのroleを表示
                echo "=== Message roles in array ==="
                jq -r '.[] | .role' "$EXEC_FILE" 2>/dev/null | sort | uniq -c || echo "Could not extract roles"

                # type=assistantのメッセージを探す
                ASSISTANT_COUNT=$(jq '[.[] | select(.type == "assistant")] | length' "$EXEC_FILE" 2>/dev/null || echo "0")
                echo "Found $ASSISTANT_COUNT assistant messages"

                if [ "$ASSISTANT_COUNT" -gt 0 ]; then
                  # 最初のAssistantメッセージを詳細に表示
                  echo "=== First Assistant Message ==="
                  jq '.[] | select(.type == "assistant") | .message' "$EXEC_FILE" 2>/dev/null | head -n 50
                  echo "=== End of First Assistant Message ==="

                  # Claudeの応答を取得（content配列からテキストを抽出）
                  CLAUDE_RESPONSE=$(jq -r '.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text' "$EXEC_FILE" 2>/dev/null | tr '\n' ' ' | sed 's/  */ /g' | cut -c1-500)

                  # 応答が取得できなかった場合の別の試み
                  if [ -z "$CLAUDE_RESPONSE" ]; then
                    echo "⚠️ Trying alternative extraction method..."
                    CLAUDE_RESPONSE=$(jq -r '.[] | select(.type == "assistant") | .message.content[0].text' "$EXEC_FILE" 2>/dev/null || echo "")
                  fi
                else
                  echo "⚠️ No assistant messages found in the array"
                  CLAUDE_RESPONSE=""
                fi
              fi

            elif jq -e 'type == "object"' "$EXEC_FILE" > /dev/null 2>&1; then
              echo "✅ File is JSON object"
              # オブジェクトの場合の処理
              echo "=== Object keys ==="
              jq -r 'keys[]' "$EXEC_FILE" 2>/dev/null | head -20 || echo "Could not extract keys"

              # messagesフィールドがあるか確認
              if jq -e '.messages' "$EXEC_FILE" > /dev/null 2>&1; then
                echo "Found 'messages' field"
                ASSISTANT_COUNT=$(jq '.messages | [.[] | select(.role == "assistant")] | length' "$EXEC_FILE" 2>/dev/null || echo "0")
                CLAUDE_RESPONSE=$(jq -r '.messages[] | select(.role == "assistant") | .content' "$EXEC_FILE" 2>/dev/null | tr '\n' ' ' | sed 's/  */ /g' | cut -c1-500)
              else
                echo "⚠️ No 'messages' field found in object"
                ASSISTANT_COUNT=0
                CLAUDE_RESPONSE=""
              fi
            else
              echo "❌ Unknown JSON structure"
              echo "Raw content (first 100 lines):"
              head -n 100 "$EXEC_FILE"
              ASSISTANT_COUNT=0
              CLAUDE_RESPONSE=""
            fi

            # 応答が空でないか確認
            if [ -z "$CLAUDE_RESPONSE" ]; then
              echo "⚠️ Claudeの応答が見つかりませんでした"
              CLAUDE_RESPONSE="応答が取得できませんでした。実行ログを確認してください。"
            else
              echo "✅ Claude Response found:"
              echo "$CLAUDE_RESPONSE"
            fi

            # 実行統計を表示（resultオブジェクトから取得）
            TURN_COUNT=$(jq -r '.[] | select(.type == "result") | .num_turns' "$EXEC_FILE" 2>/dev/null || echo "$ASSISTANT_COUNT")
            echo "Total Claude turns: $TURN_COUNT"

            # GitHub Actions出力用に改行をエスケープ
            CLAUDE_RESPONSE_ESCAPED=$(echo "$CLAUDE_RESPONSE" | sed ':a;N;$!ba;s/\n/%0A/g' | sed 's/\r/%0D/g')

            # Slack用に応答を保存
            echo "claude_response=$CLAUDE_RESPONSE_ESCAPED" >> $GITHUB_OUTPUT
            echo "turn_count=$TURN_COUNT" >> $GITHUB_OUTPUT
          else
            echo "❌ No execution file found at: $EXEC_FILE"
            echo "::error::No execution file found - Claude may not have been reached"
            echo "claude_response=Claudeに接続できませんでした" >> $GITHUB_OUTPUT
            echo "turn_count=0" >> $GITHUB_OUTPUT
          fi

      # 成功通知
      - name: Notify success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Claude OAuth Test'
          SLACK_COLOR: 'good'
          SLACK_TITLE: '✅ OAuth認証テスト成功'
          SLACK_MESSAGE: |
            OAuth認証が正常に動作しました！

            📊 **トークン状態:**
            - ステータス: `${{ steps.check-token.outputs.token_status }}`
            - 有効期限: `${{ steps.check-token.outputs.expiry_date }}`
            - 残り時間: **${{ steps.check-token.outputs.remaining_days }}日 ${{ steps.check-token.outputs.remaining_hours }}時間 ${{ steps.check-token.outputs.remaining_minutes }}分**

            🔐 **シークレット情報:**
            - ソース: **${{ steps.secret-source.outputs.secret_source }}**
            - リポジトリ: `${{ steps.secret-source.outputs.repository }}`
            - Organization: `${{ steps.secret-source.outputs.organization }}`
            - トークンハッシュ: `${{ steps.secret-source.outputs.token_hash }}`

            🤖 **Claude実行結果:**
            - モデル: `claude-opus-4-20250514`
            - 実行ファイル: `${{ steps.claude.outputs.execution_file }}`
            - 結論: `${{ steps.claude.outputs.conclusion || 'success' }}`
            - ターン数: `${{ steps.claude-results.outputs.turn_count }}`

            💬 **Claudeの応答:**
            ```
            ${{ steps.claude-results.outputs.claude_response || '応答を取得できませんでした' }}
            ```

            📦 **アーティファクト:**
            - oauth-test-execution.json

            🔗 [GitHub Actions](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
            📝 [Logs](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#step:6:1)
        continue-on-error: true

      # 失敗通知
      - name: Notify failure
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#actions'
          SLACK_USERNAME: 'Claude OAuth Test'
          SLACK_COLOR: 'danger'
          SLACK_TITLE: '❌ OAuth認証テスト失敗'
          SLACK_MESSAGE: |
            OAuth認証テストが失敗しました。

            📊 **トークン状態:**
            - ステータス: `${{ steps.check-token.outputs.token_status }}`
            - 有効期限: `${{ steps.check-token.outputs.expiry_date || 'N/A' }}`
            - 残り時間: `${{ steps.check-token.outputs.remaining_days || '0' }}日`

            🔐 **シークレット情報:**
            - ソース: **${{ steps.secret-source.outputs.secret_source }}**
            - トークンハッシュ: `${{ steps.secret-source.outputs.token_hash || 'N/A' }}`

            🚨 **エラー詳細:**
            - Claudeステップ結果: `${{ steps.claude.outcome }}`
            - Claude結論: `${{ steps.claude.outputs.conclusion || 'N/A' }}`

            🔍 **考えられる原因:**
            ${{ steps.check-token.outputs.token_status == 'expired' && '⚠️ OAuthトークンが有効期限切れです' || '' }}
            ${{ steps.check-token.outputs.token_status == 'not_set' && '⚠️ CLAUDE_EXPIRES_ATが設定されていません' || '' }}
            ${{ steps.check-token.outputs.token_status == 'expiring_soon' && '⚠️ トークンが24時間以内に期限切れです' || '' }}
            - シークレットの設定ミス
            - ネットワークエラー

            📝 **対処方法:**
            ```bash
            # 1. ローカルでトークンを再取得
            claude code login

            # 2. 認証情報を確認
            cat ~/.claude/.credentials.json
            ```

            3. GitHub Secretsを更新:
               - `CLAUDE_ACCESS_TOKEN`
               - `CLAUDE_REFRESH_TOKEN`
               - `CLAUDE_EXPIRES_AT` (ミリ秒単位)

            🔗 [GitHub Actions](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
            📝 [Error Logs](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#step:4:1)
        continue-on-error: true

      # アーティファクトの保存
      - name: Upload artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: oauth-test-results
          path: |
            oauth-test-execution.json
          retention-days: 7