name: Test Initialize No Branch

# initializeフェーズをブランチ作成なしでテスト

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number
        description: 'Issue番号'

jobs:
  test-initialize-no-branch:
    if: contains(github.event.comment.body, '@test-init-no-branch') || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r .github/scripts/python/requirements.txt
        
      - name: Setup Git Config
        run: |
          git config user.name "Claude Orchestrator"
          git config user.email "claude-orchestrator@github-actions"
          git config push.default current
          
      - name: Get Issue Information
        id: issue
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          ISSUE_NUM=${{ github.event.issue.number || github.event.inputs.issue_number }}
          ISSUE_DATA=$(gh issue view $ISSUE_NUM --json title,body)
          ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
          ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
          
          echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_TITLE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
          echo "$ISSUE_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "ISSUE_NUMBER=$ISSUE_NUM" >> $GITHUB_ENV
          
      - name: Test Parameter Parsing Only
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          echo "Testing parameter parsing without branch creation..."
          python .github/scripts/python/orchestrator.py parse-parameters \
            --issue-number ${{ env.ISSUE_NUMBER }}
          
      - name: Test Analysis Environment 
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
        run: |
          echo "Testing environment analysis..."
          
          # 環境分析の実行（ファイル作成なし）
          echo "Project structure analysis:"
          ls -la
          echo ""
          echo "Key files:"
          for file in docker-compose.yml package.json composer.json README.md; do
            if [ -f "$file" ]; then
              echo "✅ $file"
            else
              echo "❌ $file (not found)"
            fi
          done
          
          echo ""
          echo "Directory structure:"
          find . -maxdepth 2 -type d -name ".*" -prune -o -type d -print | head -20
          
      - name: Test Basic Claude Prompt
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          MCP_GITHUB_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
          NODE_ENV: 'production'
        run: |
          echo "Testing basic functionality without Claude API call..."
          echo "Issue: ${{ env.ISSUE_TITLE }}"
          echo "Body length: $(echo '${{ env.ISSUE_BODY }}' | wc -c) characters"
          
          # 作業ディレクトリの確認
          echo "Creating work directory structure..."
          mkdir -p claude/claude-work/issue-${{ env.ISSUE_NUMBER }}/dialogue
          echo "Task analysis complete - would create task list here" > claude/claude-work/issue-${{ env.ISSUE_NUMBER }}/dialogue/task-list.md
          
          echo "Work directory created successfully"
          ls -la claude/claude-work/issue-${{ env.ISSUE_NUMBER }}/
          
      - name: Display Test Results
        run: |
          echo "=== Test Results ==="
          echo "✅ Issue information retrieval"
          echo "✅ Parameter parsing"
          echo "✅ Environment analysis"
          echo "✅ Work directory creation"
          echo "✅ Basic workflow functionality"
          echo ""
          echo "❌ Branch creation (permission issue)"
          echo "❌ Claude API integration (not tested)"
          echo ""
          echo "Next step: Configure proper GitHub permissions"