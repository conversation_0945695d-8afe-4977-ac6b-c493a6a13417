name: Claude Orchestrator

# 完全な対話型実装: 各フェーズが独立したワークフロー実行として動作
# 開発者とレビュワーが真の対話を行い、品質基準を満たすまで繰り返す

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      issue_number:
        required: true
        type: number
      phase:
        description: 'Current phase (INIT, ANALYZE_ENV, ANALYZE, IMPLEMENT, REVIEW, UPDATE_KNOWLEDGE, FINALIZE)'
        required: true
        default: 'INIT'
      iteration:
        description: 'Current iteration number'
        required: false
        default: '0'
      branch_name:
        description: 'Working branch name'
        required: false
      selected_model:
        description: 'Claude model to use'
        required: false
        default: 'claude-sonnet-4-20250514'
      auth_type:
        description: 'Authentication type (OAUTH)'
        required: false
        default: 'OAUTH'

# 同一Issueの同時実行を防ぐ（順次実行を保証）
concurrency:
  group: claude-orchestrator-${{ github.event.issue.number || github.event.inputs.issue_number }}
  cancel-in-progress: false

# OAuth認証のみを使用するため、環境変数の設定は最小限にする

# ターン数制御用の環境変数(各フェーズで、claudeと対話するターン数)
env:
  # 各フェーズのターン数制限（本番環境用の適切な値）
  MAX_TURNS_ENV_ANALYSIS: '100' # 環境分析フェーズ（複数ファイル作成のため多めに設定）
  MAX_TURNS_ISSUE_ANALYSIS: '100' # Issue分析フェーズ
  MAX_TURNS_IMPLEMENT: '300' # 実装フェーズ（最も複雑なタスクのため多めに設定）
  MAX_TURNS_REVIEW: '100' # レビューフェーズ
  MAX_TURNS_KNOWLEDGE: '100' # ナレッジ更新フェーズ
  
  # MCPサーバー関連の環境変数
  # GitHubトークンは各ステップで設定済み
  # その他のMCPサーバーはGitHub Actions環境では利用不可のものが多い

jobs:
  debug-trigger:
    runs-on: ubuntu-latest
    if: github.event_name == 'issue_comment'
    permissions:
      contents: read
      issues: read
    steps:
      - name: Debug Info
        run: |
          echo "🔍 デバッグ情報"
          echo "Event: ${{ github.event_name }}"
          echo "Comment: ${{ github.event.comment.body }}"
          echo "Is PR?: ${{ github.event.issue.pull_request != null }}"
          echo "Issue Number: ${{ github.event.issue.number }}"
          echo "Contains @claude-task?: ${{ contains(github.event.comment.body, '@claude-task') }}"

  execute-phase:
    runs-on: ubuntu-latest
    timeout-minutes: 360  # ジョブ全体で6時間のタイムアウト（GitHub Actionsの最大値）
    # 初回は@claude-task、以降はworkflow_dispatch
    # PRのコメントは除外
    if: |
      (github.event_name == 'issue_comment' &&
       github.event.issue.pull_request == null &&
       contains(github.event.comment.body, '@claude-task')) ||
      github.event_name == 'workflow_dispatch'
    permissions:
      contents: write
      issues: write
      actions: write
      pull-requests: write
    outputs:
      auth_type: ${{ github.event.inputs.auth_type || 'OAUTH' }}
    steps:
      - name: Setup Phase Info
        id: setup
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "issue_number=${{ github.event.inputs.issue_number }}" >> $GITHUB_OUTPUT
            echo "phase=${{ github.event.inputs.phase }}" >> $GITHUB_OUTPUT
            echo "iteration=${{ github.event.inputs.iteration }}" >> $GITHUB_OUTPUT
            echo "branch_name=${{ github.event.inputs.branch_name }}" >> $GITHUB_OUTPUT
            echo "auth_type=${{ github.event.inputs.auth_type }}" >> $GITHUB_OUTPUT
          else
            # 初回実行（コメントから）
            echo "issue_number=${{ github.event.issue.number }}" >> $GITHUB_OUTPUT
            echo "phase=INIT" >> $GITHUB_OUTPUT
            echo "iteration=0" >> $GITHUB_OUTPUT
            echo "branch_name=" >> $GITHUB_OUTPUT
            echo "auth_type=OAUTH" >> $GITHUB_OUTPUT
          fi

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Git Config
        run: |
          git config --global user.name "Claude Sequential Bot"
          git config --global user.email "<EMAIL>"

      # 認証方式の選択（INIT phaseでのみ実行）
      - name: Auto-Select Authentication Method
        id: auth-select
        if: steps.setup.outputs.phase == 'INIT'
        run: |
          echo "🔐 認証方式を選択中..."

          # OAuth認証トークンの存在チェックのみ
          if [ -n "${{ secrets.CLAUDE_ACCESS_TOKEN }}" ]; then
            echo "✅ OAuth認証トークンが設定されています"
            echo "auth_type=OAUTH" >> $GITHUB_OUTPUT
          else
            echo "❌ OAuth認証トークンが設定されていません"
            echo "CLAUDE_ACCESS_TOKENをSecretsに設定してください"
            exit 1
          fi

      # INIT Phase: 初期化とブランチ作成
      - name: Initialize Project
        if: steps.setup.outputs.phase == 'INIT'
        id: init
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          ISSUE_NUM="${{ steps.setup.outputs.issue_number }}"

          # Issue情報を取得
          ISSUE_INFO=$(gh api repos/${{ github.repository }}/issues/${ISSUE_NUM})
          ISSUE_TITLE=$(echo "$ISSUE_INFO" | jq -r '.title')
          ISSUE_LABELS=$(echo "$ISSUE_INFO" | jq -r '.labels[].name' | tr '\n' ' ')

          # claude-doneラベルがある場合はスキップ
          if echo "$ISSUE_LABELS" | grep -q "claude-done"; then
            echo "✅ Issue already processed (has claude-done label)"
            exit 0
          fi

          # Issue本文から設定値を抽出
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            ISSUE_BODY=$(gh issue view ${{ github.event.inputs.issue_number }} --json body -q .body)
          else
            ISSUE_BODY=$(gh issue view ${{ github.event.issue.number }} --json body -q .body)
          fi
          
          # モデル選択（Issue本文から抽出、フォールバックはラベルベース）
          selectedModel='claude-sonnet-4-20250514'  # デフォルト
          displayModelName='Claude Sonnet 4'
          
          # Issue本文からモデル設定を抽出
          if echo "$ISSUE_BODY" | grep -q "claude-opus-4-20250514"; then
            selectedModel='claude-opus-4-20250514'
            displayModelName='Claude Opus 4'
          elif echo "$ISSUE_BODY" | grep -q "claude-3-5-haiku-20241022"; then
            selectedModel='claude-3-5-haiku-20241022'
            displayModelName='Claude 3.5 Haiku'
          elif echo "$ISSUE_BODY" | grep -q "claude-sonnet-4-20250514"; then
            selectedModel='claude-sonnet-4-20250514'
            displayModelName='Claude Sonnet 4'
          else
            # フォールバック：複雑度ラベルに基づく
            if echo "$ISSUE_LABELS" | grep -q "complex-task"; then
              selectedModel='claude-opus-4-20250514'
              displayModelName='Claude Opus 4'
            elif echo "$ISSUE_LABELS" | grep -q "simple-task"; then
              selectedModel='claude-3-5-haiku-20241022'
              displayModelName='Claude 3.5 Haiku'
            fi
          fi
          
          # ターン数設定の抽出（デフォルト: 100）
          max_turns_env=$(echo "$ISSUE_BODY" | grep -o "環境分析フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1)
          max_turns_env=${max_turns_env:-100}
          
          max_turns_analysis=$(echo "$ISSUE_BODY" | grep -o "Issue分析フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1)
          max_turns_analysis=${max_turns_analysis:-100}
          
          max_turns_implement=$(echo "$ISSUE_BODY" | grep -o "実装フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1)
          max_turns_implement=${max_turns_implement:-300}
          
          max_turns_review=$(echo "$ISSUE_BODY" | grep -o "レビューフェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1)
          max_turns_review=${max_turns_review:-100}
          
          iterations_limit=$(echo "$ISSUE_BODY" | grep -o "コーダー・レビュアー対話回数制限: [0-9]\+" | grep -o "[0-9]\+" | head -1)
          iterations_limit=${iterations_limit:-3}

          echo "selected_model=$selectedModel" >> $GITHUB_OUTPUT
          echo "display_model_name=$displayModelName" >> $GITHUB_OUTPUT
          echo "auth_type=OAUTH" >> $GITHUB_OUTPUT
          
          # 抽出した設定値をoutputに追加
          echo "max_turns_env=$max_turns_env" >> $GITHUB_OUTPUT
          echo "max_turns_analysis=$max_turns_analysis" >> $GITHUB_OUTPUT
          echo "max_turns_implement=$max_turns_implement" >> $GITHUB_OUTPUT
          echo "max_turns_review=$max_turns_review" >> $GITHUB_OUTPUT
          echo "iterations_limit=$iterations_limit" >> $GITHUB_OUTPUT
          
          # デバッグ出力
          echo "🔧 設定値:"
          echo "  モデル: $displayModelName ($selectedModel)"
          echo "  環境分析ターン数: $max_turns_env"
          echo "  Issue分析ターン数: $max_turns_analysis"
          echo "  実装ターン数: $max_turns_implement"
          echo "  レビューターン数: $max_turns_review"
          echo "  対話回数制限: $iterations_limit"

          # ブランチ名生成
          NORMALIZED_TITLE=$(echo "$ISSUE_TITLE" | \
            sed 's/[^a-zA-Z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\u3400-\u4DBF _-]//g' | \
            sed 's/[ _]/\-/g' | \
            sed 's/--*/-/g' | \
            sed 's/^-//;s/-$//' | \
            cut -c1-50)

          BRANCH_NAME="claude-task/${ISSUE_NUM}-${NORMALIZED_TITLE}"

          # 必要なラベルを作成
          gh label create "claude-processing" \
            --description "Claude is currently processing this issue" \
            --color "1d76db" \
            2>/dev/null || true

          gh label create "claude-done" \
            --description "Claude has completed processing this issue" \
            --color "0e8a16" \
            2>/dev/null || true

          # ブランチ作成
          # 既存のブランチを確認
          if git ls-remote --heads origin "$BRANCH_NAME" | grep -q "$BRANCH_NAME"; then
            echo "⚠️ ブランチ $BRANCH_NAME は既に存在します。番号を付けて再試行します。"
            COUNTER=2
            while git ls-remote --heads origin "${BRANCH_NAME}-${COUNTER}" | grep -q "${BRANCH_NAME}-${COUNTER}"; do
              COUNTER=$((COUNTER + 1))
            done
            BRANCH_NAME="${BRANCH_NAME}-${COUNTER}"
            echo "🌿 新しいブランチ名: $BRANCH_NAME"
          fi

          # ベースブランチの選択と作成
          # claude-codeから作成（存在しない場合はdevelopから作成）
          if git ls-remote --heads origin claude-code | grep -q claude-code; then
            echo "🌿 claude-codeブランチから新しいブランチを作成します"
            git checkout -b $BRANCH_NAME origin/claude-code
          else
            echo "⚠️ claude-codeブランチが存在しません。developから作成します"
            git checkout -b $BRANCH_NAME origin/develop
          fi
          git push -u origin $BRANCH_NAME

          # processingラベルを追加
          gh issue edit $ISSUE_NUM --add-label "claude-processing"

          # 作業ディレクトリ初期化
          WORK_DIR="claude/claude-work/issue-${ISSUE_NUM}"
          mkdir -p "$WORK_DIR/implementation"
          mkdir -p "$WORK_DIR/review"
          mkdir -p "$WORK_DIR/dialogue"

          # ナレッジベースディレクトリの初期化（存在しない場合のみ）
          KNOWLEDGE_DIR="claude/claude-knowledge"
          mkdir -p "$KNOWLEDGE_DIR/environment"
          mkdir -p "$KNOWLEDGE_DIR/patterns"
          mkdir -p "$KNOWLEDGE_DIR/issues"
          mkdir -p "$KNOWLEDGE_DIR/history"

          # 初期状態ファイル
          cat > "$WORK_DIR/state.json" << EOF
          {
            "issue_number": "${ISSUE_NUM}",
            "branch_name": "${BRANCH_NAME}",
            "phase": "INIT",
            "iteration": 0,
            "status": "IN_PROGRESS",
            "created_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
          }
          EOF

          git add .
          git commit -m "Initialize sequential dialogue for issue #${ISSUE_NUM}"
          git push

          echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT

          # Issueにコメント
          printf '## 🚀 Claude Orchestrator 開始\n\n' > /tmp/comment_dialogue_init.md
          printf '**ブランチ**: `%s`\n' "$BRANCH_NAME" >> /tmp/comment_dialogue_init.md
          printf '**モデル**: %s\n\n' "$displayModelName" >> /tmp/comment_dialogue_init.md
          printf 'これから以下のプロセスを実行します：\n' >> /tmp/comment_dialogue_init.md
          printf '1. 🔍 環境分析（初回のみ）\n' >> /tmp/comment_dialogue_init.md
          printf '2. 📋 Issue分析とタスクリスト作成\n' >> /tmp/comment_dialogue_init.md
          printf '3. 💻 実装（開発者）\n' >> /tmp/comment_dialogue_init.md
          printf '4. 📝 レビュー（レビュワー）\n' >> /tmp/comment_dialogue_init.md
          printf '5. 🔄 レビュー結果に基づく修正（必要に応じて繰り返し）\n' >> /tmp/comment_dialogue_init.md
          printf '6. 📚 ナレッジ更新\n' >> /tmp/comment_dialogue_init.md
          printf '7. ✅ 完了\n\n' >> /tmp/comment_dialogue_init.md
          printf '各フェーズは独立したワークフロー実行として、完全に分離された視点で実行されます。\n' >> /tmp/comment_dialogue_init.md
          gh issue comment $ISSUE_NUM --body-file /tmp/comment_dialogue_init.md

          # 2秒待機
          echo "⏳ 次のフェーズまで2秒待機..."
          sleep 2

          # 環境分析が必要かチェック
          if [ ! -f "claude/claude-knowledge/environment/.analysis-complete" ]; then
            echo "🔍 環境分析が必要です"
            # 環境分析フェーズを起動
            gh workflow run claude-orchestrator.yml \
              -f issue_number=${ISSUE_NUM} \
              -f phase=ANALYZE_ENV \
              -f iteration=0 \
              -f branch_name="${BRANCH_NAME}" \
              -f selected_model="${selectedModel}" \
              -f auth_type="OAUTH" \
              --ref claude-base
          else
            echo "✅ 環境分析は完了済みです"
            # 次のフェーズ（ANALYZE）を起動
            gh workflow run claude-orchestrator.yml \
              -f issue_number=${ISSUE_NUM} \
              -f phase=ANALYZE \
              -f iteration=0 \
              -f branch_name="${BRANCH_NAME}" \
              -f selected_model="${selectedModel}" \
              -f auth_type="OAUTH" \
              --ref claude-base
          fi

      # ANALYZE_ENV Phase: 環境分析
      - name: Notify Environment Analysis Start
        if: steps.setup.outputs.phase == 'ANALYZE_ENV' && env.SLACK_WEBHOOK != ''
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          if [ -n "$SLACK_WEBHOOK" ]; then
            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d '{
                "text": "🔍 環境分析Bot: 分析開始",
                "attachments": [{
                  "color": "#2196F3",
                  "fields": [
                    {"title": "Issue", "value": "#${{ steps.setup.outputs.issue_number }}", "short": true},
                    {"title": "フェーズ", "value": "環境分析", "short": true},
                    {"title": "役割", "value": "環境分析専門家", "short": true},
                    {"title": "目的", "value": "プロジェクト構造と技術スタックの分析", "short": false}
                  ]
                }]
              }'
          fi

      - name: Checkout Working Branch (Analyze Env)
        if: steps.setup.outputs.phase == 'ANALYZE_ENV' && steps.setup.outputs.branch_name != ''
        run: |
          git fetch origin
          git checkout ${{ steps.setup.outputs.branch_name }}

          # 作業ディレクトリをデバッグ出力
          echo "🗂️ 現在の作業ディレクトリ: $(pwd)"
          echo "📁 GITHUB_WORKSPACE: $GITHUB_WORKSPACE"
          echo "📋 ディレクトリ構造:"
          ls -la
          echo "📂 claude ディレクトリの確認:"
          ls -la claude/ || echo "claude ディレクトリがありません"

      - name: Analyze Environment
        if: steps.setup.outputs.phase == 'ANALYZE_ENV' && steps.setup.outputs.auth_type == 'OAUTH'
        id: analyze-env
        uses: grll/claude-code-base-action@beta
        timeout-minutes: 120  # 2時間のタイムアウト（環境分析は時間がかかる場合がある）
        env:
          GH_TOKEN: ${{ github.token }}
          MCP_GITHUB_TOKEN: ${{ github.token }}
          NODE_ENV: 'production'
        with:
          # OAuth認証設定（必須）
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}

          # モデル設定
          model: ${{ github.event.inputs.selected_model || 'claude-sonnet-4-20250514' }}

          # プロンプト
          prompt: |
            あなたはプロジェクト環境分析の専門家です。
            プロジェクト全体の構造と特性を分析し、効率的な開発のための基盤情報を提供します。

            ## 重要な前提情報
            - 現在の作業ディレクトリは GitHub Actions のワークスペースです
            - リポジトリのルートディレクトリで作業します
            - 最初に必ず pwd コマンドで現在地を確認してください

            ## ファイル作成の具体的な手順

            1. **現在地の確認と既存ナレッジの確認**
               ```bash
               pwd
               ls -la
               # 既存の環境分析ナレッジを確認
               if [ -d "claude/claude-knowledge/environment" ]; then
                 echo "既存の環境分析ナレッジを確認..."
                 ls -la claude/claude-knowledge/environment/
                 # 既存ファイルの内容を参照して、更新が必要な部分のみ分析
               fi
               mkdir -p claude/claude-knowledge/environment
               ```

            2. **プロジェクト構造の分析**
               - 既存の `project-structure.md` があれば参照し、変更点のみ更新
               - ディレクトリ構成を調査
               - 主要ファイルの配置を特定
               - `claude/claude-knowledge/environment/project-structure.md` に保存

            3. **技術スタックの分析**
               - 既存の `tech-stack.md` があれば参照し、新規追加・更新された依存関係のみ分析
               - package.json, composer.json などを確認
               - 使用されているフレームワーク、ライブラリを特定
               - `claude/claude-knowledge/environment/tech-stack.md` に保存

            4. **コーディング規約の分析**
               - 既存の `coding-conventions.md` があれば参照し、新しいパターンのみ追加
               - 既存コードのスタイルを分析
               - 命名規則やファイル構成パターンを特定
               - `claude/claude-knowledge/environment/coding-conventions.md` に保存

            5. **分析完了マーカーの作成**
               ```bash
               touch claude/claude-knowledge/environment/.analysis-complete
               echo '{"last_updated": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"}' > claude/claude-knowledge/environment/last-updated.json
               ```

            ## 重要な指示
            - 既存のナレッジがある場合は、それを基盤として差分更新を行う
            - 完全に新規作成するのではなく、既存の知識を活用して効率化する
            - 各ファイルは明確で実用的な内容にしてください
            - 将来のタスクで参照されることを念頭に置いて記述してください
          # すべてのツールを利用可能にする
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          max_turns: ${{ steps.setup.outputs.max_turns_env || env.MAX_TURNS_ENV_ANALYSIS }}

      # 環境分析の結果を確認
      - name: Debug Environment Analysis Results
        if: steps.setup.outputs.phase == 'ANALYZE_ENV' && always()
        run: |
          echo "=== Environment Analysis Debug ==="
          echo "Execution file: ${{ steps.analyze-env.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.analyze-env.outputs.conclusion }}"
          echo "Model used: ${{ steps.analyze-env.outputs.model }}"

          # 実行ファイルの存在確認
          if [ -f "${{ steps.analyze-env.outputs.execution_file }}" ]; then
            echo "✅ Execution file exists"
            echo "File size: $(ls -lh "${{ steps.analyze-env.outputs.execution_file }}" | awk '{print $5}')"

            # Claudeのすべての応答を表示
            echo "=== All Claude Responses ==="
            jq -r '.[] | select(.type == "assistant") | {
              turn: .turn,
              response: (.message.content[] | select(.type == "text") | .text)
            }' "${{ steps.analyze-env.outputs.execution_file }}" 2>/dev/null || echo "Failed to parse responses"

            # ツール使用状況を確認
            echo "=== Tool Usage ==="
            jq -r '.[] | select(.type == "assistant") | .message.tool_uses[]? | {
              tool: .name,
              input: .input
            }' "${{ steps.analyze-env.outputs.execution_file }}" 2>/dev/null || echo "No tool usage found"

            # エラーがあれば表示
            echo "=== Errors (if any) ==="
            jq -r '.[] | select(.error != null) | .error' "${{ steps.analyze-env.outputs.execution_file }}" 2>/dev/null || echo "No errors found"

            # ファイル作成の確認
            echo "=== File Creation Check ==="
            echo "Expected files in claude/claude-knowledge/environment/:"
            ls -la claude/claude-knowledge/environment/ 2>/dev/null || echo "Directory not found"

            # 最後のClaude応答を取得（最大2000文字）
            CLAUDE_RESPONSE=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "${{ steps.analyze-env.outputs.execution_file }}" 2>/dev/null | head -c 2000 || echo "")
            if [ -n "$CLAUDE_RESPONSE" ]; then
              echo "📝 Last Claude Response (first 2000 chars):"
              echo "$CLAUDE_RESPONSE"
            else
              echo "⚠️ No Claude response found in execution file"
            fi
          else
            echo "❌ Execution file not found"
          fi

      - name: Post-Analyze-Env Actions
        if: steps.setup.outputs.phase == 'ANALYZE_ENV'
        env:
          GH_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          # Claudeの実行結果を解析
          EXEC_FILE="${{ steps.analyze-env.outputs.execution_file }}"
          CLAUDE_SUMMARY=""
          TASK_STATUS=""
          FILES_CREATED=""

          if [ -f "$EXEC_FILE" ]; then
            # Claudeの最後の応答を取得（最大1000文字）
            CLAUDE_SUMMARY=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "$EXEC_FILE" 2>/dev/null | head -c 1000 || echo "応答を取得できませんでした")

            # 作成されたファイルを確認（GITHUB_WORKSPACEを使用）
            if [ -f "${GITHUB_WORKSPACE}/claude/claude-knowledge/environment/project-structure.md" ]; then
              FILES_CREATED="${FILES_CREATED}- project-structure.md ✅\n"
            else
              FILES_CREATED="${FILES_CREATED}- project-structure.md ❌\n"
            fi

            if [ -f "${GITHUB_WORKSPACE}/claude/claude-knowledge/environment/tech-stack.md" ]; then
              FILES_CREATED="${FILES_CREATED}- tech-stack.md ✅\n"
            else
              FILES_CREATED="${FILES_CREATED}- tech-stack.md ❌\n"
            fi

            if [ -f "${GITHUB_WORKSPACE}/claude/claude-knowledge/environment/coding-conventions.md" ]; then
              FILES_CREATED="${FILES_CREATED}- coding-conventions.md ✅\n"
            else
              FILES_CREATED="${FILES_CREATED}- coding-conventions.md ❌\n"
            fi

            if [ -f "${GITHUB_WORKSPACE}/claude/claude-knowledge/environment/.analysis-complete" ]; then
              FILES_CREATED="${FILES_CREATED}- .analysis-complete ✅\n"
              TASK_STATUS="✅ 環境分析完了"
            else
              FILES_CREATED="${FILES_CREATED}- .analysis-complete ❌\n"
              TASK_STATUS="⚠️ 環境分析未完了"
            fi
          else
            CLAUDE_SUMMARY="実行ファイルが見つかりません"
            TASK_STATUS="❌ 実行エラー"
          fi

          # Slack通知送信
          if [ -n "$SLACK_WEBHOOK" ]; then
            # JSONエスケープ処理（jqを使用）
            CLAUDE_SUMMARY_ESCAPED=$(echo "$CLAUDE_SUMMARY" | jq -Rs . | sed 's/^"//;s/"$//')
            FILES_CREATED_ESCAPED=$(echo "$FILES_CREATED" | jq -Rs . | sed 's/^"//;s/"$//')

            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d "{
                \"text\": \"🔍 環境分析Bot: 実行完了\",
                \"attachments\": [{
                  \"color\": \"$([[ \"$TASK_STATUS\" == *\"✅\"* ]] && echo \"good\" || echo \"warning\")\",
                  \"fields\": [
                    {\"title\": \"Issue\", \"value\": \"#${{ steps.setup.outputs.issue_number }}\", \"short\": true},
                    {\"title\": \"ステータス\", \"value\": \"$TASK_STATUS\", \"short\": true},
                    {\"title\": \"作成ファイル\", \"value\": \"$FILES_CREATED_ESCAPED\", \"short\": false},
                    {\"title\": \"Claudeの応答（要約）\", \"value\": \"$CLAUDE_SUMMARY_ESCAPED\", \"short\": false}
                  ]
                }]
              }"
          fi

          # 変更をコミット
          git add .
          git commit -m "Environment analysis for issue #${{ steps.setup.outputs.issue_number }}" || true
          git push

          # 2秒待機
          echo "⏳ 次のフェーズまで2秒待機..."
          sleep 2

          # Issue分析フェーズを起動
          gh workflow run claude-orchestrator.yml \
            -f issue_number=${{ steps.setup.outputs.issue_number }} \
            -f phase=ANALYZE \
            -f iteration=0 \
            -f branch_name="${{ steps.setup.outputs.branch_name }}" \
            -f selected_model="${{ github.event.inputs.selected_model }}" \
            -f auth_type="${{ github.event.inputs.auth_type }}" \
            --ref develop

      # ANALYZE Phase: Issue分析
      - name: Notify Issue Analysis Start
        if: steps.setup.outputs.phase == 'ANALYZE' && env.SLACK_WEBHOOK != ''
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          if [ -n "$SLACK_WEBHOOK" ]; then
            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d '{
                "text": "📋 Issue分析Bot: 分析開始",
                "attachments": [{
                  "color": "#4CAF50",
                  "fields": [
                    {"title": "Issue", "value": "#${{ steps.setup.outputs.issue_number }}", "short": true},
                    {"title": "フェーズ", "value": "Issue分析", "short": true},
                    {"title": "役割", "value": "Issue分析専門家", "short": true},
                    {"title": "目的", "value": "タスクリストの作成とナレッジベースの活用", "short": false}
                  ]
                }]
              }'
          fi

      - name: Debug Info (Analyze)
        if: steps.setup.outputs.phase == 'ANALYZE'
        run: |
          echo "🔍 デバッグ情報 (ANALYZE)"
          echo "Phase: ${{ steps.setup.outputs.phase }}"
          echo "Auth Type: ${{ steps.setup.outputs.auth_type }}"
          echo "Branch Name: ${{ steps.setup.outputs.branch_name }}"
          echo "Selected Model: ${{ github.event.inputs.selected_model }}"
          echo "Event Name: ${{ github.event_name }}"
          echo "Workflow Ref: ${{ github.workflow_ref }}"
          echo "CLAUDE_ACCESS_TOKEN exists: ${{ secrets.CLAUDE_ACCESS_TOKEN != '' }}"
          echo "CLAUDE_EXPIRES_AT exists: ${{ secrets.CLAUDE_EXPIRES_AT != '' }}"
          echo "ANTHROPIC_API_KEY exists: ${{ secrets.ANTHROPIC_API_KEY != '' }}"

          # 環境変数の確認
          echo "ENV CLAUDE_ACCESS_TOKEN: ${CLAUDE_ACCESS_TOKEN:+SET}"
          echo "ENV CLAUDE_EXPIRES_AT: ${CLAUDE_EXPIRES_AT:+SET}"
          echo "ENV ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:+SET}"

      - name: Checkout Working Branch (Analyze)
        if: steps.setup.outputs.phase == 'ANALYZE' && steps.setup.outputs.branch_name != ''
        run: |
          git fetch origin
          git checkout ${{ steps.setup.outputs.branch_name }}

      - name: Analyze Issue
        if: steps.setup.outputs.phase == 'ANALYZE' && steps.setup.outputs.auth_type == 'OAUTH'
        id: analyze-issue
        uses: grll/claude-code-base-action@beta
        timeout-minutes: 60  # 1時間のタイムアウト（Issue分析は比較的短時間）
        env:
          GH_TOKEN: ${{ github.token }}
          MCP_GITHUB_TOKEN: ${{ github.token }}
          NODE_ENV: 'production'
        with:
          # OAuth認証設定（必須）
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}

          # モデル設定
          model: ${{ github.event.inputs.selected_model || 'claude-sonnet-4-20250514' }}

          # プロンプト
          prompt: |
            あなたはIssue分析の専門家です。
            Issue #${{ steps.setup.outputs.issue_number }} を分析してタスクリストを作成してください。

            ## 重要な前提情報
            - 現在の作業ディレクトリは GitHub Actions のワークスペースです
            - リポジトリのルートディレクトリで作業します
            - 最初に必ず pwd コマンドで現在地を確認してください
            
            ## Claude用ディレクトリ構造
            プロジェクトには以下のClaude専用ディレクトリが存在します：
            
            - `claude/claude-knowledge/`: 蓄積されたナレッジベース
              - `environment/`: プロジェクト構造、技術スタック、コーディング規約
              - `patterns/`: 成功したパターンやベストプラクティス
              - `issues/`: 過去のエラーや解決方法
              - `history/`: 過去のIssue実装サマリー
            
            - `claude/claude-requirements/`: PRD（製品要求仕様書）
              - `active/`: 現在アクティブなPRD
              - `archive/`: 完了したPRD
              - `pending/`: 保留中のPRD
              - `templates/`: PRDテンプレート
            
            - `claude/claude-work/`: 作業ディレクトリ
              - `issue-{番号}/`: 各Issue専用の作業ディレクトリ
                - `dialogue/`: 対話記録
                - `implementation/`: 実装内容
                - `review/`: レビュー結果
                - `task-list.md`: タスクリスト
                - `state.json`: 進行状態

            ## 実行手順

            1. **現在地の確認とIssue内容の取得**
               ```bash
               pwd
               ls -la
               gh issue view ${{ steps.setup.outputs.issue_number }} --json title,body,labels
               ```

            2. **ナレッジベースの参照**（存在する場合）
               - `claude/claude-knowledge/environment/project-structure.md`
               - `claude/claude-knowledge/environment/tech-stack.md`
               - `claude/claude-knowledge/environment/coding-conventions.md`
               - `claude/claude-knowledge/patterns/successful-patterns.md`
               - `claude/claude-knowledge/issues/common-errors.md`

            3. **作業ディレクトリの作成とファイル生成**
               ```bash
               mkdir -p claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/dialogue
               ```

               - `claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/task-list.md` にタスクリストを作成
               - `claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/dialogue/conversation.md` に分析内容を記録

            4. **state.json の更新**
               ```bash
               # state.jsonを読み込んで更新
               cat claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/state.json
               # 内容を更新（phase: "ANALYZED", analyzed_at: 現在時刻を追加）
               ```

            ## タスクリスト作成のガイドライン
            
            **重要**: タスクリストは実装の大きな単位で作成してください。
            - **タスク数は5-25個の範囲**で作成（Issueの複雑さに応じて調整）
            - 細かい実装詳細は各タスクの説明として含める
            - 1つのタスクは1つの機能単位または1つのファイル作成/修正を表す
            - 過度に細分化しない（例：「ボタンを追加」「ボタンのイベントを追加」は1つのタスクにまとめる）
            
            **既に分析済みの内容は除外する**:
            - プロジェクト構造の把握（環境分析フェーズで完了済み）
            - 技術スタックの調査（環境分析フェーズで完了済み）
            - コーディング規約の確認（環境分析フェーズで完了済み）
            - 上記の情報はナレッジベースから参照して活用し、タスクリストには含めない
            
            タスクリストの形式：
            ```markdown
            # Issue #番号: タイトル タスクリスト
            
            ## タスク一覧
            
            ### 1. [タスク名]
            - [ ] **概要**: 何を実装するか
            - **詳細**: 実装の詳細説明
            - **ファイル**: 作成/修正するファイル
            
            ### 2. [次のタスク名]
            ...
            ```
            
            必ずファイルを作成し、具体的なタスクリストを記述してください。
          # すべてのツールを利用可能にする
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          max_turns: ${{ steps.setup.outputs.max_turns_analysis || env.MAX_TURNS_ISSUE_ANALYSIS }}

      # Issue分析の結果を確認
      - name: Debug Issue Analysis Results
        if: steps.setup.outputs.phase == 'ANALYZE' && always()
        run: |
          echo "=== Issue Analysis Debug ==="
          echo "Execution file: ${{ steps.analyze-issue.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.analyze-issue.outputs.conclusion }}"
          echo "Model used: ${{ steps.analyze-issue.outputs.model }}"

          # 実行ファイルの存在確認
          if [ -f "${{ steps.analyze-issue.outputs.execution_file }}" ]; then
            echo "✅ Execution file exists"
            echo "File size: $(ls -lh "${{ steps.analyze-issue.outputs.execution_file }}" | awk '{print $5}')"

            # Claudeのすべての応答を表示
            echo "=== All Claude Responses ==="
            jq -r '.[] | select(.type == "assistant") | {
              turn: .turn,
              response: (.message.content[] | select(.type == "text") | .text)
            }' "${{ steps.analyze-issue.outputs.execution_file }}" 2>/dev/null || echo "Failed to parse responses"

            # ツール使用状況を確認
            echo "=== Tool Usage ==="
            jq -r '.[] | select(.type == "assistant") | .message.tool_uses[]? | {
              tool: .name,
              input: .input
            }' "${{ steps.analyze-issue.outputs.execution_file }}" 2>/dev/null || echo "No tool usage found"

            # エラーがあれば表示
            echo "=== Errors (if any) ==="
            jq -r '.[] | select(.error != null) | .error' "${{ steps.analyze-issue.outputs.execution_file }}" 2>/dev/null || echo "No errors found"

            # ファイル作成の確認
            echo "=== File Creation Check ==="
            echo "Expected files in claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/:"
            ls -la claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/ 2>/dev/null || echo "Directory not found"

            # 最後のClaude応答を取得（最大2000文字）
            CLAUDE_RESPONSE=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "${{ steps.analyze-issue.outputs.execution_file }}" 2>/dev/null | head -c 2000 || echo "")
            if [ -n "$CLAUDE_RESPONSE" ]; then
              echo "📝 Last Claude Response (first 2000 chars):"
              echo "$CLAUDE_RESPONSE"
            else
              echo "⚠️ No Claude response found in execution file"
            fi
          else
            echo "❌ Execution file not found"
          fi

      - name: Post-Analyze Actions
        if: steps.setup.outputs.phase == 'ANALYZE'
        env:
          GH_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          # ブランチが指定されていればチェックアウト
          if [ -n "${{ steps.setup.outputs.branch_name }}" ]; then
            git checkout ${{ steps.setup.outputs.branch_name }}
          fi

          # Claudeの実行結果を解析
          EXEC_FILE="${{ steps.analyze-issue.outputs.execution_file }}"
          CLAUDE_SUMMARY=""
          TASK_LIST=""
          TASK_STATUS=""

          if [ -f "$EXEC_FILE" ]; then
            # Claudeの最後の応答を取得（最大1000文字）
            CLAUDE_SUMMARY=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "$EXEC_FILE" 2>/dev/null | head -c 1000 || echo "応答を取得できませんでした")

            # タスクリストの内容を確認（GITHUB_WORKSPACEを使用）
            TASK_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/task-list.md"
            if [ -f "$TASK_FILE" ]; then
              # タスクリストの内容を取得（最大20行）
              TASK_LIST=$(head -n 20 "$TASK_FILE" | sed 's/^/• /' | tr '\n' ' ')
              TASK_STATUS="✅ タスクリスト作成完了"
            else
              TASK_LIST="タスクリストが作成されていません"
              TASK_STATUS="⚠️ タスクリスト未作成"
            fi
          else
            CLAUDE_SUMMARY="実行ファイルが見つかりません"
            TASK_LIST="エラーのため取得できません"
            TASK_STATUS="❌ 実行エラー"
          fi

          # Slack通知送信
          if [ -n "$SLACK_WEBHOOK" ]; then
            # 色の設定
            if [[ "$TASK_STATUS" == *"✅"* ]]; then
              COLOR="good"
            else
              COLOR="warning"
            fi
            
            # JSONペイロードを安全に作成
            JSON_PAYLOAD=$(jq -n \
              --arg text "📋 Issue分析Bot: 分析完了" \
              --arg issue "#${{ steps.setup.outputs.issue_number }}" \
              --arg status "$TASK_STATUS" \
              --arg tasks "$TASK_LIST" \
              --arg summary "$CLAUDE_SUMMARY" \
              --arg color "$COLOR" \
              '{
                text: $text,
                attachments: [{
                  color: $color,
                  fields: [
                    {title: "Issue", value: $issue, short: true},
                    {title: "ステータス", value: $status, short: true},
                    {title: "タスクリスト", value: $tasks, short: false},
                    {title: "Claudeの応答（要約）", value: $summary, short: false}
                  ]
                }]
              }')

            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d "$JSON_PAYLOAD"
          fi

          # 変更をコミット
          git add .
          git commit -m "Analysis complete for issue #${{ steps.setup.outputs.issue_number }}" || true
          git push

          # 2秒待機
          echo "⏳ 次のフェーズまで2秒待機..."
          sleep 2

          # 実装フェーズを起動
          gh workflow run claude-orchestrator.yml \
            -f issue_number=${{ steps.setup.outputs.issue_number }} \
            -f phase=IMPLEMENT \
            -f iteration=1 \
            -f branch_name="${{ steps.setup.outputs.branch_name }}" \
            -f selected_model="${{ github.event.inputs.selected_model }}" \
            -f auth_type="${{ github.event.inputs.auth_type }}" \
            --ref develop

      # IMPLEMENT Phase: 実装
      - name: Checkout Working Branch (Implement)
        if: steps.setup.outputs.phase == 'IMPLEMENT' && steps.setup.outputs.branch_name != ''
        run: |
          git fetch origin
          git checkout ${{ steps.setup.outputs.branch_name }}
          git pull origin ${{ steps.setup.outputs.branch_name }}

      - name: Notify Implementation Start
        if: steps.setup.outputs.phase == 'IMPLEMENT' && env.SLACK_WEBHOOK != ''
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          if [ -n "$SLACK_WEBHOOK" ]; then
            # タスクリストの内容を取得
            TASK_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/task-list.md"
            TASK_LIST=""
            TASK_COUNT=0
            COMPLETED_COUNT=0
            
            if [ -f "$TASK_FILE" ]; then
              # タスクの総数を数える（### で始まるメインタスクのみをカウント）
              TASK_COUNT=$(grep -c '^### [0-9]\+\.' "$TASK_FILE" || echo "0")
              # 完了済みタスク数（実装記録ファイルがあるイテレーション数で判断）
              COMPLETED_COUNT=$((${{ steps.setup.outputs.iteration }} - 1))
              
              # タスクリストの主要項目を取得（タスク名のみ、最大5件）
              TASK_TITLES=$(grep '^### [0-9]\+\.' "$TASK_FILE" | head -5 | sed 's/^### [0-9]\+\. //' | sed 's/^/• /')
              if [ $(grep -c '^### [0-9]\+\.' "$TASK_FILE") -gt 5 ]; then
                TASK_TITLES=$(printf "%s\n• ... 他 %d タスク" "$TASK_TITLES" "$((TASK_COUNT - 5))")
              fi
            else
              TASK_TITLES="タスクリストが見つかりません"
            fi
            
            # レビュー結果の取得（前回のイテレーションがある場合）
            PREV_ITERATION=$((${{ steps.setup.outputs.iteration }} - 1))
            REVIEW_STATUS=""
            REVIEW_POINTS=""
            if [ $PREV_ITERATION -gt 0 ]; then
              REVIEW_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/review/review-${PREV_ITERATION}.md"
              if [ -f "$REVIEW_FILE" ]; then
                # レビュー判定を取得
                if grep -q "## 判定: NEEDS_IMPROVEMENT" "$REVIEW_FILE"; then
                  REVIEW_STATUS="要改善"
                  # 主要な指摘事項を取得（最大3件）
                  REVIEW_POINTS=$(grep -A 1 '^### [0-9]\+\.' "$REVIEW_FILE" | grep -v '^--$' | grep -v '^### ' | head -6 | sed 's/^/• /')
                fi
              fi
            fi
            
            # プログレスメッセージ作成
            if [ $COMPLETED_COUNT -eq 0 ]; then
              PROGRESS_MSG="🚀 初回実装開始"
            else
              PROGRESS_PERCENT=$((COMPLETED_COUNT * 100 / TASK_COUNT))
              PROGRESS_BAR=""
              for i in $(seq 1 10); do
                if [ $((i * 10)) -le $PROGRESS_PERCENT ]; then
                  PROGRESS_BAR="${PROGRESS_BAR}■"
                else
                  PROGRESS_BAR="${PROGRESS_BAR}□"
                fi
              done
              PROGRESS_MSG="${PROGRESS_BAR} ${COMPLETED_COUNT}/${TASK_COUNT} タスク (${PROGRESS_PERCENT}%)"
            fi
            
            # JSONペイロードを作成
            if [ -n "$REVIEW_STATUS" ]; then
              # レビュー後の再実装
              JSON_PAYLOAD=$(jq -n \
                --arg text "💻 開発者Bot: 実装再開 (イテレーション ${{ steps.setup.outputs.iteration }})" \
                --arg issue "#${{ steps.setup.outputs.issue_number }}" \
                --arg progress "$PROGRESS_MSG" \
                --arg status "前回レビュー: $REVIEW_STATUS" \
                --arg points "$REVIEW_POINTS" \
                --arg tasks "$TASK_TITLES" \
                '{
                  text: $text,
                  attachments: [{
                    color: "#FF9800",
                    fields: [
                      {title: "Issue", value: $issue, short: true},
                      {title: "進捗", value: $progress, short: true},
                      {title: "ステータス", value: $status, short: false},
                      {title: "改善ポイント", value: $points, short: false},
                      {title: "タスク一覧", value: $tasks, short: false}
                    ]
                  }]
                }')
            else
              # 初回実装
              JSON_PAYLOAD=$(jq -n \
                --arg text "💻 開発者Bot: 実装開始" \
                --arg issue "#${{ steps.setup.outputs.issue_number }}" \
                --arg progress "$PROGRESS_MSG" \
                --arg tasks "$TASK_TITLES" \
                '{
                  text: $text,
                  attachments: [{
                    color: "#FF9800",
                    fields: [
                      {title: "Issue", value: $issue, short: true},
                      {title: "進捗", value: $progress, short: true},
                      {title: "タスク一覧", value: $tasks, short: false}
                    ]
                  }]
                }')
            fi
            
            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d "$JSON_PAYLOAD"
          fi

      - name: Implement Task
        id: implement
        if: steps.setup.outputs.phase == 'IMPLEMENT' && steps.setup.outputs.auth_type == 'OAUTH'
        uses: grll/claude-code-base-action@beta
        timeout-minutes: 300  # 5時間のタイムアウト（実装フェーズは最も時間がかかる）
        env:
          GH_TOKEN: ${{ github.token }}
          MCP_GITHUB_TOKEN: ${{ github.token }}
          NODE_ENV: 'production'
        with:
          # OAuth認証設定（必須）
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}

          # モデル設定
          model: ${{ github.event.inputs.selected_model || 'claude-sonnet-4-20250514' }}

          # プロンプト
          prompt: |
            あなたは開発者です。レビュワーの視点は完全に忘れてください。
            Issue #${{ steps.setup.outputs.issue_number }} の実装を行います。
            イテレーション: ${{ steps.setup.outputs.iteration }}

            ## 重要な前提情報
            - 現在の作業ディレクトリは GitHub Actions のワークスペースです
            - リポジトリのルートディレクトリで作業します
            - 最初に必ず pwd コマンドで現在地を確認してください
            
            ## Claude用ディレクトリ構造
            プロジェクトには以下のClaude専用ディレクトリが存在します：
            
            - `claude/claude-knowledge/`: 蓄積されたナレッジベース
              - `environment/`: プロジェクト構造、技術スタック、コーディング規約
              - `patterns/`: 成功したパターンやベストプラクティス
              - `issues/`: 過去のエラーや解決方法
              - `history/`: 過去のIssue実装サマリー
            
            - `claude/claude-requirements/`: PRD（製品要求仕様書）
              - `active/`: 現在アクティブなPRD（実装の参考資料）
            
            - `claude/claude-work/`: 作業ディレクトリ
              - `issue-{番号}/`: 現在のIssue専用の作業ディレクトリ
                - `task-list.md`: 実装すべきタスクリスト
                - `review/`: レビュー結果（前回のイテレーションがある場合）
                - `implementation/`: 実装内容を記録
                - `dialogue/`: 対話記録

            ## 実装手順

            1. **現在地の確認とタスクリストの読み込み**
               ```bash
               pwd
               ls -la
               cat claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/task-list.md
               ```

            2. **前回のレビュー結果の確認**（存在する場合）
               ```bash
               ls -la claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/review/
               # review-*.md があれば内容を確認
               ```

            3. **ナレッジベースの参照**
               - `claude/claude-knowledge/environment/coding-conventions.md`（コーディング規約）
               - `claude/claude-knowledge/patterns/successful-patterns.md`（成功パターン）
               - `claude/claude-knowledge/patterns/anti-patterns.md`（避けるべきパターン）
               - `claude/claude-knowledge/issues/common-errors.md`（よくあるエラー）

            4. **実装記録の作成**
               ```bash
               mkdir -p claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/implementation
               ```

               - `claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/implementation/attempt-${{ steps.setup.outputs.iteration }}.md` に実装内容を記録
               - `claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/dialogue/conversation.md` に開発者としてのコメントを追記

            5. **state.json の更新**
               ```bash
               # state.jsonを読み込んで更新
               jq '.phase = "IMPLEMENTED" | .last_implementation = "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"' \
                 claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/state.json > temp.json && \
                 mv temp.json claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/state.json
               ```

            ## 実装時の重要な注意事項
            
            **タスクリストの扱い**:
            - task-list.md に記載されたタスクリストを基準に実装してください
            - **実装の品質向上のため、各タスクを内部的に細分化しても構いません**
            - ただし、進捗報告や実装記録では**元のタスクリスト番号（例：タスク1、タスク2）を使用**してください
            - 細分化は実装詳細として扱い、メインのタスク構造は維持すること
            - レビュワーへの報告は元のタスクリスト単位で行うこと
            
            **ビルドとテスト**:
            - フロントエンドのコードを変更した場合は、開発用ビルドを実行してください:
              ```bash
              npm run webpack:dev
              ```
            - ビルドエラーが発生した場合は必ず修正してください
            - 本番用ビルド（webpack:prod）は使用しないでください
            
            必ずファイルを作成し、実装内容を具体的に記述してください。
          # すべてのツールを利用可能にする
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          max_turns: ${{ steps.setup.outputs.max_turns_implement || env.MAX_TURNS_IMPLEMENT }}
        continue-on-error: true

      # 実装結果を確認
      - name: Debug Implementation Results
        if: steps.setup.outputs.phase == 'IMPLEMENT' && always()
        run: |
          echo "=== Implementation Debug ==="
          echo "Execution file: ${{ steps.implement.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.implement.outputs.conclusion }}"
          echo "Model used: ${{ steps.implement.outputs.model }}"

          # 実行ファイルの存在確認
          if [ -f "${{ steps.implement.outputs.execution_file }}" ]; then
            echo "✅ Execution file exists"
            echo "File size: $(ls -lh "${{ steps.implement.outputs.execution_file }}" | awk '{print $5}')"

            # Claudeのすべての応答を表示
            echo "=== All Claude Responses ==="
            jq -r '.[] | select(.type == "assistant") | {
              turn: .turn,
              response: (.message.content[] | select(.type == "text") | .text)
            }' "${{ steps.implement.outputs.execution_file }}" 2>/dev/null || echo "Failed to parse responses"

            # ツール使用状況を確認
            echo "=== Tool Usage ==="
            jq -r '.[] | select(.type == "assistant") | .message.tool_uses[]? | {
              tool: .name,
              input: .input
            }' "${{ steps.implement.outputs.execution_file }}" 2>/dev/null || echo "No tool usage found"

            # エラーがあれば表示
            echo "=== Errors (if any) ==="
            jq -r '.[] | select(.error != null) | .error' "${{ steps.implement.outputs.execution_file }}" 2>/dev/null || echo "No errors found"

            # ファイル作成の確認
            echo "=== File Creation Check ==="
            echo "Expected files in claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/implementation/:"
            ls -la claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/implementation/ 2>/dev/null || echo "Directory not found"

            # 最後のClaude応答を取得（最大2000文字）
            CLAUDE_RESPONSE=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "${{ steps.implement.outputs.execution_file }}" 2>/dev/null | head -c 2000 || echo "")
            if [ -n "$CLAUDE_RESPONSE" ]; then
              echo "📝 Last Claude Response (first 2000 chars):"
              echo "$CLAUDE_RESPONSE"
            else
              echo "⚠️ No Claude response found in execution file"
            fi
          else
            echo "❌ Execution file not found"
          fi

      - name: Post-Implement Actions
        if: steps.setup.outputs.phase == 'IMPLEMENT'
        env:
          GH_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          # ブランチが指定されていればチェックアウト
          if [ -n "${{ steps.setup.outputs.branch_name }}" ]; then
            git checkout ${{ steps.setup.outputs.branch_name }}
          fi

          # ターン制限エラーをチェック（実装ステップの結果を確認）
          MAX_TURNS_REACHED=false
          IMPLEMENT_OUTCOME="${{ steps.implement.outcome }}"

          # claude-code-base-actionは continue-on-error: true なので outcome を確認
          if [ "$IMPLEMENT_OUTCOME" != "success" ]; then
            echo "⚠️ 実装フェーズが正常終了しませんでした（おそらくターン制限）"
            MAX_TURNS_REACHED=true
          fi

          # Claudeの実行結果を解析
          EXEC_FILE="${{ steps.implement.outputs.execution_file }}"
          CLAUDE_SUMMARY=""
          IMPLEMENTATION_STATUS=""
          FILES_CHANGED=""

          if [ -f "$EXEC_FILE" ]; then
            # Claudeの最後の応答を取得（最大1000文字）
            CLAUDE_SUMMARY=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "$EXEC_FILE" 2>/dev/null | head -c 1000 || echo "応答を取得できませんでした")

            # 実装ファイルの確認（GITHUB_WORKSPACEを使用）
            IMPL_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/implementation/attempt-${{ steps.setup.outputs.iteration }}.md"
            if [ -f "$IMPL_FILE" ]; then
              IMPLEMENTATION_STATUS="✅ 実装記録作成済み"
            else
              IMPLEMENTATION_STATUS="⚠️ 実装記録未作成"
            fi

            # 変更されたファイルを確認
            FILES_CHANGED=$(git status --porcelain | head -10 | sed 's/^/• /')
            if [ -z "$FILES_CHANGED" ]; then
              FILES_CHANGED="• 変更なし"
            fi
          else
            CLAUDE_SUMMARY="実行ファイルが見つかりません"
            IMPLEMENTATION_STATUS="❌ 実行エラー"
            FILES_CHANGED="• エラーのため取得できません"
          fi

          # Slack通知送信
          if [ -n "$SLACK_WEBHOOK" ]; then
            # JSONエスケープ処理（jqを使用）
            CLAUDE_SUMMARY_ESCAPED=$(echo "$CLAUDE_SUMMARY" | jq -Rs . | sed 's/^"//;s/"$//')
            FILES_CHANGED_ESCAPED=$(echo "$FILES_CHANGED" | jq -Rs . | sed 's/^"//;s/"$//')

            # ターン制限の場合は警告色
            if [ "$MAX_TURNS_REACHED" = "true" ]; then
              COLOR="warning"
              STATUS_MSG="⚠️ ターン制限で終了"
            else
              COLOR="good"
              STATUS_MSG="$IMPLEMENTATION_STATUS"
            fi

            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d "{
                \"text\": \"💻 開発者Bot: 実装完了 (イテレーション ${{ steps.setup.outputs.iteration }})\",
                \"attachments\": [{
                  \"color\": \"$COLOR\",
                  \"fields\": [
                    {\"title\": \"Issue\", \"value\": \"#${{ steps.setup.outputs.issue_number }}\", \"short\": true},
                    {\"title\": \"ステータス\", \"value\": \"$STATUS_MSG\", \"short\": true},
                    {\"title\": \"変更ファイル\", \"value\": \"$FILES_CHANGED_ESCAPED\", \"short\": false},
                    {\"title\": \"Claudeの応答（要約）\", \"value\": \"$CLAUDE_SUMMARY_ESCAPED\", \"short\": false}
                  ]
                }]
              }"
          fi

          # 変更をコミット
          git add .
          git commit -m "Implementation iteration ${{ steps.setup.outputs.iteration }} for issue #${{ steps.setup.outputs.issue_number }}" || true
          git push

          # ターン制限に達した場合は終了処理へ
          if [ "$MAX_TURNS_REACHED" = "true" ]; then
            echo "🔚 ターン制限のため、実装を終了します"

            # Issueにコメント
            printf '⚠️ ターン制限に達したため、現在の実装で作業を終了します\n\n' > /tmp/turn_limit_comment.md
            printf 'イテレーション: %s\n' "${{ steps.setup.outputs.iteration }}" >> /tmp/turn_limit_comment.md
            printf 'フェーズ: 実装\n' >> /tmp/turn_limit_comment.md
            printf 'ブランチ: `%s`\n\n' "${{ steps.setup.outputs.branch_name }}" >> /tmp/turn_limit_comment.md
            printf '現在の実装内容で作業を完了とします。\n' >> /tmp/turn_limit_comment.md
            gh issue comment ${{ steps.setup.outputs.issue_number }} --body-file /tmp/turn_limit_comment.md

            # 最終処理フェーズを起動
            gh workflow run claude-orchestrator.yml \
              -f issue_number=${{ steps.setup.outputs.issue_number }} \
              -f phase=FINALIZE \
              -f iteration=${{ steps.setup.outputs.iteration }} \
              -f branch_name="${{ steps.setup.outputs.branch_name }}" \
              -f selected_model="${{ github.event.inputs.selected_model }}" \
              -f auth_type="${{ github.event.inputs.auth_type }}" \
              --ref claude-base
            exit 0
          fi

          # 2秒待機
          echo "⏳ 次のフェーズまで2秒待機..."
          sleep 2

          # レビューフェーズを起動
          gh workflow run claude-orchestrator.yml \
            -f issue_number=${{ steps.setup.outputs.issue_number }} \
            -f phase=REVIEW \
            -f iteration=${{ steps.setup.outputs.iteration }} \
            -f branch_name="${{ steps.setup.outputs.branch_name }}" \
            -f selected_model="${{ github.event.inputs.selected_model }}" \
            -f auth_type="${{ github.event.inputs.auth_type }}" \
            --ref develop

      # REVIEW Phase: レビュー
      - name: Checkout Working Branch (Review)
        if: steps.setup.outputs.phase == 'REVIEW' && steps.setup.outputs.branch_name != ''
        run: |
          git fetch origin
          git checkout ${{ steps.setup.outputs.branch_name }}
          git pull origin ${{ steps.setup.outputs.branch_name }}

      - name: Notify Review Start
        if: steps.setup.outputs.phase == 'REVIEW' && env.SLACK_WEBHOOK != ''
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          if [ -n "$SLACK_WEBHOOK" ]; then
            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d '{
                "text": "📝 レビュワーBot: レビュー開始",
                "attachments": [{
                  "color": "#9C27B0",
                  "fields": [
                    {"title": "Issue", "value": "#${{ steps.setup.outputs.issue_number }}", "short": true},
                    {"title": "イテレーション", "value": "${{ steps.setup.outputs.iteration }}", "short": true},
                    {"title": "役割", "value": "レビュワー", "short": true},
                    {"title": "作業", "value": "実装内容の品質評価", "short": false}
                  ]
                }]
              }'
          fi

      - name: Review Implementation
        id: review
        if: steps.setup.outputs.phase == 'REVIEW' && steps.setup.outputs.auth_type == 'OAUTH'
        uses: grll/claude-code-base-action@beta
        timeout-minutes: 180  # 3時間のタイムアウト（レビューは詳細な分析が必要）
        env:
          GH_TOKEN: ${{ github.token }}
          MCP_GITHUB_TOKEN: ${{ github.token }}
          NODE_ENV: 'production'
        with:
          # OAuth認証設定（必須）
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}

          # モデル設定
          model: ${{ github.event.inputs.selected_model || 'claude-sonnet-4-20250514' }}

          # プロンプト
          prompt: |
            あなたはレビュワーです。開発者の意図を推測せず、客観的に評価してください。

            重要: 現在の作業ディレクトリは GitHub Actions のワークスペースです。
            pwd コマンドで確認してから作業を開始してください。

            Issue #${{ steps.setup.outputs.issue_number }}
            イテレーション: ${{ steps.setup.outputs.iteration }}
            
            ## Claude用ディレクトリ構造
            プロジェクトには以下のClaude専用ディレクトリが存在します：
            
            - `claude/claude-knowledge/`: 蓄積されたナレッジベース
              - `environment/`: プロジェクト構造、技術スタック、コーディング規約
              - `patterns/`: 成功したパターンやベストプラクティス
              - `issues/`: 過去のエラーや解決方法
              - `history/`: 過去のIssue実装サマリー
            
            - `claude/claude-requirements/`: PRD（製品要求仕様書）
              - `active/`: 現在アクティブなPRD（要件確認用）
            
            - `claude/claude-work/`: 作業ディレクトリ
              - `issue-{番号}/`: 現在のIssue専用の作業ディレクトリ
                - `task-list.md`: 実装すべきタスクリスト
                - `implementation/`: 実装内容の記録
                - `review/`: レビュー結果を記録

            ## レビュー方針
            
            **機能要件の実現を最優先**としてレビューしてください：
            - 要求された機能が正しく実装されているか
            - Issueで指定されたタスクが完了しているか
            - 動作に支障がないか

            **非機能要件は以下の場合のみ指摘**してください：
            - 著しくパフォーマンスを低下させる実装
            - 明確なセキュリティリスクがある実装
            - 将来的に重大な問題を引き起こす可能性のある実装

            **以下は軽微な問題として扱い、機能が動作していれば許容**：
            - コーディング規約の軽微な違反
            - 変数名やコメントの細かい指摘
            - インデントやフォーマットの問題

            ## ナレッジベースの活用

            機能要件に関連する場合のみ参照：
            - claude/claude-knowledge/issues/common-errors.md（重大なエラーパターン）
            - claude/claude-knowledge/patterns/anti-patterns.md（避けるべき実装）
            - claude/claude-knowledge/history/（過去の同様のIssueの教訓）

            ## レビュー手順

            作業ディレクトリ: claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/

            1. implementation/attempt-${{ steps.setup.outputs.iteration }}.md をレビュー
            2. 機能要件の実現を中心に評価
            3. review/review-${{ steps.setup.outputs.iteration }}.md にレビュー結果を保存
            4. dialogue/conversation.md にレビュワーとしてのコメントを追記

            review/status.json を作成/更新:
            {
              "iteration": ${{ steps.setup.outputs.iteration }},
              "status": "PASS" または "NEEDS_IMPROVEMENT",
              "issues": ["重大な問題点のみ"],
              "suggestions": ["必須の改善案のみ"]
            }
          # すべてのツールを利用可能にする
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          max_turns: ${{ steps.setup.outputs.max_turns_review || env.MAX_TURNS_REVIEW }}
        continue-on-error: true

      # レビュー結果を確認
      - name: Debug Review Results
        if: steps.setup.outputs.phase == 'REVIEW' && always()
        run: |
          echo "=== Review Debug ==="
          echo "Execution file: ${{ steps.review.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.review.outputs.conclusion }}"
          echo "Model used: ${{ steps.review.outputs.model }}"

          # 実行ファイルの存在確認
          if [ -f "${{ steps.review.outputs.execution_file }}" ]; then
            echo "✅ Execution file exists"

            # Claudeの応答を取得して表示
            CLAUDE_RESPONSE=$(jq -r '.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text' "${{ steps.review.outputs.execution_file }}" 2>/dev/null | head -c 500 || echo "")
            if [ -n "$CLAUDE_RESPONSE" ]; then
              echo "📝 Claude Response (first 500 chars):"
              echo "$CLAUDE_RESPONSE"
            else
              echo "⚠️ No Claude response found in execution file"
            fi
          else
            echo "❌ Execution file not found"
          fi

      - name: Post-Review Actions
        if: steps.setup.outputs.phase == 'REVIEW'
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          # ブランチが指定されていればチェックアウト
          if [ -n "${{ steps.setup.outputs.branch_name }}" ]; then
            git checkout ${{ steps.setup.outputs.branch_name }}
          fi

          # ターン制限エラーをチェック（レビューステップの結果を確認）
          MAX_TURNS_REACHED=false
          REVIEW_OUTCOME="${{ steps.review.outcome }}"

          # claude-code-base-actionは continue-on-error: true なので outcome を確認
          if [ "$REVIEW_OUTCOME" != "success" ]; then
            echo "⚠️ レビューフェーズが正常終了しませんでした（おそらくターン制限）"
            MAX_TURNS_REACHED=true
          fi

          # レビュー結果を確認
          STATUS_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/review/status.json"
          if [ -f "$STATUS_FILE" ]; then
            REVIEW_STATUS=$(jq -r '.status' "$STATUS_FILE")
          else
            REVIEW_STATUS="UNKNOWN"
          fi

          # Claudeの実行結果を解析
          EXEC_FILE="${{ steps.review.outputs.execution_file }}"
          CLAUDE_SUMMARY=""
          REVIEW_ISSUES=""
          REVIEW_SUGGESTIONS=""

          if [ -f "$EXEC_FILE" ]; then
            # Claudeの最後の応答を取得（最大1000文字）
            CLAUDE_SUMMARY=$(jq -r '[.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text] | last' "$EXEC_FILE" 2>/dev/null | head -c 1000 || echo "応答を取得できませんでした")

            # レビュー結果の詳細を取得
            if [ -f "$STATUS_FILE" ]; then
              # 問題点を取得
              REVIEW_ISSUES=$(jq -r '.issues[]? // empty' "$STATUS_FILE" 2>/dev/null | head -5 | sed 's/^/• /')
              if [ -z "$REVIEW_ISSUES" ]; then
                REVIEW_ISSUES="• 問題なし"
              fi

              # 改善案を取得
              REVIEW_SUGGESTIONS=$(jq -r '.suggestions[]? // empty' "$STATUS_FILE" 2>/dev/null | head -5 | sed 's/^/• /')
              if [ -z "$REVIEW_SUGGESTIONS" ]; then
                REVIEW_SUGGESTIONS="• 特になし"
              fi
            else
              REVIEW_ISSUES="• レビュー結果ファイルなし"
              REVIEW_SUGGESTIONS="• レビュー結果ファイルなし"
            fi
          else
            CLAUDE_SUMMARY="実行ファイルが見つかりません"
            REVIEW_ISSUES="• エラーのため取得できません"
            REVIEW_SUGGESTIONS="• エラーのため取得できません"
          fi

          # Slack通知送信
          if [ -n "$SLACK_WEBHOOK" ]; then
            # レビュー結果に応じた色とメッセージ
            if [ "$REVIEW_STATUS" = "PASS" ]; then
              COLOR="good"
              STATUS_MSG="✅ レビュー合格"
              NEXT_STEP="ナレッジ更新へ"
            elif [ "$REVIEW_STATUS" = "NEEDS_IMPROVEMENT" ]; then
              COLOR="warning"
              STATUS_MSG="⚠️ 改善が必要"
              NEXT_STEP="イテレーション$((${{ steps.setup.outputs.iteration }} + 1)) へ"
            else
              COLOR="danger"
              STATUS_MSG="❌ レビュー結果不明"
              NEXT_STEP="確認が必要"
            fi

            # ターン制限の場合は上書き
            if [ "$MAX_TURNS_REACHED" = "true" ]; then
              COLOR="warning"
              STATUS_MSG="⚠️ ターン制限で終了"
              NEXT_STEP="現在の実装で完了"
            fi

            # 問題点の数をカウント
            ISSUE_COUNT=$(echo "$REVIEW_ISSUES" | grep -c '^•' || echo "0")
            if [ "$ISSUE_COUNT" -eq 1 ] && echo "$REVIEW_ISSUES" | grep -q "問題なし"; then
              ISSUE_COUNT=0
            fi

            # JSONペイロードを作成
            JSON_PAYLOAD=$(jq -n \
              --arg text "📝 レビュワーBot: 実装レビュー完了" \
              --arg issue "#${{ steps.setup.outputs.issue_number }}" \
              --arg iteration "イテレーション ${{ steps.setup.outputs.iteration }}" \
              --arg status "$STATUS_MSG" \
              --arg next "$NEXT_STEP" \
              --arg issues "$REVIEW_ISSUES" \
              --arg color "$COLOR" \
              '{
                text: $text,
                attachments: [{
                  color: $color,
                  fields: [
                    {title: "Issue", value: $issue, short: true},
                    {title: "イテレーション", value: $iteration, short: true},
                    {title: "判定", value: $status, short: true},
                    {title: "次のステップ", value: $next, short: true},
                    {title: "指摘事項", value: $issues, short: false}
                  ]
                }]
              }')

            # 改善案がある場合は追加
            if [ "$REVIEW_STATUS" = "NEEDS_IMPROVEMENT" ] && [ -n "$REVIEW_SUGGESTIONS" ] && ! echo "$REVIEW_SUGGESTIONS" | grep -q "特になし"; then
              # 既存のJSONから attachments配列を取得し、改善案フィールドを追加
              JSON_PAYLOAD=$(echo "$JSON_PAYLOAD" | jq \
                --arg suggestions "$REVIEW_SUGGESTIONS" \
                '.attachments[0].fields += [{title: "改善案", value: $suggestions, short: false}]')
            fi

            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d "$JSON_PAYLOAD"
          fi

          # 変更をコミット
          git add .
          git commit -m "Review iteration ${{ steps.setup.outputs.iteration }} for issue #${{ steps.setup.outputs.issue_number }}" || true
          git push

          # ターン制限に達した場合は終了処理へ
          if [ "$MAX_TURNS_REACHED" = "true" ]; then
            echo "🔚 レビューフェーズでターン制限のため、現在の実装で作業を終了します"

            # Issueにコメント
            printf '⚠️ レビューフェーズでターン制限に達したため、現在の実装で作業を終了します\n\n' > /tmp/turn_limit_review_comment.md
            printf 'イテレーション: %s\n' "${{ steps.setup.outputs.iteration }}" >> /tmp/turn_limit_review_comment.md
            printf 'フェーズ: レビュー\n' >> /tmp/turn_limit_review_comment.md
            printf 'ブランチ: `%s`\n\n' "${{ steps.setup.outputs.branch_name }}" >> /tmp/turn_limit_review_comment.md
            printf '現在の実装内容で作業を完了とします。\n' >> /tmp/turn_limit_review_comment.md
            gh issue comment ${{ steps.setup.outputs.issue_number }} --body-file /tmp/turn_limit_review_comment.md

            # 最終処理フェーズを起動
            gh workflow run claude-orchestrator.yml \
              -f issue_number=${{ steps.setup.outputs.issue_number }} \
              -f phase=FINALIZE \
              -f iteration=${{ steps.setup.outputs.iteration }} \
              -f branch_name="${{ steps.setup.outputs.branch_name }}" \
              -f selected_model="${{ github.event.inputs.selected_model }}" \
              -f auth_type="${{ github.event.inputs.auth_type }}" \
              --ref claude-base
            exit 0
          fi

          # 次のアクションを決定
          if [ "$REVIEW_STATUS" = "PASS" ]; then
            echo "✅ レビュー合格！ナレッジ更新へ"

            # Slack通知（レビュー合格）
            if [ -n "${{ secrets.SLACK_WEBHOOK }}" ]; then
              # レビュー内容の要約を取得
              REVIEW_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/review/review-${{ steps.setup.outputs.iteration }}.md"
              REVIEW_SUMMARY=""
              
              if [ -f "$REVIEW_FILE" ]; then
                # レビューファイルからコメント部分を抽出（最大200文字）
                REVIEW_SUMMARY=$(grep -A 10 -i "コメント\|評価\|総評" "$REVIEW_FILE" | head -c 200 | tr '\n' ' ' || echo "")
                if [ -z "$REVIEW_SUMMARY" ]; then
                  # コメントが見つからない場合は最初の数行を取得
                  REVIEW_SUMMARY=$(head -n 3 "$REVIEW_FILE" | head -c 200 | tr '\n' ' ')
                fi
              else
                REVIEW_SUMMARY="レビューファイルが見つかりません"
              fi
              
              # JSONエスケープ処理
              REVIEW_SUMMARY_ESCAPED=$(echo "$REVIEW_SUMMARY" | jq -Rs . | sed 's/^"//;s/"$//')
              
              curl -X POST ${{ secrets.SLACK_WEBHOOK }} \
                -H 'Content-type: application/json' \
                -d "{
                  \"text\": \"✅ レビュー合格\",
                  \"attachments\": [{
                    \"color\": \"good\",
                    \"fields\": [
                      {\"title\": \"Issue\", \"value\": \"#${{ steps.setup.outputs.issue_number }}\", \"short\": true},
                      {\"title\": \"イテレーション\", \"value\": \"${{ steps.setup.outputs.iteration }}\", \"short\": true},
                      {\"title\": \"判定\", \"value\": \"PASS\", \"short\": true},
                      {\"title\": \"次のステップ\", \"value\": \"ナレッジ更新\", \"short\": true},
                      {\"title\": \"レビュー要約\", \"value\": \"$REVIEW_SUMMARY_ESCAPED\", \"short\": false}
                    ]
                  }]
                }"
            fi

            # 2秒待機
            echo "⏳ 次のフェーズまで2秒待機..."
            sleep 2

            # ナレッジ更新フェーズを起動
            gh workflow run claude-orchestrator.yml \
              -f issue_number=${{ steps.setup.outputs.issue_number }} \
              -f phase=UPDATE_KNOWLEDGE \
              -f iteration=${{ steps.setup.outputs.iteration }} \
              -f branch_name="${{ steps.setup.outputs.branch_name }}" \
              -f selected_model="${{ github.event.inputs.selected_model }}" \
              -f auth_type="${{ github.event.inputs.auth_type }}" \
              --ref claude-base
          else
            NEXT_ITERATION=$((${{ steps.setup.outputs.iteration }} + 1))
            ITERATIONS_LIMIT=${{ steps.setup.outputs.iterations_limit || '7' }}
            if [ $NEXT_ITERATION -le $ITERATIONS_LIMIT ]; then
              echo "🔧 改善が必要です。次のイテレーションへ (${NEXT_ITERATION}/${ITERATIONS_LIMIT})"

              # Slack通知（改善が必要）
              if [ -n "${{ secrets.SLACK_WEBHOOK }}" ]; then
                # レビュー内容の要約を取得
                REVIEW_FILE="${GITHUB_WORKSPACE}/claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/review/review-${{ steps.setup.outputs.iteration }}.md"
                REVIEW_ISSUES=""
                REVIEW_SUGGESTIONS=""
                
                if [ -f "$REVIEW_FILE" ]; then
                  # 問題点を抽出
                  REVIEW_ISSUES=$(grep -A 5 -i "問題\|課題\|指摘\|改善" "$REVIEW_FILE" | head -c 150 | tr '\n' ' ' || echo "")
                  # 提案を抽出
                  REVIEW_SUGGESTIONS=$(grep -A 5 -i "提案\|推奨\|改善案\|修正" "$REVIEW_FILE" | head -c 150 | tr '\n' ' ' || echo "")
                  
                  if [ -z "$REVIEW_ISSUES" ] && [ -z "$REVIEW_SUGGESTIONS" ]; then
                    # キーワードが見つからない場合は最初の数行を取得
                    REVIEW_ISSUES=$(head -n 5 "$REVIEW_FILE" | head -c 200 | tr '\n' ' ')
                  fi
                else
                  REVIEW_ISSUES="レビューファイルが見つかりません"
                fi
                
                # JSONエスケープ処理
                REVIEW_ISSUES_ESCAPED=$(echo "$REVIEW_ISSUES" | jq -Rs . | sed 's/^"//;s/"$//')
                REVIEW_SUGGESTIONS_ESCAPED=$(echo "$REVIEW_SUGGESTIONS" | jq -Rs . | sed 's/^"//;s/"$//')
                
                curl -X POST ${{ secrets.SLACK_WEBHOOK }} \
                  -H 'Content-type: application/json' \
                  -d "{
                    \"text\": \"🔧 改善が必要\",
                    \"attachments\": [{
                      \"color\": \"warning\",
                      \"fields\": [
                        {\"title\": \"Issue\", \"value\": \"#${{ steps.setup.outputs.issue_number }}\", \"short\": true},
                        {\"title\": \"イテレーション\", \"value\": \"${{ steps.setup.outputs.iteration }}\", \"short\": true},
                        {\"title\": \"判定\", \"value\": \"NEEDS_IMPROVEMENT\", \"short\": true},
                        {\"title\": \"次のステップ\", \"value\": \"イテレーション$NEXT_ITERATION へ\", \"short\": true},
                        {\"title\": \"指摘事項\", \"value\": \"$REVIEW_ISSUES_ESCAPED\", \"short\": false}
                      ]
                    }]
                  }"
              fi

              # 2秒待機
              echo "⏳ 次のフェーズまで2秒待機..."
              sleep 2

              # 次の実装イテレーションを起動
              gh workflow run claude-orchestrator.yml \
                -f issue_number=${{ steps.setup.outputs.issue_number }} \
                -f phase=IMPLEMENT \
                -f iteration=${NEXT_ITERATION} \
                -f branch_name="${{ steps.setup.outputs.branch_name }}" \
                -f selected_model="${{ github.event.inputs.selected_model }}" \
                -f auth_type="${{ github.event.inputs.auth_type }}" \
                --ref claude-base
            else
              echo "⚠️ 最大イテレーション数に達しました"

              # 最終処理フェーズを起動
              gh workflow run claude-orchestrator.yml \
                -f issue_number=${{ steps.setup.outputs.issue_number }} \
                -f phase=FINALIZE \
                -f iteration=${{ steps.setup.outputs.iteration }} \
                -f branch_name="${{ steps.setup.outputs.branch_name }}" \
                -f selected_model="${{ github.event.inputs.selected_model }}" \
                -f auth_type="${{ github.event.inputs.auth_type }}" \
                --ref claude-base
            fi
          fi

      # UPDATE_KNOWLEDGE Phase: ナレッジ更新
      - name: Notify Knowledge Update Start
        if: steps.setup.outputs.phase == 'UPDATE_KNOWLEDGE' && env.SLACK_WEBHOOK != ''
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          if [ -n "$SLACK_WEBHOOK" ]; then
            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d '{
                "text": "📚 ナレッジキュレーター: 知見の整理開始",
                "attachments": [{
                  "color": "#607D8B",
                  "fields": [
                    {"title": "Issue", "value": "#${{ steps.setup.outputs.issue_number }}", "short": true},
                    {"title": "フェーズ", "value": "ナレッジ更新", "short": true},
                    {"title": "役割", "value": "ナレッジキュレーター", "short": true},
                    {"title": "作業", "value": "実装から得られた知見の記録", "short": false}
                  ]
                }]
              }'
          fi

      - name: Checkout Working Branch (Update Knowledge)
        if: steps.setup.outputs.phase == 'UPDATE_KNOWLEDGE' && steps.setup.outputs.branch_name != ''
        run: |
          git fetch origin
          git checkout ${{ steps.setup.outputs.branch_name }}
          git pull origin ${{ steps.setup.outputs.branch_name }}

      - name: Update Knowledge Base
        if: steps.setup.outputs.phase == 'UPDATE_KNOWLEDGE' && steps.setup.outputs.auth_type == 'OAUTH'
        id: update-knowledge
        uses: grll/claude-code-base-action@beta
        timeout-minutes: 60  # 1時間のタイムアウト（ナレッジ更新は比較的短時間）
        env:
          GH_TOKEN: ${{ github.token }}
          MCP_GITHUB_TOKEN: ${{ github.token }}
          NODE_ENV: 'production'
        with:
          # OAuth認証設定（必須）
          use_oauth: 'true'
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}

          # モデル設定
          model: ${{ github.event.inputs.selected_model || 'claude-sonnet-4-20250514' }}

          # プロンプト
          prompt: |
            あなたはナレッジキュレーターです。
            今回のIssue #${{ steps.setup.outputs.issue_number }} の実装から得られた知見を整理・記録します。

            ## 重要な前提情報
            - 現在の作業ディレクトリは GitHub Actions のワークスペースです
            - 最初に必ず pwd コマンドで現在地を確認してください

            ## 作業内容の確認

            作業ディレクトリ: claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/
            1. 全体の流れを把握:
               - dialogue/conversation.md（開発とレビューの対話記録）
               - task-list.md（実施したタスク）
            
            2. 実装の詳細を確認:
               - implementation/（各イテレーションの実装内容）
               - review/（各レビュー結果とstatus.json）
            
            3. 特に注目すべきポイント:
               - 開発者が苦労した箇所や工夫した実装
               - レビューで指摘された重要な問題と解決方法
               - 今後同様のタスクで参考になる実装パターン

            ## ナレッジベースの更新

            ナレッジディレクトリ: claude/claude-knowledge/

            1. **Issue履歴の記録**（必須）
               - history/issue-${{ steps.setup.outputs.issue_number }}-summary.md を作成
               - 以下の構成で記録:
                 - 概要: Issueの内容と最終的な実装結果
                 - 技術的な学び: 新しく学んだ技術やパターン
                 - 実装上の注意点: つまずいた箇所と解決方法
                 - 今後への教訓: 同様のタスクでの推奨アプローチ

            2. **パターンの更新**（該当する場合のみ）
               - patterns/successful-patterns.md に成功パターンを追加
                 - 開発者が効果的に実装したパターン
                 - レビューで評価された実装方法
               - patterns/anti-patterns.md に避けるべきパターンを追加
                 - レビューで指摘された問題のあるパターン
                 - 実装中に判明した非効率な方法

            3. **既知の問題の更新**（該当する場合のみ）
               - issues/common-errors.md に新しいエラーパターンを追加
               - issues/solutions.md に解決策を追加

            ## 更新のガイドライン
            - 開発者とレビュワーの両方の視点から学びを抽出
            - 具体的で実用的な内容に焦点を当てる
            - 既存ファイルへの追加は、重複を避けて簡潔に記述
            - 今後の開発で実際に役立つ形で記録する
          # すべてのツールを利用可能にする
          allowed_tools: 'Read,Write,Edit,MultiEdit,Bash,Glob,Grep,LS,Task,NotebookRead,NotebookEdit,WebFetch,TodoRead,TodoWrite,WebSearch'
          max_turns: ${{ env.MAX_TURNS_KNOWLEDGE }}

      # ナレッジ更新の結果を確認
      - name: Debug Knowledge Update Results
        if: steps.setup.outputs.phase == 'UPDATE_KNOWLEDGE' && always()
        run: |
          echo "=== Knowledge Update Debug ==="
          echo "Execution file: ${{ steps.update-knowledge.outputs.execution_file }}"
          echo "Conclusion: ${{ steps.update-knowledge.outputs.conclusion }}"
          echo "Model used: ${{ steps.update-knowledge.outputs.model }}"

          # 実行ファイルの存在確認
          if [ -f "${{ steps.update-knowledge.outputs.execution_file }}" ]; then
            echo "✅ Execution file exists"

            # Claudeの応答を取得して表示
            CLAUDE_RESPONSE=$(jq -r '.[] | select(.type == "assistant") | .message.content[] | select(.type == "text") | .text' "${{ steps.update-knowledge.outputs.execution_file }}" 2>/dev/null | head -c 500 || echo "")
            if [ -n "$CLAUDE_RESPONSE" ]; then
              echo "📝 Claude Response (first 500 chars):"
              echo "$CLAUDE_RESPONSE"
            else
              echo "⚠️ No Claude response found in execution file"
            fi
          else
            echo "❌ Execution file not found"
          fi

      - name: Post-Update-Knowledge Actions
        if: steps.setup.outputs.phase == 'UPDATE_KNOWLEDGE'
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          # 変更をコミット
          git add .
          git commit -m "Update knowledge base from issue #${{ steps.setup.outputs.issue_number }}" || true
          git push

          # 2秒待機
          echo "⏳ 次のフェーズまで2秒待機..."
          sleep 2

          # 最終処理フェーズを起動
          gh workflow run claude-orchestrator.yml \
            -f issue_number=${{ steps.setup.outputs.issue_number }} \
            -f phase=FINALIZE \
            -f iteration=${{ steps.setup.outputs.iteration }} \
            -f branch_name="${{ steps.setup.outputs.branch_name }}" \
            -f selected_model="${{ github.event.inputs.selected_model }}" \
            -f auth_type="${{ github.event.inputs.auth_type }}" \
            --ref develop

      # FINALIZE Phase: 最終処理
      - name: Notify Finalize Start
        if: steps.setup.outputs.phase == 'FINALIZE' && env.SLACK_WEBHOOK != ''
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        run: |
          if [ -n "$SLACK_WEBHOOK" ]; then
            curl -X POST $SLACK_WEBHOOK \
              -H 'Content-type: application/json' \
              -d '{
                "text": "🏁 最終処理: 作業完了",
                "attachments": [{
                  "color": "#00BCD4",
                  "fields": [
                    {"title": "Issue", "value": "#${{ steps.setup.outputs.issue_number }}", "short": true},
                    {"title": "総イテレーション", "value": "${{ steps.setup.outputs.iteration }}", "short": true},
                    {"title": "ブランチ", "value": "${{ steps.setup.outputs.branch_name }}", "short": false}
                  ]
                }]
              }'
          fi

      - name: Finalize
        if: steps.setup.outputs.phase == 'FINALIZE'
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          # ブランチが指定されていればチェックアウト
          if [ -n "${{ steps.setup.outputs.branch_name }}" ]; then
            git checkout ${{ steps.setup.outputs.branch_name }}
          fi

          # 最終状態を更新
          STATE_FILE="claude/claude-work/issue-${{ steps.setup.outputs.issue_number }}/state.json"
          jq '.phase = "COMPLETED" | .completed_at = "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"' \
            "$STATE_FILE" > tmp.json && mv tmp.json "$STATE_FILE"

          git add .
          git commit -m "Finalize sequential dialogue for issue #${{ steps.setup.outputs.issue_number }}" || true
          git push

          # Issueにコメント
          ITERATION="${{ steps.setup.outputs.iteration }}"
          BRANCH="${{ steps.setup.outputs.branch_name }}"

          printf '✅ 順次対話型実装が完了しました\n\n' > /tmp/final_comment.md
          printf '総イテレーション数: %s\n' "$ITERATION" >> /tmp/final_comment.md
          printf 'ブランチ: `%s`\n\n' "$BRANCH" >> /tmp/final_comment.md
          printf '作業内容の確認:\n' >> /tmp/final_comment.md
          printf '```bash\n' >> /tmp/final_comment.md
          printf 'git fetch origin\n' >> /tmp/final_comment.md
          printf 'git checkout %s\n' "$BRANCH" >> /tmp/final_comment.md
          printf '```\n' >> /tmp/final_comment.md
          gh issue comment ${{ steps.setup.outputs.issue_number }} --body-file /tmp/final_comment.md

          # ラベルを更新
          gh issue edit ${{ steps.setup.outputs.issue_number }} \
            --remove-label "claude-processing" || true
          gh issue edit ${{ steps.setup.outputs.issue_number }} \
            --add-label "claude-done"
