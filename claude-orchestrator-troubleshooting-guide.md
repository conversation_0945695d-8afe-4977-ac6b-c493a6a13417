# Claude Orchestrator ワークフロー修正手順書

## 概要
GitHub Actions ワークフロー「Claude Orchestrator」のエラーを修正し、完全に動作するまで対応を続ける手順書です。

## 作業サイクル（ワークフローが成功するまで繰り返す）

### 1. ワークフローの実行状況確認
```bash
# 最新のワークフロー実行を確認
gh run list --limit 5

# 特定のワークフローのみ表示
gh run list --workflow="Claude Orchestrator Main" --limit 3
```

### 2. エラーの詳細確認
```bash
# 失敗した実行の詳細を確認
gh run view <RUN_ID>

# より詳細なログが必要な場合
gh run view <RUN_ID> --log-failed

# API経由での詳細取得（ログが表示されない場合）
gh api repos/dkr-system/wordpress-appmart/actions/runs/<RUN_ID>/jobs
```

### 3. エラーの原因特定と修正

#### 主要な修正対象ファイル
- `.github/workflows/claude-phase-initialize.yml` - 初期化フェーズ
- `.github/scripts/setup-workflow-parameters.sh` - パラメータ設定スクリプト
- `.github/workflows/claude-orchestrator-main.yml` - メインワークフロー
- `.github/ISSUE_TEMPLATE/claude-orchestrator-task.yml` - issueテンプレート

#### エラー箇所の特定方法
1. エラーメッセージの行番号を確認
2. 該当ファイルの該当行を調査
3. 環境変数の未設定、文字エンコーディング、スクリプトエラーなどを確認

### 4. 修正のコミット&プッシュ（developブランチ）
```bash
# 現在のブランチを確認
git branch --show-current

# developブランチであることを確認してからコミット
git add <修正したファイル>
git commit -m "fix: <修正内容の説明>"
git push
```

### 5. sync-to-claudeの完了待機
```bash
# 15-20秒待機後、完了を確認
sleep 20 && gh run list --workflow="sync-to-claude.yml" --limit 1

# 実行中の場合は完了まで待つ
watch -n 5 'gh run list --workflow="sync-to-claude.yml" --limit 1'
```

### 6. issueへのメンション
```bash
# issue #10に@claude-taskでメンション
gh issue comment 10 --body "@claude-task <修正内容の説明>。再実行をお願いします。"
```

### 7. 手順1に戻る

## これまでに判明した問題と解決策

### 1. workflow_dispatchでのissue_number取得エラー
**症状**: `accepts 1 arg(s), received 0` エラー（Get Issue Informationステップ）

**原因**: `check-trigger.sh`でworkflow_dispatch時に`INPUT_ISSUE_NUMBER`を参照していたが、実際は`ISSUE_NUMBER`として渡されていた

**解決策**:
```bash
# check-trigger.sh の修正
# 変更前
echo "issue_number=$INPUT_ISSUE_NUMBER" >> "$GITHUB_OUTPUT"
# 変更後
echo "issue_number=$ISSUE_NUMBER" >> "$GITHUB_OUTPUT"
```

### 2. grepコマンドのpipefailエラー
**症状**: `Error at line 42: max_turns_env=$(echo "$ISSUE_BODY" | grep -o "環境分析フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1)`

**原因**: `set -euo pipefail`が設定されている状態で、grepが一致しない場合にエラーコード1を返すため

**解決策**:
```bash
# 各grepコマンドに || true を追加
max_turns_env=$(echo "$ISSUE_BODY" | grep -o "環境分析フェーズ 最大ターン数: [0-9]\+" | grep -o "[0-9]\+" | head -1 || true)
```

### 3. initializeワークフローのissue_number出力不足
**症状**: 後続のフェーズでissue_numberが参照できない

**原因**: `claude-phase-initialize.yml`のoutputsセクションにissue_numberが定義されていなかった

**解決策**:
```yaml
outputs:
  issue_number:
    value: ${{ inputs.issue_number }}
  branch_name:
    value: ${{ jobs.initialize.outputs.branch_name }}
  # 他のoutputs...
```

### 4. finalizeフェーズでのPR作成エラー
**症状**: Create Pull Requestステップが失敗（Exit code 1）

**原因**: PR本文ファイル（/tmp/pr-body.md）が作成されていなかった

**解決策**: create-pr-summary.shに以下を追加
```bash
# PR本文をファイルに保存
echo "$PR_BODY" > /tmp/pr-body.md
```

### 5. analyze-issueフェーズでのタスクリスト作成失敗
**症状**: `Found 0` タスク、`Invalid format '0'` エラー

**原因**: 
1. Claudeがタスクリストを作成しなかった
2. MCPサーバーがGitHub Actions環境で利用できていない可能性

**解決策**:
1. analyze-issueプロンプトに明示的な指示を追加
   - 「必ずTodoWriteツールを使用して、最低でも5個以上のタスクを作成」
   - 必須タスク例を明記
2. verify-task-list.shにエラーハンドリングを追加
   - タスクが0個の場合はエラー終了
   - デバッグ情報の出力強化
```bash
gh pr create --repo dkr-system/wordpress-appmart \
  --title "feat: <タイトル> #<issue番号>" \
  --body "<PR本文>" \
  --base develop \
  --head <ブランチ名>
```

### 6. ISSUE_TITLE環境変数の未設定
**症状**: `ISSUE_TITLE: Issue title is required` エラー

**原因**: ワークフローでissue情報を取得していなかった

**解決策**: 
```yaml
- name: Get Issue Information
  id: issue
  env:
    GH_TOKEN: ${{ secrets.GH_TOKEN_WORKFLOW }}
  run: |
    ISSUE_DATA=$(gh issue view ${{ inputs.issue_number }} --json title,body)
    ISSUE_TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
    ISSUE_BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
    
    # マルチライン対応
    echo "ISSUE_TITLE<<EOF" >> $GITHUB_ENV
    echo "$ISSUE_TITLE" >> $GITHUB_ENV
    echo "EOF" >> $GITHUB_ENV
    
    echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
    echo "$ISSUE_BODY" >> $GITHUB_ENV
    echo "EOF" >> $GITHUB_ENV
```

### 2. Claude Sonnet 4モデルの未対応
**症状**: モデル選択で「claude-sonnet-4-20250514」が認識されない

**原因**: setup-workflow-parameters.shにこのモデルの処理がなかった

**解決策**: スクリプトにモデル判定を追加
```bash
elif echo "$ISSUE_BODY" | grep -E -q "claude-sonnet-4-20250514"; then
    selectedModel='claude-sonnet-4-20250514'
    displayModelName='Claude Sonnet 4'
    echo "::notice::Selected model: Claude Sonnet 4"
```

### 3. マルチバイト文字の処理問題
**症状**: `Invalid format '標準的なタスク（デフォルト）'` エラー

**原因**: 日本語を含む環境変数の処理に問題があった

**解決策**: 
- 環境変数のマルチライン対応（heredoc形式）
- grep での正規表現オプション追加（`-E`）

### 4. GITHUB_OUTPUT関連のエラー（調査中）
**症状**: `Process completed with exit code 1` at line 62-63

**可能性のある原因**:
- `$GITHUB_OUTPUT` 環境変数が設定されていない
- ファイルへの書き込み権限の問題
- 変数の値に問題がある

**デバッグ方法**:
```bash
# エラーハンドリングの追加
trap 'echo "Error at line $LINENO: $BASH_COMMAND"' ERR

# GITHUB_OUTPUT の存在確認
if [ -z "${GITHUB_OUTPUT:-}" ]; then
  echo "::error::GITHUB_OUTPUT environment variable is not set"
  exit 1
fi
```

## 重要な注意事項

1. **ワークフローが完全に成功するまで作業を継続する**
   - すべてのジョブが ✓（成功）になるまで
   - エラーや警告が表示されなくなるまで

2. **根本原因の解決を優先**
   - エラーメッセージを正確に読み取る
   - 場当たり的な修正は避ける
   - 問題の本質を理解してから修正する

3. **作業の順序を守る**
   - 必ずsync-to-claudeの完了を待つ
   - claude-codeブランチへの同期が完了してからメンション

4. **デバッグ情報の活用**
   - `::notice::`、`::debug::`、`::error::` を活用
   - スクリプトには `-x` オプションやトラップを設定

## 参考情報

### 利用可能なリソース
- `ref_claude-orchestrator.yml` - 別プロジェクトで動作している参考ワークフロー
- ただし、このプロジェクトのワークフロー分割は意図的なもの

### 関連ファイルの場所
```
.github/
├── workflows/
│   ├── claude-orchestrator-main.yml
│   ├── claude-phase-*.yml
│   └── sync-to-claude.yml
├── scripts/
│   └── setup-workflow-parameters.sh
└── ISSUE_TEMPLATE/
    └── claude-orchestrator-task.yml
```

### issue情報の確認
```bash
# issue #10 の詳細を確認
gh issue view 10 --json title,body,labels
```

## ワークフロー実行のベストプラクティス

### 1. workflow_dispatchでの実行方法
```bash
# 正しい実行方法
gh workflow run "Claude Orchestrator Main" -f issue_number=<番号>

# 実行状態の確認
gh run list --workflow="Claude Orchestrator Main" --limit=1

# 詳細な進行状況の確認
gh run view <RUN_ID>
```

### 2. 実行時間の目安
- **初期化フェーズ**: 10-20秒
- **環境分析フェーズ**: 5-10分（初回のみ）
- **Issue分析フェーズ**: 2-5分
- **実装フェーズ**: 5-15分（タスク数による）
- **レビューフェーズ**: 3-8分
- **ナレッジ更新フェーズ**: 3-5分
- **最終化フェーズ**: 1-2分

### 3. 監視のポイント
- 各フェーズの開始を確認
- エラーが発生した場合は即座に対応
- 長時間（15分以上）同じステップで停止している場合は要確認

## 成功の確認基準

1. ワークフローのすべてのジョブが成功（緑色のチェックマーク）
2. issueに以下のようなコメントが投稿される：
   - 「Claude Orchestrator 開始」
   - 各フェーズの進行状況
   - 最終的な完了通知
3. 指定されたブランチが作成され、必要なファイルが初期化される

## 手動介入が必要な場合の対処法

### 1. ワークフローが部分的に失敗した場合

#### PR作成の手動実行
```bash
# 作成されたブランチを確認
git fetch --all
git branch -r | grep claude-task/<issue番号>

# PRを手動で作成
gh pr create --repo <リポジトリ名> \
  --title "feat: <タイトル> #<issue番号>" \
  --body "## 概要
Claude Orchestratorによる自動実装

### 実装内容
<実装内容の要約>

### 関連Issue
- #<issue番号>

🤖 Generated with Claude Orchestrator" \
  --base develop \
  --head <ブランチ名>
```

#### Issue の手動更新
```bash
# コメントを追加
gh issue comment <issue番号> --body "## ✅ 作業完了

Claude Orchestratorによる自動実装が完了しました。

**プルリクエスト**: #<PR番号>
**ブランチ**: \`<ブランチ名>\`

ワークフローの一部でエラーが発生しましたが、実装は正常に完了し、PRを作成しました。"

# ラベルを更新
gh issue edit <issue番号> --add-label "claude-done" --remove-label "claude-processing"
```

### 2. 実装内容の確認方法
```bash
# ブランチの差分を確認
git checkout <ブランチ名>
git diff --stat develop..HEAD

# 変更されたファイルの一覧
git diff --name-only develop..HEAD

# 特定のファイルの差分を確認
git diff develop..HEAD -- <ファイルパス>
```

### 3. ワークフローの再実行が必要な場合
- 環境分析フェーズが完了している場合は、スキップされる
- タスクリストが作成済みの場合は、続きから実行される
- 完全に最初からやり直したい場合は、issueのラベルとコメントをクリアする

## トラブルシューティングのヒント

1. **ログが表示されない場合**
   - GitHub APIを直接使用する
   - ブラウザでActionsページを確認

2. **環境変数の問題**
   - `env` コマンドで確認
   - `echo "VAR=$VAR"` でデバッグ出力

3. **権限エラー**
   - `GH_TOKEN_WORKFLOW` が正しく設定されているか確認
   - リポジトリの設定でActionsの権限を確認

4. **ワークフローのタイムアウト**
   - 各フェーズには適切なタイムアウトが設定されている
   - 長時間実行されている場合は、Claudeの応答待ちの可能性

---

この手順書は随時更新してください。新しい問題と解決策が見つかった場合は、このドキュメントに追記することで、将来の作業がスムーズになります。