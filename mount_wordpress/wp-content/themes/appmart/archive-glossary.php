<?php
/*
Template Name: 用語集アーカイブ
Template Post Type: post,page

*/
?>

<?php get_header(); ?>

<?php
// glossary投稿を取得
$args = [
  'post_type' => 'glossary',
  'posts_per_page' => -1,
  'orderby' => 'title',
  'order' => 'ASC'
];
$glossary_posts = get_posts($args);

// 50音順で並び替え
// $sorted_posts = [];
// foreach ($glossary_posts as $post) {
//   $yomi = get_field('yomi', $post->ID);
//     if (!$yomi) {
//       $yomi = $post->post_title;
//     }
//   $sorted_posts[$yomi] = $post;
// }
// ksort($sorted_posts, SORT_STRING);
// アルファベットとそれ以外で分離してソート
$alpha_posts = [];
$japanese_posts = [];

foreach ($glossary_posts as $post) {
  $first_char = mb_substr($post->post_title, 0, 1);

  // アルファベットかどうかを判定
    if (preg_match('/^[a-zA-Z]/', $first_char)) {
      // アルファベットの場合はタイトルをキーにして格納
      $alpha_posts[strtolower($post->post_title)] = $post;
    } else {
      // それ以外の場合はyomiフィールドを取得
      $yomi = get_field('yomi', $post->ID);
        if (!$yomi) {
          $yomi = $post->post_title;
        }
      $japanese_posts[$yomi] = $post;
    }
}

// それぞれソート
ksort($japanese_posts, SORT_STRING);
ksort($alpha_posts, SORT_STRING);

// 日本語→アルファベットの順でマージ
$sorted_posts = $japanese_posts + $alpha_posts;

?>

<!-- Start page__header -->
<div class="page__header"
  style="background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/photo/appmart-blog.jpg);">
  <div class="container">
    <div class="page__header__title">
      <span>GLOSSARY</span>
      <h1 class="page__header__main">用語集</h1>
    </div>
  </div>
  <div class="page__scroll"></div>
</div>
<!-- End page__header -->

 <div class="breadcrumb-inner">
    <div class="container">
        <!-- Start breadcrumb -->
                    <div class="breadcrumb-area">
            <!-- Breadcrumb NavXT 6.6.0 -->
<span property="itemListElement" typeof="ListItem"><a property="item" typeof="WebPage" title="Go to Appmart株式会社（アップマート）." href="//localhost:3000" class="home"><span property="name">TOP</span></a><meta property="position" content="1"></span>    &gt;    <span property="itemListElement" typeof="ListItem"><a property="item" typeof="WebPage" title="Go to 用語集." href="//localhost:3000/glossary/" class="post post-glossary-archive"><span property="name">用語集</span></a><meta property="position" content="2"></span></div>
                <!-- End breadcrumb -->
    </div>
</div>

<!-- Start main -->
<main class="content__main">
  <section class="glossary-archive">

    <h2 class="glossary-title">
      マーケティング用語集
    </h2>
    <?php if ($sorted_posts) : ?>
    <div class="glossary-list">
      <?php foreach ($japanese_posts as $post) : ?>
      <?php setup_postdata($post); ?>
      <div class="glossary-list__item">
        <a href="<?php the_permalink(); ?>">
          <div class="item-title">
            <?php the_title(); ?>
          </div>
        </a>
      </div>
      <?php endforeach; ?>
    </div>
    <div class="glossary-list">
      <?php foreach ($alpha_posts as $post) : ?>
      <?php setup_postdata($post); ?>
      <div class="glossary-list__item">
        <a href="<?php the_permalink(); ?>">
          <div class="item-title">
            <?php the_title(); ?>
          </div>
        </a>
      </div>
      <?php endforeach; ?>
    </div>
    <?php endif; ?>
    <?php wp_reset_postdata(); ?>
  </section>

</main>
<!-- End main -->

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>
