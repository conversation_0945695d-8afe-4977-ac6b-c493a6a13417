<?php get_header(); ?>

    <main class="content__main">

    	<?php echo get_template_part('template-parts/breadcrumb'); ?>

        <section class="section">

            <div class="section__search">

                <div class="container">
                    <div class="primary">

                        <?php if ( empty( get_search_query()) ) : ?>
                            <div class="section_title">
                                <h2>検索キーワードが未入力です。</h2>
                            </div>
                            <div class="search_box_area">
                                <?php get_search_form(); ?>
                            </div>
                        <?php else : ?>
    
                        <?php
                            global $wp_query;
                            $total_results = $wp_query->found_posts;
                            $search_query = get_search_query();
                        ?>
                        <div class="section_title">
                            <h2><?php echo $search_query; ?>の検索結果<span class="result_num">（<?php echo $total_results; ?>件）</span></h2>
                        </div>
    
                        <div class="search_results">                       
                            <ul>
                                <?php
                                    if( $total_results >0 ):
                                    if(have_posts()): ?>
    
                                <ul class="entry__list">
                                <?php while(have_posts()): the_post();?>
                                
                                    <?php get_template_part('template-parts/blogpost_loop'); ?>

                    
                                <?php endwhile; ?>

                                <?php endif; ?>

                                <div class="pagination">
                                        <?php
                                            the_posts_pagination(array(
                                                'prev_text' => '<',
                                                'next_text' => '>',
                                                'mid_size' => '0'
                                                )
                                            );
                                        ?>
                                    </div>

                                <?php else : ?>
                                
                                <p style="margin: 20px 0;"><?php echo $search_query; ?> に一致する記事は見つかりませんでした。</p>
                                <div class="search_box_area">
                                    <?php get_search_form(); ?>
                                </div>
                                <div class="btn">
                                    <a href="<?php echo esc_url( home_url('/blog') ); ?>" class="btn__black">
                                        <span>記事一覧へ戻る</span>
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg" alt="→">
                                    </a>
                                </div>

                                <?php endif; ?>
                                
                            </ul>
                        </div>
                        <?php endif; ?>

                    </div>

                    <?php  get_template_part('template-parts/sidebar'); ?>

                </div>
            </div>

        </section>
    </main>

<?php get_template_part('template-parts/section_contact'); ?>
<?php get_footer(); ?>