<?php

/**
 * Template part for CTA button section
 *
 * @package AppMart
 * @subpackage OwnedMedia
 *
 * 使用例:
 *
 * // デフォルト設定で呼び出し
 * get_template_part('template-parts/owned-media-cta');
 *
 * // セクションIDを指定
 * get_template_part('template-parts/owned-media-cta', null, array(
 *     'section_id' => 'owned-media-cta-2'
 * ));
 *
 * // 背景色とボタン内容をカスタマイズ
 * get_template_part('template-parts/owned-media-cta', null, array(
 *     'background_color' => 'background-color: #f8f9fa;',
 *     'title_accent' => 'お問い合わせ',
 *     'button1_text' => 'サービス資料をダウンロード',
 *     'button1_url' => '/download',
 *     'button2_text' => '無料相談を申し込む',
 *     'button2_url' => '/contact',
 *     'section_id' => 'contact-cta'
 * ));
 *
 * Advanced Custom Fields対応:
 * - url_service: サービス資料ボタンのリンク先URL
 * - url_owned_media: オウンドメディア詳細ボタンのリンク先URL
 * これらのフィールドが設定されている場合、自動的にボタンのリンク先に適用されます。
 */

if (!defined('ABSPATH')) {
    exit;
}

// Advanced Custom Fieldsからリンク先URLを取得（ACFプラグインが有効な場合のみ）
$acf_service_url = '';
$acf_owned_media_url = '';

if (function_exists('get_field')) {
    $acf_service_url = get_field('url_service');
    $acf_owned_media_url = get_field('url_owned_media');
}

// デフォルト値の設定
$defaults = array(
    'background_color' => '', // 背景色（CSSクラス名またはスタイル）
    'title_prefix' => '\ まずは',
    'title_accent' => 'ご相談',
    'title_suffix' => 'から /',
    'button1_text' => 'オウンドメディア運用代行の<br>サービス資料を見る',
    'button1_url' => !empty($acf_service_url) ? $acf_service_url : '#',
    'button1_style' => 'outline', // outline または filled
    'button2_text' => 'オウンドメディア運用代行の<br>詳細を聞く',
    'button2_url' => !empty($acf_owned_media_url) ? $acf_owned_media_url : '#',
    'button2_style' => 'filled', // outline または filled
    'section_id' => 'owned-media-cta',
    'section_class' => 'owned-media-cta'
);

// 渡されたパラメータとデフォルト値をマージ
$params = wp_parse_args($args ?? array(), $defaults);

// パラメータでURLが明示的に指定されていない場合のみACFフィールドを適用
if (!isset($args['button1_url']) && !empty($acf_service_url)) {
    $params['button1_url'] = $acf_service_url;
}
if (!isset($args['button2_url']) && !empty($acf_owned_media_url)) {
    $params['button2_url'] = $acf_owned_media_url;
}

// 背景色の処理
$background_style = '';
if (!empty($params['background_color'])) {
    // CSSクラス名の場合とスタイルの場合を判定
    if (strpos($params['background_color'], ':') !== false) {
        // スタイル形式の場合（例: "background-color: #f0f0f0;"）
        $background_style = ' style="' . esc_attr($params['background_color']) . '"';
    } else {
        // CSSクラス名の場合
        $params['section_class'] .= ' ' . $params['background_color'];
    }
}
?>

<!-- CTAボタンセクション -->
<section class="<?php echo esc_attr($params['section_class']); ?>" id="<?php echo esc_attr($params['section_id']); ?>" <?php echo $background_style; ?>>
    <div class="owned-media-cta__container">
        <h2 class="owned-media-cta__title">
            <span class="owned-media-cta__title-prefix"><?php echo esc_html($params['title_prefix']); ?></span>
            <span class="owned-media-cta__title-accent"><?php echo esc_html($params['title_accent']); ?></span>
            <span class="owned-media-cta__title-suffix"><?php echo esc_html($params['title_suffix']); ?></span>
        </h2>
        <div class="owned-media-cta__buttons">
            <a href="<?php echo esc_url($params['button1_url']); ?>" class="owned-media-cta__button owned-media-cta__button--<?php echo esc_attr($params['button1_style']); ?>">
                <span class="owned-media-cta__button-text"><?php echo $params['button1_text']; ?></span>
            </a>
            <a href="<?php echo esc_url($params['button2_url']); ?>" class="owned-media-cta__button owned-media-cta__button--<?php echo esc_attr($params['button2_style']); ?>">
                <span class="owned-media-cta__button-text"><?php echo $params['button2_text']; ?></span>
            </a>
        </div>
    </div>
</section>