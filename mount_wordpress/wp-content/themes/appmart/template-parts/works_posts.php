                    <ul class="works__list">
						<?php 
						$args = array (
							'posts_per_page' => -1,
							'post_type' => 'works'
						);
						$works_post = get_posts($args);
						?>
						<?php if( $works_post) : ?>

						<?php foreach ( $works_post as $post ) : setup_postdata($post);  ?>
                        
                        <?php 
						$works_url = get_field('works-url');
						$works_media = get_field('works-media');
						?>
                        
						<li class="works__item">
							<a href="<?php echo esc_url($works_url); ?>" target="_blank" rel="noopener noreferrer">
								<div class="works__item__eyecatch">
									<div class="img" style="background-image: url(<?php the_post_thumbnail_url('full'); ?>);"></div>
								</div>
								<h4><?php echo $works_media; ?></h4>
								<p><?php the_title(); ?></p>
							</a>
						</li>

						<?php endforeach;
						wp_reset_postdata();
						?>
						<?php endif; ?>

                    </ul>