<?php 
							global $post;
							$term = array_shift(get_the_terms($post->ID, 'blog-category'));

							$args = array (
								'numberposts' => 6,
								'post_type' => 'blog',
								'taxonomy' => 'blog-category',
								'term' => $term->slug,
								'post__not_in' => array($post->ID),
								'orderby' => 'rand'
							);

							$related_posts = get_posts($args);
							if ( $related_posts ) :
							?>

							<div class="single__blog__related-posts">
								<h2>関連記事</h2>
								<ul class="single__blog__related-posts__list">

									<?php 
									foreach ($related_posts as $post) : setup_postdata($post);
									?>

									<li>
										<a href="<?php the_permalink(); ?>">

											<div class="left">
												<div class="related-posts__eyecatch">
													<div class="img" style="background-image: url(<?php the_post_thumbnail_url('full'); ?>);"></div>
												</div>
											</div>

											<div class="right">
												<h3>
													<?php the_title(); ?>
												</h3>
												<time datetime="<?php the_time( 'Y-m-d' ); ?>"><?php the_time( 'Y.m.d' ); ?></time>
											</div>
											<ul class="category_label sp">
												<?php
												$terms = get_the_terms($post->ID, 'blog-tag');
												if ( $terms ) {
													foreach ( $terms as $term ) {
														echo '<li>'.$term->name.'</li>';
													}
												}
												?>
											</ul>
										</a>
									</li>

									<?php endforeach; ?>

								</ul>
							</div>

							<?php 
							endif; 
							wp_reset_postdata(); 
							?>
