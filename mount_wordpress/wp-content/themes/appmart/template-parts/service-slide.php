<?php
global $image_extension;
$slide_images_dir = get_template_directory() . '/assets/images/service/slide';
$slide_images = get_slide_images($slide_images_dir);
$supportsWebp = strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false;

$top_images = array_filter($slide_images, function ($image_url) use ($image_extension) {
  return is_slide_image($image_url, '/top_\d+_.+\\' . $image_extension . '$/i');
});
$bottom_images = array_filter($slide_images, function ($image_url) use ($image_extension) {
  return is_slide_image($image_url, '/bottom_\d+_.+\\' . $image_extension . '$/i');
});
?>

<div class="slideshow">
  <div class="row row-top">
    <?php foreach (array_merge($top_images, $top_images, $top_images) as $image_url) : ?>
      <div class="slide__top">
        <img loading="lazy" src="<?php echo esc_url(str_replace(get_template_directory(), get_template_directory_uri(), $image_url)); ?>" alt="Top Image">
      </div>
    <?php endforeach; ?>
  </div>
  <div class="row row-bottom">
    <?php foreach (array_merge($bottom_images, $bottom_images, $bottom_images) as $image_url) : ?>
      <div class="slide__bottom">
        <img loading="lazy" src="<?php echo esc_url(str_replace(get_template_directory(), get_template_directory_uri(), $image_url)); ?>" alt="Bottom Image">
      </div>
    <?php endforeach; ?>
  </div>
</div>

<script>
  const rowTop = document.querySelector('.row-top');
  const rowBottom = document.querySelector('.row-bottom');

  const slideCountTop = rowTop.children.length / 3;
  const slideCountBottom = rowBottom.children.length / 3;

  const slideWidthTop = rowTop.children[0].offsetWidth + parseInt(window.getComputedStyle(rowTop.children[0]).marginRight);
  const slideWidthBottom = rowBottom.children[0].offsetWidth + parseInt(window.getComputedStyle(rowBottom.children[0]).marginRight);

  const slideWidthTotalTop = slideCountTop * slideWidthTop;
  const slideWidthTotalBottom = slideCountBottom * slideWidthBottom;

  let positionTop = 0;
  let positionBottom = 0;

  // スライド速度
  const speedTop = 0.5;
  const speedBottom = 0.75;

  // スライド実行(top)
  function moveSlideshowTop() {
    positionTop -= speedTop;

    if (positionTop <= -slideWidthTotalTop) {
      positionTop = 0;
    }

    rowTop.style.transform = `translateX(${positionTop}px)`;

    requestAnimationFrame(moveSlideshowTop);
  }

  // スライド実行(bottom)
  function moveSlideshowBottom() {
    positionBottom -= speedBottom;

    if (positionBottom <= -slideWidthTotalBottom) {
      positionBottom = 0;
    }

    rowBottom.style.transform = `translateX(${positionBottom}px)`;

    requestAnimationFrame(moveSlideshowBottom);
  }

  moveSlideshowTop();
  moveSlideshowBottom();
</script>