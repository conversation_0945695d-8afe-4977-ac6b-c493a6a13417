<?php
/*
Template Name: ブログ詳細
Template Post Type: post,page

*/
?>

<?php get_header(); ?>

	<!-- Start main -->
    <main class="page__main">

		<section class="section"><!-- single blog -->
			<div class="single__blog">
				<div class="container">
					<div class="single__blog__cont">
						<div class="single__blog__primary primary">
							<div class="single__blog__top">
								<h1><?php the_title(); ?></h1>
								<time datetime="<?php the_time('Y-m-d'); ?>"><?php the_time('Y.m.d'); ?></time>
								<ul class="category_label">
									<?php
									$terms = get_the_terms($post->ID, 'blog-tag');
									if ( $terms ) {
										foreach ( $terms as $term ) {
											echo '<li>'.$term->name.'</li>';
										}
									}
									?>
								</ul>
							</div>

							<?php 
							$checked = get_field('eyecatch_for_single');
							if ($checked) :
							?>
							<div class="single__blog__eyecatch">
								<?php the_post_thumbnail( 'large' ); ?>
							</div>
							<?php endif; ?>

							<?php 
							while ( have_posts() ) : the_post();
							?>

							<article class="entry">
								<?php the_content(); ?>
							</article>


							<div class="single__blog__share">
								<p><span>記事をシェア</span></p>
								<?php 
								$share_title = get_the_title();
								$share_url = get_the_permalink();
								?>
								<ul class="sns__list">
									<li><a href="https://twitter.com/share?text=<?=$share_title?>&url=<?=$share_url?>" class="twitter" target="_blank" rel="noopener noreferrer"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/twitter.png" alt="Twitter"><span>Twitter</span></a></li>
									<li><a href="https://www.facebook.com/share.php?u=<?=$share_url?>" class="facebook" target="_blank" rel="noopener noreferrer"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/Facebook.png" alt="Facebook"><span>Facebook</span></a></li>
									<li><a href="http://b.hatena.ne.jp/entry/<?=$share_url?>" class="hatena" target="_blank" rel="noopener noreferrer"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/hatena.png" alt="はてなブックマーク"><span>はてなブックマーク</span></a></li>
									<li><a href="https://social-plugins.line.me/lineit/share?url=<?=$share_url?>" class="line" target="_blank" rel="noopener noreferrer"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/line.png" alt="line"><span>line</span></a></li>
								</ul>
							</div>

							<div class="single__blog__author">
								<div class="pic"><?php echo get_avatar( get_the_author_id(), 150 ); ?></div>
								<div class="body">
									<p class="name">記事の投稿者： <span><?php the_author(); ?></span></p>
									<p class="text">
										<?php the_author_meta( 'description' ); ?>
									</p>
								</div>
							</div>

							<?php endwhile;?>

							<?php get_template_part('template-parts/related_posts'); ?>

							<div class="btn">
								<a href="<?php echo esc_url( home_url('/blog') ); ?>" class="btn__black">
									<span>記事一覧へ戻る</span>
									<img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg" alt="→">
								</a>
							</div>
						</div>

						<?php get_template_part('template-parts/sidebar'); ?>
					</div>
				</div>
			</div>
		</section><!-- single blog -->

    </main>
    <!-- End main -->

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>