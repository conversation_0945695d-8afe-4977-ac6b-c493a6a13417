jQuery(function ($) {
  // ======================================================
  // header service modal
  // ======================================================
  {
    var headerSer = $('.header__service');
    var headerSerSp = $('.header__service__sp img');
    var headerSerModal = $('.header__service__modal');

    if (window.matchMedia('(min-width: 1201px)').matches) {
      headerSer.hover(
        function () {
          headerSerModal.stop().slideDown(300);
        },
        function () {
          headerSerModal.stop().slideUp(300);
        },
      );
    } else {
      headerSerSp.click(function () {
        if (headerSerModal.hasClass('open')) {
          headerSerModal.removeClass('open');
          headerSerModal.slideUp();
          headerSerSp.attr(
            'src',
            'https://appmart.co.jp/wp-content/themes/appmart/assets/images/svg/plus.svg',
          );
        } else {
          headerSerModal.addClass('open');
          headerSerModal.slideDown();
          headerSerSp.attr(
            'src',
            'https://appmart.co.jp/wp-content/themes/appmart/assets/images/svg/minus.svg',
          );
        }
      });
    }
  }

  // ======================================================
  // Page Loader
  // ======================================================
  {
    $(window).load(function () {
      loaded();
    });
    $(function () {
      setTimeout(function () {
        loaded();
      }, 0);
    });
    $(function () {
      setTimeout(function () {
        $('.fadein__cont').css('transform', 'initial');
      }, 0);
    });
    function loaded() {
      $('.pageLoader').fadeOut(0);
      setTimeout(function () {
        $('.fadein__cont').addClass('active');
      }, 0);
    }
  }

  // ======================================================
  // smooth scroll
  // ======================================================
  {
    $('.smooth__scroll').click(function () {
      var href = $(this).attr('href');
      var position = $(href).offset().top;
      $('html, body').animate(
        {
          scrollTop: position,
        },
        1000,
      );
    });
  }

  // ======================================================
  // back to top js
  // ======================================================
  {
    $('#topBtn').hide();

    $(window).scroll(function () {
      var scrollPosi = $(window).scrollTop();

      if (window.matchMedia('(min-width: 768px)').matches) {
        var pcY = 1500;

        if (scrollPosi > pcY) {
          $('#topBtn').fadeIn();
        } else {
          $('#topBtn').fadeOut();
        }
      }

      if (window.matchMedia('(max-width: 767px)').matches) {
        var spY = 1400;

        if (scrollPosi > spY) {
          $('#topBtn').fadeIn();
        } else {
          $('#topBtn').fadeOut();
        }
      }
    });
  }

  {
    $('#topBtn').click(function () {
      $('html, body').animate(
        {
          scrollTop: 0,
        },
        1000,
      );
    });
  }

  // ======================================================
  // spMenu
  // ======================================================
  {
    var hamburger = $('.hamburger');
    var menuTrigger = $('.menu__trigger');
    var headerNav = $('.header__right nav');

    menuTrigger.click(function () {
      if (hamburger.hasClass('active')) {
        spMenuClose();
      } else {
        spMenuOpen();
      }
    });

    if (window.matchMedia('(max-width: 1200px)').matches) {
      $('.header__right nav ul li a').click(function () {
        spMenuClose();
      });
    }

    function spMenuOpen() {
      hamburger.addClass('active');
      menuTrigger.addClass('active');
      headerNav.fadeIn();
      bodyFix();
    }

    function spMenuClose() {
      hamburger.removeClass('active');
      menuTrigger.removeClass('active');
      headerNav.fadeOut();
      bodyFixReset();
    }

    // ------------- body fix -------------
    var bodyElm = $('body');
    var scrollPosi;

    function bodyFix() {
      scrollPosi = $(window).scrollTop();
      bodyElm.css({
        position: 'fixed',
        width: '100%',
        'z-index': '1',
        top: -scrollPosi,
      });
    }

    function bodyFixReset() {
      bodyElm.css({
        position: 'relative',
        width: 'auto',
        top: 'auto',
      });

      $('html, body').scrollTop(scrollPosi);
    }
  }

  // ======================================================
  // document-archive tab
  // ======================================================
  {
    $('.tab').click(function () {
      $('.is-active').removeClass('is-active');
      $(this).addClass('is-active');
      $('.is-show').removeClass('is-show');
      const index = $(this).index();
      $('.group').eq(index).addClass('is-show');
    });
  }

  {
    if (window.matchMedia('(max-width: 767px)').matches) {
      $('.tab_toggle').click(function () {
        $('.tab__list li').not('.all').slideToggle();
        $('.tab_toggle').toggleClass('is-open');
      });
    }
  }

  // ======================================================
  // banner
  // ======================================================
  {
    $(window).scroll(function () {
      var scroll = $(this).scrollTop();
      var wh = $(this).height();

      var banner = $('.banner');

      // contactOffset = $('.section__contact').offset().top;
      contactOffset = $('#contactOffset').offset().top;

      if (window.matchMedia('(min-width: 768px)').matches) {
        if (!banner.hasClass('is_fixed')) {
          bannerOffest = banner.offset().top;
        }

        if (scroll + 104 > bannerOffest && scroll < contactOffset - wh) {
          banner.addClass('is_fixed');
        } else {
          banner.removeClass('is_fixed');
        }
      }
    });
  }

  // ======================================================
  // accordion
  // ======================================================
  {
    $(document).ready(function ($) {
      $('.faq__item-a--accordion').hide();

      $('.faq__item-toggle').on('click', function () {
        $(this).siblings('.faq__item-a--accordion').slideToggle();
        $(this).find('.faq__arrow-icon').toggleClass('open');

        $('.faq__item-a--accordion')
          .not($(this).siblings('.faq__item-a--accordion'))
          .slideUp();
        $('.faq__arrow-icon')
          .not($(this).find('.faq__arrow-icon'))
          .removeClass('open');
      });
    });
  }
});
