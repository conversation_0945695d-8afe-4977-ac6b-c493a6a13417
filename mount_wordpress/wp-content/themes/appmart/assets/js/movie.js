jQuery(document).ready( function( $ ) {

    $('.popup').magnificPopup({
    type: 'iframe',
    preloader: false
    });
    //閉じるリンクの設定
    $(document).on('click', '.popup-close', function (e) {
        e.preventDefault();
        $.magnificPopup.close();
    });

    $('.movieslider').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 4000,
        arrows: false,
        dots: true,
        responsive: [
          {
            breakpoint: 768,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
            //   arrows: false,
            }
          }
        ]
        });

    // $('.movieslider').slick({
    //     dots: true,
    //     infinite: true,
    //     slidesToShow: 3,
    //     slidesToScroll: 1,
    //     autoplay: true,
    //     autoplaySpeed: 4000,
    //     arrows: false,
    //     responsive: [
    //         {
    //             breakpoint: 980,
    //             settings: {
    //                 slidesToShow: 2,
    //             }
    //         }, {
    //             breakpoint: 768,
    //             settings: {
    //                 slidesToShow: 1,
    //             } 
    //         }
    //     ]
    // });

});