/* eslint-disable no-undef */
document.addEventListener('DOMContentLoaded', function () {
  const tooltip = document.createElement('div');
  tooltip.className = 'glossary-tooltip';
  document.body.appendChild(tooltip);

  let activeTermElement = null;
  let isOverTooltip = false;
  const HOVER_THRESHOLD = 50;
  let lastMousePosition = { x: 0, y: 0 };

  const glossaryTerms = document.querySelectorAll('.glossary-term');

  // ツールチップの位置設定
  const TOOLTIP_POSITION = {
    // 基本オフセット
    PADDING: 10,
    OFFSET: 5,

    // 配置方法の定数
    PLACEMENT: {
      RIGHT_BOTTOM: 'right-bottom', // 右下（現在の設定）
      RIGHT_CENTER: 'right-center', // 右中央
      LEFT_BOTTOM: 'left-bottom', // 左下
      LEFT_CENTER: 'left-center', // 左中央
      TOP_CENTER: 'top-center', // 上中央
      BOTTOM_CENTER: 'bottom-center', // 下中央
    },

    // デフォルトの配置
    DEFAULT: 'right-bottom',
  };

  function calculateTooltipPosition(mouseX, mouseY, tooltipRect, placement) {
    const position = { x: 0, y: 0 };

    switch (placement) {
      case TOOLTIP_POSITION.PLACEMENT.RIGHT_CENTER:
        position.x = mouseX + TOOLTIP_POSITION.OFFSET;
        position.y = mouseY - tooltipRect.height / 2;
        break;

      case TOOLTIP_POSITION.PLACEMENT.LEFT_CENTER:
        position.x = mouseX - tooltipRect.width - TOOLTIP_POSITION.OFFSET;
        position.y = mouseY - tooltipRect.height / 2;
        break;

      case TOOLTIP_POSITION.PLACEMENT.TOP_CENTER:
        position.x = mouseX - tooltipRect.width / 2;
        position.y = mouseY - tooltipRect.height - TOOLTIP_POSITION.OFFSET;
        break;

      case TOOLTIP_POSITION.PLACEMENT.BOTTOM_CENTER:
        position.x = mouseX - tooltipRect.width / 2;
        position.y = mouseY + TOOLTIP_POSITION.OFFSET;
        break;

      case TOOLTIP_POSITION.PLACEMENT.LEFT_BOTTOM:
        position.x = mouseX - tooltipRect.width - TOOLTIP_POSITION.OFFSET;
        position.y = mouseY + TOOLTIP_POSITION.OFFSET;
        break;

      case TOOLTIP_POSITION.PLACEMENT.RIGHT_BOTTOM:
      default:
        position.x = mouseX + TOOLTIP_POSITION.OFFSET;
        position.y = mouseY + TOOLTIP_POSITION.OFFSET;
        break;
    }

    return position;
  }

  // マウス位置の追跡
  document.addEventListener('mousemove', (e) => {
    lastMousePosition.x = e.clientX;
    lastMousePosition.y = e.clientY;

    // アクティブな用語上にいる場合は距離チェックをスキップ
    if (activeTermElement) {
      const termRect = activeTermElement.getBoundingClientRect();
      if (
        e.clientX >= termRect.left &&
        e.clientX <= termRect.right &&
        e.clientY >= termRect.top &&
        e.clientY <= termRect.bottom
      ) {
        return;
      }
    }

    if (tooltip.classList.contains('active') && !isOverTooltip) {
      const tooltipRect = tooltip.getBoundingClientRect();
      const distance = getDistanceFromTooltip(
        lastMousePosition.x,
        lastMousePosition.y,
        tooltipRect,
      );

      // console.log('Distance from tooltip:', distance);
      // console.log('HOVER_THRESHOLD:', HOVER_THRESHOLD);
      // console.log('isOverTooltip:', isOverTooltip);

      if (distance > HOVER_THRESHOLD) {
        // console.log('Attempting to hide tooltip due to distance');
        hideTooltip();
      }
    }
  });

  // 用語へのイベントリスナー設定
  glossaryTerms.forEach((term) => {
    term.addEventListener('mouseenter', showTooltip);
    term.addEventListener('mouseleave', handleTermMouseLeave);
  });

  // ツールチップのイベント管理
  tooltip.addEventListener('mouseenter', () => {
    // console.log('Tooltip mouseenter - setting isOverTooltip to true');
    isOverTooltip = true;
  });

  tooltip.addEventListener('mouseleave', () => {
    // console.log('Tooltip mouseleave - setting isOverTooltip to false');
    isOverTooltip = false;
    handleTooltipLeave();
  });

  // ツールチップ表示処理
  async function showTooltip(e) {
    const term = e.target;
    activeTermElement = term;
    // console.log('Showing tooltip for term:', term.dataset.term);

    try {
      const content = await getTooltipContent(
        term.dataset.term,
        term.dataset.description,
        term.href,
        term.dataset.id,
      );

      tooltip.innerHTML = content;
      updateTooltipPosition(e);

      // 表示状態の更新
      requestAnimationFrame(() => {
        tooltip.classList.add('active');
        // 表示直後の距離チェックを防ぐため、短い遅延を設定
        setTimeout(() => {
          if (!tooltip.classList.contains('active')) return;
          // console.log('Tooltip fully activated');
        }, 50);
      });
    } catch (error) {
      console.error('ツールチップの読み込みに失敗しました:', error);
    }
  }

  // ツールチップ位置の更新
  function updateTooltipPosition(e) {
    const mouseX = e.clientX;
    const mouseY = e.clientY;

    // 一時的に表示して寸法を取得
    tooltip.style.display = 'block';
    tooltip.style.visibility = 'hidden';
    tooltip.style.opacity = '0';

    const tooltipRect = tooltip.getBoundingClientRect();
    // console.log('Tooltip dimensions:', tooltipRect);

    // 基本位置の計算
    let { x, y } = calculateTooltipPosition(mouseX, mouseY, tooltipRect, TOOLTIP_POSITION.DEFAULT);

    // 画面右端のチェック
    if (x + tooltipRect.width > window.innerWidth - TOOLTIP_POSITION.PADDING) {
      const leftPosition = calculateTooltipPosition(
        mouseX,
        mouseY,
        tooltipRect,
        TOOLTIP_POSITION.PLACEMENT.LEFT_BOTTOM,
      );
      x = leftPosition.x;
    }

    // 画面下端のチェック
    if (y + tooltipRect.height > window.innerHeight - TOOLTIP_POSITION.PADDING) {
      const topPosition = calculateTooltipPosition(
        mouseX,
        mouseY,
        tooltipRect,
        TOOLTIP_POSITION.PLACEMENT.TOP_CENTER,
      );
      y = topPosition.y;
    }

    // 左端と上端のチェック
    x = Math.max(TOOLTIP_POSITION.PADDING, x);
    y = Math.max(TOOLTIP_POSITION.PADDING, y);

    // 位置を適用
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
    tooltip.style.visibility = 'visible';
    tooltip.style.opacity = '1';

    // console.log('Mouse position:', { x: mouseX, y: mouseY });
    // console.log('Final position:', { x, y });
  }

  function getDistanceFromTooltip(x, y, tooltipRect) {
    const closestX = Math.max(tooltipRect.left, Math.min(x, tooltipRect.right));
    const closestY = Math.max(tooltipRect.top, Math.min(y, tooltipRect.bottom));

    return Math.sqrt(Math.pow(x - closestX, 2) + Math.pow(y - closestY, 2));
  }

  // マウスが用語から離れた時の処理
  function handleTermMouseLeave() {
    // console.log('Term mouseleave - starting hide timeout');

    setTimeout(() => {
      // 用語から離れた後にactiveTermElementを更新
      activeTermElement = null;
      // console.log('Term hide timeout - isOverTooltip:', isOverTooltip);
      if (!isOverTooltip) {
        hideTooltip();
      }
    }, 100);
  }

  // マウスがツールチップから離れた時の処理
  function handleTooltipLeave() {
    // console.log('Tooltip leave - starting hide timeout');
    isOverTooltip = false; // 即座にfalseに設定

    setTimeout(() => {
      // console.log('Tooltip hide timeout - isOverTooltip:', isOverTooltip);
      if (!isOverTooltip && !activeTermElement) {
        hideTooltip();
      }
    }, 100);
  }

  // ツールチップを非表示にする
  function hideTooltip() {
    // console.log('Hiding tooltip');
    tooltip.classList.remove('active');
    tooltip.style.display = 'none';
    activeTermElement = null;
    isOverTooltip = false;
  }

  // ツールチップのコンテンツを取得
  async function getTooltipContent(term, description, permalink, id, retryCount = 0) {
    // console.log('=== Tooltip Request Debug ===');
    // console.log('Sending request with nonce:', appmart.nonce);
    // console.log('Retry count:', retryCount);

    const formData = new FormData();
    formData.append('action', 'get_glossary_tooltip');
    formData.append('term', term);
    formData.append('description', description);
    formData.append('permalink', permalink);
    formData.append('nonce', appmart.nonce);
    formData.append('ID', id);

    try {
      const response = await fetch(appmart.ajaxurl, {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
      });

      const data = await response.json();
      // console.log('Server response:', data);

      // 新しいnonceが含まれている場合は更新
      if (data.data && data.data.new_nonce) {
        // console.log('Updating nonce:', data.data.new_nonce);
        appmart.nonce = data.data.new_nonce;
      }

      // エラーチェック
      if (!response.ok) {
        if (response.status === 401 && retryCount < 1) {
          // console.log('Authentication failed, retrying with new nonce');
          return getTooltipContent(term, description, permalink, id, retryCount + 1);
        }
        throw new Error(data.data?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.data?.message || 'Unknown error occurred');
      }

      return data.data.html || '';
    } catch (error) {
      console.error('Ajax request failed:', error);
      if (retryCount < 1) {
        // console.log('Retrying request after error');
        return getTooltipContent(term, description, permalink, id, retryCount + 1);
      }
      throw error;
    }
  }
});
