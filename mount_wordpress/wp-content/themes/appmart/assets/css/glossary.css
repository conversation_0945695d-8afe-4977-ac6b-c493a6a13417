a.glossary-term {
  font-weight: 600;
  color: #45908c;
  cursor: help;
  border-bottom: 1px solid #45908c;
}
a.glossary-term:hover {
  border-bottom-style: solid;
}

.glossary-tooltip {
  position: fixed;
  z-index: 1000;
  display: none;
  max-width: 450px;
  pointer-events: auto;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  opacity: 0;
  -webkit-transition: opacity 0.2s ease;
  transition: opacity 0.2s ease;
}
.glossary-tooltip.active {
  display: block;
  opacity: 1;
}
.glossary-tooltip__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 12px;
}
.glossary-tooltip__image {
  max-width: 140px;
  margin-right: 18px;
  -o-object-fit: contain;
     object-fit: contain;
}
.glossary-tooltip__image img {
  width: 100%;
  height: auto;
}
.glossary-tooltip__term {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.2;
  color: #45908c;
}
.glossary-tooltip__description {
  margin: 0 0 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
  word-break: break-word;
  white-space: pre-line;
}
.glossary-tooltip__description:last-child {
  margin-bottom: 0;
}
.glossary-tooltip__link {
  display: inline-block;
  font-size: 12px;
  color: #06c;
  text-decoration: none;
}
.glossary-tooltip__link:hover {
  text-decoration: underline;
}

.glossary-archive {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  max-width: 970px;
  margin: 0 auto;
}
.glossary-archive .glossary-title {
  width: 100%;
  padding-bottom: 24px;
  margin-bottom: 32px;
  font-size: 32px;
  font-weight: bold;
  color: #006835;
  text-align: center;
  border-bottom: 1px solid #006835;
}
.glossary-archive .glossary-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 12px;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin-top: 24px;
}
.glossary-archive .glossary-list__item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: calc((100% - 48px) / 3);
  min-width: 280px;
}
.glossary-archive .glossary-list__item::before {
  position: absolute;
  top: 50%;
  left: 0;
  content: "- ";
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.glossary-archive .glossary-list__item .item-title {
  padding-left: 16px;
  font-size: 16px;
  color: #666;
  text-align: left;
}

.glossary-single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  max-width: 1080px;
  margin: 0 auto;
}
.glossary-single .sidebar {
  margin-top: 56px;
}
.glossary-single__content {
  width: 70%;
  padding: 32px 0;
}
.glossary-single__title {
  padding-bottom: 24px;
  margin-bottom: 32px;
  font-size: 32px;
  font-weight: bold;
  color: #006835;
  text-align: center;
  border-bottom: 1px solid #45908c;
}
.glossary-single__thumbnail {
  width: 100%;
  margin: 0 auto;
  margin-top: 48px;
  margin-bottom: 32px;
}
.glossary-single__thumbnail img {
  max-width: 100%;
  height: auto;
}
.glossary-single__bunner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 auto;
  margin-top: 48px;
}
.glossary-single__bunner img {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  height: auto;
}
.glossary-single__description {
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.5;
  color: #666;
}

@media screen and (width <= 979px) {
  .glossary-archive .glossary-title {
    width: unset;
    padding: 8px 16px;
    margin: 0 16px;
  }
  .glossary-archive .glossary-list {
    gap: 12px;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    padding: 0 16px;
    margin-top: 24px;
  }
  .glossary-archive .glossary-list__item {
    width: calc((100% - 48px) / 2);
    min-width: 280px;
  }
  .glossary-single {
    padding: 0 16px;
  }
}
@media screen and (width <= 769px) {
  .glossary-archive .glossary-title {
    font-size: 22px;
  }
  .glossary-archive .glossary-list__item {
    width: 100%;
  }
  .glossary-single {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    overflow: hidden;
  }
  .glossary-single .sidebar {
    margin-top: unset;
  }
  .glossary-single__content {
    width: 100%;
    padding: 16px 0;
  }
}/*# sourceMappingURL=glossary.css.map */