@charset "UTF-8";
.owned-media-lp * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}

@media (max-width: 768px) {
  .owned-media-pc-only {
    display: none !important;
  }
}

.owned-media-sp-only {
  display: none !important;
}
@media (max-width: 768px) {
  .owned-media-sp-only {
    display: block !important;
  }
}

.underline {
  position: relative;
  display: inline-block;
}
.underline::before {
  position: absolute;
  bottom: -10px;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 14px;
  content: "";
  background-color: #fff54b;
  border-radius: 7px;
}

.owned-media-lp {
  width: 100%;
  overflow-x: hidden;
}

.owned-media-cta {
  position: relative;
  width: 100%;
  background-color: #f9f9f9;
}
.owned-media-cta__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 100%;
  padding: 80px 0;
}
@media (max-width: 768px) {
  .owned-media-cta__container {
    max-width: 100%;
  }
}
.owned-media-cta__title {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 700;
  line-height: 46px;
  text-align: center;
  white-space: nowrap;
}
.owned-media-cta__title-prefix, .owned-media-cta__title-suffix {
  color: #333;
  letter-spacing: -0.43px;
}
.owned-media-cta__title-accent {
  color: #fa6b58;
  letter-spacing: -0.43px;
}
.owned-media-cta__buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 42px;
  margin-top: 80px;
}
.owned-media-cta__button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 620px;
  height: 160px;
  padding: 24px 56px 24px 32px;
  text-decoration: none;
  border-radius: 160px;
  -webkit-box-shadow: 0 9px 19px rgba(82, 134, 120, 0.13);
          box-shadow: 0 9px 19px rgba(82, 134, 120, 0.13);
  -webkit-transition: -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  transition: -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease, -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
}
.owned-media-cta__button:hover {
  -webkit-box-shadow: 0 12px 24px rgba(82, 134, 120, 0.2);
          box-shadow: 0 12px 24px rgba(82, 134, 120, 0.2);
  -webkit-transform: translateY(-3px);
          transform: translateY(-3px);
}
.owned-media-cta__button--outline {
  background-color: #f9f9f9;
  border: 9px solid #fa6b58;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.owned-media-cta__button--outline .owned-media-cta__button-text {
  font-size: clamp(24px, 3vw, 38px);
  color: #fa6b58;
  white-space: nowrap;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
}
.owned-media-cta__button--outline:hover {
  background-color: #fa6b58;
  border-color: #fa6b58;
}
.owned-media-cta__button--outline:hover .owned-media-cta__button-text {
  color: #fff;
}
.owned-media-cta__button--outline:hover .owned-media-cta__button-arrow path {
  stroke: #fff;
}
.owned-media-cta__button--filled {
  background-color: #fa6b58;
  border: 9px solid #fa6b58;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.owned-media-cta__button--filled .owned-media-cta__button-text {
  font-size: clamp(24px, 3vw, 38px);
  font-weight: 700;
  color: #fff;
  white-space: nowrap;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
}
.owned-media-cta__button--filled:hover {
  background-color: #f9f9f9;
  border-color: #fa6b58;
}
.owned-media-cta__button--filled:hover .owned-media-cta__button-text {
  color: #fa6b58;
}
.owned-media-cta__button--filled:hover .owned-media-cta__button-arrow path {
  stroke: #fa6b58;
}
.owned-media-cta__button-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 59px;
  text-align: center;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-cta__container {
    padding: 40px 20px;
  }
  .owned-media-cta__title {
    font-size: clamp(24px, 6vw, 32px);
    line-height: 1.4;
    white-space: normal;
  }
  .owned-media-cta__buttons {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 20px;
    width: 100%;
    margin-top: 40px;
  }
  .owned-media-cta__button {
    width: 100%;
    max-width: none;
    height: auto;
    padding: 10px 20px;
    border-radius: 90px;
  }
  .owned-media-cta__button--outline, .owned-media-cta__button--filled {
    font-size: 22px;
    border: 2px solid #fa6b58;
  }
  .owned-media-cta__button--outline .owned-media-cta__button-text, .owned-media-cta__button--filled .owned-media-cta__button-text {
    font-size: unset;
    font-size: clamp(12px, 4vw, 22px);
    font-weight: 400;
    line-height: 1.2;
    white-space: normal;
  }
}

.owned-media-fv {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #fff;
  position: relative;
  padding: 0;
  padding-top: 80px;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.4)), color-stop(33.17%, rgba(227, 243, 255, 0.4)), color-stop(64.42%, rgba(129, 204, 231, 0.4)));
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(227, 243, 255, 0.4) 33.17%, rgba(129, 204, 231, 0.4) 64.42%);
}
@media (max-width: 768px) {
  .owned-media-fv {
    padding: 80px 0 40px;
  }
}
.owned-media-fv::before {
  position: absolute;
  bottom: 0;
  left: 45%;
  z-index: 0;
  width: 105%;
  height: 800px;
  content: "";
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/fv-main-illustration.png");
  background-repeat: no-repeat;
  background-position: center bottom;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-fv__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
  display: flex;
  -ms-flex-direction: row;
      flex-direction: row;
  max-width: 1600px;
  height: 900px;
  padding-top: 80px;
}
@media (max-width: 768px) {
  .owned-media-fv__container {
    max-width: 100%;
  }
}
.owned-media-fv__container .fv-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 50%;
  min-width: 750px;
  height: 685px;
  -webkit-transform: rotate(-5deg);
          transform: rotate(-5deg);
}
.owned-media-fv__container .fv-left__catch {
  position: relative;
}
.owned-media-fv__container .fv-left__catch .catch-copy {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 16px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin-bottom: 32px;
}
.owned-media-fv__container .fv-left__catch .catch-copy::before, .owned-media-fv__container .fv-left__catch .catch-copy::after {
  position: absolute;
  top: 50%;
  width: 4px;
  height: 90px;
  content: "";
  background-color: #333;
}
.owned-media-fv__container .fv-left__catch .catch-copy::before {
  left: -64px;
  -webkit-transform: translateY(-50%) rotate(-20deg);
          transform: translateY(-50%) rotate(-20deg);
}
.owned-media-fv__container .fv-left__catch .catch-copy::after {
  right: -64px;
  -webkit-transform: translateY(-50%) rotate(20deg);
          transform: translateY(-50%) rotate(20deg);
}
.owned-media-fv__container .fv-left__catch .catch-copy__text {
  font-size: clamp(24px, 3vw, 46px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  text-align: center;
}
.owned-media-fv__container .fv-left__catch .catch-copy__char-dot {
  position: relative;
  display: inline-block;
}
.owned-media-fv__container .fv-left__catch .catch-copy__char-dot::before {
  position: absolute;
  top: -5px;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  background-color: #333;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-fv__container .fv-left__pill {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 100px;
  margin-bottom: 32px;
}
.owned-media-fv__container .fv-left__pill .pill {
  padding: 18px 32px;
  font-size: clamp(24px, 3vw, 50px);
  font-weight: 700;
  line-height: 1;
  border: 8px solid #333;
}
.owned-media-fv__container .fv-left__pill .pill-left {
  padding-right: 16px;
  color: #fff;
  background-color: #333;
  border-radius: 50px 0 0 50px;
}
.owned-media-fv__container .fv-left__pill .pill-left__text {
  color: #fff;
}
.owned-media-fv__container .fv-left__pill .pill-right {
  padding-left: 16px;
  color: #333;
  background-color: #fff;
  border-radius: 0 50px 50px 0;
}
.owned-media-fv__container .fv-left__pill .pill-right__text {
  color: #333;
}
.owned-media-fv__container .fv-left__message {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 32px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-fv__container .fv-left__message .message-text {
  font-weight: 900;
  line-height: 1;
  vertical-align: middle;
}
.owned-media-fv__container .fv-left__message .message-text.strong {
  font-size: clamp(200px, 10vw, 237px);
  font-weight: 900;
  color: #fa6b58;
  white-space: nowrap;
}
.owned-media-fv__container .fv-left__message .message-text.accent {
  font-size: clamp(100px, 10vw, 161px);
  color: #333;
}
.owned-media-fv__container .fv-left__message .message-text.normal {
  font-size: clamp(80px, 10vw, 131px);
  color: #333;
}
.owned-media-fv__container .fv-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  width: 50%;
}
.owned-media-fv__container .fv-right__form .form-container {
  width: 375px;
  height: 618px;
  padding: 40px;
  background-color: #f9f9f9;
  border-radius: 20px;
}
.owned-media-fv__container .fv-right__form .form-container__text {
  font-size: 24px;
  font-weight: 700;
}
@media (max-width: 1200px) {
  .owned-media-fv__container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
    height: auto;
  }
  .owned-media-fv .fv-left {
    width: 100%;
    height: auto;
    margin-bottom: 40px;
  }
  .owned-media-fv .fv-right {
    width: 100%;
    margin-bottom: 80px;
  }
  .owned-media-fv .fv-right__form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    width: 100%;
  }
}
@media (max-width: 768px) {
  .owned-media-fv {
    padding-top: 40px;
  }
  .owned-media-fv::before {
    left: 50%;
    width: 105%;
    height: 430px;
    background-image: url("/wp-content/themes/appmart/assets/images/s-owned/fv-main-illustration-sp.png");
    background-position: center top;
    background-size: cover;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
  .owned-media-fv__container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    height: auto;
    padding-top: 0;
    padding-right: 0;
    padding-left: 0;
  }
  .owned-media-fv__container .fv-left {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    width: 100%;
    height: auto;
    height: 700px;
    padding: 20px;
    -webkit-transform: none;
            transform: none;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy {
    margin-bottom: 20px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy::before, .owned-media-fv__container .fv-left__catch .catch-copy::after {
    height: 60px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy::before {
    left: -12px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy::after {
    right: -12px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy__text {
    font-size: clamp(18px, 4vw, 32px);
  }
  .owned-media-fv__container .fv-left__pill {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    min-width: 100%;
    height: 60px;
    margin-bottom: 20px;
  }
  .owned-media-fv__container .fv-left__pill .pill {
    padding: 12px;
    font-size: clamp(22px, 4vw, 48px);
    border: 4px solid #333;
  }
  .owned-media-fv__container .fv-left__pill .pill-left {
    padding-right: 4px;
  }
  .owned-media-fv__container .fv-left__pill .pill-right {
    padding-left: 4px;
  }
  .owned-media-fv__container .fv-left__message {
    gap: 16px;
  }
  .owned-media-fv__container .fv-left__message .message-text.strong {
    font-size: clamp(80px, 10vw, 90px);
  }
  .owned-media-fv__container .fv-left__message .message-text.accent {
    font-size: clamp(50px, 10vw, 60px);
  }
  .owned-media-fv__container .fv-left__message .message-text.normal {
    font-size: clamp(40px, 10vw, 50px);
  }
  .owned-media-fv__container .fv-right {
    display: none;
  }
  .owned-media-fv__form-mobile {
    display: block;
    width: 100%;
    padding: 40px 20px;
    margin-top: 40px;
  }
  .owned-media-fv__form-mobile .form-container {
    width: 100%;
    max-width: 375px;
    height: auto;
    padding: 30px 20px;
    margin: 0 auto;
  }
  .owned-media-fv__form-mobile .form-container__text {
    font-size: 20px;
  }
}
@media (max-width: 480px) {
  .owned-media-fv {
    padding-top: 0;
  }
}

.owned-media-first-appeal {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #b1e2d5;
  width: 100%;
  padding-top: 120px;
}
@media (max-width: 768px) {
  .owned-media-first-appeal {
    padding: 80px 0 40px;
  }
}
.owned-media-first-appeal__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-first-appeal__container {
    max-width: 100%;
  }
}
.owned-media-first-appeal__container::after {
  position: absolute;
  top: -200px;
  left: 50%;
  z-index: 1;
  width: 200px;
  height: 200px;
  content: "";
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/first-appeal-decoration.png");
  background-repeat: no-repeat;
  background-size: contain; /* 背景画像のサイズを指定 */
  -webkit-transform: translateX(-50%) rotate(5deg);
          transform: translateX(-50%) rotate(5deg);
}
.owned-media-first-appeal__message-area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  text-align: center;
}
.owned-media-first-appeal__main-message {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 100px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-weight: 900;
}
.owned-media-first-appeal__main-message::after {
  position: absolute;
  bottom: -120px;
  left: 65%;
  z-index: 1;
  width: 440px;
  height: 140px;
  content: "";
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/graffiti-vector.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-first-appeal__main-message-line1, .owned-media-first-appeal__main-message-line2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.owned-media-first-appeal__main-message-line1 {
  margin-right: 8px;
}
.owned-media-first-appeal__main-message-from {
  font-size: clamp(50px, 7vw, 100px);
  line-height: 1;
  color: #333;
  letter-spacing: -3px;
  white-space: nowrap;
  -webkit-transform: rotate(-5deg);
          transform: rotate(-5deg);
}
.owned-media-first-appeal__main-message-connector {
  font-size: clamp(40px, 5.6vw, 80px);
  line-height: 1;
  color: #333;
  letter-spacing: -0.03em;
  white-space: nowrap;
}
.owned-media-first-appeal__main-message-to {
  font-size: clamp(60px, 8.5vw, 121px);
  line-height: 1;
  color: #fa6b58;
  letter-spacing: -3.63px;
  white-space: nowrap;
  -webkit-transform: rotate(-5deg);
          transform: rotate(-5deg);
}
.owned-media-first-appeal__main-message-suffix {
  position: relative;
  left: -22px;
  font-size: clamp(40px, 5.6vw, 80px);
  line-height: 1;
  color: #333;
  letter-spacing: -0.03em;
  white-space: nowrap;
}
.owned-media-first-appeal__sub-message {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 10px;
  width: 100%;
  text-align: center;
}
.owned-media-first-appeal__sub-message-text {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(24px, 3vw, 38px);
  font-weight: 700;
  line-height: 1.8;
  text-align: center;
  letter-spacing: -0.03em;
}
.owned-media-first-appeal__sub-message-line1 {
  display: block;
  margin-bottom: 10px;
  color: #333;
}
.owned-media-first-appeal__sub-message-line2 {
  display: inline-block;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: auto;
  padding: 12px 16px;
  margin: 0 auto;
  line-height: 1.4;
  color: #fff;
  text-align: center;
  background-color: #fa6b58;
  border-radius: 4px;
}
.owned-media-first-appeal__people {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: clamp(20px, 5vw, 60px);
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  margin-bottom: 40px;
}
.owned-media-first-appeal__people-message {
  -webkit-box-ordinal-group: 4;
      -ms-flex-order: 3;
          order: 3;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(30px, 4vw, 78px);
  font-weight: 900;
  line-height: 1.4;
  color: #333;
  text-align: center;
  letter-spacing: -2.34px;
  white-space: nowrap;
}
.owned-media-first-appeal__people-images {
  display: contents;
}
.owned-media-first-appeal__person {
  height: 180px;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: bottom;
     object-position: bottom;
}
.owned-media-first-appeal__person--1 {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
  width: clamp(35px, 8vw, 47px);
}
.owned-media-first-appeal__person--2 {
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2;
  width: clamp(55px, 12vw, 73px);
}
.owned-media-first-appeal__person--3 {
  -webkit-box-ordinal-group: 5;
      -ms-flex-order: 4;
          order: 4;
  width: clamp(38px, 9vw, 51px);
}
.owned-media-first-appeal__person--4 {
  -webkit-box-ordinal-group: 6;
      -ms-flex-order: 5;
          order: 5;
  width: clamp(49px, 11vw, 65px);
}
.owned-media-first-appeal__decoration {
  position: absolute;
  top: 0;
  right: 5%;
  width: clamp(150px, 15vw, 219px);
  height: auto;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-first-appeal {
    padding: 60px 0 40px;
  }
  .owned-media-first-appeal__container {
    padding: 0 16px;
  }
  .owned-media-first-appeal__container::after {
    top: -120px;
    width: 95px;
    height: 95px;
  }
  .owned-media-first-appeal__main-message {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    margin-bottom: 120px;
  }
  .owned-media-first-appeal__main-message::after {
    bottom: -100px;
    left: 50%;
    width: 100%;
    height: 100px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
  .owned-media-first-appeal__main-message-line1, .owned-media-first-appeal__main-message-line2 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 8px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .owned-media-first-appeal__main-message-line1 {
    margin-right: 0;
    margin-bottom: 8px;
  }
  .owned-media-first-appeal__main-message-from {
    font-size: clamp(32px, 8vw, 50px);
    -webkit-transform: none;
            transform: none;
  }
  .owned-media-first-appeal__main-message-connector {
    font-size: clamp(24px, 6vw, 40px);
  }
  .owned-media-first-appeal__main-message-to {
    font-size: clamp(40px, 10vw, 60px);
    -webkit-transform: none;
            transform: none;
  }
  .owned-media-first-appeal__main-message-suffix {
    position: static;
    left: unset;
    font-size: clamp(24px, 6vw, 40px);
  }
  .owned-media-first-appeal__sub-message {
    gap: 20px;
    margin-bottom: 20px;
  }
  .owned-media-first-appeal__sub-message-text {
    font-size: clamp(16px, 4vw, 24px);
    line-height: 1.6;
    text-align: center;
    word-wrap: break-word;
    white-space: normal;
  }
  .owned-media-first-appeal__sub-message-line2 {
    display: inline-block;
    width: 100%;
    height: auto;
    padding: 8px 12px;
    margin: 0;
    font-size: clamp(14px, 4vw, 20px);
  }
  .owned-media-first-appeal__people {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 20px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    margin-bottom: 20px;
  }
  .owned-media-first-appeal__people-message {
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1;
    margin-bottom: 20px;
    font-size: clamp(30px, 10vw, 40px);
  }
  .owned-media-first-appeal__people-images {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 10px;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: flex-end;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
  }
  .owned-media-first-appeal__person {
    height: clamp(80px, 20vw, 120px);
  }
  .owned-media-first-appeal__person--1 {
    -webkit-box-ordinal-group: unset;
        -ms-flex-order: unset;
            order: unset;
  }
  .owned-media-first-appeal__person--2 {
    -webkit-box-ordinal-group: unset;
        -ms-flex-order: unset;
            order: unset;
  }
  .owned-media-first-appeal__person--3 {
    -webkit-box-ordinal-group: unset;
        -ms-flex-order: unset;
            order: unset;
  }
  .owned-media-first-appeal__person--4 {
    -webkit-box-ordinal-group: unset;
        -ms-flex-order: unset;
            order: unset;
  }
  .owned-media-first-appeal__decoration {
    display: none;
  }
}

.owned-media-partner-logos {
  width: 100%;
  padding: 80px 0;
  overflow: hidden;
  background-color: #f9f9f9;
}
.owned-media-partner-logos__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0;
}
@media (max-width: 768px) {
  .owned-media-partner-logos__container {
    max-width: 100%;
  }
}
.owned-media-partner-logos__logos-wrapper {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 30px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  min-height: 120px;
}
.owned-media-partner-logos__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  gap: 25px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
}
.owned-media-partner-logos__row--1 {
  margin-bottom: 15px;
}
.owned-media-partner-logos__row--2 {
  margin-top: 15px;
}
.owned-media-partner-logos__logo-group {
  width: auto;
  height: auto;
  max-height: 60px;
  margin: 0 15px;
  -o-object-fit: contain;
     object-fit: contain;
  mix-blend-mode: multiply;
}
.owned-media-partner-logos__scrolling-container {
  display: none;
}
.owned-media-partner-logos__scrolling-track {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}
@media (max-width: 768px) {
  .owned-media-partner-logos {
    height: 200px;
    padding: 60px 0;
    overflow: hidden;
  }
  .owned-media-partner-logos__container {
    position: relative;
    width: 100%;
    max-width: none;
    height: 200px;
    padding: 0;
    overflow: hidden;
  }
  .owned-media-partner-logos__logos-wrapper {
    display: none;
  }
  .owned-media-partner-logos__scrolling-container {
    position: absolute;
    top: 0;
    left: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
    height: 200px;
  }
  .owned-media-partner-logos__scrolling-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    height: 50px;
    overflow: hidden;
  }
  .owned-media-partner-logos__scrolling-row--top {
    margin-bottom: 20px;
  }
  .owned-media-partner-logos__scrolling-row--top .owned-media-partner-logos__scrolling-track {
    -webkit-animation: scroll-logos 40s linear infinite;
            animation: scroll-logos 40s linear infinite;
  }
  .owned-media-partner-logos__scrolling-row--bottom {
    margin-top: 0;
    margin-bottom: 40px;
  }
  .owned-media-partner-logos__scrolling-row--bottom .owned-media-partner-logos__scrolling-track {
    -webkit-animation: scroll-logos 40s linear infinite;
            animation: scroll-logos 40s linear infinite;
  }
  .owned-media-partner-logos__scrolling-track {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 30px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
  }
  .owned-media-partner-logos__scrolling-logo {
    -ms-flex-negative: 0;
        flex-shrink: 0;
    width: auto;
    height: auto;
    max-height: 45px;
    margin: 0 15px;
    -o-object-fit: contain;
       object-fit: contain;
    mix-blend-mode: multiply;
  }
}
@-webkit-keyframes scroll-logos {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-1 * var(--track-width, 50%)));
            transform: translateX(calc(-1 * var(--track-width, 50%)));
  }
}
@keyframes scroll-logos {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-1 * var(--track-width, 50%)));
            transform: translateX(calc(-1 * var(--track-width, 50%)));
  }
}

.owned-media-empathy {
  position: relative;
  width: 100%;
  background-color: #f9f9f9;
}
.owned-media-empathy__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 100%;
  padding: 130px 0;
}
@media (max-width: 768px) {
  .owned-media-empathy__container {
    max-width: 100%;
  }
}
.owned-media-empathy__header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 115px;
}
.owned-media-empathy__subtitle {
  margin: 0 auto;
  text-align: center;
}
.owned-media-empathy__subtitle-text {
  position: relative;
  z-index: 2;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
  color: #333;
  letter-spacing: -1.44px;
}
.owned-media-empathy__title {
  display: inline-block;
  text-align: center;
}
.owned-media-empathy__title-text {
  position: relative;
  z-index: 2;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(40px, 5vw, 56px);
  font-weight: 700;
  line-height: 1;
  color: #333;
  letter-spacing: -1.68px;
  white-space: nowrap;
}
.owned-media-empathy__title-text::after {
  position: absolute;
  bottom: 8px;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 39px;
  content: "";
  background-color: #f9f9f9;
}
@media (max-width: 768px) {
  .owned-media-empathy__title-text {
    font-size: 56px;
  }
}
.owned-media-empathy__checklist {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  padding: 80px;
  border: 8px solid #b1e2d5;
}
.owned-media-empathy__checklist::after {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 161px;
  height: 161px;
  content: "";
  background: linear-gradient(45deg, #b1e2d5 50%, #f9f9f9 50%);
}
.owned-media-empathy__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  list-style: none;
}
.owned-media-empathy__item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  padding: 48px 0;
}
.owned-media-empathy__item::before {
  position: absolute;
  bottom: 50%;
  left: 0;
  z-index: 1;
  width: 58px;
  height: 58px;
  content: "";
  border: 7px solid #7dc8b6;
  border-radius: 9px;
  -webkit-transform: translateY(50%);
          transform: translateY(50%);
}
.owned-media-empathy__item::after {
  position: absolute;
  bottom: 55%;
  left: 8px;
  z-index: 2;
  width: 83px;
  height: 83px;
  content: "";
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/check-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  -webkit-transform: translateY(50%);
          transform: translateY(50%);
}
.owned-media-empathy__item-text {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin: 0;
  margin-left: 96px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(24px, 3vw, 46px);
  font-weight: 700;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.owned-media-empathy__item-text::before {
  position: absolute;
  bottom: 0;
  left: 96px;
  width: 90%;
  height: 1px;
  content: "";
  border-bottom: 6px dashed #c9e9ed;
}
.owned-media-empathy__item-accent {
  font-weight: 900;
  color: #fa6b58;
}
@media (max-width: 768px) {
  .owned-media-empathy__container {
    padding: 60px 20px;
  }
  .owned-media-empathy__header {
    margin-bottom: 40px;
  }
  .owned-media-empathy__title {
    background-color: #b1e2d5;
  }
  .owned-media-empathy__subtitle {
    background-color: #b1e2d5;
  }
  .owned-media-empathy__subtitle-text {
    font-size: 20px;
    letter-spacing: -0.5px;
  }
  .owned-media-empathy__title-text {
    font-size: 24px;
    letter-spacing: -1px;
    white-space: normal;
  }
  .owned-media-empathy__title-text::after {
    background-color: #b1e2d5;
  }
  .owned-media-empathy__checklist {
    padding: 40px 20px;
    border-width: 4px;
  }
  .owned-media-empathy__checklist::after {
    top: -4px;
    right: -4px;
    width: 31px;
    height: 31px;
  }
  .owned-media-empathy__item {
    padding: 30px 0;
  }
  .owned-media-empathy__item::before {
    width: 40px;
    height: 40px;
    border-width: 4px;
  }
  .owned-media-empathy__item::after {
    left: 4px;
    width: 50px;
    height: 50px;
  }
  .owned-media-empathy__item-text {
    margin-left: 60px;
    font-size: clamp(16px, 4vw, 20px);
  }
  .owned-media-empathy__item-text::before {
    left: 60px;
    border-bottom-width: 3px;
  }
}

.owned-media-discovery {
  position: relative;
  width: 100%;
  background-image: linear-gradient(to right, rgba(97, 106, 109, 0.15) 1px, transparent 1px), linear-gradient(to bottom, rgba(97, 106, 109, 0.15) 1px, transparent 1px);
  background-size: 48px 48px;
}
.owned-media-discovery::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: linear-gradient(135deg, #a0c1bb 50%, transparent 50%);
}
.owned-media-discovery::after {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 1;
  width: 80px;
  height: 80px;
  content: "";
  background-color: #f9f9f9;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(135deg) skew(20deg, 20deg);
          transform: translateX(-50%) translateY(-50%) rotate(135deg) skew(20deg, 20deg);
}
.owned-media-discovery__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
  width: 100%;
  max-width: 1400px;
  padding-top: 80px;
}
@media (max-width: 768px) {
  .owned-media-discovery__container {
    max-width: 100%;
  }
}
.owned-media-discovery__header {
  width: 100%;
  margin: 0 auto;
}
.owned-media-discovery__title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
}
.owned-media-discovery__title-prefix {
  position: relative;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(40px, 5vw, 60px);
  font-weight: 900;
  line-height: 1.2;
  color: #333;
  letter-spacing: -1.5px;
}
.owned-media-discovery__title-main {
  position: relative;
  bottom: -32px;
  margin: 0 18px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(100px, 10vw, 186px);
  font-weight: 900;
  line-height: 1.2;
  color: white;
  letter-spacing: 10px;
  white-space: nowrap;
  -webkit-transform: rotate(-5deg);
          transform: rotate(-5deg);
  -webkit-text-stroke: 8px #333;
  paint-order: stroke;
}
.owned-media-discovery__title-main::before {
  position: absolute;
  top: 0;
  right: -110px;
  z-index: 1;
  width: 93px;
  height: 103px;
  content: "";
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-accent.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  -webkit-transform: rotate(5deg);
          transform: rotate(5deg);
}
.owned-media-discovery__title-suffix {
  position: relative;
  margin-left: 20px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(40px, 5vw, 61px);
  font-weight: 700;
  color: #333;
  letter-spacing: -1.83px;
}
.owned-media-discovery__accent {
  position: absolute;
  top: -4px;
  right: 250px;
  width: 101px;
  height: 111px;
}
.owned-media-discovery__content {
  position: relative;
  width: 100%;
  height: 1430px;
}
.owned-media-discovery__illustration-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
}
.owned-media-discovery__illustration {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 10;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.owned-media-discovery__person {
  position: relative;
  top: -100px;
  width: auto;
  max-width: 211px;
  height: auto;
  max-height: 445px;
  -o-object-fit: contain;
     object-fit: contain;
}
.owned-media-discovery__desk {
  position: absolute;
  top: 100px;
  left: 0;
  width: 354px;
  height: 350px;
  -o-object-fit: contain;
     object-fit: contain;
}
.owned-media-discovery__reasons {
  position: relative;
  width: 100%;
  max-width: 1300px;
  height: 100%;
}
.owned-media-discovery__reason {
  position: absolute;
}
.owned-media-discovery__reason--1 {
  top: 0;
  left: 30px;
}
.owned-media-discovery__reason--1 .owned-media-discovery__reason-bubble {
  width: 597px;
  height: 602px;
}
.owned-media-discovery__reason--1 .owned-media-discovery__reason-bubble::before {
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-bubble-1.png");
  -webkit-transform: rotate(-15deg);
          transform: rotate(-15deg);
}
.owned-media-discovery__reason--1 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -80%);
          transform: translate(-50%, -80%);
}
.owned-media-discovery__reason--2 {
  top: 9px;
  left: 540px;
}
.owned-media-discovery__reason--2 .owned-media-discovery__reason-bubble {
  width: 611px;
  height: 616px;
}
.owned-media-discovery__reason--2 .owned-media-discovery__reason-bubble::before {
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-bubble-1.png");
  -webkit-transform: rotate(16deg);
          transform: rotate(16deg);
}
.owned-media-discovery__reason--2 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -80%);
          transform: translate(-50%, -80%);
}
.owned-media-discovery__reason--3 {
  top: 430px;
  right: -30px;
}
.owned-media-discovery__reason--3 .owned-media-discovery__reason-bubble {
  width: 491px;
  height: 437px;
}
.owned-media-discovery__reason--3 .owned-media-discovery__reason-bubble::before {
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-bubble-2.png");
}
.owned-media-discovery__reason--3 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -80%);
          transform: translate(-50%, -80%);
}
.owned-media-discovery__reason--4 {
  right: 0;
  bottom: 243px;
}
.owned-media-discovery__reason--4 .owned-media-discovery__reason-bubble {
  width: 506px;
  height: 458px;
}
.owned-media-discovery__reason--4 .owned-media-discovery__reason-bubble::before {
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-bubble-3.png");
}
.owned-media-discovery__reason--4 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.owned-media-discovery__reason--5 {
  bottom: 0;
  left: 406px;
}
.owned-media-discovery__reason--5 .owned-media-discovery__reason-bubble {
  width: 514px;
  height: 522px;
}
.owned-media-discovery__reason--5 .owned-media-discovery__reason-bubble::before {
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-bubble-4.png");
}
.owned-media-discovery__reason--5 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -35%);
          transform: translate(-50%, -35%);
}
.owned-media-discovery__reason--6 {
  top: 527px;
  left: 0;
}
.owned-media-discovery__reason--6 .owned-media-discovery__reason-bubble {
  width: 610px;
  height: 612px;
}
.owned-media-discovery__reason--6 .owned-media-discovery__reason-bubble::before {
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-bubble-1.png");
  -webkit-transform: rotate(-160deg);
          transform: rotate(-160deg);
}
.owned-media-discovery__reason--6 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-55%, -35%);
          transform: translate(-55%, -35%);
}
.owned-media-discovery__reason-bubble {
  position: relative;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-discovery__reason-bubble::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.owned-media-discovery__reason-text {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  width: 80%;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(16px, 2.3vw, 32px);
  line-height: 1.16;
  text-align: center;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.owned-media-discovery__reason-strong {
  font-weight: 900;
  color: #3c8b86;
}
.owned-media-discovery__reason-weak {
  font-weight: 700;
  color: #5f6061;
}
@media (max-width: 768px) {
  .owned-media-discovery {
    height: auto;
    padding: 60px 0;
  }
  .owned-media-discovery__container {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding: 0 20px;
  }
  .owned-media-discovery__bg-image {
    top: 50px;
    height: auto;
  }
  .owned-media-discovery__arrow {
    top: 20px;
    width: 120px;
    height: 56px;
  }
  .owned-media-discovery__header {
    max-width: 100%;
    padding-top: 80px;
  }
  .owned-media-discovery__title {
    position: relative;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 8px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
  }
  .owned-media-discovery__title-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .owned-media-discovery__title-prefix {
    display: block;
    font-size: 28px;
  }
  .owned-media-discovery__title-main {
    position: static;
    display: block;
    font-size: 72px;
    letter-spacing: 7.92px;
    -webkit-text-stroke: 7px #333;
    text-stroke: 7px #333;
    -webkit-transform: rotate(-5deg);
            transform: rotate(-5deg);
  }
  .owned-media-discovery__title-main::before {
    position: absolute;
    top: 12px;
    right: -32px;
    width: 32px;
    height: 32px;
    content: "";
    background-image: url("/wp-content/themes/appmart/assets/images/s-owned/discovery-accent.png");
    background-repeat: no-repeat;
  }
  .owned-media-discovery__title-suffix {
    display: block;
    margin-left: 0;
    font-size: 28px;
    letter-spacing: 1.2px;
  }
  .owned-media-discovery__title-suffix-small {
    display: 24px;
  }
  .owned-media-discovery__accent {
    position: static;
    display: inline-block;
    width: 50px;
    height: 56px;
    margin-left: 10px;
    vertical-align: middle;
  }
  .owned-media-discovery__content {
    position: static;
    max-width: 100%;
    height: auto;
    margin-top: 60px;
  }
  .owned-media-discovery__illustration-container {
    position: static;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    height: auto;
  }
  .owned-media-discovery__illustration {
    position: static;
    height: 180px;
    margin-bottom: 0;
    text-align: center;
    -webkit-transform: none;
            transform: none;
  }
  .owned-media-discovery__person {
    top: -180px;
    left: -100px;
    width: 120px;
    height: 253px;
  }
  .owned-media-discovery__desk {
    position: absolute;
    top: 250px;
    left: 30%;
    width: 200px;
    height: 198px;
  }
  .owned-media-discovery__reasons {
    position: static;
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    width: 100%;
    padding: 0 10px;
  }
  .owned-media-discovery__reason {
    position: static !important;
  }
  .owned-media-discovery__reason-bubble {
    width: 100% !important;
    height: auto !important;
    height: 94px;
    padding: 10px 20px;
    background-color: #e6edf1;
    border-radius: 47px;
    -webkit-box-shadow: 0 0 10px rgba(135, 159, 170, 0.9);
            box-shadow: 0 0 10px rgba(135, 159, 170, 0.9);
  }
  .owned-media-discovery__reason-bubble::before {
    display: none;
  }
  .owned-media-discovery__reason-text {
    position: static !important;
    top: auto !important;
    left: auto !important;
    width: 100% !important;
    font-size: 20px;
    -webkit-transform: none !important;
            transform: none !important;
  }
  .owned-media-discovery__reason-weak {
    font-size: 18px;
  }
}

.owned-media-success {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #fff;
  padding-top: 32px;
  background-image: linear-gradient(to right, rgba(97, 106, 109, 0.15) 1px, transparent 1px), linear-gradient(to bottom, rgba(97, 106, 109, 0.15) 1px, transparent 1px);
  background-size: 48px 48px;
}
@media (max-width: 768px) {
  .owned-media-success {
    padding: 80px 0 40px;
  }
}
.owned-media-success__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-success__container {
    max-width: 100%;
  }
}
.owned-media-success__header {
  position: relative;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-success__title-wrapper {
  position: relative;
  display: inline-block;
}
.owned-media-success__title-wrapper::before {
  position: absolute;
  top: 20%;
  left: 50%;
  z-index: 1;
  width: 110%;
  height: 100%;
  content: "";
  background-image: url("../images/s-owned/success-title-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-success__title {
  position: relative;
  z-index: 2;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  text-align: center;
}
.owned-media-success__title-main {
  display: block;
  font-size: clamp(100px, 10vw, 172px);
  font-weight: 900;
  line-height: 1.2;
  color: #fa6b58;
  letter-spacing: 2px;
}
.owned-media-success__title-sub {
  position: relative;
  display: block;
  padding-right: 48px;
  font-size: clamp(52px, 10vw, 92px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  letter-spacing: 1px;
}
.owned-media-success__title-mark {
  position: absolute;
  right: 0;
  bottom: 70px;
  font-size: clamp(100px, 10vw, 202px);
  font-weight: 700;
  color: #333;
  -webkit-transform: translate(50%, 50%) rotate(15deg);
          transform: translate(50%, 50%) rotate(15deg);
}
.owned-media-success__description {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 100px;
  text-align: center;
}
.owned-media-success__description-text {
  position: relative;
  z-index: 1;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(24px, 10vw, 56px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  white-space: nowrap;
}
.owned-media-success__description-text.marker {
  background-color: #fff54b;
}
.owned-media-success__description-strong {
  font-size: clamp(28px, 10vw, 62px);
}
.owned-media-success__description-break {
  display: none;
}
.owned-media-success__diagram {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
}
@media (max-width: 768px) {
  .owned-media-success {
    padding: 40px 20px;
  }
  .owned-media-success__container {
    width: 100%;
    padding: 0;
  }
  .owned-media-success__header {
    margin-bottom: 30px;
  }
  .owned-media-success__title {
    text-align: center;
  }
  .owned-media-success__title-wrapper::before {
    top: 0;
  }
  .owned-media-success__title-main {
    font-size: clamp(40px, 8vw, 60px);
    line-height: 1.3;
    letter-spacing: 1px;
  }
  .owned-media-success__title-sub {
    display: inline-block;
    padding-right: 0;
    font-size: clamp(24px, 6vw, 36px);
    line-height: 1.3;
    letter-spacing: 0.5px;
  }
  .owned-media-success__title-mark {
    position: static;
    display: inline-block;
    margin-top: 10px;
    font-size: clamp(30px, 6vw, 40px);
    -webkit-transform: none;
            transform: none;
  }
  .owned-media-success__description {
    padding: 0;
    margin-bottom: 32px;
  }
  .owned-media-success__description-text {
    font-size: clamp(18px, 4vw, 24px);
    line-height: 1.4;
    white-space: normal;
  }
  .owned-media-success__description-text.marker {
    padding: 2px 4px;
    background-color: #fff54b;
    border-radius: 4px;
  }
  .owned-media-success__description-strong {
    font-size: clamp(20px, 5vw, 28px);
  }
  .owned-media-success__description-break {
    display: block;
  }
  .owned-media-success__diagram {
    width: 100%;
    height: auto;
    padding: 0;
  }
  .owned-media-success__diagram img {
    width: 100%;
    height: auto;
    -o-object-fit: contain;
       object-fit: contain;
  }
}

.owned-media-service {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 700px, #b1e2d5 700px, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-service {
    padding: 80px 0 40px;
  }
}
.owned-media-service__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-service__container {
    max-width: 100%;
  }
}
.owned-media-service__header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-service__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-service__subtitle {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-service__subtitle::before, .owned-media-service__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-service__subtitle::before {
  margin-right: 8px;
}
.owned-media-service__subtitle::after {
  margin-left: 8px;
}
.owned-media-service__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-service__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-service__title {
    font-size: 28px;
  }
  .owned-media-service__subtitle {
    gap: 4px;
  }
  .owned-media-service__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-service__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-service__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-service__list {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 100px;
  width: 100%;
}
.owned-media-service__item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 56px;
  padding: 120px 110px 60px;
  background-color: #fff;
  border-radius: 34px;
  -webkit-box-shadow: 0 0 14px rgba(67, 226, 184, 0.6);
          box-shadow: 0 0 14px rgba(67, 226, 184, 0.6);
}
.owned-media-service__item:not(:last-child)::after {
  position: absolute;
  bottom: -80px;
  left: 50%;
  width: 140px;
  height: 50px;
  content: "";
  background-image: url("../images/s-owned/service-arrow.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-service__item-top {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 60px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-right: 200px;
}
.owned-media-service__item-bottom {
  width: 100%;
}
.owned-media-service__item-number {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 18px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.owned-media-service__item-number::before {
  position: absolute;
  top: -50px;
  left: 50%;
  width: 194px;
  height: 22px;
  content: "";
  background-image: radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), -webkit-gradient(linear, left top, right top, from(#d9d9d9), to(#d9d9d9));
  background-image: radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), radial-gradient(circle, #d9d9d9 11px, transparent 11px), linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%);
  background-repeat: no-repeat;
  background-position: 0 0, 28px 0, 57px 0, 86px 0, 115px 0, 143px 0, 172px 0, 11px 10px;
  background-size: 22px 22px, 22px 22px, 22px 22px, 22px 22px, 22px 22px, 22px 22px, 22px 22px, 172px 3px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-service__item-number-main {
  font-size: clamp(80px, 10vw, 100px);
  font-weight: 500;
  line-height: 1;
  color: #7dc8b6;
  letter-spacing: 1px;
}
.owned-media-service__item-number-sub {
  position: relative;
  margin-top: -25px;
  font-family: "SF Pro Text", helvetica, sans-serif;
  font-size: 21px;
  font-weight: 500;
  line-height: 1;
  color: #7dc8b6;
  letter-spacing: 0.21px;
}
.owned-media-service__item-number-sub::before, .owned-media-service__item-number-sub::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-service__item-number-sub::before {
  margin-right: 8px;
}
.owned-media-service__item-number-sub::after {
  margin-left: 8px;
}
.owned-media-service__item-content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  max-width: 715px;
}
.owned-media-service__item-title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(40px, 2.3vw, 45px);
  font-weight: 700;
}
.owned-media-service__item-title-normal {
  color: #333;
  letter-spacing: 0.2px;
}
.owned-media-service__item-title-accent {
  font-size: clamp(50px, 2.3vw, 55px);
  color: #fa6b58;
  letter-spacing: 0.3px;
}
.owned-media-service__item-description {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.92;
  color: #333;
  letter-spacing: 0.24px;
}
.owned-media-service__item-icon {
  position: absolute;
  top: -160px;
  right: -10%;
  width: clamp(280px, 25vw, 365px);
  height: clamp(200px, 25vw, 250px);
  -o-object-fit: contain;
     object-fit: contain;
}
.owned-media-service__item-icon--01 {
  width: clamp(280px, 25vw, 365px);
  height: clamp(200px, 25vw, 250px);
}
.owned-media-service__item-icon--02 {
  width: clamp(280px, 25vw, 343px);
  height: clamp(200px, 25vw, 296px);
}
.owned-media-service__item-icon--03 {
  width: clamp(280px, 25vw, 385px);
  height: clamp(200px, 25vw, 247px);
}
.owned-media-service__item-icon--04 {
  width: clamp(280px, 25vw, 401px);
  height: clamp(200px, 25vw, 296px);
}
.owned-media-service__item-icon--05 {
  width: clamp(280px, 25vw, 422px);
  height: clamp(200px, 25vw, 268px);
}
.owned-media-service__item-icon--06 {
  width: clamp(280px, 25vw, 439px);
  height: clamp(200px, 25vw, 247px);
}
.owned-media-service__item-icon--07 {
  width: clamp(280px, 25vw, 498px);
  height: clamp(200px, 25vw, 223px);
}
@media (max-width: 768px) {
  .owned-media-service {
    min-height: auto;
    padding: 40px 20px;
  }
  .owned-media-service__container {
    padding: 0;
  }
  .owned-media-service__title {
    font-size: clamp(32px, 6vw, 40px);
    letter-spacing: 0.4px;
  }
  .owned-media-service__subtitle-text {
    font-size: clamp(18px, 4vw, 20px);
    letter-spacing: 0.2px;
  }
  .owned-media-service__list {
    gap: 40px;
    padding: 0;
  }
  .owned-media-service__item {
    gap: 30px;
    padding: 40px 20px;
    border-radius: 20px;
  }
  .owned-media-service__item:not(:last-child)::after {
    display: none;
  }
  .owned-media-service__item-top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 30px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding-right: 0;
    margin-right: 0;
  }
  .owned-media-service__item-bottom {
    text-align: center;
  }
  .owned-media-service__item-number {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    padding-top: 30px;
  }
  .owned-media-service__item-number::before {
    position: absolute;
    top: 0;
    left: 50%;
    height: 22px;
    content: "";
    background-image: radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), -webkit-gradient(linear, left top, right top, from(#d9d9d9), to(#d9d9d9));
    background-image: radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), radial-gradient(circle, #d9d9d9 7.5px, transparent 7.5px), linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%);
    background-repeat: no-repeat;
    background-position: 20px 0, 65px 0, 110px 0, 155px 0, 200px 0, 27.5px 9px;
    background-size: 15px 15px, 15px 15px, 15px 15px, 15px 15px, 15px 15px, 165px 2px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
  .owned-media-service__item-number-main {
    font-size: clamp(28px, 6vw, 32px);
    font-size: 64px;
    line-height: 1;
  }
  .owned-media-service__item-number-sub {
    margin-top: 0;
    font-size: clamp(12px, 3vw, 14px);
    letter-spacing: 0.14px;
  }
  .owned-media-service__item-number-sub::before, .owned-media-service__item-number-sub::after {
    display: inline-block;
    width: 10px;
    height: 3px;
    content: "";
    background-color: #3ab795;
    border-radius: 2px;
  }
  .owned-media-service__item-number-sub::before {
    margin-right: 6px;
  }
  .owned-media-service__item-number-sub::after {
    margin-left: 6px;
  }
  .owned-media-service__item-content {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    max-width: none;
    padding-top: 0;
  }
  .owned-media-service__item-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    margin-bottom: 15px;
    font-size: clamp(20px, 5vw, 24px);
    line-height: 1.5;
  }
  .owned-media-service__item-title-normal {
    text-align: center;
    letter-spacing: 0;
  }
  .owned-media-service__item-title-accent {
    font-size: clamp(24px, 6vw, 28px);
    text-align: center;
    letter-spacing: 0;
  }
  .owned-media-service__item-description {
    font-size: clamp(14px, 3.5vw, 16px);
    line-height: 1.8;
    letter-spacing: 0.16px;
  }
  .owned-media-service__item-icon {
    position: static;
    width: 100%;
    max-width: 200px;
    height: auto;
    margin: 0 auto;
  }
  .owned-media-service__flow-bg {
    display: none;
  }
}

.owned-media-support {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 750px, #b1e2d5 750px, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-support {
    padding: 80px 0 40px;
  }
}
.owned-media-support__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-support__container {
    max-width: 100%;
  }
}
.owned-media-support__header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-support__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-support__subtitle {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-support__subtitle::before, .owned-media-support__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-support__subtitle::before {
  margin-right: 8px;
}
.owned-media-support__subtitle::after {
  margin-left: 8px;
}
.owned-media-support__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-support__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-support__title {
    font-size: 28px;
  }
  .owned-media-support__subtitle {
    gap: 4px;
  }
  .owned-media-support__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-support__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-support__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-support__grid {
  display: grid;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  grid-template-columns: repeat(2, 1fr);
  gap: 38px;
  margin: 0 auto;
}
.owned-media-support__item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 18px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 634px;
  height: 679px;
  padding: 46px 8px 8px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 43px;
  -webkit-box-shadow: 0 0 14px rgba(0, 0, 0, 0.25);
          box-shadow: 0 0 14px rgba(0, 0, 0, 0.25);
}
.owned-media-support__item-title-area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 90%;
  height: 155px;
  background-color: #5f6061;
  border-radius: 13px;
}
.owned-media-support__item-title {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 34px;
  font-weight: 700;
  line-height: 54px;
  color: #fff;
  text-align: center;
  white-space: nowrap;
}
.owned-media-support__item-images {
  position: relative;
  bottom: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 336px;
  min-height: 336px;
  padding: 27px;
  background-color: #e1ede8;
  border-radius: 10px 10px 36px 36px;
}
.owned-media-support__item-image {
  overflow: hidden;
  border-radius: 14px;
}
.owned-media-support__item-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.owned-media-support__item-image--wide {
  width: 100%;
  max-width: none;
}
.owned-media-support__item-content {
  width: 540px;
  text-align: center;
}
.owned-media-support__item-text {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  line-height: 44px;
  color: #333;
  text-align: center;
  letter-spacing: 0.24px;
}
.owned-media-support__item-normal {
  font-weight: 500;
  letter-spacing: 0.06px;
}
.owned-media-support__item-accent {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 0.08px;
}
@media (max-width: 768px) {
  .owned-media-support {
    padding: 80px 0 60px;
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 450px, #b1e2d5 450px, #b1e2d5 100%);
  }
  .owned-media-support::before {
    height: calc(100% - 200px);
  }
  .owned-media-support__header {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 24px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    margin-bottom: 80px;
    text-align: center;
  }
  .owned-media-support__title {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(52px, 10vw, 80px);
    font-weight: 700;
    color: #3ab795;
  }
  .owned-media-support__subtitle {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 12px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .owned-media-support__subtitle::before, .owned-media-support__subtitle::after {
    display: inline-block;
    width: 15px;
    height: 4px;
    content: "";
    background-color: #3ab795;
    border-radius: 2px;
  }
  .owned-media-support__subtitle::before {
    margin-right: 8px;
  }
  .owned-media-support__subtitle::after {
    margin-left: 8px;
  }
  .owned-media-support__subtitle-text {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 38px;
    font-weight: 500;
    line-height: 1;
    color: #3ab795;
    letter-spacing: 0.38px;
  }
}
@media (max-width: 768px) and (max-width: 768px) {
  .owned-media-support__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-support__title {
    font-size: 28px;
  }
  .owned-media-support__subtitle {
    gap: 4px;
  }
  .owned-media-support__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-support__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-support__subtitle-text {
    font-size: 24px;
  }
}
@media (max-width: 768px) {
  .owned-media-support__grid {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    grid-template-columns: 1fr;
    gap: 40px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .owned-media-support__item {
    width: 100%;
    height: auto;
    padding-top: 20px;
    border-radius: 20px;
  }
  .owned-media-support__item-title-area {
    width: 95%;
    height: 92px;
    padding-right: 12px;
    padding-left: 12px;
  }
  .owned-media-support__item-title {
    max-width: 100%;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0.8px;
    white-space: wrap;
  }
  .owned-media-support__item-normal {
    font-size: 20px;
  }
  .owned-media-support__item-accent {
    font-size: 22px;
    font-weight: 700;
  }
  .owned-media-support__item-images {
    position: relative;
    bottom: auto;
    left: auto;
    width: 100%;
    height: unset;
    min-height: unset;
    border-radius: 0 0 20px 20px;
  }
  .owned-media-support__item-content {
    position: relative;
    bottom: auto;
    left: auto;
    width: 95%;
  }
  .owned-media-support__item-text {
    font-size: 16px;
    line-height: 24px;
  }
}

.owned-media-merit {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #f9f9f9;
}
@media (max-width: 768px) {
  .owned-media-merit {
    padding: 80px 0 40px;
  }
}
.owned-media-merit__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-merit__container {
    max-width: 100%;
  }
}
.owned-media-merit__decorations {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.owned-media-merit__decoration {
  position: absolute;
  width: 410px;
  height: 413px;
}
.owned-media-merit__decoration--1 {
  top: 531px;
  left: 273px;
}
.owned-media-merit__decoration--2 {
  top: 1195px;
  right: 297px;
}
.owned-media-merit__decoration--3 {
  top: 1910px;
  left: 273px;
}
.owned-media-merit__decoration--4 {
  top: 2555px;
  right: 297px;
}
.owned-media-merit__decoration--5 {
  top: 3270px;
  left: 273px;
}
.owned-media-merit__header {
  position: relative;
  z-index: 2;
  margin-bottom: 120px;
  text-align: center;
}
.owned-media-merit__brand {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 24px;
}
.owned-media-merit__brand-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 88px;
  padding: 12px 24px;
  content: "";
  background-color: #3ab795;
  border-radius: 44px;
}
.owned-media-merit__brand-text::before {
  position: absolute;
  bottom: -16px;
  left: 50%;
  z-index: -1;
  display: block;
  width: 32px;
  height: 32px;
  content: "";
  background-color: #3ab795;
  -webkit-transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
          transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
}
.owned-media-merit__brand-name {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 0.32px;
}
.owned-media-merit__brand-suffix {
  font-size: 28px;
  letter-spacing: 0.08px;
}
.owned-media-merit__brand-service {
  position: absolute;
  top: 4px;
  left: 169px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.28px;
  white-space: nowrap;
}
.owned-media-merit__catch-line1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 18px;
}
.owned-media-merit__catch-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 55px;
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  letter-spacing: 0.55px;
}
.owned-media-merit__catch-wo {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 42px;
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  letter-spacing: 0.42px;
}
.owned-media-merit__catch-save {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 83px;
  font-weight: 900;
  line-height: 1.2;
  color: #3ab795;
  letter-spacing: 0.83px;
}
.owned-media-merit__title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-merit__title-number {
  font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
  font-size: 160px;
  font-weight: 590;
  text-align: center;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  -webkit-background-clip: text;
          background-clip: text;
  -webkit-text-fill-color: transparent;
}
.owned-media-merit__title-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
}
.owned-media-merit__title-suffix {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 86px;
  font-weight: 500;
  line-height: 1.2;
  color: #333;
  letter-spacing: 0.86px;
}
.owned-media-merit__title-main {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 86px;
  font-weight: 500;
  line-height: 1.2;
  color: #333;
}
.owned-media-merit__title-accent {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 106px;
  font-weight: 700;
  line-height: 1.2;
  color: #3ab795;
  letter-spacing: -12px;
}
.owned-media-merit__list {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 120px;
}
.owned-media-merit__item {
  position: relative;
  width: 100%;
}
.owned-media-merit__item-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.owned-media-merit__item-image {
  position: relative;
  z-index: 3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: clamp(487px, 25vw, 567px);
  height: clamp(487px, 25vw, 567px);
  background-image: url("../images/s-owned/subtract-9.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.owned-media-merit__item-image::before {
  position: absolute;
  top: -15%;
  left: -5%;
  z-index: -1;
  width: 100%;
  max-width: 410px;
  height: 100%;
  max-height: 412px;
  content: "";
  background-image: url("../images/s-owned/merit-bg.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.owned-media-merit__item-img {
  position: relative;
  display: inline-block;
  width: 487px;
  height: auto;
  border-radius: 25px;
  -webkit-box-shadow: 0 0 24px rgba(90, 134, 151, 0.5);
          box-shadow: 0 0 24px rgba(90, 134, 151, 0.5);
}
.owned-media-merit__item-text-area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  max-width: 700px;
}
.owned-media-merit__item-header {
  margin-bottom: 50px;
}
.owned-media-merit__item-number {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
.owned-media-merit__item-number-text {
  font-family: "SF Pro Text", "Noto Sans JP", helvetica, sans-serif;
  font-size: 160px;
  font-weight: 300;
  color: #3ab795;
  letter-spacing: 1.6px;
}
.owned-media-merit__item-number-icon {
  position: relative;
  width: 128px;
  height: 127px;
}
.owned-media-merit__item-number-bg {
  position: absolute;
  top: 0;
  left: 3px;
  width: 118px;
  height: 127px;
}
.owned-media-merit__item-number-label {
  position: absolute;
  top: 37px;
  left: 0;
  width: 100%;
  font-family: "SF Pro Text", "Noto Sans JP", helvetica, sans-serif;
  font-size: 49px;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  letter-spacing: 0.49px;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  -webkit-background-clip: text;
          background-clip: text;
  -webkit-text-fill-color: transparent;
}
.owned-media-merit__item-text {
  width: 100%;
}
.owned-media-merit__item-title {
  margin: 0 0 30px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 45px;
  font-weight: 700;
  line-height: 1.5;
}
.owned-media-merit__item-title-main {
  font-size: 55px;
  color: #fa6b58;
}
.owned-media-merit__item-title-sub {
  font-size: 45px;
  color: #333;
}
.owned-media-merit__item-title-accent {
  font-size: 45px;
  font-weight: 900;
  color: #333;
}
.owned-media-merit__item-desc {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.9;
  color: #333;
  text-shadow: 0 0 9px #fff;
  letter-spacing: 0.24px;
}
.owned-media-merit__item--02 .owned-media-merit__item-content, .owned-media-merit__item--04 .owned-media-merit__item-content {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}
@media (max-width: 768px) {
  .owned-media-merit {
    min-height: auto;
    padding: 80px 0 60px;
  }
  .owned-media-merit__container {
    width: 100%;
    max-width: 100%;
    padding: 0 20px;
    margin: 0 auto;
  }
  .owned-media-merit__decoration {
    display: none;
  }
  .owned-media-merit__header {
    width: 100%;
    margin-bottom: 60px;
    text-align: center;
  }
  .owned-media-merit__brand {
    width: 100%;
    margin-bottom: 0;
  }
  .owned-media-merit__brand-text {
    width: 100%;
    height: 60px;
    padding: 8px 20px;
    margin: 0 auto;
    font-size: 20px;
  }
  .owned-media-merit__brand-text::before {
    bottom: -12px;
    width: 24px;
    height: 24px;
  }
  .owned-media-merit__brand-name {
    font-size: 20px;
  }
  .owned-media-merit__brand-suffix {
    font-size: 18px;
  }
  .owned-media-merit__brand-service {
    position: static;
    margin-top: 5px;
    font-size: 18px;
  }
  .owned-media-merit__catch {
    width: 100%;
    margin-bottom: 20px;
  }
  .owned-media-merit__catch-line1 {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    gap: 0;
    width: 100%;
  }
  .owned-media-merit__catch-text {
    font-size: clamp(18px, 3vw, 22px);
    white-space: nowrap;
  }
  .owned-media-merit__catch-divider {
    width: 100px;
    height: 3px;
  }
  .owned-media-merit__catch-wo {
    font-size: clamp(18px, 3vw, 22px);
  }
  .owned-media-merit__catch-save {
    font-size: clamp(32px, 4vw, 48px);
    white-space: nowrap;
  }
  .owned-media-merit__title {
    gap: 0;
    width: 100%;
  }
  .owned-media-merit__title-number {
    font-size: clamp(60px, 12vw, 80px);
  }
  .owned-media-merit__title-text {
    gap: 5px;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .owned-media-merit__title-suffix, .owned-media-merit__title-main {
    font-size: clamp(32px, 6vw, 42px);
  }
  .owned-media-merit__title-accent {
    font-size: clamp(42px, 8vw, 52px);
  }
  .owned-media-merit__list {
    gap: 60px;
    width: 100%;
  }
  .owned-media-merit__item {
    width: 100%;
  }
  .owned-media-merit__item-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
    gap: 30px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    min-height: auto;
  }
  .owned-media-merit__item-image {
    -ms-flex-negative: 0;
        flex-shrink: 0;
    width: 100%;
    max-width: 350px;
    height: 350px;
    margin: 0 auto;
  }
  .owned-media-merit__item-img {
    width: 90%;
    height: auto;
  }
  .owned-media-merit__item-text-area {
    width: 100%;
    max-width: none;
    text-align: center;
  }
  .owned-media-merit__item-header {
    width: 100%;
    margin-bottom: 30px;
  }
  .owned-media-merit__item-number {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
  }
  .owned-media-merit__item-number-text {
    font-size: clamp(60px, 12vw, 80px);
  }
  .owned-media-merit__item-number-icon {
    width: 80px;
    height: 80px;
  }
  .owned-media-merit__item-number-bg {
    width: 74px;
    height: 80px;
  }
  .owned-media-merit__item-number-label {
    top: 20px;
    font-size: 24px;
  }
  .owned-media-merit__item-text {
    width: 100%;
    text-align: center;
  }
  .owned-media-merit__item-title {
    width: 100%;
    font-size: clamp(20px, 4vw, 24px);
    text-align: center;
  }
  .owned-media-merit__item-title-main {
    font-size: clamp(24px, 5vw, 28px);
  }
  .owned-media-merit__item-title-sub {
    font-size: clamp(20px, 4vw, 24px);
  }
  .owned-media-merit__item-title-accent {
    font-size: clamp(24px, 5vw, 28px);
  }
  .owned-media-merit__item-desc {
    width: 100%;
    font-size: clamp(14px, 3vw, 16px);
    line-height: 1.7;
    text-align: center;
  }
  .owned-media-merit__item--02 .owned-media-merit__item-content, .owned-media-merit__item--04 .owned-media-merit__item-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
  }
}

.owned-media-system-support {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 770px, #b1e2d5 770px, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-system-support {
    padding: 80px 0 40px;
  }
}
.owned-media-system-support__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-system-support__container {
    max-width: 100%;
  }
}
.owned-media-system-support__header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-system-support__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-system-support__subtitle {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-system-support__subtitle::before, .owned-media-system-support__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-system-support__subtitle::before {
  margin-right: 8px;
}
.owned-media-system-support__subtitle::after {
  margin-left: 8px;
}
.owned-media-system-support__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-system-support__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-system-support__title {
    font-size: 28px;
  }
  .owned-media-system-support__subtitle {
    gap: 4px;
  }
  .owned-media-system-support__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-system-support__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-system-support__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-system-support__visual {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
}
.owned-media-system-support__visual .visual-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -ms-flex-pack: distribute;
      justify-content: space-around;
  width: 100%;
  height: 100%;
}
.owned-media-system-support__visual .visual-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 32px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  min-height: 300px;
}
.owned-media-system-support__visual .visual-left__text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 96px;
  padding: 34px 56px;
  font-size: 45px;
  font-weight: 700;
  color: #fff;
  background-color: #7dc8b6;
  border-radius: 48px;
}
.owned-media-system-support__visual .visual-left__image {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  gap: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  padding: 100px 48px;
  background-color: #fff;
  border-radius: 18px;
  -webkit-box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
          box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
}
.owned-media-system-support__visual .visual-left__image::after {
  position: absolute;
  top: 30%;
  right: -290px;
  width: 230px;
  height: 140px;
  content: "";
  background-image: url("/wp-content/themes/appmart/assets/images/s-owned/support-arrow.png");
  background-repeat: no-repeat;
  background-size: contain;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.owned-media-system-support__visual .visual-left__image img {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -o-object-fit: cover;
     object-fit: cover;
}
.owned-media-system-support__visual .visual-left__image img:first-child {
  width: 382px;
  height: 376px;
}
.owned-media-system-support__visual .visual-left__image img:last-child {
  width: 233px;
  height: 279px;
}
.owned-media-system-support__visual .visual-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 32px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  min-height: 300px;
}
.owned-media-system-support__visual .visual-right__text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 96px;
  padding: 34px 56px;
  font-size: 45px;
  font-weight: 700;
  color: #fff;
  background-color: #7dc8b6;
  border-radius: 48px;
}
.owned-media-system-support__visual .visual-right__image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  padding: 100px 48px;
  background-color: #fff;
  border-radius: 18px;
  -webkit-box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
          box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
}
.owned-media-system-support__visual .visual-right__image img {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  width: 233px;
  height: 279px;
  -o-object-fit: cover;
     object-fit: cover;
}
.owned-media-system-support__team-image {
  width: 1370px;
  height: 712px;
  -o-object-fit: contain;
     object-fit: contain;
}
.owned-media-system-support__message {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  margin-top: 64px;
}
.owned-media-system-support__message-bubble {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 140px;
  padding: 56px;
  background-color: #fff;
  border-radius: 70px;
  -webkit-box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
          box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
}
.owned-media-system-support__message-bubble::before {
  position: absolute;
  top: -35px;
  left: 65%;
  z-index: 0;
  width: 70px;
  height: 70px;
  content: "";
  background-color: #fff;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.owned-media-system-support__message-accent {
  position: relative;
  z-index: 2;
  font-size: 42px;
  font-weight: 900;
  color: #fa6b58;
  letter-spacing: 0.18px;
  -webkit-text-stroke: 1px #fa6b58;
}
.owned-media-system-support__message-accent::before {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 12px;
  content: "";
  background-color: #fff54b;
}
.owned-media-system-support__message-text {
  font-size: 42px;
  font-weight: 900;
  color: #333;
  letter-spacing: 0.18px;
}
@media (max-width: 768px) {
  .owned-media-system-support {
    padding: 60px 0 40px;
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 400px, #b1e2d5 400px, #b1e2d5 100%);
  }
  .owned-media-system-support__container {
    padding: 0 20px;
  }
  .owned-media-system-support__header {
    margin-bottom: 80px;
  }
  .owned-media-system-support__visual {
    gap: 40px;
  }
  .owned-media-system-support__visual .visual-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
    gap: 20px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .owned-media-system-support__visual .visual-left {
    position: relative;
    gap: 0;
    width: 100%;
    min-height: auto;
  }
  .owned-media-system-support__visual .visual-left__text {
    position: absolute;
    bottom: -25px;
    left: 50%;
    z-index: 2;
    width: 70%;
    height: 50px;
    padding: 20px 30px;
    font-size: clamp(24px, 6vw, 32px);
    border-radius: 30px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
  .owned-media-system-support__visual .visual-left__image {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
    gap: 20px;
    width: 100%;
    padding: 40px 20px;
    border-radius: 12px;
  }
  .owned-media-system-support__visual .visual-left__image::after {
    top: -15px;
    left: 50%;
    z-index: 1;
    width: 100px;
    height: 40px;
    content: "";
    -webkit-transform: translateX(-50%) rotate(90deg);
            transform: translateX(-50%) rotate(90deg);
  }
  .owned-media-system-support__visual .visual-left__image img:first-child {
    width: clamp(200px, 50vw, 280px);
    height: clamp(196px, 49vw, 275px);
  }
  .owned-media-system-support__visual .visual-left__image img:last-child {
    width: clamp(120px, 30vw, 180px);
    height: clamp(144px, 36vw, 216px);
  }
  .owned-media-system-support__visual .visual-right {
    position: relative;
    gap: 0;
    width: 100%;
    min-height: auto;
  }
  .owned-media-system-support__visual .visual-right__text {
    position: absolute;
    top: -25px;
    left: 50%;
    z-index: 2;
    width: 70%;
    height: 50px;
    padding: 20px 30px;
    font-size: clamp(24px, 6vw, 32px);
    border-radius: 30px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
  .owned-media-system-support__visual .visual-right__image {
    width: 100%;
    padding: 40px 20px;
    border-radius: 12px;
    -webkit-box-shadow: 0 0 8px 0 rgba(58, 183, 149, 0.3);
            box-shadow: 0 0 8px 0 rgba(58, 183, 149, 0.3);
  }
  .owned-media-system-support__visual .visual-right__image img {
    width: clamp(120px, 30vw, 180px);
    height: clamp(144px, 36vw, 216px);
  }
  .owned-media-system-support__team-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    margin: 40px 0;
  }
  .owned-media-system-support__message {
    margin-top: 40px;
  }
  .owned-media-system-support__message-bubble {
    z-index: 3;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
    height: auto;
    padding: 22px 16px;
    border-radius: 16px;
  }
  .owned-media-system-support__message-bubble::before {
    top: 0;
    left: 60%;
    width: 40px;
    height: 40px;
    -webkit-transform: translateX(-50%) rotate(45deg) skew(30deg, 30deg);
            transform: translateX(-50%) rotate(45deg) skew(30deg, 30deg);
  }
  .owned-media-system-support__message-accent {
    z-index: 3;
    font-size: 24px;
    font-weight: 900;
    -webkit-text-stroke: unset;
    background-color: #fff54b;
  }
  .owned-media-system-support__message-accent::before {
    display: none;
  }
  .owned-media-system-support__message-text {
    z-index: 3;
    font-size: 24px;
    font-weight: 500;
  }
}

.owned-media-case-study {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #f9f9f9;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f9f9f9), color-stop(60%, #f9f9f9), color-stop(60%, #b1e2d5), to(#b1e2d5));
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 60%, #b1e2d5 60%, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-case-study {
    padding: 80px 0 40px;
  }
}
.owned-media-case-study__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-case-study__container {
    max-width: 100%;
  }
}
.owned-media-case-study__header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-case-study__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-case-study__subtitle {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-case-study__subtitle::before, .owned-media-case-study__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-case-study__subtitle::before {
  margin-right: 8px;
}
.owned-media-case-study__subtitle::after {
  margin-left: 8px;
}
.owned-media-case-study__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-case-study__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-case-study__title {
    font-size: 28px;
  }
  .owned-media-case-study__subtitle {
    gap: 4px;
  }
  .owned-media-case-study__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-case-study__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-case-study__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-case-study__content {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
}
.owned-media-case-study__service-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
}
.owned-media-case-study__service-title {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 67px;
  padding: 20px 48px;
  margin: 0 auto 80px;
  background-color: #fa6b58;
}
.owned-media-case-study__service-category, .owned-media-case-study__service-name {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 45px;
  font-weight: 700;
  line-height: 28px;
  color: #fff;
  letter-spacing: 0.45px;
  white-space: nowrap;
}
.owned-media-case-study__service-description {
  margin-bottom: 32px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 42px;
  font-weight: 500;
  line-height: 28px;
  color: #191919;
  text-align: center;
  letter-spacing: 0.42px;
}
.owned-media-case-study__service-period {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 500;
  line-height: 28px;
  color: #989898;
  text-align: center;
  letter-spacing: 0.24px;
}
.owned-media-case-study__results-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  padding: 90px;
  margin-top: 48px;
  background-color: #fff;
  border: 6px solid #3c8b86;
  border-radius: 20px;
  -webkit-box-shadow: 0 0 35px 0 rgb(60, 139, 134);
          box-shadow: 0 0 35px 0 rgb(60, 139, 134);
}
.owned-media-case-study__results-container .result-wrapper {
  width: 100%;
}
.owned-media-case-study__results-container .result-wrapper .result-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
}
.owned-media-case-study__results-container .result-wrapper .result-inner__main {
  font-size: 46px;
  font-weight: 500;
  color: #191919;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-wrapper .result-inner__accent {
  font-size: 46px;
  font-weight: 700;
  color: #191919;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-wrapper .result-inner__strong {
  font-size: 74px;
  font-weight: 900;
  color: #fa6b58;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-inner__description {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.owned-media-case-study__results-container .result-inner__description .result-container {
  position: absolute;
  top: 700px;
  left: 200px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  padding: 24px;
  border: 2px solid #3ab795;
  border-radius: 20px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-top {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 100%;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 3px solid #3ab795;
}
.owned-media-case-study__results-container .result-inner__description .result-container-top::after {
  position: absolute;
  bottom: -22px;
  left: 50%;
  width: 18px;
  height: 18px;
  content: "";
  background-color: #fff;
  border-bottom: 3px solid #3ab795;
  -webkit-transform: translateY(-50%) rotate(45deg);
          transform: translateY(-50%) rotate(45deg);
}
.owned-media-case-study__results-container .result-inner__description .result-container-top__text {
  font-size: 28px;
  font-weight: 700;
  color: #3ab795;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom {
  position: relative;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  width: 100%;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  height: 32px;
  font-weight: 700;
  color: #3ab795;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text .normal {
  font-size: 24px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text .small {
  font-size: 19px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text .strong {
  font-size: 53px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text .design {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 48px;
  height: 48px;
  padding: 4px;
  margin: 0 8px;
  background-color: #3ab795;
  border-radius: 8px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text .design > .text-lg {
  font-size: 28px;
  color: #fff;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text .design > .text-md {
  font-size: 24px;
  color: #fff;
}
.owned-media-case-study__results-container .result-graph {
  width: 100%;
  margin-top: 48px;
}
.owned-media-case-study__results-container .result-graph img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-case-study {
    padding: 80px 0 320px;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f9f9f9), color-stop(45%, #f9f9f9), color-stop(45%, #b1e2d5), to(#b1e2d5));
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 45%, #b1e2d5 45%, #b1e2d5 100%);
  }
  .owned-media-case-study__container {
    padding: 0 20px;
  }
  .owned-media-case-study__header {
    margin-bottom: 60px;
  }
  .owned-media-case-study__service-title {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    gap: 0;
    width: 100%;
    height: auto;
    padding: 22px;
    margin: 0 auto 40px;
  }
  .owned-media-case-study__service-category, .owned-media-case-study__service-name {
    font-size: 22px;
    line-height: 1.2;
    white-space: nowrap;
  }
  .owned-media-case-study__service-description {
    margin-bottom: 20px;
    font-size: clamp(20px, 5vw, 28px);
    line-height: 1.4;
  }
  .owned-media-case-study__service-period {
    font-size: clamp(16px, 4vw, 20px);
    line-height: 1.4;
  }
  .owned-media-case-study__content {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .owned-media-case-study__results-container {
    position: relative;
    max-width: 336px;
    height: 439px;
    padding: 40px 20px;
    margin-top: 220px;
    border: 2px solid #3ab795;
    border-width: 4px;
    border-radius: 15px;
    -webkit-box-shadow: 0 0 4px 0 rgba(60, 139, 134, 0.2);
            box-shadow: 0 0 4px 0 rgba(60, 139, 134, 0.2);
  }
  .owned-media-case-study__results-container .result-wrapper {
    position: absolute;
    top: -190px;
    left: 0;
    z-index: 3;
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border: 2px solid #b1e2d5;
    border-radius: 15px;
    -webkit-box-shadow: 0 0 8px 0 rgba(60, 139, 134, 0.2);
            box-shadow: 0 0 8px 0 rgba(60, 139, 134, 0.2);
  }
  .owned-media-case-study__results-container .result-wrapper::after {
    position: absolute;
    bottom: -20px;
    left: 30%;
    z-index: 0;
    width: 20px;
    height: 20px;
    content: "";
    background-color: #fff;
    -webkit-transform: translateY(-50%) rotate(45deg) skew(30deg, 30deg);
            transform: translateY(-50%) rotate(45deg) skew(30deg, 30deg);
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    line-height: 1.2;
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner__main, .owned-media-case-study__results-container .result-wrapper .result-inner__accent {
    font-size: 24px;
    text-align: center;
    white-space: nowrap;
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner__strong {
    font-size: 32px;
    text-align: center;
    white-space: nowrap;
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner__break {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    height: 0;
  }
  .owned-media-case-study__results-container .result-inner__description {
    width: 100%;
    height: 180px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container {
    position: absolute;
    top: 430px;
    left: 0;
    z-index: 3;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    width: 100%;
    height: 200px;
    padding: 20px;
    margin-top: 30px;
    background-color: #fff;
    border-width: 2px;
    border-radius: 15px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-top {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    height: unset;
    padding-bottom: 8px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-top__text {
    font-size: 24px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 8px;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    height: auto;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: flex-end;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
    line-height: 1;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: flex-end;
    line-height: 1;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.normal {
    font-size: 20px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.small {
    font-size: 16px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.strong {
    font-size: 32px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design {
    position: relative;
    bottom: -8px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 42px;
    height: 42px;
    padding: 2px;
    margin: 0 4px;
    background-color: #3ab795;
    border-radius: 6px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design .text-lg {
    font-size: 28px;
    color: #fff;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design .text-md {
    font-size: 20px;
    color: #fff;
  }
  .owned-media-case-study__results-container .result-graph {
    margin-top: 0;
  }
}

.owned-media-contract-flow {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #f9f9f9;
}
@media (max-width: 768px) {
  .owned-media-contract-flow {
    padding: 80px 0 40px;
  }
}
.owned-media-contract-flow__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-contract-flow__container {
    max-width: 100%;
  }
}
.owned-media-contract-flow__header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-contract-flow__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-contract-flow__subtitle {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-contract-flow__subtitle::before, .owned-media-contract-flow__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-contract-flow__subtitle::before {
  margin-right: 8px;
}
.owned-media-contract-flow__subtitle::after {
  margin-left: 8px;
}
.owned-media-contract-flow__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-contract-flow__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-contract-flow__title {
    font-size: 28px;
  }
  .owned-media-contract-flow__subtitle {
    gap: 4px;
  }
  .owned-media-contract-flow__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-contract-flow__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-contract-flow__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-contract-flow__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 72px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
}
.owned-media-contract-flow .flow-step {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  gap: 32px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  min-height: 254px;
  padding: 48px 48px 48px 0;
  background-color: #fff;
  -webkit-filter: drop-shadow(5px 5px 8px rgba(0, 0, 0, 0.3));
          filter: drop-shadow(5px 5px 8px rgba(0, 0, 0, 0.3));
  border-radius: 20px;
}
.owned-media-contract-flow .flow-step::after {
  position: absolute;
  bottom: -28px;
  left: 48%;
  width: 56px;
  height: 56px;
  content: "";
  background-color: #fff;
  -webkit-transform: rotate(135deg) skew(20deg, 20deg);
          transform: rotate(135deg) skew(20deg, 20deg);
}
.owned-media-contract-flow .flow-step__number {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  padding: 18px 48px 18px 32px;
  font-family: "Yu Gothic", "YuGothic", "Hiragino Kaku Gothic ProN", "Hiragino Sans", meiryo, sans-serif;
  font-size: 92px;
  font-size: 92px;
  font-weight: 700;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #7fd5bf;
  border-radius: 0 75px 75px 0;
}
.owned-media-contract-flow .flow-step__description {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  color: #5f6061;
}
.owned-media-contract-flow .flow-step__description-title {
  font-size: 54px;
  font-weight: 700;
  line-height: normal;
}
.owned-media-contract-flow .flow-step__description-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
.owned-media-contract-flow .flow-step__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 194px;
  height: auto;
}
.owned-media-contract-flow .flow-step__icon img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-contract-flow {
    padding: 60px 0;
  }
  .owned-media-contract-flow__container {
    padding: 0 20px;
  }
  .owned-media-contract-flow__header {
    margin-bottom: 60px;
  }
  .owned-media-contract-flow__content {
    gap: 40px;
  }
  .owned-media-contract-flow .flow-step {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 30px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    min-height: auto;
    padding: 30px 38px;
    overflow: hidden;
    border-radius: 8px;
    -webkit-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.08);
            box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.08);
  }
  .owned-media-contract-flow .flow-step::after {
    z-index: 1;
  }
  .owned-media-contract-flow .flow-step__number {
    position: absolute;
    top: -60px;
    left: -60px;
    width: 120px;
    height: 120px;
    padding: 15px 20px;
    font-size: clamp(40px, 8vw, 60px);
    border-radius: 50%;
  }
  .owned-media-contract-flow .flow-step__number-text {
    position: absolute;
    right: 20px;
    bottom: 20px;
    font-size: 28px;
    color: #fff;
  }
  .owned-media-contract-flow .flow-step__description {
    gap: 20px;
    text-align: center;
  }
  .owned-media-contract-flow .flow-step__description-title {
    font-size: clamp(28px, 6vw, 36px);
    line-height: 1.3;
  }
  .owned-media-contract-flow .flow-step__description-text {
    font-size: clamp(16px, 4vw, 20px);
    line-height: 1.5;
  }
  .owned-media-contract-flow .flow-step__icon {
    width: 120px;
    height: auto;
  }
}

.owned-media-faq {
  width: 100%;
  padding: 180px 0 100px;
  background-color: #fff;
}
@media (max-width: 768px) {
  .owned-media-faq {
    padding: 80px 0 40px;
  }
}
.owned-media-faq__container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  max-width: 1332px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-faq__container {
    max-width: 100%;
  }
}
.owned-media-faq__header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-faq__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-faq__subtitle {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.owned-media-faq__subtitle::before, .owned-media-faq__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-faq__subtitle::before {
  margin-right: 8px;
}
.owned-media-faq__subtitle::after {
  margin-left: 8px;
}
.owned-media-faq__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-faq__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-faq__title {
    font-size: 28px;
  }
  .owned-media-faq__subtitle {
    gap: 4px;
  }
  .owned-media-faq__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-faq__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-faq__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-faq .faq__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.owned-media-faq .faq__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 60px;
  padding: 40px 0;
  border-bottom: 1px solid #a6bfc3;
}
.owned-media-faq .faq__item .faq-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}
.owned-media-faq .faq__item .faq-text-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.owned-media-faq .faq__item .faq-text-wrap.answer {
  padding-left: 72px;
}
.owned-media-faq .faq__item .faq-text::before {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 46px;
  height: 46px;
  font-size: 28px;
  font-weight: 900;
  line-height: 1;
  color: #fff;
  border-radius: 7px;
}
.owned-media-faq .faq__item .faq-text.question::before {
  content: "Q";
  background-color: #3ab795;
}
.owned-media-faq .faq__item .faq-text.answer::before {
  content: "A";
  background-color: #a6bfc3;
}
.owned-media-faq .faq__item .faq-text-text {
  font-size: 38px;
  font-weight: 700;
  line-height: normal;
  color: #5f6061;
}
.owned-media-faq .faq__item .faq-text-text.question {
  color: #5f6061;
}
.owned-media-faq .faq__item .faq-text-text.answer {
  font-size: 24px;
  font-weight: 400;
  line-height: 1.5;
  color: #333;
}
@media (max-width: 768px) {
  .owned-media-faq {
    padding: 60px 0 40px;
  }
  .owned-media-faq__container {
    padding: 0 20px;
  }
  .owned-media-faq__header {
    margin-bottom: 40px;
  }
  .owned-media-faq__title {
    font-size: clamp(32px, 8vw, 40px);
    letter-spacing: -0.5px;
  }
  .owned-media-faq__subtitle {
    font-size: clamp(18px, 4vw, 20px);
    letter-spacing: 0.2px;
  }
  .owned-media-faq .faq__item {
    gap: 30px;
    padding: 30px 0;
    border-bottom: 1px solid #a6bfc3;
  }
  .owned-media-faq .faq__item .faq-text {
    gap: 15px;
  }
  .owned-media-faq .faq__item .faq-text-wrap {
    gap: 15px;
  }
  .owned-media-faq .faq__item .faq-text-wrap.answer {
    padding-left: 0;
  }
  .owned-media-faq .faq__item .faq-text::before {
    width: 36px;
    height: 36px;
    font-size: 20px;
    border-radius: 5px;
  }
  .owned-media-faq .faq__item .faq-text-text {
    font-size: clamp(18px, 4.5vw, 24px);
    line-height: 1.4;
  }
  .owned-media-faq .faq__item .faq-text-text.question {
    font-weight: 700;
    color: #5f6061;
  }
  .owned-media-faq .faq__item .faq-text-text.answer {
    font-size: clamp(16px, 4vw, 18px);
    font-weight: 400;
    line-height: 1.6;
    color: #333;
  }
}/*# sourceMappingURL=owned-media.css.map */