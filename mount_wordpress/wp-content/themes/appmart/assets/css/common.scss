@import './variable';
@import './reset';


html, body {
    font-family: '游ゴシック', 'Open Sans', sans-serif;
}
a {
    text-decoration: none;
    transition: all .3s;
    &:hover {
        color: $color--text--sub !important;
    }
}
a[target="_blank"] {
	color: $color--accent--orange !important;
    &:hover {
        opacity: 1 !important;
        color: #DD523F !important;
    }
}
img {
    display: block;
    pointer-events: none;
    // backface-visibility: hidden;
    // -webkit-backface-visibility: hidden;
}
h1, h2, h3, h4, h5 {
    // letter-spacing: 1px;
    color: $color--text;
    line-height: 1.2;
}
p {
    margin: 0;
    font-size: 16px;
    color: $color--text;
    line-height: 1.7;
}
.container {
    max-width: 1080px;
    margin: 0 auto;
    width: 100%;
    position: relative;
    box-sizing: border-box;
}
.btn {
    margin-top: 65px;
    text-align: center;
    a {
        margin: 0 auto;
        @include btn(25px 0, 360px, initial, initial, 6px);
        @include flex(center, center, initial);
        text-align: center;
        position: relative;
        display: inline-block;
        span {
            font-size: 18px;
            color: $color--white;
            font-weight: bold;
        }
        img {
            @include absolute(20px, auto, 50%, 30px, initial, initial, initial, translateY(-50%), 1);
            width: 20px;
            height: auto;
        }
        &:hover {
            transform: translateY(4px);
            box-shadow: none !important;
            opacity: 1;
        }
    }

    &__black {
        background-color: $color--text !important;
        box-shadow: 0 4px 0 $color--base--green !important;
    }

    &__orange {
        background-color: $color--accent--orange !important;
        box-shadow: 0 4px 0 #DD523F !important;
        width: 520px !important;
    }

    &__white {
        background-color: #fff !important;
        outline: 1px solid $color--accent--orange !important;
        outline-offset: -1px;
        box-shadow: 0 4px 0 #DD523F !important;
        width: 520px !important;
        span {
            color: $color--accent--orange !important;
        }
    }

    &__dl {
        width: 100% !important;
        padding-top: 14px !important;
        padding-bottom: 12px !important;
    }

    &.col2 a {
        @media (min-width: 768px){
            width: 360px !important;

            &:first-child {
                margin-right: 64px;
            }
        }
    }

}
.fadein__cont {
    opacity: 0;
    transform: translateY(10px);
    transition-duration: .4s;
    -webkit-transition-duration: .4s;
    transition-timing-function: linear;
    -webkit-transition-timing-function: linear;
    &.active {
        transform: translateY(0px);
        opacity: 1;
    }
}
.works {
    &__list {
        @include flex(initial, flex-start, wrap);
        margin-top: 85px;
    }

    &__item {
        width: calc(33.333% - 20px);
        background: $color--white;
        border-radius: 20px;
        padding-bottom: 30px;
        margin-bottom: 30px;
        margin-right: 30px;
        &:nth-of-type(3n) {
            margin-right: 0;
        }
        &::marker {
            display: none;
        }

        a {
            display: block;
            &:hover {
                .works__item__eyecatch {
                    .img {
                        transform: scale(1.1);
                    }
                }
            }
        }

        &__eyecatch {
            // height: 220px;
            position: relative;
            padding-top: 56%;
            overflow: hidden;
            border-radius: 20px 20px 0 0;
            .img {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border-radius: 20px 20px 0 0;
                transform: scale(1);
                transition: transform 0.5s;
                background-size: cover;
                background-repeat: no-repeat;
                background-position: center center;
            }
        }
        h4 {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0 25px;
        }
        p {
            font-size: 14px;
            text-align: center;
        }
    }
}


// ==============================================
// pageLoader
// ==============================================
.pageLoader {
    display: none;
    @include absolute(initial, initial, 0, 0, 0, 0, $color--base--blue, initial, 1000);
    position: fixed;

    &__inner {
        @include absolute(initial, initial, 0, 0, 0, 0, initial, initial, initial);
        @include flex(center, center, initial);
    }

    &__logo {

        img {
            width: 100px;
            height: 100%;
        }
    }

}
// ローディングアイコン
.spinner {
    margin: 0 auto 20px;
    width: 70px;
    height: 50px;
    text-align: center;
}
  .spinner > div {
      margin: 0 1px;
    background-color: #000;
    height: 100%;
    width: 6px;
    border-radius: 3px;
    display: inline-block;
    -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    animation: sk-stretchdelay 1.2s infinite ease-in-out;
  }

  .spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
  }

  .spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
  }

  .spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }

  .spinner .rect5 {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
  }

  @-webkit-keyframes sk-stretchdelay {
    0%, 40%, 100% { -webkit-transform: scaleY(0.4) }
    20% { -webkit-transform: scaleY(1.0) }
  }

  @keyframes sk-stretchdelay {
    0%, 40%, 100% {
      transform: scaleY(0.4);
      -webkit-transform: scaleY(0.4);
    }  20% {
      transform: scaleY(1.0);
      -webkit-transform: scaleY(1.0);
    }
  }
// ローディングアイコンはここまで


// ==============================================
// header
// ==============================================
.header {
    width: 100%;
    height: 80px;
    position: fixed;
    top: 0;
    left: 0;
    box-sizing: border-box;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, .16);
    background-color: $color--white;
    z-index: 999;

    .container {
        height: 100%;
        @include flex(space-between, center, initial);
    }

    &__left {
        a {
            h1 {
                width: 320px;
                img {
                    width: 100%;
                    height: auto;
                }
            }
        }
    }

    &__right {
        nav > ul {
            @include flex(initial, center, initial);
            & > li {
                margin-left: 40px;
                height: 80px;
                @include flex(initial, center, initial);
                a {
                    color: $color--text;
                    font-size: 16px;
                    font-weight: bold;
                    position: relative;
                    &:after {
                        content: '';
                        @include absolute(100%, 2px, initial, initial, -8px, 0, $color--text, scale(0, 1), initial);
                        transform-origin: left top;
                        transition: transform .3s;
                    }
                    &:hover {
                        opacity: 1 !important;
                        &::after {
                            transform: scale(1, 1);
                        }
                    }
                    &.current {
                        &:after {
                            transform: scale(1, 1);
                        }
                    }
                }
            }
        }
    }

    &__service {

        & > a {
            display: flex;
            align-items: center;
            img {
                margin-left: 8px;
                width: 8px;
                height: auto;
            }
        }

        &__sp {
            display: none;
            align-items: center;
            cursor: pointer;
            color: $color--text;
            font-size: 16px;
            font-weight: bold;
            img {
                margin-left: 8px;
                padding: 10px;
                width: 12px;
                height: auto;
                pointer-events: initial;
            }
        }

        &__modal {
            display: none;
            padding-top: 40px;
            padding-bottom: 40px;
            @include absolute(100%, initial, 80px, 0, initial, 0, $color--base--blue, initial, 999);
            position: fixed;

            .container ul {
                width: 100%;
                @include flex(space-between, center, wrap);
                li {
                    position: relative;
                    &:before {
                        content: '';
                        @include absolute(1px, 10px, 50%, -30px, initial, initial, $color--text--sub, translateY(-50%) rotate(15deg), 1);
                    }
                    &:last-child {
                        &:before {
                            display: none;
                        }
                    }

                    a {
                        font-weight: normal;
                        font-size: 13px;
                        display: inline-block;
                        text-align: center;
                    }
                }
            }
        }
    }

    &__recruit {
        display: none !important;
        a {
            color: $color--text !important;
            &:hover {
                color: $color--text !important;
            }
        }
    }

    &__contact a {
        @include btn(15px 30px, initial, $color--accent--orange, 0 4px 0 #DD523F, 6px);
        color: $color--white !important;
        &:hover {
            transform: translateY(4px);
            box-shadow: none !important;
            opacity: 1;
            &::after {
                display: none;
            }
        }
    }

    &__sp__bottom {
        display: none !important;
    }

}


// ==============================================
// spMenu
// ==============================================
.spMenu {
    display: none;
    cursor: pointer;
}
.menu__trigger,
.menu__trigger span {
    display: inline-block;
    transition: all .4s;
    box-sizing: border-box;
}
.menu__trigger {
    width: 60px;
    height: 60px;
    box-sizing: border-box;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: all .2s;
    &.active {
        .hamburger span {
            &:nth-of-type(1) {
                -webkit-transform: translateY(12px) rotate(-45deg);
                transform: translateY(11px) rotate(-45deg);
            }
            &:nth-of-type(2) {
                opacity: 0;
            }
            &:nth-of-type(3) {
                -webkit-transform: translateY(-9px) rotate(45deg);
                transform: translateY(-9px) rotate(45deg);
            }
        }
    }
}
.hamburger {
    margin-bottom: 0;
    width: 35px;
    height: 22px;
    position: relative;
    span {
        position: absolute;
        left: 0;
        width: 100%;
        height: 2px;
        background: $color--main;
        &:nth-of-type(2) {
            top: 10px;
        }
        &:nth-of-type(3) {
            bottom: 0;
        }
    }
}


// ==============================================
// main
// ==============================================
main {
    display: block;
}
.page__main {
    padding: 110px 0 90px;
}
.page__default {
    padding: 60px 0 40px;
}
.content__main {
    padding: 100px 0;
}

// ==============================================
// common section
// ==============================================
.section {
    position: relative;

    // ----------- section__title -----------
    &__title {

        &__left {
            & > span {
                margin-bottom: 25px;
                padding-left: 42px;
                font-size: 20px;
                display: inline-block;
                position: relative;
                color: $color--text;
                font-size: 20px;
                font-weight: normal;
                text-transform: uppercase;
                &:before {
                    content: '';
                    @include absolute(32px, 2px, 50%, initial, initial, 0, $color--accent--orange, translateY(-50%), 1);
                }
            }
            h2 {
                color: $color--text--strong;
                font-size: 40px;
                font-weight: bold;
                span {
                    display: inline-block;
                }
            }
            h3 {
                color: $color--text--strong;
                font-size: 32px;
                font-weight: bold;
                span {
                    display: inline-block;
                }
            }

            &.ttl__sm {
                span {
                    font-size: 18px;
                }
            }
        }

        &__centered {
            margin: 0 auto;
            display: table;

            img {
                margin: 0 auto 10px;
                width: 80px;
                height: auto;
            }
            & > span {
                margin-bottom: 25px;
                padding-left: 42px;
                font-size: 20px;
                display: inline-block;
                position: relative;
                color: $color--text;
                font-size: 20px;
                font-weight: normal;
                text-transform: uppercase;
                &:before {
                    content: '';
                    @include absolute(32px, 2px, 50%, initial, initial, 0, $color--accent--orange, translateY(-50%), 1);
                }
            }
            h2 {
                color: $color--text--strong;
                font-size: 40px;
                font-weight: bold;
                span {
                    display: inline-block;
                }
            }
        }

    }

    // ----------- section__more -----------
    &__more {
        display: flex;
        align-items: center;

        span {
            margin-right: 15px;
            margin-left: auto;
            font-size: 16px;
            font-weight: normal;
            color: $color--text;
        }

        img {
            width: 20px;
            height: auto;
        }
    }

    // ----------- section__about -----------
    &__about {
        padding-bottom: 100px;

        &__title {
            h2 {
                color: $color--text--strong;
                font-size: 40px;
                text-align: center;
                font-weight: bold;
            }
        }

        &__cont {
            margin-top: 50px;
            @include flex(space-between, center,initial);
            a {
                @include btn(35px 40px, 520px, $color--base--blue, 0 4px 0 #DADBDC, 20px);
                &:hover {
                    opacity: .6 !important;
                }
                .section__title__left {
                    margin-bottom: 50px;
                }
            }
        }

    }

     // ----------- BLOG section__document-download -----------
    .sample-form {
      textarea {
        max-width: 200px;
      }
    }
    &__document-download {
        padding-top: 0px;
        padding-bottom: 40px;
        // background-color: $color--base--blue;

        &__title {
            text-align: center;
            margin-bottom: 32px;

            span {
                font-size: 20px;
                text-transform: uppercase;
            }
            h2 {
                color: $color--text;
                font-size: 20px;
                font-weight: bold;
            }
        }

        &__description {
          margin-top: 24px;
          p {
            font-size: 14px;
            font-weight: 400;
            line-height: 1.7;
          }
        }

        &__eyecatch {
        padding-top: 76%;
        position: relative;
        margin-bottom: 24px;
        border-radius: 6px;
        border: 1px solid #808080;
          .img {
            @include absolute (100%, 100%, 0, 0, 0, 0, transparent, initial, initial);
            background-position: center center;
            background-size: cover;
            border-radius: 6px;
          }
        }

        &__left {
          width: 48%;
        }
        &__right {
          width: 48%;
          margin-left: auto;
          display: flex;
          flex-direction: column;
          justify-content: start;

          p {
            font-size: 13px;
            font-weight: 400;
            // line-height: 1.7;
            margin-bottom: 24px;
            // white-space: nowrap;
          }
        }

        &__cont {
            margin-top: 48px;
            padding: 48px 18px;
            align-items: center;
            background-color: #FFF;
            border: 1px solid $color--text;
            @include flex(space-between, start,initial);

            a {
                @include btn(20px 40px, 520px, initial, initial, 20px);
                @include flex(space-between, center,initial);
                &:first-child {
                    background-color: $color--main;
                    box-shadow: 0 4px 0 #23746F;
                }
                &:last-child {
                    background-color: $color--accent--orange;
                    box-shadow: 0 4px 0 #DD523F;
                }
                &:hover {
                    transform: translateY(4px);
                    box-shadow: none !important;
                    opacity: 1;
                }

                img {
                    height: auto;
                }

                p {
                    color: $color--white;
                    text-align: center;
                    &:first-child {
                        font-size: 28px;
                        font-weight: medium;
                    }
                    &:last-child {
                        font-size: 18px;
                        font-weight: normal;
                    }
                }
            }
        }

    }

    // ----------- section__contact -----------
    &__contact {
        padding-top: 100px;
        padding-bottom: 100px;
        background-color: $color--base--blue;

        &__logo {
            margin: 0 auto -10px;
            width: 720px;
        }

        &__title {
            text-align: center;

            span {
                font-size: 20px;
                text-transform: uppercase;
            }
            h2 {
                margin-top: 20px;
                color: $color--text--strong;
                font-size: 40px;
                font-weight: bold;
            }
        }

        &__cont {
            margin-top: 64px;
            padding-top: 64px;
            border-top: 1px solid $color--text;
            @include flex(space-between, center,initial);

            a {
                @include btn(20px 40px, 520px, initial, initial, 20px);
                @include flex(space-between, center,initial);
                &:first-child {
                    background-color: $color--main;
                    box-shadow: 0 4px 0 #23746F;
                }
                &:last-child {
                    background-color: $color--accent--orange;
                    box-shadow: 0 4px 0 #DD523F;
                }
                &:hover {
                    transform: translateY(4px);
                    box-shadow: none !important;
                    opacity: 1;
                }

                img {
                    height: auto;
                }

                p {
                    color: $color--white;
                    text-align: center;
                    &:first-child {
                        font-size: 28px;
                        font-weight: medium;
                    }
                    &:last-child {
                        font-size: 18px;
                        font-weight: normal;
                    }
                }
            }
        }

        &__icon {
            width: 64px;
        }
        &__arrow {
            width: 30px;
        }

    }

}

// ==============================================
// footer
// ==============================================
.footer {

    .container {
        padding-top: 100px;
        padding-bottom: 100px;
        @include flex(space-between, flex-start, initial);
    }

    &__left {
        width: 320px;

        &__top {
            margin-bottom: 30px;
            a {
                margin-bottom: 25px;
                width: 240px;
                display: block;
                img {
                    width: 100%;
                    height: auto;
                }
            }
            p {
                font-weight: bold;
                font-size: 14px;
                color: $color--text--sub;
            }
        }

        &__bottom {
            p {
                color: $color--text--sub;
                font-size: 14px;
            }
        }

    }

    &__right {
        margin-left: auto;
        width: 740px;
        position: relative;
        nav > ul {
            display: flex;
            & > li {
                width: 25%;
                ul li {
                    margin-bottom: 15px;
                    a {
                        font-size: 14px;
                        color: $color--text;
                        font-weight: normal;
                    }
                }
            }
        }
    }

    &__link__main {
        margin-bottom: 25px !important;
        a {
            font-size: 16px !important;
            color: $color--text !important;
            font-weight: bold !important;
            &:hover {
                color: $color--text--sub !important;
            }
        }
    }

}
.copyright {
    padding: 25px 0;
    background-color: $color--main;
    text-align: center;
    p {
        color: $color--white;
        font-size: 12px;
        font-weight: normal;
    }
}
#topBtn {
    @include absolute(65px, 65px, initial, 40px, 40px, initial, $color--main, initial, 998);
    @include flex(center, center, initial);
    position: fixed !important;
    border-radius: 50%;
    cursor: pointer;

    img {
        width: 12px;
        height: auto;
    }
}