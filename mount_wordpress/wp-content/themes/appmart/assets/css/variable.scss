
// ----------- color -----------
$color--base--blue: #F5F6F7; // ベースカラー
$color--base--green: #E1EDE8; // ベースカラー
$color--main: #3c8b86; // メインカラー

$color--text: #333333; // テキストカラー
$color--text--sub: #808080; // テキストカラー
$color--text--strong: #006835; // テキストカラー

$color--accent--orange: #FA6B58; // アクセントカラー
$color--accent--yellow: #F3C11D; // アクセントカラー

$color--white: #ffffff;
$color--black: #000000;


// ----------- mixin -----------
@mixin absolute($width, $height, $top, $right, $bottom, $left, $bgc, $transform, $z) {
    width: $width;
    height: $height;
    position: absolute;
    top: $top;
    right: $right;
    bottom: $bottom;
    left: $left;
    background: $bgc;
    transform: $transform;
    z-index: $z;
}
@mixin flex($jc, $ai, $fw) {
    display: flex;
    justify-content: $jc;
    align-items: $ai;
    flex-wrap: $fw;
}
@mixin btn($pd, $width, $bg, $bshadow, $bradius) {
    padding: $pd;
    width: $width;
    background-color: $bg;
    box-shadow: $bshadow;
    border-radius: $bradius;
    box-sizing: border-box;
}