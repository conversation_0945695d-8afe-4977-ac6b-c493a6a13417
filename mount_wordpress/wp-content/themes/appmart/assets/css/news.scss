@import './variable';

.page01 {
  &__news {
    padding: 150px 0;

    .news-list {
      display: flex;
      flex-flow: column wrap;
      width: 100%;
      padding: 48px 0;

      &__item {
        display: flex;
        gap: 48px;
        width: 100%;
        padding: 16px 0;
        border-bottom: 1px dashed $color--text;
        border-image: repeating-linear-gradient(
            to right,
            $color--text 0,
            $color--text 4px,
            transparent 4px,
            transparent 8px
          )
          1;

        &--date {
          flex-shrink: 0;
          padding: 0 16px;
          font-size: 16px;
          font-weight: bold;
          line-height: 1.5;
          color: $color--text;
        }

        &--title {
          position: relative;
          flex: 1;
          min-width: 0;
          padding-right: 64px;
          font-size: 16px;
          font-weight: bold;
          color: $color--text;

          &::after {
            position: absolute;
            top: 50%;
            right: 24px;
            width: 14px;
            height: 14px;
            color: $color--text;
            content: url('../images/svg/news_arrow.svg');
            transform: translateY(-50%);
          }

          a {
            display: block;
            width: 100%;
            line-height: 1.5;
            color: $color--text;
            word-break: break-all;
            word-wrap: break-word;
          }
        }
      }
    }
  }
}

.content__main.news {
  padding-top: 80px;

  .breadcrumb-inner {
    margin-bottom: 60px;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .archive-news {
    .news-list {
      display: flex;
      flex-flow: column wrap;
      width: 100%;
      padding: 48px 0;

      &__item {
        display: flex;
        gap: 48px;
        width: 100%;
        padding: 16px 0;
        border-bottom: 1px dashed $color--text;
        border-image: repeating-linear-gradient(
            to right,
            $color--text 0,
            $color--text 4px,
            transparent 4px,
            transparent 8px
          )
          1;

        &--date {
          flex-shrink: 0;
          padding: 0 16px;
          font-size: 16px;
          font-weight: bold;
          line-height: 1.5;
          color: $color--text;
        }

        &--title {
          position: relative;
          flex: 1;
          min-width: 0;
          padding-right: 64px;
          font-size: 16px;
          font-weight: bold;
          color: $color--text;

          &::after {
            position: absolute;
            top: 50%;
            right: 24px;
            width: 14px;
            height: 14px;
            color: $color--text;
            content: url('../images/svg/news_arrow.svg');
            transform: translateY(-50%);
          }

          a {
            display: block;
            width: 100%;
            line-height: 1.5;
            color: $color--text;
            word-break: break-all;
            word-wrap: break-word;
          }
        }
      }
    }
  }

  .single {
    &-news {
      &__cont {
        display: flex;
        flex-flow: column wrap;
        width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      &__top {
        max-width: 100%;

        h1 {
          position: relative;
          padding-top: 25px;
          margin-bottom: 32px;
          font-size: 32px;
          font-weight: bold;
          line-height: 50px;

          &::before {
            content: '';

            @include absolute(
              64px,
              5px,
              0,
              initial,
              initial,
              0,
              $color--accent--orange,
              translateY(-50%),
              1
            );

            border-radius: 20px;
          }
        }

        time {
          display: block;
          margin-bottom: 15px;
          font-size: 14px;
          text-align: right;
        }

        &--update {
          margin-right: 12px;
        }
      }

      &__eyecatch {
        width: 100%;
        margin-top: 80px;

        img {
          width: 100%;
          height: auto;
        }
      }

      .entry {
        width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
    }
  }
}

@media screen and (width <= 767px) {
  .page01 {
    &__news {
      padding: 60px 0;

      .news-list {
        padding: 32px 0;

        &__item {
          gap: 24px;

          &--date {
          }

          &--title {
            &::after {
            }

            a {
            }
          }
        }
      }
    }
  }

  .content__main.news {
    padding-top: 48px;

    .breadcrumb-inner {
      margin-bottom: 48px;
    }

    .archive-news {
      .news-list {
        padding: 24px 0;

        &__item {
          gap: 24px;

          &--date {
            font-size: 14px;
          }

          &--title {
            &::after {
            }

            a {
              font-size: 14px;
            }
          }
        }
      }
    }

    .single {
      &-news {
        &__cont {
        }

        &__top {
          h1 {
            &::before {
            }
          }

          time {
          }

          &--update {
          }
        }

        &__eyecatch {
          img {
          }
        }

        .entry {
        }
      }
    }
  }
}
