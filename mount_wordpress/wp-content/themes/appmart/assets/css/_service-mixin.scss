@mixin section-padding($td: 48px) {
  padding: 80px 0 80px;

  @media (max-width: 768px) {
    padding: $td 0 $td 0;
  }
}

@mixin sp {
  @media only screen and (max-width: 768px) {
    @content;
  }
}

@mixin pc {
  @media only screen and (min-width: 769px) {
    @content;
  }
}

@mixin section-above {
  padding: 80px 0;
  margin-bottom: 80px;
  height: 270px;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  position: relative;
  text-align: center;
  z-index: -2;

  @include sp() {
    padding: 48px 0;
    margin-bottom: 48px;
    height: 204px;
  }

  &:before {
    content: '';
    @include absolute(
      100%,
      100%,
      0,
      0,
      0,
      0,
      rgba(51, 51, 51, 0.5),
      initial,
      0
    );
    z-index: -1;
  }

  .text {
    &--s {
      position: relative;
      font-size: 32px;
      font-weight: 700;
      color: #fff;
      display: block;

      @include sp() {
        font-size: 16px;
      }
    }
    &--l {
      font-size: 44px;
      margin-top: 32px;
      color: #fff;
      display: inline-block;

      @include sp() {
        font-size: 28px;
        font-weight: 700;
        margin-top: 20px;
      }
    }
  }
}

@mixin circle-icon {
  margin-right: 20px;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #3c8b86;

  @include sp() {
    min-width: 50px;
    max-width: 50px;
    min-height: 50px;
    max-height: 50px;
    margin-right: 20px;
  }
}
