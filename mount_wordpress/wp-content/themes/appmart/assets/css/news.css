.page01__news {
  padding: 150px 0;
}
.page01__news .news-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column wrap;
          flex-flow: column wrap;
  width: 100%;
  padding: 48px 0;
}
.page01__news .news-list__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 48px;
  width: 100%;
  padding: 16px 0;
  border-bottom: 1px dashed #333333;
  -o-border-image: repeating-linear-gradient(to right, #333333 0, #333333 4px, transparent 4px, transparent 8px) 1;
     border-image: repeating-linear-gradient(to right, #333333 0, #333333 4px, transparent 4px, transparent 8px) 1;
}
.page01__news .news-list__item--date {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  padding: 0 16px;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  color: #333333;
}
.page01__news .news-list__item--title {
  position: relative;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  min-width: 0;
  padding-right: 64px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}
.page01__news .news-list__item--title::after {
  position: absolute;
  top: 50%;
  right: 24px;
  width: 14px;
  height: 14px;
  color: #333333;
  content: url("../images/svg/news_arrow.svg");
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.page01__news .news-list__item--title a {
  display: block;
  width: 100%;
  line-height: 1.5;
  color: #333333;
  word-break: break-all;
  word-wrap: break-word;
}

.content__main.news {
  padding-top: 80px;
}
.content__main.news .breadcrumb-inner {
  margin-bottom: 60px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.content__main.news .archive-news .news-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column wrap;
          flex-flow: column wrap;
  width: 100%;
  padding: 48px 0;
}
.content__main.news .archive-news .news-list__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 48px;
  width: 100%;
  padding: 16px 0;
  border-bottom: 1px dashed #333333;
  -o-border-image: repeating-linear-gradient(to right, #333333 0, #333333 4px, transparent 4px, transparent 8px) 1;
     border-image: repeating-linear-gradient(to right, #333333 0, #333333 4px, transparent 4px, transparent 8px) 1;
}
.content__main.news .archive-news .news-list__item--date {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  padding: 0 16px;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  color: #333333;
}
.content__main.news .archive-news .news-list__item--title {
  position: relative;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  min-width: 0;
  padding-right: 64px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}
.content__main.news .archive-news .news-list__item--title::after {
  position: absolute;
  top: 50%;
  right: 24px;
  width: 14px;
  height: 14px;
  color: #333333;
  content: url("../images/svg/news_arrow.svg");
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.content__main.news .archive-news .news-list__item--title a {
  display: block;
  width: 100%;
  line-height: 1.5;
  color: #333333;
  word-break: break-all;
  word-wrap: break-word;
}
.content__main.news .single-news__cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column wrap;
          flex-flow: column wrap;
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.content__main.news .single-news__top {
  max-width: 100%;
}
.content__main.news .single-news__top h1 {
  position: relative;
  padding-top: 25px;
  margin-bottom: 32px;
  font-size: 32px;
  font-weight: bold;
  line-height: 50px;
}
.content__main.news .single-news__top h1::before {
  content: "";
  width: 64px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1;
  border-radius: 20px;
}
.content__main.news .single-news__top time {
  display: block;
  margin-bottom: 15px;
  font-size: 14px;
  text-align: right;
}
.content__main.news .single-news__top--update {
  margin-right: 12px;
}
.content__main.news .single-news__eyecatch {
  width: 100%;
  margin-top: 80px;
}
.content__main.news .single-news__eyecatch img {
  width: 100%;
  height: auto;
}
.content__main.news .single-news .entry {
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

@media screen and (width <= 767px) {
  .page01__news {
    padding: 60px 0;
  }
  .page01__news .news-list {
    padding: 32px 0;
  }
  .page01__news .news-list__item {
    gap: 24px;
  }
  .content__main.news {
    padding-top: 48px;
  }
  .content__main.news .breadcrumb-inner {
    margin-bottom: 48px;
  }
  .content__main.news .archive-news .news-list {
    padding: 24px 0;
  }
  .content__main.news .archive-news .news-list__item {
    gap: 24px;
  }
  .content__main.news .archive-news .news-list__item--date {
    font-size: 14px;
  }
  .content__main.news .archive-news .news-list__item--title a {
    font-size: 14px;
  }
}/*# sourceMappingURL=news.css.map */