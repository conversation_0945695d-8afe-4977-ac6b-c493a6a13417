.owned-media {
  font-family: 'Noto Sans JP', sans-serif;
  color: #333333;
  line-height: 1.4;
}
@media (max-width: 767px) {
  .owned-media__pc {
    display: none !important;
  }
}
.owned-media__sp {
  display: none !important;
}
@media (max-width: 767px) {
  .owned-media__sp {
    display: block !important;
  }
}
.owned-media .container {
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 20px;
}
.owned-media__fv {
  position: relative;
  width: 100%;
  min-height: 1024px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .owned-media__fv {
    min-height: 817px;
  }
}
.owned-media__fv-bg {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1920px;
  height: 945px;
  z-index: 1;
}
.owned-media__fv-bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
}
@media (max-width: 767px) {
  .owned-media__fv-bg {
    width: 100%;
    left: 0;
    transform: none;
    height: 817px;
  }
}
.owned-media__fv-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: flex-start;
  height: 100%;
  max-width: 1300px;
  margin: 0 auto;
  padding: 80px 20px 0;
}
@media (max-width: 767px) {
  .owned-media__fv-container {
    flex-direction: column;
    justify-content: flex-start;
    padding: 45px 20px 0;
  }
}
.owned-media__fv-left {
  flex: 1;
  max-width: 885px;
  padding-right: 40px;
}
@media (max-width: 767px) {
  .owned-media__fv-left {
    padding-right: 0;
    width: 100%;
    order: 1;
    text-align: center;
  }
}
.owned-media__fv-right {
  flex-shrink: 0;
  width: 375px;
}
@media (max-width: 767px) {
  .owned-media__fv-right {
    width: 100%;
    order: 3;
    margin-top: 20px;
  }
}
.owned-media__fv-catch {
  position: relative;
  width: 762px;
  margin: 0 auto;
}
@media (max-width: 767px) {
  .owned-media__fv-catch {
    width: 335px;
    margin: 0 auto;
  }
}
.owned-media__fv-catch-main {
  position: relative;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-main {
    margin-bottom: 20px;
  }
}
.owned-media__fv-catch-label {
  display: inline-block;
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 900;
  font-size: 50px;
  color: #ffffff;
  letter-spacing: -0.06em;
  line-height: 1;
  background: #333333;
  border-radius: 62px;
  padding: 25px 42px;
  vertical-align: middle;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-label {
    font-size: 25px;
    padding: 12px 21px;
    border-radius: 31px;
  }
}
.owned-media__fv-catch-sub {
  display: inline-block;
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 900;
  font-size: 50px;
  color: #333333;
  letter-spacing: -0.06em;
  line-height: 1;
  background: #ffffff;
  border: 5px solid #333333;
  border-radius: 62px;
  padding: 25px 42px;
  margin-left: 10px;
  vertical-align: middle;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-sub {
    font-size: 25px;
    padding: 12px 21px;
    border-radius: 31px;
    border-width: 2.5px;
    margin-left: 5px;
  }
}
.owned-media__fv-catch-target {
  position: relative;
  text-align: center;
  margin-bottom: 40px;
  padding: 20px 0;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-target {
    margin-bottom: 20px;
    padding: 10px 0;
  }
}
.owned-media__fv-catch-target-decorations {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-target-decorations {
    top: 5px;
    gap: 5px;
  }
}
.owned-media__fv-catch-target-circle {
  width: 5.42px;
  height: 5.42px;
  background: #19191a;
  border-radius: 50%;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-target-circle {
    width: 3px;
    height: 3px;
  }
}
.owned-media__fv-catch-target-main,
.owned-media__fv-catch-target-sub {
  display: block;
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 700;
  font-size: 34px;
  color: #19191a;
  letter-spacing: 0.01em;
  line-height: 1.2em;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-target-main,
  .owned-media__fv-catch-target-sub {
    font-size: 17px;
  }
}
.owned-media__fv-catch-target-lines {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-target-lines {
    gap: 5px;
  }
}
.owned-media__fv-catch-target-line {
  width: 2px;
  height: 20px;
  background: #19191a;
}
.owned-media__fv-catch-target-line:first-child {
  height: 30px;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-target-line {
    width: 1px;
    height: 10px;
  }
  .owned-media__fv-catch-target-line:first-child {
    height: 15px;
  }
}
.owned-media__fv-catch-action {
  position: relative;
  width: 762px;
  height: 400px;
  margin: 0 auto;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-action {
    width: 335px;
    height: 200px;
  }
}
.owned-media__fv-catch-maru {
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 900;
  font-size: 237px;
  color: #fa6b58;
  letter-spacing: -0.17em;
  line-height: 1;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 3;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-maru {
    font-size: 118px;
    top: 0;
    left: 0;
  }
}
.owned-media__fv-catch-goto {
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 900;
  font-size: 161px;
  color: #333333;
  letter-spacing: -0.13em;
  line-height: 1;
  position: absolute;
  left: 380px;
  top: 26px;
  z-index: 2;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-goto {
    font-size: 80px;
    left: 180px;
    top: 13px;
  }
}
.owned-media__fv-catch-text {
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 900;
  font-size: 129px;
  color: #333333;
  line-height: 1;
  position: absolute;
  left: 70px;
  bottom: 0;
  white-space: nowrap;
  z-index: 1;
}
@media (max-width: 767px) {
  .owned-media__fv-catch-text {
    font-size: 64px;
    left: 35px;
    bottom: 0;
  }
}
.owned-media__fv-form {
  width: 375px;
  height: 618px;
}
@media (max-width: 767px) {
  .owned-media__fv-form {
    width: 100%;
    height: auto;
  }
}
.owned-media__fv-form-wrapper {
  background: #ffffff;
  border: 2px solid #7dc8b6;
  border-radius: 10px;
  width: 375px;
  height: 618px;
  box-shadow: 0px 0px 24px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}
@media (max-width: 767px) {
  .owned-media__fv-form-wrapper {
    width: 100%;
    height: auto;
    min-height: 400px;
  }
}
.owned-media__fv-form-wrapper iframe {
  width: 100%;
  height: 100%;
  border: none;
}
.owned-media__fv-illust {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 202.41px;
  z-index: 1;
  width: 1929px;
  height: 821.59px;
  pointer-events: none;
}
@media (max-width: 767px) {
  .owned-media__fv-illust {
    position: relative;
    order: 2;
    width: 100%;
    height: auto;
    top: 0;
    left: 0;
    transform: none;
    margin-bottom: 20px;
    pointer-events: auto;
  }
}
.owned-media__fv-illust img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
@media (max-width: 767px) {
  .owned-media__fv-illust img {
    object-fit: contain;
    max-width: 375px;
    margin: 0 auto;
    display: block;
  }
}
.owned-media__first-appeal {
  position: relative;
  padding: 80px 0;
}
@media (max-width: 767px) {
  .owned-media__first-appeal {
    padding: 40px 0;
  }
}
.owned-media__first-appeal-bg {
  position: absolute;
  top: 106px;
  left: 0;
  width: 100%;
  height: calc(100% - 106px);
  background: #b1e2d5;
  z-index: 1;
}
@media (max-width: 767px) {
  .owned-media__first-appeal-bg {
    top: 53px;
  }
}
.owned-media__first-appeal-content {
  position: relative;
  z-index: 2;
  text-align: center;
}
.owned-media__first-appeal-title {
  font-size: 78px;
  font-weight: 900;
  color: #333333;
  letter-spacing: -3%;
  line-height: 1.4;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .owned-media__first-appeal-title {
    font-size: 39px;
    margin-bottom: 20px;
  }
}
.owned-media__first-appeal-image img {
  max-width: 100%;
  height: auto;
}
.owned-media__partners {
  position: relative;
  padding: 40px 0;
  overflow: hidden;
}
.owned-media__partners-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f9f9f9;
  z-index: 1;
}
.owned-media__partners-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: none;
}
.owned-media__partners-logos {
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 40px;
  padding: 0 20px;
}
@media (max-width: 767px) {
  .owned-media__partners-logos {
    animation: scroll-horizontal 20s linear infinite;
    width: 200%;
    gap: 20px;
  }
}
.owned-media__partners-logo-item {
  flex-shrink: 0;
}
.owned-media__partners-logo-item img {
  max-width: 100%;
  height: auto;
  max-height: 60px;
}
@media (max-width: 767px) {
  .owned-media__partners-logo-item img {
    max-height: 40px;
  }
}
.owned-media__cta {
  position: relative;
  padding: 80px 0;
}
@media (max-width: 767px) {
  .owned-media__cta {
    padding: 40px 0;
  }
}
.owned-media__cta-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  z-index: 1;
}
.owned-media__cta-form {
  text-align: center;
}
.owned-media__cta-form-wrapper {
  background: #7dc8b6;
  border-radius: 8px;
  padding: 40px;
  min-height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 767px) {
  .owned-media__cta-form-wrapper {
    min-height: 204px;
    padding: 20px;
  }
}
.owned-media__cta-form-wrapper p {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
}
@media (max-width: 767px) {
  .owned-media__cta-form-wrapper p {
    font-size: 16px;
  }
}
.owned-media__empathy {
  position: relative;
  padding: 80px 0;
}
@media (max-width: 767px) {
  .owned-media__empathy {
    padding: 40px 0;
  }
}
.owned-media__empathy-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f9f9f9;
  z-index: 1;
}
.owned-media__empathy-title {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 60px;
  max-width: 1300px;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 767px) {
  .owned-media__empathy-title {
    margin-bottom: 30px;
  }
}
.owned-media__empathy-title-decoration {
  width: 700px;
  height: 39px;
  background: #b1e2d5;
  margin: 0 auto 32px;
}
@media (max-width: 767px) {
  .owned-media__empathy-title-decoration {
    width: 350px;
    height: 20px;
    margin-bottom: 16px;
  }
}
.owned-media__empathy-title-main {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #333333;
  letter-spacing: -3%;
  line-height: 1.2;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .owned-media__empathy-title-main {
    font-size: 24px;
    margin-bottom: 10px;
  }
}
.owned-media__empathy-title-sub {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 56px;
  font-weight: 700;
  color: #333333;
  letter-spacing: -3%;
  line-height: 1.2;
  margin-bottom: 25px;
}
@media (max-width: 767px) {
  .owned-media__empathy-title-sub {
    font-size: 28px;
    margin-bottom: 13px;
  }
}
.owned-media__empathy-checklist {
  position: relative;
  z-index: 2;
  background: #ffffff;
  border: 8px solid #b1e2d5;
  border-radius: 34px;
  padding: 60px;
  box-shadow: 0px 0px 14px 0px rgba(67, 226, 184, 0.6);
  max-width: 1300px;
  margin: 0 auto;
}
@media (max-width: 767px) {
  .owned-media__empathy-checklist {
    padding: 30px;
    border-width: 4px;
  }
}
.owned-media__empathy-checklist-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40px;
  padding-bottom: 40px;
  border-bottom: 6px dashed #b1e2d5;
}
@media (max-width: 767px) {
  .owned-media__empathy-checklist-item {
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom-width: 3px;
  }
}
.owned-media__empathy-checklist-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.owned-media__empathy-checklist-icon {
  flex-shrink: 0;
  width: 58px;
  height: 58px;
  background: #ffffff;
  border: 7px solid #7dc8b6;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  margin-top: 8px;
}
@media (max-width: 767px) {
  .owned-media__empathy-checklist-icon {
    width: 40px;
    height: 40px;
    border-width: 4px;
    margin-right: 15px;
  }
}
.owned-media__empathy-checklist-icon img {
  max-width: 80%;
  height: auto;
}
.owned-media__empathy-checklist-text {
  font-family: 'Yu Gothic', sans-serif;
  font-size: 46px;
  font-weight: 700;
  color: #333333;
  letter-spacing: 4%;
  line-height: 0.96;
}
@media (max-width: 767px) {
  .owned-media__empathy-checklist-text {
    font-size: 20px;
    line-height: 1.4;
  }
}
.owned-media__discovery {
  position: relative;
  padding: 80px 0;
  background: linear-gradient(135deg, #e3f3ff 0%, #b1e2d5 100%);
}
@media (max-width: 767px) {
  .owned-media__discovery {
    padding: 40px 0;
  }
}
.owned-media__discovery-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.owned-media__discovery-bg-mask {
  position: absolute;
  top: 89px;
  left: 0;
  width: 100%;
  height: calc(100% - 89px);
  background: linear-gradient(135deg, #a0c1bb 0%, rgba(192, 192, 192, 0.5) 100%);
}
.owned-media__discovery-bg-arrow {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 410px;
  height: 154px;
  background: #f9f9f9;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
@media (max-width: 767px) {
  .owned-media__discovery-bg-arrow {
    width: 205px;
    height: 77px;
  }
}
.owned-media__discovery-title {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 60px;
  max-width: 1302px;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 767px) {
  .owned-media__discovery-title {
    margin-bottom: 30px;
  }
}
.owned-media__discovery-title-text {
  display: flex;
  align-items: baseline;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}
.owned-media__discovery-title-start {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 50px;
  font-weight: 900;
  color: #333333;
  letter-spacing: -3%;
  line-height: 1.2;
}
@media (max-width: 767px) {
  .owned-media__discovery-title-start {
    font-size: 25px;
  }
}
.owned-media__discovery-title-emphasis {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 186px;
  font-weight: 900;
  color: #f9f9f9;
  letter-spacing: 11%;
  line-height: 1.2;
  -webkit-text-stroke: 7px #333333;
  text-stroke: 7px #333333;
}
@media (max-width: 767px) {
  .owned-media__discovery-title-emphasis {
    font-size: 93px;
    -webkit-text-stroke: 4px #333333;
    text-stroke: 4px #333333;
  }
}
.owned-media__discovery-title-end {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 61px;
  font-weight: 700;
  color: #333333;
  letter-spacing: -3%;
  line-height: 1.2;
}
@media (max-width: 767px) {
  .owned-media__discovery-title-end {
    font-size: 30px;
  }
}
.owned-media__discovery-title-decoration {
  display: flex;
  justify-content: center;
  gap: 47px;
}
.owned-media__discovery-title-line {
  width: 0;
  height: 103px;
  border-left: 8px solid #333333;
}
@media (max-width: 767px) {
  .owned-media__discovery-title-line {
    height: 51px;
    border-left-width: 4px;
  }
}
.owned-media__discovery-title-line:nth-child(2) {
  height: 74px;
}
@media (max-width: 767px) {
  .owned-media__discovery-title-line:nth-child(2) {
    height: 37px;
  }
}
.owned-media__discovery-title-line:nth-child(1) {
  height: 54px;
}
@media (max-width: 767px) {
  .owned-media__discovery-title-line:nth-child(1) {
    height: 27px;
  }
}
.owned-media__discovery-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
}
@media (max-width: 767px) {
  .owned-media__discovery-content {
    flex-direction: column;
    min-height: 400px;
  }
}
.owned-media__discovery-illustration {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 40px;
}
@media (max-width: 767px) {
  .owned-media__discovery-illustration {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
.owned-media__discovery-person {
  margin-right: 20px;
}
.owned-media__discovery-person img {
  max-width: 211px;
  height: auto;
}
@media (max-width: 767px) {
  .owned-media__discovery-person img {
    max-width: 105px;
  }
}
.owned-media__discovery-main-image img {
  max-width: 354px;
  height: auto;
}
@media (max-width: 767px) {
  .owned-media__discovery-main-image img {
    max-width: 177px;
  }
}
.owned-media__discovery-speeches {
  position: relative;
  width: 600px;
  height: 500px;
}
@media (max-width: 767px) {
  .owned-media__discovery-speeches {
    width: 300px;
    height: 250px;
  }
}
.owned-media__discovery-speech {
  position: absolute;
  background: #ffffff;
  border: 2px solid #333333;
  border-radius: 20px;
  padding: 15px 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
@media (max-width: 767px) {
  .owned-media__discovery-speech {
    padding: 8px 12px;
    border-radius: 10px;
  }
}
.owned-media__discovery-speech p {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 0;
  white-space: nowrap;
}
@media (max-width: 767px) {
  .owned-media__discovery-speech p {
    font-size: 12px;
  }
}
.owned-media__discovery-speech--1 {
  top: 0;
  left: 30px;
}
.owned-media__discovery-speech--2 {
  top: 10px;
  right: 0;
}
.owned-media__discovery-speech--3 {
  top: 160px;
  right: 50px;
}
.owned-media__discovery-speech--4 {
  bottom: 80px;
  right: 0;
}
.owned-media__discovery-speech--5 {
  bottom: 0;
  left: 100px;
}
.owned-media__discovery-speech--6 {
  top: 120px;
  left: 0;
}
.owned-media__concept,
.owned-media__services,
.owned-media__support-image,
.owned-media__merits,
.owned-media__support-team,
.owned-media__case-studies,
.owned-media__contract-flow,
.owned-media__faq {
  position: relative;
  padding: 80px 0;
}
@media (max-width: 767px) {
  .owned-media__concept,
  .owned-media__services,
  .owned-media__support-image,
  .owned-media__merits,
  .owned-media__support-team,
  .owned-media__case-studies,
  .owned-media__contract-flow,
  .owned-media__faq {
    padding: 40px 0;
  }
}
.owned-media__concept-bg,
.owned-media__services-bg,
.owned-media__support-image-bg,
.owned-media__merits-bg,
.owned-media__support-team-bg,
.owned-media__case-studies-bg,
.owned-media__contract-flow-bg,
.owned-media__faq-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.owned-media__concept-title,
.owned-media__services-title,
.owned-media__support-image-title,
.owned-media__merits-title,
.owned-media__support-team-title,
.owned-media__case-studies-title,
.owned-media__contract-flow-title,
.owned-media__faq-title {
  text-align: center;
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .owned-media__concept-title,
  .owned-media__services-title,
  .owned-media__support-image-title,
  .owned-media__merits-title,
  .owned-media__support-team-title,
  .owned-media__case-studies-title,
  .owned-media__contract-flow-title,
  .owned-media__faq-title {
    margin-bottom: 30px;
  }
}
.owned-media__concept-title h2,
.owned-media__services-title h2,
.owned-media__support-image-title h2,
.owned-media__merits-title h2,
.owned-media__support-team-title h2,
.owned-media__case-studies-title h2,
.owned-media__contract-flow-title h2,
.owned-media__faq-title h2 {
  font-size: 50px;
  font-weight: 900;
  color: #333333;
  line-height: 1.2;
}
@media (max-width: 767px) {
  .owned-media__concept-title h2,
  .owned-media__services-title h2,
  .owned-media__support-image-title h2,
  .owned-media__merits-title h2,
  .owned-media__support-team-title h2,
  .owned-media__case-studies-title h2,
  .owned-media__contract-flow-title h2,
  .owned-media__faq-title h2 {
    font-size: 22px;
  }
}
.owned-media__services-bg {
  background: linear-gradient(to bottom, #ffffff 0%, #f9f9f9 30%, #f9f9f9 100%);
}
.owned-media__services-item {
  background: #ffffff;
  border-radius: 34px;
  box-shadow: 0px 0px 14px 0px rgba(67, 226, 184, 0.6);
  padding: 40px;
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  gap: 40px;
}
@media (max-width: 767px) {
  .owned-media__services-item {
    flex-direction: column;
    padding: 20px;
    gap: 20px;
    text-align: center;
  }
}
.owned-media__services-item-number {
  flex-shrink: 0;
  text-align: center;
}
.owned-media__services-item-number-text {
  font-family: 'SF Pro Text', sans-serif;
  font-size: 100px;
  font-weight: 500;
  color: #7dc8b6;
  letter-spacing: 1%;
  line-height: 1.19;
  display: block;
}
@media (max-width: 767px) {
  .owned-media__services-item-number-text {
    font-size: 60px;
  }
}
.owned-media__services-item-number-service {
  font-family: 'SF Pro Text', sans-serif;
  font-size: 21px;
  font-weight: 500;
  color: #7dc8b6;
  letter-spacing: 1%;
  line-height: 1.19;
  display: block;
  margin-top: 10px;
}
@media (max-width: 767px) {
  .owned-media__services-item-number-service {
    font-size: 16px;
  }
}
.owned-media__services-item-content {
  flex: 1;
}
.owned-media__services-item-title {
  font-size: 45px;
  font-weight: 700;
  color: #333333;
  letter-spacing: 1%;
  line-height: 1.71;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .owned-media__services-item-title {
    font-size: 20px;
    margin-bottom: 15px;
  }
}
.owned-media__services-item-description {
  font-size: 24px;
  font-weight: 400;
  color: #333333;
  letter-spacing: 1%;
  line-height: 1.9;
}
@media (max-width: 767px) {
  .owned-media__services-item-description {
    font-size: 16px;
    line-height: 1.6;
  }
}
.owned-media__services-item-icon {
  flex-shrink: 0;
}
@media (max-width: 767px) {
  .owned-media__services-item-icon {
    order: -1;
  }
}
.owned-media__services-item-icon img {
  max-width: 200px;
  height: auto;
}
@media (max-width: 767px) {
  .owned-media__services-item-icon img {
    max-width: 120px;
  }
}

@keyframes scroll-horizontal {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}
