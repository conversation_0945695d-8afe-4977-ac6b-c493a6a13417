@media screen and (max-width: 1600px) {
  .section__service-detail__footer p {
    width: calc(100% - 450px);
    text-align: left;
  }
  .section__service-detail__footer img {
    right: 0;
  }
}
@media screen and (max-width: 1200px) {
  .mv__title h1 {
    font-size: 40px;
  }
  .mv__scroll .txt {
    margin-bottom: 10px;
  }
  .section__service-detail .section__title {
    padding-left: 70px;
  }
  .section__service-detail__item .wrap p span {
    display: inline;
  }
  .section__service-detail__item ul li .body p span {
    display: inline;
  }
  .section__service-detail__footer p {
    font-size: 24px;
  }
  .single__blog__share .sns__list li {
    width: calc(50% - 50px);
    margin: 10px 0 0;
  }
  .single__blog__share .sns__list li:nth-of-type(2n) {
    margin-left: auto;
  }
  .single__blog__share .sns__list li + li {
    margin-left: 0;
  }
  .is_fixed {
    width: 239.2px;
    right: calc((100vw - 1040px) / 2);
  }
}
@media screen and (max-width: 1080px) {
  .is_fixed {
    width: calc(23% - 10px);
    right: 20px;
  }
}
@media screen and (max-width: 979px) {
  .mv__bg__inner {
    padding-top: 120%;
  }
  .mv__bg__inner .img {
    background-image: url(../images/illust/appmart-top_sp.png);
  }
  .mv__content {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .mv__title {
    text-align: center;
  }
  .mv__title h1 {
    font-size: 40px;
  }
  .mv__scroll .txt {
    margin-bottom: 20px;
  }
  .mv__scroll .bar {
    height: 80px;
  }
  .page01__service__item {
    width: calc(33.333% - 13.3333333333px);
  }
  .page01__service__item + .page01__service__item {
    margin-left: 20px;
  }
  .page01__service__item__pic {
    height: 180px;
  }
  .page01__service__item__pic img {
    width: 90%;
  }
  .page01__service__item__title h3 {
    font-size: 30px;
  }
  .page01__service__item__list {
    padding: 0 20px 30px;
  }
  .page01__service__item__list li {
    font-size: 14px;
    padding-left: 28px;
  }
  .page01__service__item__list li:before {
    width: 20px;
  }
  .page01__mission__lead span {
    display: inline-block;
  }
  .page01__mission__lead span + span {
    margin-top: 15px;
  }
  .page01__client__bottom h3 span.line {
    max-width: 200px;
  }
  .page01__client__bottom___cont__item img {
    width: 150px;
  }
  .page01__blog > .container {
    display: block;
  }
  .page01__blog__bg {
    width: 100%;
    border-radius: 0;
    right: 0;
    bottom: 0;
  }
  .page01__blog__left > .hide_sp {
    display: none;
  }
  .page01__blog__right {
    margin-top: 45px;
    padding-left: 0;
  }
  .page01__blog__right > .show_sp {
    display: block;
  }
  .company-overview__item dt {
    width: 200px;
  }
  .company-overview__item dd {
    width: calc(100% - 200px);
  }
  .section__service__intro {
    padding-bottom: 60px;
  }
  .section__service__intro .service__intro__lead h2 {
    font-size: 30px;
  }
  .section__service__intro .service__intro__cont {
    margin-top: 60px;
  }
  .section__service__intro .service__intro__cont__item__pic {
    height: 180px;
  }
  .section__service__intro .service__intro__cont__item::before {
    height: 60px;
    top: -30px;
  }
  .section__service__intro .service__intro__cont__item__title h3 {
    font-size: 30px;
  }
  .section__service__intro .service__intro__cont__item__list {
    padding-left: 15px;
  }
  .section__service__intro .service__intro__cont__item__list li {
    padding-left: 25px;
  }
  .section__service__intro .service__intro__cont__item__list li::before {
    width: 20px;
  }
  .section__service__intro .service__intro__cont__item__list li a {
    font-size: 16px;
  }
  .section__service-detail {
    padding-top: 60px;
  }
  .section__service-detail .section__title {
    font-size: 24px;
  }
  .section__service-detail__item .outerbox--strategy::after, .section__service-detail__item .outerbox--analysis::after {
    width: 40px;
    height: 40px;
    left: -20px;
  }
  .section__service-detail__item .outerbox--creative::after {
    width: 40px;
    height: 40px;
    right: -20px;
  }
  .section__service-detail__item .outerbox--strategy::before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: -20px;
  }
  .section__service-detail__item .middlebox {
    padding: 60px 0;
  }
  .section__service-detail__item .innerbox {
    padding: 50px 40px;
  }
  .section__service-detail__item .innerbox:before {
    font-size: 80px;
  }
  .section__service-detail__item .item__title h3 {
    font-size: 28px;
  }
  .section__service-detail__item .wrap {
    padding-top: 10px;
  }
  .section__service-detail__item .wrap p,
  .section__service-detail__item .wrap img {
    width: 50%;
  }
  .section__service-detail__item ul li .head {
    padding: 20px;
  }
  .section__service-detail__item ul li .head h4 .icon {
    width: 40px;
    height: 40px;
    background-size: 25px 25px;
  }
  .section__service-detail__item ul li .head h4 span {
    font-size: 15px;
  }
  .section__service-detail__item ul li .body {
    padding: 20px;
  }
  .section__service-detail__item ul li .body .more_btn a {
    font-size: 15px;
  }
  .section__service-detail__item .innerbox--strategy .item__title::before, .section__service-detail__item .innerbox--strategy .item__title::after {
    left: -124px;
  }
  .section__service-detail__item .innerbox--creative .item__title::before, .section__service-detail__item .innerbox--creative .item__title::after {
    right: -124px;
  }
  .section__service-detail__item .innerbox--analysis .item__title::before, .section__service-detail__item .innerbox--analysis .item__title::after {
    left: -124px;
  }
  .section__service-detail__footer p {
    padding-left: 0;
    width: calc(100% - 330px);
  }
  .section__service-detail__footer p span {
    display: inline;
  }
  .section__service-detail__footer img {
    margin-left: 30px;
    width: 300px;
  }
  .post-type-archive-document .content__main {
    padding-top: 30px;
  }
  .archive__wp__tab ul li {
    font-size: 16px;
  }
  .archive__wp__cont__item {
    width: calc((100% - 30px) / 2);
    margin-right: 0;
  }
  .archive__wp__cont__item:not(:nth-of-type(2n)) {
    margin-right: 30px;
  }
  .archive__blog__primary .entry__list {
    margin-top: 20px;
  }
  .pagination nav.navigation {
    display: block;
  }
  .single__blog__related-posts__list > li {
    width: calc((100% - 40px) / 3);
    margin-right: 20px;
  }
  .sidebar {
    width: 28%;
  }
  .sidebar .searchbox input#searchBox {
    height: 50px;
    padding: 10px;
  }
  .sidebar .searchbox input#searchBox::-moz-placeholder {
    font-size: 10px;
  }
  .sidebar .searchbox input#searchBox::placeholder {
    font-size: 10px;
  }
  .sidebar .searchbox button#searchSubmit {
    top: 13px;
    right: 10px;
  }
  .sidebar .category h4 {
    font-size: 24px;
  }
  .is_fixed {
    width: calc(28% - 11px);
  }
  .category_label li {
    padding: 8px 10px 8px 40px;
  }
}
@media screen and (max-width: 767px) {
  .show_sp {
    display: block;
  }
  .hide_sp {
    display: none;
  }
  .mv {
    margin-top: 50px;
  }
  .mv__title h1 {
    font-size: 23px;
    letter-spacing: 0.04em;
  }
  .mv__title h1 span {
    border-bottom: 5px solid #3c8b86;
    padding: 6px 8px;
  }
  .mv__scroll .txt {
    font-size: 12px;
    margin-bottom: 15px;
  }
  .mv__scroll .bar {
    width: 1px;
    height: 120px;
  }
  .page01__service {
    padding-bottom: 50px;
  }
  .page01__service__cont {
    display: block;
  }
  .page01__service__item {
    width: 100%;
  }
  .page01__service__item::before {
    display: none;
  }
  .page01__service__item + .page01__service__item {
    margin-left: 0;
    margin-top: 30px;
  }
  .page01__service__item__pic {
    height: initial;
    padding-top: 30%;
    position: relative;
  }
  .page01__service__item__pic img {
    width: 70%;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  .page01__service__item__title img {
    min-width: 30px;
    min-height: 40px;
  }
  .page01__service__item__title h3 {
    font-size: 20px;
    margin-left: 10px;
  }
  .page01__mission {
    background-size: 70%;
    padding: 60px 0;
  }
  .page01__mission__bg {
    width: 300vw;
  }
  .page01__mission__lead h3 {
    line-height: 1;
  }
  .page01__mission__lead h3 span {
    display: inline-block;
    font-size: 24px;
    line-height: 1.3;
  }
  .page01__mission__wrap {
    display: block;
  }
  .page01__mission__wrap img {
    margin-right: auto;
    max-width: 300px;
  }
  .page01__mission__text p {
    font-size: 16px;
  }
  .page01__client__top {
    padding: 60px 0 0;
  }
  .page01__client__top::before {
    top: 300px;
  }
  .page01__client__top .works__list {
    display: block;
    margin-top: 30px;
  }
  .page01__client__top .works__item {
    width: 100%;
  }
  .page01__client__top__bglogo {
    font-size: 60px;
    top: 10px;
  }
  .page01__client__bottom {
    padding: 40px 0;
  }
  .page01__client__bottom h3 {
    text-align: center;
  }
  .page01__client__bottom h3::after {
    display: none;
  }
  .page01__client__bottom h3 span {
    font-size: 18px;
    line-height: 1.6388888889;
    padding-right: 0;
  }
  .page01__client__bottom__cont__item {
    width: calc((100% - 60px) / 3);
    margin-right: 0;
  }
  .page01__client__bottom__cont__item:not(:nth-of-type(3n)) {
    margin-right: 30px;
  }
  .page01__client__bottom__cont__item:last-of-type {
    margin-left: auto;
    margin-right: auto;
  }
  .page01__client__bottom__cont__item img {
    width: 100%;
  }
  .page01__blog {
    padding: 60px 0;
  }
  .page01__blog__bg {
    width: 100%;
    border-radius: 0;
    right: 0;
    bottom: 0;
  }
  .page01__blog > .container {
    display: block;
  }
  .page01__blog__left {
    width: 100%;
  }
  .page01__blog__right {
    width: 100%;
    padding-left: 0;
    margin-top: 30px;
  }
  .page01__blog__right__item__body h3 {
    font-size: 15px;
    margin-bottom: 15px;
    line-height: 22px;
  }
  .entry {
    padding: 30px 0;
  }
  .entry h2 {
    font-size: 22px;
    margin: 60px 0 20px;
  }
  .entry h3 {
    font-size: 20px;
  }
  .entry h4 {
    font-size: 18px;
    font-weight: bold;
  }
  .entry h5 {
    font-size: 16px;
    font-weight: bold;
  }
  .company-overview__item {
    display: block;
    padding: 22px 0;
  }
  .company-overview dt,
  .company-overview dd {
    width: 100%;
    padding: 0;
  }
  .company-overview dt {
    margin-bottom: 15px;
  }
  .company-overview dd .hq_map {
    padding-top: 114%;
  }
  .company-overview dd address {
    line-height: 1.1875;
  }
  .section__service__intro {
    background: linear-gradient(transparent 0%, transparent 10%, #E1EDE8 10%, #E1EDE8 100%);
    padding-bottom: 60px;
  }
  .section__service__intro__lead h2 {
    font-size: 22px;
  }
  .section__service__intro .service__intro__cont {
    display: block;
  }
  .section__service__intro .service__intro__cont__item {
    width: 100%;
    margin: 20px auto 0;
  }
  .section__service__intro .service__intro__cont__item:before {
    display: none;
  }
  .section__service__intro .service__intro__cont__item__pic {
    height: initial;
    position: relative;
    padding-top: 30%;
  }
  .section__service__intro .service__intro__cont__item__pic img {
    width: 70%;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  .section__service-detail {
    padding: 60px 0;
  }
  .section__service-detail .section__title {
    font-size: 20px;
  }
  .section__service-detail__item .outerbox {
    width: calc(100% - 20px);
  }
  .section__service-detail__item .middlebox {
    width: calc(100% - 30px);
  }
  .section__service-detail__item .middlebox--strategy {
    border-radius: 0 0 0 30px;
  }
  .section__service-detail__item .middlebox--creative {
    border-radius: 0 30px 30px 0;
  }
  .section__service-detail__item .middlebox--analysis {
    border-radius: 30px 0 0 0;
  }
  .section__service-detail__item .innerbox {
    right: -20px;
    padding: 20px 10px;
  }
  .section__service-detail__item .innerbox--creative {
    left: -20px;
  }
  .section__service-detail__item .innerbox--creative .wrap img {
    margin: 0 auto 15px;
  }
  .section__service-detail__item .item__title {
    padding-left: 15px;
  }
  .section__service-detail__item .item__title::before {
    width: 23px;
  }
  .section__service-detail__item .item__title::after {
    width: 9px;
    height: 9px;
  }
  .section__service-detail__item .item__title img {
    width: 30px;
    height: 30px;
  }
  .section__service-detail__item .item__title h3 {
    font-size: 20px;
    margin-left: 10px;
  }
  .section__service-detail__item .wrap {
    display: block;
  }
  .section__service-detail__item .wrap p {
    width: 100%;
    font-size: 14px;
  }
  .section__service-detail__item .wrap img {
    max-width: 250px;
    margin: 0 auto 15px;
  }
  .section__service-detail__item ul {
    display: block;
  }
  .section__service-detail__item ul li {
    width: 100%;
  }
  .section__service-detail__item .innerbox::before {
    font-size: 40px;
    top: 20px;
  }
  .section__service-detail__item .innerbox--strategy .item__title::before {
    left: -30px;
  }
  .section__service-detail__item .innerbox--strategy .item__title::after {
    left: -36px;
  }
  .section__service-detail__item .innerbox--creative .item__title::before {
    right: -30px;
  }
  .section__service-detail__item .innerbox--creative .item__title::after {
    right: -36px;
  }
  .section__service-detail__item .innerbox--analysis .item__title::before {
    left: -30px;
  }
  .section__service-detail__item .innerbox--analysis .item__title::after {
    left: -36px;
  }
  .section__service-detail__footer {
    padding: 20px 0;
  }
  .section__service-detail__footer p {
    width: calc(100% - 120px);
    font-size: 16px;
  }
  .section__service-detail__footer img {
    width: 120px;
  }
  .section__search .section_title {
    margin-bottom: 15px;
  }
  .section__search .section_title h2 {
    font-size: 24px;
  }
  .section__search .entry__list {
    padding: 20px 0;
  }
  .section__search .entry__body h3 {
    font-size: 14px;
    line-height: 22px;
  }
  .section__not-found .section_title {
    margin-bottom: 15px;
  }
  .section__not-found .section_title h2 {
    font-size: 24px;
  }
  .post-type-archive-document .content__main {
    padding-top: 30px;
  }
  .archive__wp__tab {
    margin-bottom: 30px;
  }
  .archive__wp__tab ul {
    display: block;
  }
  .archive__wp__tab ul li.tab {
    width: 100%;
    border: 1px solid #006835;
    background: transparent;
    font-size: 16px;
    color: #006835;
    text-align: left;
    padding-left: 20px;
    padding-right: 20px;
  }
  .archive__wp__tab ul li.tab + li {
    margin-top: 5px;
  }
  .archive__wp__tab ul li.tab:not(.all) {
    display: none;
  }
  .archive__wp__tab ul li.tab.all {
    position: relative;
  }
  .archive__wp__tab ul li.tab .tab_toggle {
    display: inline-block;
    width: 25px;
    height: 25px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 20px;
    background: transparent url(../images/icon/round-arrow_grey.png) no-repeat center center/contain;
    z-index: 2;
  }
  .archive__wp__tab ul li.tab .tab_toggle.is-open {
    background-image: url(../images/icon/round-arrow_grey-up.png);
  }
  .archive__wp__cont__title {
    font-size: 20px;
    margin-bottom: 25px;
  }
  .archive__wp__cont__item {
    width: 100%;
    margin-right: 0;
    margin: 0 auto 40px;
  }
  .archive__wp__cont__item:not(:nth-of-type(2n)) {
    margin-right: 0;
  }
  .archive__blog__cont {
    display: block;
  }
  .archive__blog__primary .entry__list {
    padding: 20px 0;
  }
  .archive__blog__primary .entry__body h3 {
    font-size: 14px;
    line-height: 22px;
  }
  .pagination .nav-links {
    padding: 10px 0;
  }
  .pagination .nav-links a,
  .pagination .nav-links span {
    margin: 4px 2px;
    font-size: 14px;
    padding: 8px 13px;
  }
  .sample-form textarea {
    max-width: 90% !important;
  }
  .section__document-download__left {
    width: 100%;
    margin-bottom: 24px;
  }
  .section__document-download__right {
    width: 100%;
    text-align: center;
  }
  .section__document-download__right p {
    font-size: 14px;
    font-weight: bold;
  }
  .section__document-download__cont {
    display: block;
  }
  .single__wp__cont {
    display: block;
  }
  .single__wp__left {
    width: 100%;
  }
  .single__wp__content__box .body p {
    font-size: 16px;
  }
  .single__wp__right {
    width: 100%;
    margin-top: 35px;
  }
  .single__blog__cont {
    display: block;
  }
  .single__blog__top h1 {
    line-height: 35px;
    font-size: 22px;
  }
  .single__blog__eyecatch {
    margin-top: 30px;
  }
  .single__blog__share {
    padding: 20px 0 30px;
    border-top: 1px solid #808080;
  }
  .single__blog__share p {
    font-size: 20px;
    margin-bottom: 20px;
  }
  .single__blog__share p span {
    padding-left: 30px;
  }
  .single__blog__share p span::before {
    width: 24px;
    height: 24px;
  }
  .single__blog__share .sns__list {
    justify-content: center;
  }
  .single__blog__share .sns__list li {
    width: 50px;
    height: 50px;
  }
  .single__blog__share .sns__list li:nth-of-type(2n) {
    margin-left: 25px;
  }
  .single__blog__share .sns__list li + li {
    margin-left: 25px;
  }
  .single__blog__share .sns__list li a {
    padding: 12px 10px;
  }
  .single__blog__share .sns__list li a img {
    width: 25px;
    height: 25px;
  }
  .single__blog__share .sns__list li a span {
    display: none;
  }
  .single__blog__author {
    display: block;
  }
  .single__blog__author .pic {
    margin: 0 auto;
    margin-bottom: 20px;
  }
  .single__blog__author .body {
    width: 100%;
  }
  .single__blog__related-posts h2 {
    font-size: 20px;
  }
  .single__blog__related-posts h2::before {
    width: 30px;
    height: 2.5px;
  }
  .single__blog__related-posts__list {
    display: block;
  }
  .single__blog__related-posts__list > li {
    width: 100%;
  }
  .single__blog__related-posts__list > li a {
    display: flex;
    justify-content: initial;
    align-items: stretch;
    flex-wrap: wrap;
  }
  .single__blog__related-posts__list > li a h3 {
    margin-top: 0;
  }
  .single__blog__related-posts__list > li a time {
    display: block;
  }
  .single__blog__related-posts__list > li a .category_label li {
    padding: 7px 7px 7px 24px;
    margin-top: 10px;
  }
  .single__blog__related-posts__list > li a .category_label li::before {
    width: 10px;
    height: 10px;
  }
  .single__blog__related-posts__list > li a time {
    margin-top: 5px;
    color: #333333;
  }
  .single__blog__related-posts__list > li .left {
    width: 35%;
  }
  .single__blog__related-posts__list > li .right {
    width: calc(65% - 10px);
    margin-left: auto;
  }
  .primary {
    width: 100%;
  }
  .sidebar {
    width: 100%;
    margin-left: 0;
    margin-top: 100px;
  }
  .sidebar .category h4 {
    font-size: 20px;
  }
  .sidebar .category h4::before {
    width: 30px;
    height: 2.5px;
  }
  .sidebar .banner a img {
    width: 100%;
  }
  .category_label li {
    font-size: 10px;
    padding: 7px 15px 7px 30px;
  }
  .category_label li::before {
    width: 15px;
    height: 15px;
  }
  .category_label.sp {
    display: block;
    margin-top: 8px;
    display: none;
  }
  .category_label.pc {
    display: none;
  }
}
@media screen and (max-width: 1200px) {
  .container {
    padding-right: 20px;
    padding-left: 20px;
  }
  .header__right nav {
    display: none;
    padding: 60px 100px;
    background-color: #ffffff;
    position: fixed;
    top: 80px;
    right: 0;
    bottom: 0;
    left: 0;
    overflow-y: scroll;
  }
  .header__right nav ul {
    display: block;
  }
  .header__right nav ul li {
    margin-left: 0;
    padding: 20px 0;
    width: 100%;
    height: initial;
  }
  .header__service {
    display: block !important;
  }
  .header__service > a {
    display: none;
  }
  .header__service__sp {
    display: flex;
  }
  .header__service__modal {
    margin-top: 20px;
    padding: 0;
    top: initial;
    background: none;
    position: initial;
  }
  .header__service__modal .container ul {
    justify-content: flex-start;
  }
  .header__service__modal .container ul li {
    padding: 5px 0;
    width: 25%;
  }
  .header__service__modal .container ul li:before {
    display: none;
  }
  .header__recruit {
    display: block !important;
  }
  .header__sp__bottom {
    margin-top: 20px;
    width: initial !important;
    display: block !important;
  }
  .header__sp__bottom p {
    margin-top: 30px;
    color: #808080;
  }
  .spMenu {
    display: block;
  }
  .footer__left {
    margin-right: 30px;
    width: 300px;
  }
  .footer__right {
    width: calc(100% - 330px);
  }
  .section__intro__left {
    margin-right: 50px;
  }
  .section__intro__right {
    width: calc(100% - 650px);
  }
  .section__concerns__top ul li {
    width: 350px;
  }
  .section__system__left {
    margin-right: 50px;
    width: 400px;
  }
  .section__system__right {
    width: calc(100% - 450px);
  }
  .section__compare__item:first-child {
    left: -20px;
  }
  .section__compare__item:first-child:before {
    left: 0;
  }
  .section__compare__item:first-child:after {
    right: -400px;
    font-size: 100px;
  }
  .section__compare__item:last-child {
    right: -20px;
  }
  .section__compare__item:last-child::before {
    right: 0;
  }
  .section__compare__item:last-child:after {
    left: -330px;
    font-size: 100px;
  }
  .section__about__cont a {
    width: calc(50% - 15px);
  }
  .section__about__cont a:first-child {
    margin-right: 30px;
  }
  .section__contact__cont a {
    width: calc(50% - 15px);
  }
  .section__contact__cont a:first-child {
    margin-right: 30px;
  }
  .section__contact__logo {
    width: 100%;
  }
  .solution__title {
    padding-right: 0;
    padding-left: 0;
    width: 100%;
  }
  .footer__link__main a {
    font-size: 15px !important;
  }
  .content-marketing .section__plan__desc br {
    display: none;
  }
  .content-marketing .section__plan h4 span:nth-child(2) {
    font-size: 30px;
  }
  .saiyou-ownedmedia .section__point__desc {
    width: 100%;
  }
  .saiyou-ownedmedia .section__plan__warn span {
    display: inline-block;
  }
}
@media screen and (max-width: 979px) {
  .header__right nav {
    padding: 60px 50px;
  }
  .header__service__modal .container ul li {
    width: 33.3333%;
  }
  nav {
    display: none;
  }
  .section__intro {
    padding-bottom: 120px;
  }
  .section__intro__cont {
    display: block;
  }
  .section__intro__left {
    margin-right: 50px;
    width: 100%;
  }
  .section__intro__left h2 {
    margin-bottom: 40px;
  }
  .section__intro__right {
    margin: 50px auto 0;
    width: 70%;
  }
  .section__concerns__top {
    margin-top: 0;
    margin-bottom: 30px;
    padding-top: 0;
  }
  .section__concerns__top ul {
    position: relative;
    top: initial;
    right: initial;
    bottom: initial;
    left: initial;
  }
  .section__concerns__top ul li {
    margin-bottom: 20px;
    padding: 25px 0;
    width: 100%;
    position: relative !important;
    top: initial !important;
    right: initial !important;
    bottom: initial !important;
    left: initial !important;
    transform: initial !important;
    border-radius: 10px;
  }
  .section__concerns__top ul li:last-child {
    margin-bottom: 25px;
  }
  .section__system {
    background-image: none;
    position: relative;
    overflow: hidden;
  }
  .section__system__bg__top, .section__system__bg__bottom {
    display: block;
  }
  .section__system__cont {
    flex-direction: column-reverse;
  }
  .section__system__left {
    margin-right: 0;
  }
  .section__system__right {
    margin-bottom: 40px;
    width: 100%;
  }
  .section__feature__cont ul li {
    margin-right: 0;
    width: 100%;
  }
  .section__feature__cont ul li:nth-child(5) {
    margin-bottom: 60px;
  }
  .section__flow {
    padding-top: 120px;
  }
  .section__compare__item {
    width: 90%;
  }
  .section__compare__item::after {
    display: none;
  }
  .section__point__item.full__bg .section__point__img ul li img {
    width: 140px;
  }
  .section__point__item__inner {
    display: block;
  }
  .section__point__item__inner .section__point__img {
    margin: 0 auto;
    width: 250px;
  }
  .section__point__left {
    width: 100%;
  }
  .section__plan {
    padding-top: 120px;
  }
  .section__plan__item {
    margin-right: 0;
    margin-bottom: 80px !important;
    width: 100%;
  }
  .section__plan__item:last-child {
    margin-bottom: 0 !important;
  }
  .section__case__cont ul li {
    width: calc(50% - 15px);
  }
  .section__case__cont ul li:nth-child(3n) {
    margin-right: 30px;
  }
  .section__case__cont ul li:nth-child(2n) {
    margin-right: 0;
  }
  .section__contact__cont {
    display: block;
  }
  .section__contact__cont a {
    width: 100%;
  }
  .section__contact__cont a:first-child {
    margin-right: 0;
    margin-bottom: 25px;
  }
  .solution__title h2 > span {
    font-size: 36px;
  }
  .solution__title h2 > span span {
    font-size: 46px;
  }
  .solution__title h2 img {
    width: 330px;
  }
  .footer .container {
    padding-top: 50px;
    padding-bottom: 50px;
    display: block;
  }
  .footer__left {
    margin: 0 auto;
    text-align: center;
  }
  .footer__left__top {
    margin-bottom: 0;
  }
  .footer__left__bottom {
    display: none;
  }
  .content__main {
    padding: 120px 0;
  }
  .page__main {
    padding: 80px 0 60px;
  }
  .page__default {
    padding: 40px 0 20px;
  }
  .content-marketing .section__plan__desc {
    text-align: center;
  }
  .attribution .section__plan__item__inner {
    padding: 40px;
  }
  .attribution .section__plan__item__inner ul {
    padding: 0 0 0 30px;
    width: calc(100% - 280px);
  }
  .attribution .section__plan__item__inner ul li {
    margin-bottom: 20px;
  }
  .attribution .section__plan__item__inner ul li:last-child {
    margin-bottom: 0;
  }
  .attribution .section__plan__item__inner ul li p {
    font-size: 16px;
  }
  .attribution .section__plan__left {
    padding-right: 30px;
    width: 280px;
    box-sizing: border-box;
  }
  .attribution .section__plan__left img {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .container {
    padding-right: 15px;
    padding-left: 15px;
  }
  .btn {
    margin-top: 40px;
  }
  .btn a {
    padding-top: 20px;
    padding-bottom: 17px;
    width: 100% !important;
  }
  .btn a span {
    font-size: 14px;
  }
  .btn a img {
    width: 15px;
  }
  .btn.col2 a:first-child {
    margin-bottom: 32px;
  }
  .pageLoader__logo img {
    width: 80px;
  }
  .header {
    height: 50px;
  }
  .header__left a h1 {
    width: 160px;
  }
  .header__right nav {
    top: 50px;
  }
  .header__right nav ul li {
    margin-bottom: 30px;
    padding: 0;
  }
  .header__right nav ul li:nth-child(5) {
    margin-bottom: 60px;
  }
  .header__service__sp {
    justify-content: space-between;
  }
  .header__service__modal .container ul li {
    margin-bottom: 10px !important;
    width: 100%;
  }
  .header__contact {
    padding-top: 25px !important;
    border-top: 1px solid #333333;
  }
  .header__contact a {
    width: 100%;
    text-align: center;
    font-size: 14px;
  }
  .header__sp__bottom p {
    font-size: 12px;
  }
  .menu__trigger {
    width: initial;
    height: 50px;
  }
  .menu__trigger.active .hamburger span:nth-of-type(1) {
    transform: translateY(9px) rotate(-45deg);
  }
  .menu__trigger.active .hamburger span:nth-of-type(3) {
    transform: translateY(-7px) rotate(45deg);
  }
  .hamburger {
    width: 28px;
    height: 18px;
  }
  .hamburger span:nth-of-type(2) {
    top: 8px;
  }
  .section__title__left img, .section__title__centered img {
    width: 50px;
  }
  .section__title__left > span, .section__title__centered > span {
    margin-bottom: 12px;
    padding-left: 22px;
    font-size: 12px !important;
  }
  .section__title__left > span:before, .section__title__centered > span:before {
    width: 16px;
  }
  .section__title__left h3, .section__title__left h2, .section__title__centered h3, .section__title__centered h2 {
    font-size: 22px !important;
    line-height: 1.5;
  }
  .section__more__btn span {
    font-size: 15px;
  }
  .section__intro {
    padding-bottom: 60px;
  }
  .section__intro__left h2 {
    margin-bottom: 30px;
    font-size: 20px;
  }
  .section__intro__left ul li {
    margin-bottom: 20px;
  }
  .section__intro__left ul li p {
    font-size: 16px;
  }
  .section__concerns {
    padding-top: 40px;
    padding-bottom: 100px;
  }
  .section__concerns__cont {
    margin-top: 40px;
  }
  .section__concerns__top img {
    width: 100px;
  }
  .section__concerns__bottom p {
    font-size: 16px;
  }
  .section__concerns__item {
    margin-right: 0;
    margin-bottom: 30px;
    width: 100%;
  }
  .section__concerns__item:last-child {
    margin-bottom: 0;
  }
  .section__concerns__item img {
    margin-bottom: 30px;
    width: 80px;
  }
  .section__concerns__item__desc {
    min-height: initial !important;
    padding: 20px;
  }
  .section__concerns__item__desc:before {
    height: 30px;
    top: -15px;
  }
  .section__concerns__solution {
    padding-bottom: 60px !important;
    border-radius: 0 0 30px 30px;
  }
  .section__concerns__solution .section__title__left {
    display: block;
  }
  .section__concerns__solution h3 {
    font-size: 20px;
  }
  .section__concerns__solution h3 span:before {
    height: 8px;
  }
  .section__concerns__solution__cont {
    margin-top: 40px;
  }
  .section__concerns__solution__item {
    margin-right: 0;
    margin-bottom: 40px !important;
    width: 100%;
  }
  .section__concerns__solution__item:last-child {
    margin-bottom: 0;
  }
  .section__concerns__solution__item__icon {
    width: 80px;
    height: 80px;
  }
  .section__concerns__solution__item__icon:before {
    height: 25px;
    bottom: -12px;
  }
  .section__concerns__solution__item__icon img {
    height: 50px;
  }
  .section__concerns__solution__item h3, .section__concerns__solution__item h4 {
    font-size: 18px;
  }
  .section__concerns__line {
    height: 50px;
    bottom: -25px;
  }
  .section__system {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .section__system .section__title__centered h2 {
    text-align: center;
    display: grid;
  }
  .section__system__bg__top {
    width: 120%;
    left: -80px;
  }
  .section__system__cont {
    margin-top: 40px;
  }
  .section__system__right {
    margin-bottom: 30px;
  }
  .section__system__right ul li {
    margin-bottom: 25px;
    padding-right: 50px;
    padding-left: 20px;
    width: 100%;
    border-radius: 10px;
  }
  .section__system__right ul li > span {
    margin-right: 12px;
    font-size: 32px;
  }
  .section__system__right ul li p {
    font-size: 16px;
    text-align: center;
  }
  .section__system__left {
    width: 150px;
  }
  .section__feature__cont {
    margin-top: 40px;
  }
  .section__feature__cont ul li {
    margin-bottom: 25px !important;
  }
  .section__feature__cont ul li:last-child {
    margin-bottom: 0 !important;
  }
  .section__feature__num {
    padding: 15px 8px 5px;
    width: 40px;
    border-radius: 10px 0 10px 0;
  }
  .section__feature__num::before {
    height: calc(100% + 15px);
  }
  .section__feature__num span {
    font-size: 22px;
  }
  .section__feature__top h4 {
    margin-right: 20px;
    padding: 13px 13px 5px;
    width: calc(100% - 40px);
    font-size: 18px;
  }
  .section__feature__bottom {
    padding: 20px;
  }
  .section__feature__icon {
    width: 80px;
  }
  .section__feature__icon img {
    width: 60px;
  }
  .section__feature__desc {
    width: calc(100% - 80px);
  }
  .section__feature__desc p {
    font-size: 14px;
  }
  .section__case {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .section__case::before {
    top: 400px;
  }
  .section__case__cont {
    margin-top: 40px;
  }
  .section__case__cont ul li {
    margin-right: 0 !important;
    margin-bottom: 25px;
    width: 100%;
    border-radius: 10px;
  }
  .section__case__cont ul li:last-child {
    margin-bottom: 0 !important;
  }
  .section__case__cont ul li div {
    border-radius: 10px 10px 0 0;
  }
  .section__case__cont ul li h3 {
    margin-bottom: 15px;
  }
  .section__compare {
    padding-top: 60px;
    padding-bottom: 0;
  }
  .section__compare__cont {
    margin-top: 40px;
  }
  .section__compare__item {
    padding: 30px 0 !important;
    width: 100%;
  }
  .section__compare__item::before {
    left: -15px !important;
    right: -15px !important;
    border-radius: 0 !important;
  }
  .section__compare__item:first-child {
    margin-bottom: 25px;
    left: 0;
  }
  .section__compare__item:last-child {
    right: 0;
  }
  .section__compare__item .container {
    padding-right: 0;
    padding-left: 0;
  }
  .section__compare__item ul {
    margin-top: 30px;
  }
  .section__compare__item ul li {
    margin-bottom: 20px;
    font-size: 16px;
  }
  .section__compare__item img {
    width: 110px;
    top: 30px;
    right: 15px;
    transform: initial;
  }
  .section__flow {
    padding-top: 60px;
  }
  .section__flow__cont {
    margin-top: 40px;
  }
  .section__flow__item__left {
    margin-right: 20px;
    padding-top: 15px;
    width: 50px;
  }
  .section__flow__item__left:before {
    width: 2px;
    top: 65px;
  }
  .section__flow__item__left:after {
    border-top: 5px solid #3c8b86;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
  }
  .section__flow__item__right {
    padding: 15px 0;
    width: calc(100% - 70px);
  }
  .section__flow__item__right__top {
    margin-bottom: 17px;
  }
  .section__flow__item__right__top h3 {
    width: calc(100% - 70px);
    font-size: 17px;
    line-height: 1.6;
  }
  .section__flow__item__right__bottom p {
    font-size: 14px;
  }
  .section__flow__num {
    width: 50px;
    height: 50px;
  }
  .section__flow__num div span:first-child {
    margin-bottom: 2px;
    font-size: 10px;
  }
  .section__flow__num div span:last-child {
    font-size: 18px;
  }
  .section__flow__icon {
    margin-right: 20px;
    /*width: 50px;
    height: 50px;*/
  }
  .section__flow__icon img {
    width: 120px;
  }
  .section__flow__icon:before {
    height: 16px;
    bottom: -12px;
  }
  .section__point {
    padding-top: 60px;
  }
  .section__point__cont {
    margin-top: 30px;
  }
  .section__point__item {
    padding: 40px 0 30px;
  }
  .section__point__item .section__title__left.ttl__sm h3 {
    font-size: 18px !important;
  }
  .section__point__item .section__title__left.ttl__sm h3:before {
    height: 8px;
    bottom: 4px;
  }
  .section__point__item.half__bg:before {
    height: 80%;
  }
  .section__point__item.full__bg .section__point__img ul li {
    width: 50%;
  }
  .section__point__item.full__bg .section__point__img ul li:nth-child(1), .section__point__item.full__bg .section__point__img ul li:nth-child(2) {
    margin-bottom: 20px;
  }
  .section__point__item.full__bg .section__point__img ul li img {
    margin-bottom: 15px;
    width: 100px;
  }
  .section__point__item.full__bg .section__point__img ul li p {
    font-size: 14px;
  }
  .section__point__desc {
    margin: 15px 0;
    width: 100%;
  }
  .section__voice {
    padding-top: 60px;
  }
  .section__voice__cont {
    margin-top: 35px;
  }
  .section__voice__item {
    margin-bottom: 25px;
    padding: 30px;
  }
  .section__voice__item__left {
    margin-bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: initial;
  }
  .section__voice__item__left img {
    margin: 0 25px 0 0;
    width: 60px;
  }
  .section__voice__item__left p {
    font-size: 16px;
  }
  .section__voice__item__right {
    width: 100%;
  }
  .section__voice__item__right h3 {
    margin-bottom: 15px;
  }
  .section__voice__item__right h3 img {
    margin-right: 10px;
  }
  .section__voice__item__right h3 > span span {
    font-size: 16px;
  }
  .section__voice__item__right h3 > span span::before {
    height: 6px;
    bottom: 3px;
  }
  .section__voice__item__right p {
    font-size: 14px;
  }
  .section__case__slide {
    padding-top: 60px;
  }
  .section__case__slide__cont {
    margin-top: 40px;
  }
  .section__case__slide .slick-slide img {
    width: 100%;
    height: 250px;
  }
  .section__case__slide .slick-dots {
    bottom: -40px;
  }
  .section__case__slide .slick-dots li {
    margin: 0 10px;
  }
  .section__case__slide .slick-dots li button:before {
    font-size: 12px;
  }
  .section__plan {
    padding-top: 60px;
  }
  .section__plan__cont {
    margin-top: 40px;
  }
  .section__plan h3 {
    padding-top: 12px;
    padding-bottom: 12px;
    font-size: 18px !important;
    border-radius: 10px 10px 0 0;
  }
  .section__plan h4 {
    padding-top: 12px;
    padding-bottom: 12px;
    border-radius: 0 0 10px 10px;
  }
  .section__plan h4 span:first-child {
    font-size: 16px !important;
  }
  .section__plan h4 span:nth-child(2) {
    font-size: 24px !important;
  }
  .section__plan__item {
    margin-bottom: 40px !important;
    border-radius: 10px;
  }
  .section__plan__item__inner {
    padding: 30px 25px;
  }
  .section__plan__item__inner ul li {
    margin-bottom: 20px;
  }
  .section__plan__item__inner ul li img {
    width: 20px;
  }
  .section__plan__item__inner ul li p {
    font-size: 16px;
  }
  .section__plan__desc {
    padding-bottom: 20px;
    font-size: 16px;
  }
  .section__plan__warn {
    margin-top: 25px;
    text-align: left;
    font-size: 16px;
  }
  .section__faq {
    padding-top: 60px;
  }
  .section__faq__cont {
    margin-top: 40px;
  }
  .section__faq__cont ul li {
    margin-bottom: 30px;
  }
  .section__faq__cont ul li h3 {
    margin-bottom: 15px;
  }
  .section__faq__cont ul li h3 span {
    margin-right: 15px;
    width: 40px;
    height: 40px;
    font-weight: normal;
    font-size: 20px;
  }
  .section__faq__cont ul li h3 p {
    width: calc(100% - 55px);
    font-size: 16px;
  }
  .section__faq__cont ul li h3 p br {
    display: none;
  }
  .section__faq__cont ul li p {
    font-size: 14px;
  }
  .section__faq__cont ul li a {
    margin-top: 15px;
    font-size: 15px;
  }
  .section__about {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .section__about__title h2 {
    font-size: 22px;
  }
  .section__about__cont {
    margin-top: 30px;
    display: block;
  }
  .section__about__cont a {
    padding: 20px 30px;
    width: 100%;
    display: block;
  }
  .section__about__cont a:first-child {
    margin-right: 0;
    margin-bottom: 30px;
  }
  .section__about__cont a .section__title__left {
    margin-bottom: 25px;
  }
  .section__contact {
    padding-top: 60px;
    padding-bottom: 50px;
  }
  .section__contact__title span {
    font-size: 12px;
  }
  .section__contact__title h2 {
    margin-top: 10px;
    font-size: 22px;
  }
  .section__contact__logo {
    margin-bottom: -3px;
    width: 70%;
  }
  .section__contact__cont {
    margin-top: 40px;
    padding-top: 30px;
  }
  .section__contact__cont a {
    padding: 15px 40px;
    border-radius: 8px;
  }
  .section__contact__cont a img {
    width: 40px;
  }
  .section__contact__cont a p:first-child {
    font-size: 24px;
  }
  .section__contact__cont a p:last-child {
    font-size: 12px;
  }
  .section__contact__arrow {
    width: 20px !important;
  }
  .solution__title {
    top: -37px;
  }
  .solution__title h2 > span {
    font-size: 17px;
  }
  .solution__title h2 > span span {
    font-size: 22px;
  }
  .solution__title h2 img {
    margin-right: 5px;
    width: 160px;
  }
  .footer .container {
    padding-top: 35px;
    padding-bottom: 35px;
  }
  .footer__left a {
    margin: 0 auto 20px;
    width: 200px;
    display: inline-block;
  }
  .footer__left__top p {
    font-size: 12px;
  }
  .copyright {
    padding: 10px 0;
  }
  #topBtn {
    width: 40px;
    height: 40px;
    right: 15px;
    bottom: 30px;
  }
  .content__main {
    padding: 70px 0;
  }
  .page__main {
    padding: 40px 0;
  }
  .single-document .content__main,
  .single-blog .content__main,
  .search .content__main {
    margin-top: 50px;
  }
  .single-document .breadcrumb-inner,
  .single-blog .breadcrumb-inner,
  .search .breadcrumb-inner {
    padding-top: 25px;
    margin-bottom: 30px;
  }
  .page__header {
    margin-top: 50px;
    padding-top: 30px;
    padding-bottom: 45px;
  }
  .page__header__title > span {
    font-size: 12px;
  }
  .page__header__title h1 {
    margin-top: 12px;
    line-height: 1.4;
  }
  .page__header__main {
    font-size: 25px;
  }
  .page__header__catch {
    font-size: 22px;
  }
  .page__scroll {
    height: 40px;
    bottom: -20px;
  }
  .page__title {
    margin-bottom: 40px;
  }
  .page__title span {
    font-size: 14px;
  }
  .page__title h1 {
    margin-top: 10px;
    font-size: 26px;
  }
  .page__title h1:before {
    height: 8px;
  }
  .content-marketing .section__plan__cont {
    margin-top: 80px;
  }
  .content-marketing .section__plan__item:nth-child(1):before {
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 16px;
    top: -47px;
    border-radius: 10px;
  }
  .owned-media .section__concerns {
    padding-bottom: 40px;
  }
  .owned-media .section__concerns__solution__cont {
    margin-bottom: 60px;
  }
  .attribution .section__plan h3 > span:first-child {
    margin-right: 10px;
    font-size: 18px;
  }
  .attribution .section__plan h3 > span:last-child span {
    font-size: 24px;
  }
  .attribution .section__plan__item__inner {
    display: block;
    padding: 25px 30px;
  }
  .attribution .section__plan__item__inner ul {
    padding: 0;
    width: 100%;
  }
  .attribution .section__plan__item__inner ul li p {
    font-size: 16px;
  }
  .attribution .section__plan__left {
    margin-bottom: 25px;
    padding-right: 0;
    padding-bottom: 20px;
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #333333;
  }
  .attribution .section__plan__left img {
    margin: 0 auto;
    width: 160px;
  }
  .saiyou-ownedmedia .section__point__item:first-child {
    margin-bottom: 25px;
  }
  .breadcrumb-inner {
    padding-top: 35px;
  }
  .entry .wpcf7 input[type=text],
  .entry .wpcf7 input[type=email] {
    height: 40px;
  }
  .entry .wpcf7 textarea {
    min-height: 100px;
  }
  .entry .wpcf7 select {
    width: 100%;
    height: 40px;
    font-size: 14px;
  }
  .entry .wpcf7 input[type=submit] {
    width: 100%;
  }

  /*owndmedia section-FVボタンエリア*/
  .owndmedia-btn-box {
    display: block;
    margin-bottom: 70px;   
  }
  .owndmedia-btn-box .btn .btn__blue {
    margin: 0 0 30px 0;
  }
  /*owndmedia section-成功事例*/
  .container .owndmedia-caseintro p {
    font-size: 15px;
    padding:20px 0;
  }
  .supportcase-dataimage .container p {
    font-size: 1.2em;
  }
  .supportcase-dataimage .container img .sp {
    display: block;
  }
  .supportcase-dataimage .container img .pc {
    display: none;
  }

  /*owndmedia section-運用体制*/
  .container .owndmedia-operational-structure h3 {
    font-size: 1.3em;
  }
  .operational-structure-image .container img .sp {
    display: block;
  }
  .operational-structure-image .container img .pc {
    display: none;
  }

/*owndmedia section-契約の流れ*/
  .flow-contract .container ul li.flowlist {
    width: 349px;
    height: 374px;
    background-image: url(../images/s-owned/contractflow_bg_sp_01.png);
    background-repeat:none;
    background-size: contain;
    margin: 0 auto 20px auto;
  }
    .flow-contract .container ul li.flowlist .flexbox-contaractlist {
    display: block;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .flexbox-flow{
    display: flex;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .flownumber {
    width:92px;
    height:123px;
    padding:48px 0 0 14px;
    text-align: left;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .flownumber span {
    font-size: 2.8em;
    color: #ffffff;
    font-weight: 700;  
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .icatchimg {
    width: auto;
    height: 217px;
    padding:27px 0 0 0;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .icatchimg img {
    width:88%;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .koumoku-naiyou {
    height: 131px;
    padding: 0 13px;
    text-align: center;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .koumoku-naiyou h3 {
    color: #23746F;
    padding-bottom: 8px;
    font-size: 1.3em;
  }
  .flow-contract .container ul li.flowlist .flexbox-contaractlist .koumoku-naiyou p {
    font-size: 1.1em;
    font-weight: 400;
  }
  .flow-contract .container ul li.flowlistfinish {
    width: 349px;
    height: 343px;
    background-image: url(../images/s-owned/contractflow_bg_sp_02.png) ;
    background-repeat:none;
    background-size: contain;
    margin: 0 auto;
  }
    .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist {
    display: block;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .flexbox-flow{
    display: flex;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .flownumber {
    width:92px;
    height:123px;
    padding:48px 0 0 14px;
    text-align: left;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .flownumber span {
    font-size: 2.8em;
    color: #ffffff;
    font-weight: 700;  
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .icatchimg {
    width: auto;
    height: 217px;
    padding:27px 0 0 0;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .icatchimg img {
    width:88%;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .koumoku-naiyou {
    height: 131px;
    padding: 0 13px;
    text-align: center;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .koumoku-naiyou h3 {
    color: #23746F;
    padding-bottom: 8px;
    font-size: 1.3em;
  }
  .flow-contract .container ul li.flowlistfinish .flexbox-contaractlist .koumoku-naiyou p {
    font-size: 1.1em;
    font-weight: 400;


}/*# sourceMappingURL=sp.css.map */