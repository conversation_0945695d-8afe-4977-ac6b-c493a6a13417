@charset "UTF-8";
.page__header {
  display: none;
}

.content__main {
  padding-top: 0px;
}

@media (max-width: 768px) {
  body > div.fadein__cont.active > div.breadcrumb-inner {
    padding-top: 30px;
  }
}

.page-template-s-whitepaper * {
  text-decoration: none;
  list-style: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-family: '游ゴシック', sans-serif;
  font-weight: 700;
  color: #191919;
}

.page-template-s-whitepaper .pc {
  display: block;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .pc {
    display: none !important;
  }
}

.page-template-s-whitepaper .sp {
  display: none;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .sp {
    display: block;
  }
}

.page-template-s-whitepaper .red {
  color: #FA6B58;
}

.page-template-s-whitepaper .underline {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(70%, transparent), color-stop(0%, #F3C11D));
  background: linear-gradient(transparent 70%, #F3C11D 0%);
  display: inline;
  position: relative;
  z-index: 2;
}

.page-template-s-whitepaper .h2_above {
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #3C8B86;
  text-align: center;
  display: block;
  margin-bottom: 12px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .h2_above {
    font-size: 12px;
  }
}

.page-template-s-whitepaper h2 {
  font-size: 32px;
  text-align: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper h2 {
    font-size: 28px;
    line-height: 42px;
  }
}

.page-template-s-whitepaper img {
  max-width: 100%;
}

.page-template-s-whitepaper .mainvisual-section {
  padding: 35px 0 0 0;
  text-align: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section {
    padding-top: 0px;
  }
}

.page-template-s-whitepaper .mainvisual-section .above {
  padding: 40px 0 77px;
  height: auto;
  width: 100%;
  background-image: url(../images/newwhitepaper/mv_bg.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  position: relative;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .above {
    background-image: url(../images/newwhitepaper/mv_bg_sp.png);
    padding: 38px 0 40px;
  }
}

.page-template-s-whitepaper .mainvisual-section .above::before, .page-template-s-whitepaper .mainvisual-section .above::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  width: 1px;
  height: 40px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .above::before, .page-template-s-whitepaper .mainvisual-section .above::after {
    height: 20px;
  }
}

.page-template-s-whitepaper .mainvisual-section .above::before {
  bottom: 0;
  background-color: #fff;
}

.page-template-s-whitepaper .mainvisual-section .above::after {
  bottom: -40px;
  background-color: #333333;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .above::after {
    bottom: -20px;
  }
}

.page-template-s-whitepaper .mainvisual-section .above .h1_above {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  display: block;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .above .h1_above {
    font-size: 12px;
  }
}

.page-template-s-whitepaper .mainvisual-section .above h1 {
  font-size: 40px;
  margin: 13px 0 31px;
  color: #fff;
  display: inline-block;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .above h1 {
    font-size: 24px;
    margin: 10px 0 26px;
  }
}

.page-template-s-whitepaper .mainvisual-section .above .h1_bottom {
  font-size: 20px;
  color: #fff;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .above .h1_bottom {
    font-size: 16px;
    line-height: 200%;
  }
}

.page-template-s-whitepaper .mainvisual-section .container {
  text-align: center;
  width: 100%;
}

.page-template-s-whitepaper .mainvisual-section .container .bottom {
  max-width: 1080px;
  margin: 0 auto;
  padding: 25px 0 0 0;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom {
    padding: 32px 20px 24px;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul {
    font-size: 12px;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul li {
  font-weight: 400;
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul li:not(:first-of-type) {
  margin-left: 19px;
  position: relative;
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul li:not(:first-of-type)::before {
  content: '>';
  position: absolute;
  top: 0;
  left: -14px;
  font-size: 14px;
  font-weight: 400;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul li:not(:first-of-type)::before {
    font-size: 12px;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul li a {
  font-size: 14px;
  font-weight: 400;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .breadcrumb ul li a {
    font-size: 12px;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .client {
  margin-top: 48px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .client {
    margin-top: 32px;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .client h2 {
  font-size: 24px;
  display: inline-block;
  position: relative;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .client h2 {
    font-size: 20px;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .client h2::before, .page-template-s-whitepaper .mainvisual-section .container .bottom .client h2::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 2px;
  height: 26px;
  background-color: #191919;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .client h2::before, .page-template-s-whitepaper .mainvisual-section .container .bottom .client h2::after {
    display: none;
  }
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .client h2::before {
  left: -20px;
  -webkit-transform: rotate(-20deg);
          transform: rotate(-20deg);
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .client h2::after {
  right: -20px;
  -webkit-transform: rotate(20deg);
          transform: rotate(20deg);
}

.page-template-s-whitepaper .mainvisual-section .container .bottom .client .grid {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: (1fr)[7];
      grid-template-columns: repeat(7, 1fr);
  margin-top: 26px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .mainvisual-section .container .bottom .client .grid {
    -ms-grid-columns: (1fr)[3];
        grid-template-columns: repeat(3, 1fr);
    margin-top: 30px;
  }
  .page-template-s-whitepaper .mainvisual-section .container .bottom .client .grid img:nth-child(13), .page-template-s-whitepaper .mainvisual-section .container .bottom .client .grid img:nth-child(14) {
    margin-left: 50%;
  }
}

.page-template-s-whitepaper .benefit-section {
  padding: 80px 0 80px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section {
    padding: 24px 0 48px 0;
  }
}

.page-template-s-whitepaper .benefit-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container h2 .sp_mini {
    font-size: 16px;
    display: block;
  }
}

.page-template-s-whitepaper .benefit-section .container .benefit {
  margin: 48px auto 0;
  max-width: 880px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container .benefit {
    margin-top: 32px;
  }
}

.page-template-s-whitepaper .benefit-section .container .benefit .item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.page-template-s-whitepaper .benefit-section .container .benefit .item:not(:first-of-type) {
  margin-top: 42px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container .benefit .item:not(:first-of-type) {
    margin-top: 40px;
  }
}

.page-template-s-whitepaper .benefit-section .container .benefit .item .left {
  width: 73px;
  margin-right: 20px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container .benefit .item .left {
    min-width: 48px;
    max-width: 48px;
    margin-right: 17px;
  }
}

.page-template-s-whitepaper .benefit-section .container .benefit .item .right .h3_above {
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  color: #3C8B86;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container .benefit .item .right .h3_above {
    font-size: 12px;
  }
}

.page-template-s-whitepaper .benefit-section .container .benefit .item .right .h3_above::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  right: -120px;
  background-color: #808080;
  width: 100px;
  height: 1px;
}

.page-template-s-whitepaper .benefit-section .container .benefit .item .right h3 {
  font-size: 24px;
  margin-top: 12px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .benefit-section .container .benefit .item .right h3 {
    font-size: 16px;
    line-height: 200%;
  }
}

.page-template-s-whitepaper .problem-section {
  padding: 80px 0;
  background-color: #F5F6F7;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section {
    padding: 48px 0 48px 0;
  }
}

.page-template-s-whitepaper .problem-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .problem-section .container .problem {
  margin-top: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem {
    display: block;
  }
}

.page-template-s-whitepaper .problem-section .container .problem .item {
  width: 340px;
  text-align: center;
  margin-top: 36px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item {
    width: unset;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .page-template-s-whitepaper .problem-section .container .problem .item:not(:first-of-type) {
    margin-top: 36px;
  }
}

.page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble, .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  width: 320px;
  height: 77px;
  background-color: #fff;
  border-radius: 8px 8px 0 8px;
  padding: 0 25px;
  text-align: left;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble, .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv {
    width: 220px;
    height: 80px;
    padding: 0 15px;
  }
}

.page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble:first-child, .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv:first-child {
  margin-left: 20px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble:first-child, .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv:first-child {
    margin-left: 0;
  }
}

.page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble::before, .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv::before {
  content: '';
  position: absolute;
  right: 0;
  bottom: -16px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 16px 16px 0;
  border-color: transparent #ffffff transparent transparent;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble::before, .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv::before {
    bottom: -12px;
    border-width: 0 12px 12px 0;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv {
    border-radius: 8px 8px 8px 0;
  }
  .page-template-s-whitepaper .problem-section .container .problem .item .thought .bubble-rv::before {
    left: 0;
    -webkit-transform: rotate(-90deg);
            transform: rotate(-90deg);
  }
}

.page-template-s-whitepaper .problem-section .container .problem .item .thought p {
  font-size: 15px;
  color: #808080;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item .thought p {
    font-size: 14px;
  }
}

.page-template-s-whitepaper .problem-section .container .problem .item img {
  width: 110px;
  margin: 0 auto;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item img {
    width: 80px;
    margin: 0 0 0 36px;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .problem-section .container .problem .item:nth-child(2) {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .page-template-s-whitepaper .problem-section .container .problem .item:nth-child(2) .thought p {
    border-radius: 8px 8px 8px 0;
  }
  .page-template-s-whitepaper .problem-section .container .problem .item:nth-child(2) .thought p::before {
    right: unset;
    left: 0;
    border-width: 16px 16px 0 0;
    border-color: #ffffff transparent transparent transparent;
  }
  .page-template-s-whitepaper .problem-section .container .problem .item:nth-child(2) img {
    margin: 0 36px 0 0;
  }
}

.page-template-s-whitepaper .strength-section {
  padding: 80px 0;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section {
    padding: 48px 0 48px 0;
  }
}

.page-template-s-whitepaper .strength-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .strength-section .container .middle {
  text-align: center;
  margin-bottom: 48px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .middle {
    margin-bottom: 32px;
  }
}

.page-template-s-whitepaper .strength-section .container .middle .mini {
  font-size: 24px;
  display: block;
  line-height: 48px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .middle .mini {
    font-size: 16px;
    line-height: 42px;
  }
}

.page-template-s-whitepaper .strength-section .container .middle .mini .gray {
  color: #808080;
}

.page-template-s-whitepaper .strength-section .container .middle .big {
  font-size: 32px;
  line-height: 48px;
  display: block;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .middle .big {
    font-size: 28px;
    line-height: 42px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength {
  margin-top: 80px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength {
    margin-top: 53px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item {
    display: block;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item:not(:first-of-type) {
  margin-top: 100px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item:not(:first-of-type) {
    margin-top: 68px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item .content {
  margin-right: 64px;
  position: relative;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .content {
    margin: 0;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item .content .h3_above {
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  color: #3C8B86;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .content .h3_above {
    font-size: 12px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item .content .h3_above::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  right: -120px;
  background-color: #808080;
  width: 100px;
  height: 1px;
}

.page-template-s-whitepaper .strength-section .container .strength .item .content h3 {
  display: inline-block;
  font-size: 24px;
  margin: 32px 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .content h3 {
    font-size: 20px;
    margin: 24px 0 32px;
    display: inline-block;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item .content h3 .red {
  font-size: 32px;
}

.page-template-s-whitepaper .strength-section .container .strength .item .content h3 .red::before {
  bottom: 5px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .content h3 .red::before {
    bottom: .5px;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .content h3 .red {
    font-size: 24px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item .content .desc {
  font-size: 16px;
  line-height: 200%;
  font-weight: 400;
}

.page-template-s-whitepaper .strength-section .container .strength .item .content .number {
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  position: absolute;
  font-size: 120px;
  color: #F0F0F0;
  top: 0;
  right: 0;
  line-height: 48px;
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .content .number {
    top: 0;
    font-size: 80px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item .image {
  min-width: 476px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item .image {
    min-width: unset;
    margin-top: 40px;
  }
}

.page-template-s-whitepaper .strength-section .container .strength .item:nth-of-type(even) {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.page-template-s-whitepaper .strength-section .container .strength .item:nth-of-type(even) .content {
  margin-right: 0;
  margin-left: 64px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .strength-section .container .strength .item:nth-of-type(even) .content {
    margin: 0;
  }
}

.page-template-s-whitepaper .work-section {
  padding: 80px 0;
  background-color: #EBF9F3;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section {
    padding: 48px 0 48px 0;
  }
}

.page-template-s-whitepaper .work-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .work-section .container .h2_bottom {
  margin-top: 32px;
  font-size: 16px;
  line-height: 200%;
  font-weight: 400;
  text-align: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .h2_bottom {
    margin-top: 40px;
    text-align: left;
  }
}

.page-template-s-whitepaper .work-section .container .work {
  margin-top: 32px;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: (1fr)[2];
      grid-template-columns: repeat(2, 1fr);
  gap: 32px 48px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work {
    display: block;
  }
}

.page-template-s-whitepaper .work-section .container .work .item {
  -ms-grid-row-align: end;
  -ms-flex-item-align: end;
      align-self: flex-end;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work .item:not(:first-of-type) {
    margin-top: 24px;
  }
}

.page-template-s-whitepaper .work-section .container .work .item .images {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work .item .images {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}

.page-template-s-whitepaper .work-section .container .work .item .images img {
  width: 250px;
  -webkit-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work .item .images img:not(:first-of-type) {
    margin-left: 18px;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work .item .images img {
    width: 160px;
  }
}

.page-template-s-whitepaper .work-section .container .work .item p {
  font-size: 16px;
  text-align: center;
  margin-top: 16px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work .item p {
    margin-top: 12px;
  }
}

.page-template-s-whitepaper .work-section .container .work .item:nth-child(3) .images img {
  width: 132px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .work-section .container .work .item:nth-child(3) .images img {
    width: 100px;
  }
}

.page-template-s-whitepaper .voice-section {
  padding: 80px 0;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section {
    padding: 48px 0 48px 0;
  }
}

.page-template-s-whitepaper .voice-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .voice-section .container .voice {
  margin-top: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice {
    display: block;
  }
}

.page-template-s-whitepaper .voice-section .container .voice .item {
  padding: 24px;
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  width: 320px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item {
    padding: 20px 24px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item:not(:first-of-type) {
    margin-top: 32px;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item .heading {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding: 0 20px;
  }
}

.page-template-s-whitepaper .voice-section .container .voice .item .heading h3 {
  font-size: 16px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item .heading h3 {
    font-size: 14px;
  }
}

.page-template-s-whitepaper .voice-section .container .voice .item .heading img {
  width: 92px;
  margin: 16px 0 19px;
  margin-right: auto;
  margin-left: auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item .heading img {
    width: 67px;
    margin: 0;
  }
}

.page-template-s-whitepaper .voice-section .container .voice .item .red {
  font-size: 18px;
  text-align: left;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item .red {
    margin-top: 20px;
  }
}

.page-template-s-whitepaper .voice-section .container .voice .item .desc {
  text-align: left;
  font-size: 14px;
  line-height: 200%;
  font-weight: 400;
  margin-top: 21px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .voice-section .container .voice .item .desc {
    margin-top: 25px;
  }
}

.page-template-s-whitepaper .price-section {
  padding: 80px 0;
  background-color: #EBF9F3;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section {
    padding: 48px 0 48px 0;
  }
}

.page-template-s-whitepaper .price-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .price-section .container .box {
  margin-top: 32px;
}

.page-template-s-whitepaper .price-section .container .box h3 {
  background: #191919;
  color: #fff;
  text-align: center;
  font-size: 24px;
  padding: 18px 0;
  border-radius: 8px 8px 0 0;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box h3 {
    font-size: 18px;
  }
}

.page-template-s-whitepaper .price-section .container .box h3 span {
  color: #fff;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box h3 span {
    font-size: 14px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content {
  padding: 24px 0 52px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content {
    padding: 0;
    padding-bottom: 37px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex {
    display: block;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .flex .item {
  width: 280px;
  padding: 0 40px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex .item {
    padding: 16px 0;
    margin: 0 auto;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .flex .item:not(:first-of-type) {
  border-left: 1px solid #808080;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex .item:not(:first-of-type) {
    border-left: none;
    border-top: 1px solid #808080;
  }
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex .item .heading {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .flex .item img {
  width: 120px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex .item img {
    margin: 0;
    margin-right: 18px;
    width: 80px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .flex .item h4 {
  margin-top: 0px;
  font-size: 18px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex .item h4 {
    font-size: 16px;
    margin: 0;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .flex .item p {
  margin-top: 24px;
  font-size: 14px;
  line-height: 200%;
  text-align: left;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .flex .item p {
    margin-top: 8px;
    padding: 0 9px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .price-wrap {
  margin: 28px 0 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .price-wrap {
    margin: 0 0 16px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .price-wrap .term {
  font-size: 18px;
  line-height: 28px;
  margin-right: 28px;
  color: #808080;
}

.page-template-s-whitepaper .price-section .container .box .content .price-wrap .price {
  font-size: 18px;
  line-height: 28px;
}

.page-template-s-whitepaper .price-section .container .box .content .price-wrap .price .red {
  font-size: 32px;
  line-height: 28px;
}

.page-template-s-whitepaper .price-section .container .box .content .rice {
  text-align: center;
  margin-bottom: 24px;
  font-size: 12px;
  color: #808080;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .rice {
    margin-bottom: 16px;
    font-size: 11px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .ul {
  font-size: 20px;
  display: inline-block;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .price-section .container .box .content .ul {
    font-size: 16px;
  }
}

.page-template-s-whitepaper .price-section .container .box .content .ul span::before {
  height: 6px;
  bottom: 0;
}

.page-template-s-whitepaper .flow-section {
  padding: 80px 0;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section {
    padding: 48px 0 48px 0;
  }
}

.page-template-s-whitepaper .flow-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow {
  margin-top: 42px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow {
    margin-top: 32px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.page-template-s-whitepaper .flow-section .container .flow .item:not(:first-of-type) {
  margin-top: 25px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item:not(:first-of-type) {
    margin-top: unset;
    padding-top: 15px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item:not(:last-of-type) {
  position: relative;
}

.page-template-s-whitepaper .flow-section .container .flow .item:not(:last-of-type)::before {
  content: '';
  position: absolute;
  left: 53px;
  bottom: 7px;
  background-color: #3C8B86;
  width: 2px;
  height: calc(100% - 110px);
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item:not(:last-of-type)::before {
    left: 24px;
    height: calc(100% - 50px);
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item:not(:last-of-type)::after {
  content: '';
  position: absolute;
  left: 50px;
  bottom: 1px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6.86px 4px 0 4px;
  border-color: #3C8B86 transparent transparent transparent;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item:not(:last-of-type)::after {
    left: 21px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .number_circle {
  margin-right: 40px;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: #3C8B86;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .number_circle {
    min-width: 50px;
    max-width: 50px;
    min-height: 50px;
    max-height: 50px;
    margin-right: 20px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .number_circle .step {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .number_circle .step {
    font-size: 10px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .number_circle .number {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 32px;
  font-weight: 500;
  color: #fff;
  line-height: 1;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .number_circle .number {
    font-size: 18px;
    margin-top: 0px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .content {
  padding-bottom: 25px;
  border-bottom: 1px solid #E0E0E0;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.page-template-s-whitepaper .flow-section .container .flow .item .content .heading {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}

.page-template-s-whitepaper .flow-section .container .flow .item .content .heading::before {
  content: '';
  position: absolute;
  left: 53px;
  bottom: -10px;
  background-color: #808080;
  width: 1px;
  height: 20px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .content .heading::before {
    display: none;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .content .heading img {
  width: 110px;
  margin-right: 48px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .content .heading img {
    width: 32px;
    margin-right: 20px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .content .heading h3 {
  font-size: 24px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .content .heading h3 {
    font-size: 17px;
  }
}

.page-template-s-whitepaper .flow-section .container .flow .item .content .desc {
  font-size: 16px;
  font-weight: 400;
  line-height: 28px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .flow-section .container .flow .item .content .desc {
    margin-top: 17px;
    font-size: 14px;
  }
}

.page-template-s-whitepaper .flow-section .container .rice {
  margin-top: 32px;
  font-size: 14px;
  line-height: 200%;
  font-weight: 400;
  color: #808080;
}

.page-template-s-whitepaper .faq-section {
  padding: 80px 0 80px 0;
  background-color: #F5F6F7;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section {
    padding: 48px 0 20px 0;
  }
}

.page-template-s-whitepaper .faq-section .container {
  max-width: 1080px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq {
  margin-top: 32px;
}

.page-template-s-whitepaper .faq-section .container .faq__items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.page-template-s-whitepaper .faq-section .container .faq__item:not(:first-child) {
  margin-top: 32px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq__item:not(:first-child) {
    margin-top: 20px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq__item:not(:last-child) {
  border-bottom: 1px solid #E0E0E0;
}

.page-template-s-whitepaper .faq-section .container .faq__item-q {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 32px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq__item-q {
    margin-bottom: 24px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq__item-q--icon {
  width: 64px;
  height: 64px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq__item-q--icon {
    width: 40px;
    height: 40px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq__item-q--text {
  font-size: 20px;
  line-height: 1.5;
  padding-left: 24px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq__item-q--text {
    font-size: 16px;
    padding-left: 10px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq__item-a {
  margin-bottom: 32px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq__item-a {
    margin-bottom: 20px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq__item-a--text {
  font-size: 16px;
  font-weight: 400;
}

.page-template-s-whitepaper .faq-section .container .faq__item-mc {
  margin-bottom: 0;
}

.page-template-s-whitepaper .faq-section .container .faq dl div {
  padding-bottom: 25px;
  border-bottom: 1px solid #E0E0E0;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq dl div {
    padding-bottom: unset;
    border-bottom: unset;
  }
}

.page-template-s-whitepaper .faq-section .container .faq dl div:not(:first-of-type) {
  margin-top: 39px;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq dl div:not(:first-of-type) {
    padding-top: unset;
    margin-top: 45px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq dl div dt {
  padding-left: 80px;
  font-size: 20px;
  position: relative;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq dl div dt {
    padding: 0 0 0 50px;
    font-size: 16px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq dl div dt::before {
  content: '';
  background-image: url(../images/newwhitepaper/q.png);
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 64px;
  width: 64px;
  height: 64px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq dl div dt::before {
    width: 40px;
    height: 40px;
    top: -5px;
  }
}

.page-template-s-whitepaper .faq-section .container .faq dl div dd {
  margin-top: 44px;
  font-size: 16px;
  line-height: 200%;
  font-weight: 400;
}

@media (max-width: 768px) {
  .page-template-s-whitepaper .faq-section .container .faq dl div dd {
    margin-top: 29px;
  }
}
/*# sourceMappingURL=newwhitepaper.css.map */