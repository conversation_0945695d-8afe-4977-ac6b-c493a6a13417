@import './variable';

@media screen and (max-width: 1600px) {
  .section {
    &__service-detail {
      &__footer {
        p {
          width: calc(100% - 450px);
          text-align: left;
        }
        img {
          right: 0;
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .mv {
    &__title h1 {
      font-size: 40px;
    }
    &__scroll .txt {
      margin-bottom: 10px;
    }
  }
  // ----------- section -----------
  .section {
    &__service-detail {
      .section__title {
        padding-left: 70px;
      }
      &__item {
        .wrap p {
          span {
            display: inline;
          }
        }
        ul li .body p span {
          display: inline;
        }
      }
      &__footer {
        p {
          font-size: 24px;
        }
      }
    }
  }
  // ----------- single -----------
  .single {
    &__blog {
      &__share {
        .sns__list {
          li {
            width: calc(50% - 50px);
            margin: 10px 0 0;
            &:nth-of-type(2n) {
              margin-left: auto;
            }
          }
          li + li {
            margin-left: 0;
          }
        }
      }
    }
  }
  .is_fixed {
    width: calc(1040px * 0.23);
    right: calc((100vw - 1040px) / 2);
  }
}

@media screen and (max-width: 1080px) {
  .is_fixed {
    width: calc(100% * 0.23 - 10px);
    right: 20px;
  }
}

@media screen and (max-width: 979px) {
  // ----------- page -----------
  .mv {
    &__bg {
      &__inner {
        padding-top: 120%;
        .img {
          background-image: url(../images/illust/appmart-top_sp.png);
        }
      }
    }

    &__content {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    &__title {
      text-align: center;
      h1 {
        font-size: 40px;
      }
    }
    &__scroll {
      .txt {
        margin-bottom: 20px;
      }
      .bar {
        height: 80px;
      }
    }
  }
  .page01 {
    &__service {
      &__item {
        width: calc(33.333% - (40px / 3));
        & + & {
          margin-left: 20px;
        }
        &__pic {
          height: 180px;
          img {
            width: 90%;
          }
        }
        &__title {
          h3 {
            font-size: 30px;
          }
        }
        &__list {
          padding: 0 20px 30px;
          li {
            font-size: 14px;
            padding-left: 28px;
            &:before {
              width: 20px;
            }
          }
        }
      }
    }

    &__mission {
      &__lead {
        span {
          display: inline-block;
          & + span {
            margin-top: 15px;
          }
        }
      }
    }

    &__client {
      &__bottom {
        h3 {
          span.line {
            max-width: 200px;
          }
        }
        &___cont {
          &__item {
            img {
              width: 150px;
            }
          }
        }
      }
    }

    &__blog {
      > .container {
        display: block;
      }
      &__bg {
        width: 100%;
        border-radius: 0;
        right: 0;
        bottom: 0;
      }
      &__left {
        > .hide_sp {
          display: none;
        }
      }
      &__right {
        margin-top: 45px;
        padding-left: 0;
        > .show_sp {
          display: block;
        }
      }
    }
  }

  .company-overview {
    &__item {
      dt {
        width: 200px;
      }
      dd {
        width: calc(100% - 200px);
      }
    }
  }
  // ----------- section -----------
  .section {
    &__service__intro {
      padding-bottom: 60px;

      .service__intro__lead {
        h2 {
          font-size: 30px;
        }
      }
      .service__intro__cont {
        margin-top: 60px;

        &__item {
          &__pic {
            height: 180px;
          }
        }

        &__item::before {
          height: 60px;
          top: -30px;
        }
        &__item__title {
          h3 {
            font-size: 30px;
          }
        }
        &__item__list {
          padding-left: 15px;
          li {
            padding-left: 25px;
            &::before {
              width: 20px;
            }
            a {
              font-size: 16px;
            }
          }
        }
      }
    }
    &__service-detail {
      padding-top: 60px;

      .section__title {
        font-size: 24px;
      }
      &__item {
        .outerbox {
          &--strategy,
          &--analysis {
            &::after {
              width: 40px;
              height: 40px;
              left: -20px;
            }
          }
          &--creative {
            &::after {
              width: 40px;
              height: 40px;
              right: -20px;
            }
          }
          &--strategy {
            &::before {
              width: 40px;
              height: 40px;
              top: -20px;
              left: -20px;
            }
          }
        }
        .middlebox {
          padding: 60px 0;
        }
        .innerbox {
          padding: 50px 40px;
          &:before {
            font-size: 80px;
          }
        }

        .item__title {
          h3 {
            font-size: 28px;
          }
        }

        .wrap {
          padding-top: 10px;
          p,
          img {
            width: 50%;
          }
        }

        ul li {
          // width: calc(50% - 20px);
          .head {
            padding: 20px;
            h4 {
              .icon {
                width: 40px;
                height: 40px;
                background-size: 25px 25px;
              }
              span {
                font-size: 15px;
              }
            }
          }
          .body {
            padding: 20px;
            .more_btn {
              a {
                font-size: 15px;
              }
            }
          }
        }
        .innerbox--strategy {
          .item__title {
            &::before,
            &::after {
              left: -124px;
            }
          }
        }
        .innerbox--creative {
          .item__title {
            &::before,
            &::after {
              right: -124px;
            }
          }
        }
        .innerbox--analysis {
          .item__title {
            &::before,
            &::after {
              left: -124px;
            }
          }
        }
      }
      &__footer {
        p {
          padding-left: 0;
          width: calc(100% - 330px);
          span {
            display: inline;
          }
        }
        img {
          margin-left: 30px;
          width: 300px;
        }
      }
    }
  }
  // ----------- archive -----------

  .post-type-archive-document .content__main {
    padding-top: 30px;
  }

  .archive {
    &__wp {
      &__tab {
        ul li {
          font-size: 16px;
        }
      }
      &__cont {
        &__item {
          width: calc((100% - 30px) / 2);
          margin-right: 0;
          &:not(:nth-of-type(2n)) {
            margin-right: 30px;
          }
        }
      }
    }

    &__blog {
      &__primary {
        .entry__list {
          margin-top: 20px;
        }
      }
    }
  }

  .pagination {
    nav.navigation {
      display: block;
    }
  }
  // ----------- single -----------
  .single {
    &__blog {
      &__related-posts__list {
        > li {
          width: calc((100% - 40px) / 3);
          margin-right: 20px;
        }
      }
    }
  }
  // ----------- sidebar -----------
  .sidebar {
    width: 28%;

    .searchbox {
      input#searchBox {
        height: 50px;
        padding: 10px;
        &::placeholder {
          font-size: 10px;
        }
      }
      button#searchSubmit {
        top: 13px;
        right: 10px;
      }
    }
    .category {
      h4 {
        font-size: 24px;
      }
    }
  }
  .is_fixed {
    width: calc(100% * 0.28 - 11px);
  }
  // ----------- category label -----------
  .category_label {
    li {
      padding: 8px 10px 8px 40px;
    }
  }
}
@media screen and (max-width: 767px) {
  .show_sp {
    display: block;
  }
  .hide_sp {
    display: none;
  }

  .mv {
    margin-top: 50px;

    &__title {
      h1 {
        font-size: 23px;
        letter-spacing: 0.04em;
        span {
          border-bottom: 5px solid #3c8b86;
          padding: 6px 8px;
        }
      }
    }
    &__scroll {
      .txt {
        font-size: 12px;
        margin-bottom: 15px;
      }
      .bar {
        width: 1px;
        height: 120px;
      }
    }
  }

  // ----------- page -----------
  .page01 {
    &__service {
      padding-bottom: 50px;

      &__cont {
        display: block;
      }
      &__item {
        width: 100%;
        &::before {
          display: none;
        }
        & + & {
          margin-left: 0;
          margin-top: 30px;
        }

        &__pic {
          height: initial;
          padding-top: 30%;
          position: relative;
          img {
            width: 70%;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        &__title {
          img {
            min-width: 30px;
            min-height: 40px;
          }
          h3 {
            font-size: 20px;
            margin-left: 10px;
          }
        }
      }
    }

    &__mission {
      background-size: 70%;
      padding: 60px 0;

      &__bg {
        width: 300vw;
      }
      &__lead {
        h3 {
          line-height: 1;
          span {
            display: inline-block;
            font-size: 24px;
            line-height: 1.3;
          }
        }
      }
      &__wrap {
        display: block;

        img {
          margin-right: auto;
          max-width: 300px;
        }
      }
      &__text {
        p {
          font-size: 16px;
        }
      }
    }

    &__client {
      &__top {
        padding: 60px 0 0;

        &::before {
          top: 300px;
        }

        .works {
          &__list {
            display: block;
            margin-top: 30px;
          }
          &__item {
            width: 100%;
          }
        }

        &__bglogo {
          font-size: 60px;
          top: 10px;
        }
      }

      &__bottom {
        padding: 40px 0;

        h3 {
          text-align: center;
          &::after {
            display: none;
          }

          span {
            font-size: 18px;
            line-height: (59 / 36);
            padding-right: 0;
          }
        }

        &__cont {
          &__item {
            width: calc((100% - 60px) / 3);
            margin-right: 0;
            &:not(:nth-of-type(3n)) {
              margin-right: 30px;
            }
            &:last-of-type {
              margin-left: auto;
              margin-right: auto;
            }

            img {
              width: 100%;
            }
          }
        }
      }
    }

    &__blog {
      padding: 60px 0;

      &__bg {
        width: 100%;
        border-radius: 0;
        right: 0;
        bottom: 0;
      }

      > .container {
        display: block;
      }

      &__left {
        width: 100%;
      }
      &__right {
        width: 100%;
        padding-left: 0;
        margin-top: 30px;
        &__item {
          &__body {
            h3 {
              font-size: 15px;
              margin-bottom: 15px;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
  .entry {
    padding: 30px 0;

    h2 {
      font-size: 22px;
      margin: 60px 0 20px;
    }
    h3 {
      font-size: 20px;
    }
    h4 {
      font-size: 18px;
      font-weight: bold;
    }
    h5 {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .company-overview {
    &__item {
      display: block;
      padding: 22px 0;
    }

    dt,
    dd {
      width: 100%;
      padding: 0;
    }

    dt {
      margin-bottom: 15px;
    }

    dd {
      .hq_map {
        padding-top: 114%;
      }

      address {
        line-height: (38 / 32);
      }
    }
  }

  // ----------- section -----------

  .section {
    &__service__intro {
      background: linear-gradient(
        transparent 0%,
        transparent 10%,
        $color--base--green 10%,
        $color--base--green 100%
      );
      padding-bottom: 60px;

      &__lead {
        h2 {
          font-size: 22px;
        }
      }
      .service__intro {
        &__cont {
          display: block;
          &__item {
            width: 100%;
            margin: 20px auto 0;
            &:before {
              display: none;
            }
            &__pic {
              height: initial;
              position: relative;
              padding-top: 30%;
              img {
                width: 70%;
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
        }
      }
    }
    &__service-detail {
      padding: 60px 0;
      .section__title {
        font-size: 20px;
      }
      &__item {
        .outerbox {
          width: calc(100% - 20px);
        }

        .middlebox {
          width: calc(100% - 30px);
          &--strategy {
            border-radius: 0 0 0 30px;
          }
          &--creative {
            border-radius: 0 30px 30px 0;
          }
          &--analysis {
            border-radius: 30px 0 0 0;
          }
        }

        .innerbox {
          right: -20px;
          padding: 20px 10px;
          &--creative {
            left: -20px;
            .wrap {
              img {
                margin: 0 auto 15px;
              }
            }
          }
        }

        .item__title {
          padding-left: 15px;
          &::before {
            width: 23px;
          }
          &::after {
            width: 9px;
            height: 9px;
          }

          img {
            width: 30px;
            height: 30px;
          }
          h3 {
            font-size: 20px;
            margin-left: 10px;
          }
        }
        .wrap {
          display: block;
          p {
            width: 100%;
            font-size: 14px;
          }
          img {
            max-width: 250px;
            margin: 0 auto 15px;
          }
        }
        ul {
          display: block;
          li {
            width: 100%;
          }
        }
        .innerbox::before {
          font-size: 40px;
          top: 20px;
        }
        .innerbox--strategy {
          .item__title {
            &::before {
              left: -30px;
            }
            &::after {
              left: -36px;
            }
          }
        }
        .innerbox--creative {
          .item__title {
            &::before {
              right: -30px;
            }
            &::after {
              right: -36px;
            }
          }
        }
        .innerbox--analysis {
          .item__title {
            &::before {
              left: -30px;
            }
            &::after {
              left: -36px;
            }
          }
        }
      }
      &__footer {
        padding: 20px 0;
        p {
          width: calc(100% - 120px);
          font-size: 16px;
        }
        img {
          width: 120px;
        }
      }
    }
    &__search {
      .section_title {
        margin-bottom: 15px;
        h2 {
          font-size: 24px;
        }
      }
      .entry__list {
        padding: 20px 0;
      }

      .entry__body {
        h3 {
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
    &__not-found {
      .section_title {
        margin-bottom: 15px;
        h2 {
          font-size: 24px;
        }
      }
    }
  }
  // ----------- archive -----------
  .post-type-archive-document .content__main {
    padding-top: 30px;
  }
  .archive {
    &__wp {
      &__tab {
        margin-bottom: 30px;
        ul {
          display: block;
          li.tab {
            width: 100%;
            border: 1px solid $color--text--strong;
            background: transparent;
            font-size: 16px;
            color: $color--text--strong;
            text-align: left;
            padding-left: 20px;
            padding-right: 20px;

            & + li {
              margin-top: 5px;
            }

            &:not(.all) {
              display: none;
            }

            &.all {
              position: relative;
            }

            .tab_toggle {
              display: inline-block;
              width: 25px;
              height: 25px;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              right: 20px;
              background: transparent url(../images/icon/round-arrow_grey.png)
                no-repeat center center / contain;
              z-index: 2;
              &.is-open {
                background-image: url(../images/icon/round-arrow_grey-up.png);
              }
            }
          }
        }
      }
      &__cont {
        &__title {
          font-size: 20px;
          margin-bottom: 25px;
        }

        &__item {
          width: 100%;
          margin-right: 0;
          margin: 0 auto 40px;
          &:not(:nth-of-type(2n)) {
            margin-right: 0;
          }
        }
      }
    }

    &__blog {
      &__cont {
        display: block;
      }
      &__primary {
        .entry__list {
          padding: 20px 0;
        }

        .entry__body {
          h3 {
            font-size: 14px;
            line-height: 22px;
          }
          &--update {
            // margin-right: 12px;
            // margin-bottom: 8px;
            // &::after {
            //   content: "\A";
            //   white-space: pre;
            // }
          }
        }
      }
    }
  }

  .pagination {
    .nav-links {
      padding: 10px 0;
      a,
      span {
        margin: 4px 2px;
        font-size: 14px;
        padding: 8px 13px;
      }
    }
  }

  // ----------- BLOG section__document-download -----------
  .sample-form {
    textarea {
      max-width: 90% !important;
    }
  }
  .section {
    &__document-download {
      &__left {
        width: 100%;
        margin-bottom: 24px;
      }
      &__right {
        width: 100%;
        text-align: center;
        p {
          font-size: 14px;
          font-weight: bold;
        }
      }
      &__eyecatch {
      }
      &__cont {
        display: block;
      }
    }
  }

  // ----------- single -----------
  .single {
    &__wp {
      &__cont {
        display: block;
      }
      &__left {
        width: 100%;
      }

      &__content {
        &__box {
          .body p {
            font-size: 16px;
          }
        }
      }

      &__right {
        width: 100%;
        margin-top: 35px;
      }
    }

    &__blog {
      &__cont {
        display: block;
      }

      &__top {
        h1 {
          line-height: 35px;
          font-size: 22px;
        }
      }
      &__eyecatch {
        margin-top: 30px;
      }
      &__share {
        padding: 20px 0 30px;
        border-top: 1px solid #808080;

        p {
          font-size: 20px;
          margin-bottom: 20px;
          span {
            padding-left: 30px;
            &::before {
              width: 24px;
              height: 24px;
            }
          }
        }

        .sns__list {
          justify-content: center;

          li {
            width: 50px;
            height: 50px;
            &:nth-of-type(2n) {
              margin-left: 25px;
            }
          }

          li + li {
            margin-left: 25px;
          }

          li a {
            padding: 12px 10px;
            img {
              width: 25px;
              height: 25px;
            }
            span {
              display: none;
            }
          }
        }
      }
      &__author {
        display: block;
        .pic {
          margin: 0 auto;
          margin-bottom: 20px;
        }

        .body {
          width: 100%;
        }
      }
      &__related-posts {
        h2 {
          font-size: 20px;
          &::before {
            width: 30px;
            height: 2.5px;
          }
        }
        &__list {
          display: block;
          > li {
            width: 100%;
            a {
              @include flex(initial, stretch, wrap);

              h3 {
                margin-top: 0;
              }
              time {
                display: block;
              }
              .category_label {
                li {
                  padding: 7px 7px 7px 24px;
                  margin-top: 10px;
                  &::before {
                    width: 10px;
                    height: 10px;
                  }
                }
              }
              time {
                margin-top: 5px;
                color: $color--text;
              }
            }

            .left {
              width: 35%;
            }
            .right {
              width: calc(65% - 10px);
              margin-left: auto;
            }
          }
        }
      }
    }
  }

  // ----------- common for blog-archive / blog-single -----------
  .primary {
    width: 100%;
  }
  .sidebar {
    width: 100%;
    margin-left: 0;
    margin-top: 100px;

    .category {
      h4 {
        font-size: 20px;
        &::before {
          width: 30px;
          height: 2.5px;
        }
      }
    }
    .banner {
      // width: 100%;
      a img {
        width: 100%;
      }
    }
  }
  .category_label {
    li {
      font-size: 10px;
      padding: 7px 15px 7px 30px;
      &::before {
        width: 15px;
        height: 15px;
      }
    }
  }
  .category_label.sp {
    display: block;
    margin-top: 8px;
    display: none;
  }
  .category_label.pc {
    display: none;
  }
}
