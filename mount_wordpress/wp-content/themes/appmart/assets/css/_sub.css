.show_sp {
  display: none;
}

.company-overview__item {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
  border-top: 1px solid #808080;
}
.company-overview__item:last-of-type {
  border-bottom: 1px solid #808080;
}
.company-overview__item:nth-of-type(7) dd {
  line-height: 1.8;
}
.company-overview__item dt {
  width: 300px;
  font-weight: bold;
  line-height: 1.1875;
  padding: 30px 0 30px 30px;
  box-sizing: border-box;
  color: #333;
}
.company-overview__item dd {
  width: calc(100% - 300px);
  line-height: 1.1875;
  padding: 30px 0;
  color: #333;
}
.company-overview__item dd .heading {
  font-weight: bold;
}
.company-overview__item dd .hq_map {
  width: 100%;
  padding-top: 28.66%;
  margin: 25px 0;
  position: relative;
}
.company-overview__item dd .hq_map iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.company-overview__item dd a[href^=tel] {
  pointer-events: none;
  color: #333333;
}
.company-overview__item dd address {
  line-height: 1.8;
}

.section__service__intro {
  background: linear-gradient(transparent 0%, transparent 30%, #E1EDE8 30%, #E1EDE8 100%);
  padding-bottom: 150px;
}
.section__service__intro .service__intro__lead h2 {
  color: #006835;
  font-size: 40px;
  font-weight: bold;
  text-align: center;
}
.section__service__intro .service__intro__cont {
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: wrap;
  margin-top: 85px;
}
.section__service__intro .service__intro__cont__item {
  width: calc(33.333% - 20px);
  background: #ffffff;
  box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.16);
  border-radius: 20px;
  margin-right: 30px;
  position: relative;
}
.section__service__intro .service__intro__cont__item::before {
  content: "";
  width: 1px;
  height: 96px;
  position: absolute;
  top: -50px;
  right: initial;
  bottom: initial;
  left: 50%;
  background: #333333;
  transform: translateX(-50%);
  z-index: initial;
}
.section__service__intro .service__intro__cont__item:nth-child(3n) {
  margin-right: 0;
}
.section__service__intro .service__intro__cont__item__pic {
  height: 200px;
  overflow: hidden;
}
.section__service__intro .service__intro__cont__item__pic img {
  width: 90%;
  margin: auto;
}
.section__service__intro .service__intro__cont__item__title {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  margin: 25px 0 40px;
}
.section__service__intro .service__intro__cont__item__title h3 {
  font-size: 40px;
  font-weight: bold;
  margin-left: 20px;
}
.section__service__intro .service__intro__cont__item__list {
  padding-left: 30px;
  padding-bottom: 40px;
}
.section__service__intro .service__intro__cont__item__list li {
  padding-left: 42px;
  position: relative;
}
.section__service__intro .service__intro__cont__item__list li::before {
  content: "";
  width: 32px;
  height: 1px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  background: #333333;
  transform: translateY(-50%);
  z-index: initial;
}
.section__service__intro .service__intro__cont__item__list li a {
  color: #333333;
  font-size: 18px;
}
.section__service__intro .service__intro__cont__item__list li + li {
  margin-top: 30px;
}
.section__service-detail {
  padding-top: 150px;
}
.section__service-detail .section__title {
  max-width: 1050px;
  margin: auto;
  font-size: 32px;
  font-weight: bold;
  padding-left: 50px;
  box-sizing: border-box;
}
.section__service-detail__item + .section__service-detail__item {
  margin-top: -3px;
}
.section__service-detail__item .outerbox {
  width: calc(100% - 50px);
  max-width: 1050px;
  margin: 0 auto;
  position: relative;
}
.section__service-detail__item .outerbox::after {
  content: "";
  width: 56px;
  height: 56px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: transparent url(../images/icon/round-arrow_orange.png) no-repeat center center/cover;
}
.section__service-detail__item .outerbox--strategy {
  border-radius: 0 0 0 70px;
}
.section__service-detail__item .outerbox--strategy::before {
  content: "";
  width: 56px;
  height: 56px;
  position: absolute;
  top: -28px;
  right: initial;
  bottom: initial;
  left: -28px;
  background: initial;
  transform: translateY(-50%);
  z-index: initial;
  background: transparent url(../images/icon/round-arrow_black.png) no-repeat center center/cover;
}
.section__service-detail__item .outerbox--strategy::after {
  left: -28px;
}
.section__service-detail__item .outerbox--creative {
  border-radius: 0 70px 0 0;
}
.section__service-detail__item .outerbox--creative::after {
  right: -28px;
}
.section__service-detail__item .outerbox--analysis::after {
  left: -28px;
}
.section__service-detail__item .middlebox {
  width: calc(100% - 70px);
  border-left: 3px solid #333333;
  border-bottom: 3px solid #333333;
  border-radius: 0 0 0 70px;
  padding: 150px 0;
}
.section__service-detail__item .middlebox--creative {
  border-left: 0;
  border-top: 3px solid #333333;
  border-right: 3px solid #333333;
  border-bottom: 3px solid #333333;
  border-radius: 0 70px 70px 0;
  margin-right: 0;
  margin-left: auto;
}
.section__service-detail__item .middlebox--analysis {
  border-top: 3px solid #333333;
  border-bottom: 0;
  border-radius: 70px 0 0 0;
}
.section__service-detail__item .innerbox {
  width: 100%;
  max-width: 1050px;
  border-radius: 20px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.16);
  padding: 80px 100px;
  position: relative;
  box-sizing: border-box;
}
.section__service-detail__item .innerbox::before {
  display: block;
  width: 100%;
  content: attr(data-en);
  font-family: "Open Sans", sans-serif;
  font-size: 140px;
  font-weight: bold;
  color: rgba(255, 126, 118, 0.2);
  text-transform: uppercase;
  text-align: center;
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.section__service-detail__item .innerbox--strategy {
  right: -75px;
}
.section__service-detail__item .innerbox--creative {
  left: -75px;
}
.section__service-detail__item .innerbox--creative::before {
  color: rgba(51, 186, 210, 0.2);
}
.section__service-detail__item .innerbox--analysis {
  right: -75px;
}
.section__service-detail__item .innerbox--analysis::before {
  color: rgba(161, 228, 197, 0.2);
}
.section__service-detail__item .item__title {
  display: flex;
  align-items: center;
  position: relative;
}
.section__service-detail__item .item__title h3 {
  font-size: 40px;
  font-weight: bold;
  margin-left: 20px;
}
.section__service-detail__item .item__title::before {
  content: "";
  width: 120px;
  height: 3px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #333333;
}
.section__service-detail__item .item__title::after {
  content: "";
  border-radius: 50%;
  width: 15px;
  height: 15px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #F3C11D;
  z-index: 2;
}
.section__service-detail__item .wrap {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
.section__service-detail__item .wrap img {
  width: 45%;
  margin-left: auto;
}
.section__service-detail__item .wrap p {
  width: 50%;
}
.section__service-detail__item .wrap p span {
  display: inline-block;
}
.section__service-detail__item .innerbox--strategy .item__title::before {
  left: -175px;
}
.section__service-detail__item .innerbox--strategy .item__title::after {
  left: -182px;
}
.section__service-detail__item .innerbox--creative .wrap {
  flex-direction: row;
}
.section__service-detail__item .innerbox--creative .wrap img {
  margin-left: 0;
  margin-right: auto;
}
.section__service-detail__item .innerbox--creative .item__title {
  justify-content: flex-end;
}
.section__service-detail__item .innerbox--creative .item__title::before {
  right: -175px;
}
.section__service-detail__item .innerbox--creative .item__title::after {
  right: -183px;
}
.section__service-detail__item .innerbox--analysis .item__title::before {
  left: -175px;
}
.section__service-detail__item .innerbox--analysis .item__title::after {
  left: -182px;
}
.section__service-detail__item ul {
  margin-top: 45px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.section__service-detail__item ul li {
  margin-right: 50px;
  width: calc(50% - 25px);
  margin-top: 40px;
}
.section__service-detail__item ul li:nth-child(2n) {
  margin-right: 0;
}
.section__service-detail__item ul li .head {
  background: #333333;
  padding: 30px 20px;
  border-radius: 20px 20px 0 0;
}
.section__service-detail__item ul li .head h4 {
  display: flex;
  align-items: center;
}
.section__service-detail__item ul li .head h4 .icon {
  flex-shrink: 0;
  background: #ffffff;
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background-position: center center;
  background-size: 40px 40px;
  background-repeat: no-repeat;
  margin-right: 15px;
}
.section__service-detail__item ul li .head h4 span:not(.icon) {
  display: inline-block;
  color: #ffffff;
  font-size: 20px;
  font-weight: bold;
}
.section__service-detail__item ul li .body {
  background: #FAFAFA;
  border-radius: 0 0 20px 20px;
  padding: 30px;
}
.section__service-detail__item ul li .body p {
  font-size: 14px;
  line-height: 25px;
}
.section__service-detail__item ul li .body p span {
  display: inline-block;
}
.section__service-detail__item ul li .body .more_btn {
  text-align: right;
  margin-top: 25px;
}
.section__service-detail__item ul li .body .more_btn a {
  display: inline-block;
  font-size: 16px;
  color: #333333;
  padding-right: 30px;
  position: relative;
}
.section__service-detail__item ul li .body .more_btn a::after {
  content: "";
  width: 18px;
  height: 11px;
  position: absolute;
  top: 50%;
  right: 0;
  bottom: initial;
  left: initial;
  background: transparent;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/svg/right_orange.svg);
  background-position: center;
  background-size: contain;
}
.section__service-detail__footer {
  width: 100%;
  padding: 50px 0;
  background: #3c8b86;
}
.section__service-detail__footer > .container {
  position: relative;
}
.section__service-detail__footer p {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
}
.section__service-detail__footer p span {
  display: inline-block;
}
.section__service-detail__footer img {
  width: 450px;
  height: auto;
  position: absolute;
  right: -225px;
  top: 50%;
  transform: translateY(-50%);
}
.section__search > .container {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.section__search .section_title h2 {
  color: #006835;
  font-size: 40px;
}
.section__not-found > .container {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.section__not-found .section_title {
  margin-bottom: 30px;
}
.section__not-found .section_title h2 {
  color: #006835;
  font-size: 40px;
}

.single__wp__cont {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.single__wp__left {
  width: 48%;
}
.single__wp__eyecatch {
  padding-top: 76%;
  position: relative;
  margin-bottom: 30px;
  border-radius: 6px;
  border: 1px solid #808080;
}
.single__wp__eyecatch .img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: transparent;
  transform: initial;
  z-index: initial;
  background-position: center center;
  background-size: cover;
  border-radius: 6px;
}
.single__wp__title h1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.7;
  margin-bottom: 40px;
}
.single__wp__content p {
  line-height: 1.7;
}
.single__wp__content p + p {
  margin-top: 30px;
}
.single__wp__content__box {
  margin: 50px 0 30px;
}
.single__wp__content__box .head {
  font-size: 20px;
  font-weight: bold;
  background: #333333;
  color: #ffffff;
  text-align: center;
  border-radius: 20px 20px 0 0;
  padding: 15px 0;
}
.single__wp__content__box .body {
  background: #FAFAFA;
  border-radius: 0 0 20px 20px;
  padding: 30px 25px;
}
.single__wp__content__box .body p {
  font-size: 18px;
  padding-left: 34px;
  position: relative;
}
.single__wp__content__box .body p::before {
  content: "";
  width: 24px;
  height: 24px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  background: initial;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/svg/check.svg);
  background-size: cover;
}
.single__wp__content__box .body p + p {
  margin-top: 23px;
}
.single__wp__right {
  width: 48%;
  margin-left: auto;
}
.single__wp__right p {
  margin-bottom: 45px;
}
.single__blog__cont {
  display: flex;
}
.single__blog__top h1 {
  font-size: 32px;
  font-weight: bold;
  line-height: 50px;
  padding-top: 25px;
  margin-bottom: 32px;
  position: relative;
}
.single__blog__top h1:before {
  content: "";
  width: 64px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 20px;
}
.single__blog__top time {
  display: block;
  font-size: 14px;
  text-align: right;
  margin-bottom: 15px;
}
.single__blog__top--update {
  margin-right: 12px;
}
.single__blog__eyecatch {
  width: 100%;
  margin-top: 80px;
}
.single__blog__eyecatch img {
  width: 100%;
  height: auto;
}
.single__blog__share {
  padding: 60px 0 40px;
}
.single__blog__share p {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
}
.single__blog__share p span {
  display: inline-block;
  padding-left: 44px;
  position: relative;
}
.single__blog__share p span::before {
  content: "";
  width: 32px;
  height: 32px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  background: initial;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/icon/share.png);
  background-position: center;
  background-size: contain;
}
.single__blog__share .sns__list {
  display: flex;
  flex-wrap: wrap;
}
.single__blog__share .sns__list li {
  width: calc((100% - 60px) / 4);
}
.single__blog__share .sns__list li + li {
  margin-left: 20px;
}
.single__blog__share .sns__list a {
  display: block;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  padding: 17px 10px;
  border-radius: 6px;
  background: #1DA1F2;
}
.single__blog__share .sns__list a:hover {
  opacity: 0.6 !important;
}
.single__blog__share .sns__list a img {
  display: inline-block;
  width: 20px;
  height: 20px;
}
.single__blog__share .sns__list a span {
  display: inline-block;
  color: #ffffff;
  margin-left: 8px;
  font-size: 14px;
}
.single__blog__share .sns__list a.facebook {
  background: #305097;
}
.single__blog__share .sns__list a.hatena {
  background: #00A4DE;
}
.single__blog__share .sns__list a.line {
  background: #00B900;
}
.single__blog__share .sns__list a.line span {
  text-transform: uppercase;
}
.single__blog__author {
  background: #F5F6F7;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
}
.single__blog__author .pic {
  width: 150px;
  height: 150px;
}
.single__blog__author .pic img {
  border-radius: 50%;
}
.single__blog__author .body {
  width: calc(100% - 182px);
  margin-left: auto;
}
.single__blog__author .body .name {
  color: #006835;
  margin-bottom: 25px;
}
.single__blog__author .body .name span {
  font-weight: bold;
}
.single__blog__related-posts {
  margin-top: 40px;
}
.single__blog__related-posts h2 {
  font-size: 28px;
  font-weight: bold;
  padding-top: 16px;
  margin-bottom: 30px;
  position: relative;
}
.single__blog__related-posts h2::before {
  content: "";
  width: 60px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: initial;
  z-index: initial;
  border-radius: 20px;
}
.single__blog__related-posts__list {
  display: flex;
  justify-content: initial;
  align-items: initial;
  flex-wrap: wrap;
}
.single__blog__related-posts__list > li {
  width: calc((100% - 80px) / 3);
  margin-right: 40px;
  margin-bottom: 25px;
}
.single__blog__related-posts__list > li:nth-of-type(3n) {
  margin-right: 0;
}
.single__blog__related-posts__list > li a {
  display: block;
}
.single__blog__related-posts__list > li a:hover .related-posts__eyecatch .img {
  transform: scale(1.1);
}
.single__blog__related-posts__list > li a .related-posts__eyecatch {
  overflow: hidden;
  padding-top: 55%;
  position: relative;
  border-radius: 6px;
}
.single__blog__related-posts__list > li a .related-posts__eyecatch .img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1);
  transition: transform 0.5s;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.single__blog__related-posts__list > li a h3 {
  font-size: 13px;
  font-weight: bold;
  line-height: 1.2307692308;
  margin-top: 18px;
}
.single__blog__related-posts__list > li a time {
  display: none;
}

.archive__blog__cont {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.archive__wp__tab {
  margin-bottom: 80px;
}
.archive__wp__tab ul {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.archive__wp__tab ul li {
  width: calc((100% - 30px) / 3);
  border: 1px solid #808080;
  color: #808080;
  font-size: 20px;
  font-weight: bold;
  border-radius: 6px;
  text-align: center;
  padding: 10px 0;
  margin-right: 15px;
  box-sizing: border-box;
}
.archive__wp__tab ul li:nth-of-type(3n) {
  margin-right: 0;
}
.archive__wp__tab ul li:hover {
  cursor: pointer;
}
.archive__wp__tab ul li.is-active {
  border-color: #006835;
  background: #006835;
  color: #ffffff;
}
.archive__wp__cont__title {
  font-size: 40px;
  font-weight: bold;
  color: #006835;
  text-align: center;
  margin-bottom: 50px;
}
.archive__wp__cont .group {
  display: none;
}
.archive__wp__cont .group.is-show {
  display: block;
}
.archive__wp__cont__list {
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: wrap;
}
.archive__wp__cont__item {
  width: calc((100% - 120px) / 3);
  margin-bottom: 60px;
  margin-right: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.archive__wp__cont__item:nth-of-type(3n) {
  margin-right: 0;
}
.archive__wp__cont__item__top h2 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.7;
  margin-bottom: 10px;
}
.archive__wp__cont__item__top p {
  font-size: 14px;
  line-height: 1.7;
}
.archive__wp__cont__item__eyecatch {
  padding-top: 75%;
  position: relative;
  margin-bottom: 10px;
  border: 1px solid #808080;
  border-radius: 6px;
  overflow: hidden;
}
.archive__wp__cont__item__eyecatch img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: initial;
  transform: initial;
  z-index: initial;
  border-radius: 6px;
}

.primary {
  width: 70%;
}

.sidebar {
  width: 23%;
  margin-left: auto;
}
.sidebar .category {
  margin-bottom: 50px;
}
.sidebar .category h4 {
  font-size: 28px;
  font-weight: bold;
  padding-top: 20px;
  padding-left: 40px;
  position: relative;
}
.sidebar .category h4::before {
  content: "";
  width: 60px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: initial;
  z-index: initial;
  border-radius: 20px;
}
.sidebar .category h4::after {
  content: "";
  width: 28px;
  height: 28px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: 0;
  left: 0;
  background: initial;
  transform: initial;
  z-index: initial;
  background-image: url(../images/icon/tag.png);
  background-position: center center;
  background-size: cover;
}
.sidebar .category__list {
  margin-top: 30px;
}
.sidebar .category__list li {
  padding-left: 15px;
  position: relative;
}
.sidebar .category__list li::before {
  content: "-";
  /* width: 5px; */
  /* height: 1px; */
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  color: #333333;
  transform: translateY(-50%);
  z-index: initial;
}
.sidebar .category__list li + li {
  margin-top: 15px;
}
.sidebar .category__list a {
  color: #333333;
}
.sidebar .banner {
  margin-top: 24px;
}
.sidebar .banner__list li + li {
  margin-top: 40px;
}
.sidebar .banner__list li a {
  display: block;
}
.sidebar .banner__list li a:hover {
  opacity: 0.6 !important;
}
.sidebar .banner__list li a img {
  width: 100%;
}

.is_fixed {
  position: fixed;
  width: 248.4px;
  top: 80px;
  right: calc((100% - 1080px) / 2);
  z-index: 990;
}

.tr_reset {
  transform: initial;
}

.search_box_area .searchform {
  height: 100px;
  position: relative;
  max-width: 100%;
}
.search_box_area input.searchfield {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 100%;
  height: 64px;
  padding: 0 10px 0 25px;
  position: absolute;
  top: 0;
  left: 0;
  outline: 0;
  border: 1px solid #333333;
  border-radius: 6px;
  box-sizing: border-box;
}
.search_box_area input.searchsubmit {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 20px;
  right: 24px;
  background: none;
  padding: 0;
}

.category_label {
  display: none;
  width: 100%;
  overflow: auto;
  white-space: nowrap;
}
.category_label li {
  display: inline-block;
  font-size: 14px;
  color: #ffffff;
  background: #3D3D3F;
  border-radius: 6px;
  padding: 11px 13px 11px 40px;
  margin-right: 10px;
  position: relative;
}
.category_label li:before {
  content: "";
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 13px;
  background: transparent;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/icon/tag-w.png);
  background-position: center center;
  background-size: cover;
}
.category_label li a {
  color: #ffffff;
}

.pagination h2.screen-reader-text {
  display: none;
}
.pagination .nav-links {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 50px 0 30px;
}
.pagination .nav-links span, .pagination .nav-links a {
  display: inline-block;
  color: #333;
  background: #ffffff;
  font-size: 16px;
  border: 1px solid #808080;
  box-shadow: 0px 0px 3px rgba(128, 128, 128, 0.16);
  border-radius: 6px;
  padding: 10px 15px;
  margin: 0 10px;
}
.pagination .nav-links span:hover, .pagination .nav-links a:hover {
  opacity: 0.6 !important;
}
.pagination .nav-links span.current, .pagination .nav-links a.current {
  background: #333;
  color: #ffffff;
}

.entry {
  padding: 40px 0 60px;
}
.entry h2 {
  font-size: 24px;
  border-top: 3px solid #3c8b86;
  border-bottom: 3px solid #3c8b86;
  padding: 15px 0;
  margin: 60px 0 20px;
  font-weight: bold;
}
.entry h3 {
  font-size: 22px;
  border-left: 7px solid #3c8b86;
  padding: 5px 20px 5px 10px;
  margin: 40px 0 15px;
}
.entry h4 {
  font-size: 20px;
  margin: 30px 0 10px;
  position: relative;
  padding-left: 25px;
}
.entry h4::before {
  content: "";
  width: 18px;
  height: 18px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: url(../images/svg/tick.svg) no-repeat center center/cover;
}
.entry h5 {
  font-size: 18px;
  margin: 30px 0 10px;
}
.entry p {
  margin-bottom: 20px;
  line-height: 1.9em;
  letter-spacing: 1.5px;
  text-align: justify;
}
.entry a {
  color: #FA6B58;
  text-decoration: none;
}
.entry a:hover {
  color: #DD523F;
}
.entry ul {
  padding-left: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
}
.entry ul > li {
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 1.5;
  list-style-type: disc;
  color: #333333;
}
.entry ol {
  padding-left: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
}
.entry ol > li {
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 1.5;
  list-style-type: decimal;
  color: #333333;
}
.entry li {
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 1.5;
  list-style-type: disc;
  color: #333333;
}
.entry a.cta-link {
  display: table;
  margin: 30px auto;
  text-align: center;
  font-weight: bold;
  padding: 15px 30px;
  width: initial;
  background-color: #fa6b58;
  box-shadow: 0 4px 0 #dd523f;
  border-radius: 6px;
  box-sizing: border-box;
  color: #ffffff !important;
}
.entry a.cta-link:hover {
  transform: translateY(4px);
  box-shadow: none !important;
  opacity: 1 !important;
}
.entry img {
  margin: 20px auto;
  max-width: 100%;
  height: auto;
  pointer-events: auto;
}
.entry .marker-yellow {
  background: linear-gradient(to bottom, transparent 70%, #f3c11d 70%);
}
.entry .marker-green {
  background: linear-gradient(to bottom, transparent 70%, #3C8B86 70%);
}
.entry strong {
  font-weight: bold;
}
.entry table {
  color: #333;
  margin-bottom: 20px;
  line-height: 1.7;
}
.entry table thead th {
  background: #006835;
  color: #ffffff;
  padding: 10px 15px;
  border-right: #ffffff solid 1px;
  border-bottom: #ffffff solid 1px;
}
.entry table thead th:last-child {
  border-right: #006835 solid 1px;
}
.entry table tbody td {
  background: #ffffff;
  padding: 10px 15px;
  border-left: #006835 solid 1px;
  border-bottom: #006835 solid 1px;
  border-right: #006835 solid 1px;
  vertical-align: top;
}
.entry table tbody th {
  background: #006835;
  padding: 10px 15px;
  color: #ffffff;
  border-bottom: #ffffff solid 1px;
  vertical-align: top;
}
.entry table.darkgreen {
  border-collapse: collapse;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.7;
}
.entry table.darkgreen th {
  background: #006835;
  color: #ffffff;
}
.entry table.darkgreen th, .entry table.darkgreen td {
  border: solid 1px #ededed;
  padding: 6px 10px;
}
.entry > div.pointbox {
  position: relative;
  margin: 2em 0 1.5em;
  padding: 1em 1.5em;
  border: solid 3px #006835;
}
.entry > div.pointbox > ul {
  margin-top: 20px;
}
.entry > div.pointbox .box-title {
  position: absolute;
  display: inline-block;
  top: -27px;
  left: -3px;
  padding: 3px 10px;
  height: 25px;
  line-height: 25px;
  vertical-align: middle;
  font-size: 17px;
  background: #006835;
  color: #ffffff;
  font-weight: bold;
  border-radius: 5px 5px 0 0;
}
.entry > div.pointbox p {
  padding: 0;
  margin: 0;
}
.entry .mokuji {
  position: relative;
  margin: 2em 0;
  padding: 25px 10px 7px;
  border: solid 2px #006835;
}
.entry .mokuji > ul {
  margin-top: 20px;
}
.entry .mokuji .box-title {
  position: absolute;
  display: inline-block;
  top: -2px;
  left: -2px;
  padding: 3px 10px;
  height: 25px;
  line-height: 25px;
  vertical-align: middle;
  font-size: 17px;
  background: #006835;
  color: #ffffff;
  font-weight: bold;
}
.entry .mokuji p {
  padding: 0;
  margin: 0;
}
.entry iframe {
  width: 100%;
}

.blog-widget {
  display: flex;
  justify-content: space-around;
}
.blog-widget__ele {
  margin: 0 10px;
}
.blog-widget__ele a {
  position: relative;
  z-index: 2;
}

.entry__list {
  margin-top: 45px;
}
.entry__list > li {
  border-top: 1px solid #808080;
  padding: 30px 0;
}
.entry__list > li:last-of-type {
  border-bottom: 1px solid #808080;
}
.entry__list a {
  display: block;
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: wrap;
}
.entry__list a:hover .entry__eyecatch .img {
  transform: scale(1.1);
}
.entry__list a .left {
  width: 33%;
}
.entry__list a .right {
  width: 63%;
  margin-left: auto;
}

.entry__eyecatch {
  overflow: hidden;
  padding-top: 56%;
  position: relative;
  border-radius: 6px;
}
.entry__eyecatch .img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1);
  transition: transform 0.5s;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.entry__body {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.entry__body h3 {
  font-size: 20px;
  font-weight: bold;
  line-height: 32px;
  margin-bottom: 20px;
}
.entry__body time {
  display: block;
  font-size: 14px;
  color: #333333;
  font-style: normal;
}
.entry__body--update {
  margin-right: 12px;
}

.category_label.sp {
  display: none;
}

.archive .casestudy.content__main {
  padding-top: 35px;
}
@media (max-width: 767px) {
  .archive .casestudy.content__main {
    padding-top: 27px;
  }
}
.archive .casestudy .archive__wp p.center {
  margin-bottom: 44px;
  line-height: 32px;
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp p.center {
    text-align: center;
  }
}
@media (max-width: 767px) {
  .archive .casestudy .archive__wp p.center {
    margin-bottom: 31px;
  }
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp__tab {
    margin-bottom: 48px;
  }
}
.archive .casestudy .archive__wp__tab ul li.is-active {
  border-color: #3C8B86;
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp__tab ul li.is-active {
    background-color: #3C8B86;
  }
}
@media (max-width: 767px) {
  .archive .casestudy .archive__wp__tab ul li.tab {
    color: #3C8B86;
    border-color: #3C8B86;
  }
  .archive .casestudy .archive__wp__tab ul li.tab .tab_toggle {
    background-image: url(../images/icon/round-arrow_green.svg);
  }
}
.archive .casestudy .archive__wp__cont__title {
  color: #3C8B86;
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp__cont__title {
    margin-bottom: 38px;
    font-size: 32px;
  }
}
.archive .casestudy__lists {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-template-columns: repeat(auto-fill, minmax(min(250px, 100%), 1fr));
  grid-gap: 32px 28px;
  justify-content: center;
  width: 100%;
}
@media (max-width: 768px) {
  .archive .casestudy__lists {
    grid-gap: 24px 15px;
  }
}
.archive .casestudy__lists__item {
  padding-bottom: 20px;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.29);
  border-radius: 6px;
  overflow: hidden;
}
.archive .casestudy__lists__item__link {
  display: grid;
  grid-template-columns: 20px 1fr 20px;
}
.archive .casestudy__lists__item__link > *:not(img) {
  grid-column: 2;
}
.archive .casestudy__lists__item__link__img {
  grid-column: 1/4;
  margin-bottom: 16px;
  width: 100%;
  height: auto;
}
.archive .casestudy__lists__item__link h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
}
.archive .casestudy__lists__item__link p {
  margin-bottom: 10px;
  color: #707070;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.3;
}
.archive .casestudy__lists__item__link__cat {
  justify-self: start;
  padding: 6px 8px;
  color: #3C8B86;
  font-size: 12px;
  line-height: 1;
  border: 1px solid #3C8B86;
  border-radius: 4px;
}

.single-casestudy .breadcrumb-inner {
  margin-inline: auto;
  padding-block: 0 58px;
  max-width: 756px;
}

.single__casestudy {
  margin-inline: auto;
  width: 100%;
  max-width: 756px;
}
.single__casestudy__cont h1 {
  position: relative;
  margin-bottom: 32px;
  padding-top: 25px;
  font-size: 32px;
  font-weight: bold;
  line-height: 50px;
}
.single__casestudy__cont h1:before {
  content: "";
  width: 64px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 20px;
}
@media (max-width: 768px) {
  .single__casestudy__cont h1 {
    margin-bottom: 25px;
    font-size: 22px;
    line-height: 35px;
  }
}
.single__casestudy__cont__cat {
  display: inline-block;
  margin-bottom: 40px;
  padding: 6px 8px;
  color: #3C8B86;
  font-size: 12px;
  line-height: 1;
  border: 1px solid #3C8B86;
  border-radius: 4px;
}
@media (max-width: 768px) {
  .single__casestudy__cont__cat {
    margin-bottom: 30px;
  }
}
.single__casestudy__cont__img {
  display: block;
  margin-bottom: 60px;
  width: 100%;
  height: auto;
}
@media (max-width: 768px) {
  .single__casestudy__cont__img {
    margin-bottom: 50px;
  }
}

.archive .webinar.content__main {
  padding-top: 35px;
}
@media (max-width: 767px) {
  .archive .webinar.content__main {
    padding-top: 27px;
  }
}
.archive .webinar .archive__wp p {
  margin-bottom: 44px;
  line-height: 32px;
}
@media (max-width: 767px) {
  .archive .webinar .archive__wp p {
    margin-bottom: 31px;
  }
}
.archive .webinar__lists {
  padding-block: 30px;
  border: 0 solid #DCDCDC;
  border-top-width: 1px;
}
@media (min-width: 769px) {
  .archive .webinar__lists {
    padding-inline: 40px;
  }
}
.archive .webinar__lists:last-of-type {
  border-bottom-width: 1px;
}
.archive .webinar__lists__link {
  display: grid;
  justify-content: start;
  grid-gap: 32px;
}
@media (min-width: 769px) {
  .archive .webinar__lists__link {
    grid-template-columns: 500px 1fr;
  }
}
@media (max-width: 768px) {
  .archive .webinar__lists__link {
    grid-gap: 25px;
  }
}
.archive .webinar__lists__link:hover {
  opacity: 0.8;
}
.archive .webinar__lists__link__img {
  width: 100%;
  height: auto;
}
.archive .webinar__lists__link__dtl {
  display: grid;
  place-content: start;
  place-items: start;
}
.archive .webinar__lists__link__dtl__status {
  display: grid;
  place-items: center;
  margin-bottom: 10px;
  padding-inline: 10px;
  height: 28px;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  border-radius: 4px;
}
.archive .webinar__lists__link__dtl__status.open {
  background-color: #3C8B86;
}
.archive .webinar__lists__link__dtl__status.close {
  background-color: #707070;
}
.archive .webinar__lists__link__dtl h3 {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: bold;
  line-height: 1.5;
}
.archive .webinar__lists__link__dtl .date, .archive .webinar__lists__link__dtl .venue {
  display: grid;
  align-items: center;
  grid-auto-flow: column;
  grid-gap: 16px;
  color: #191919;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}
.archive .webinar__lists__link__dtl .date::before, .archive .webinar__lists__link__dtl .venue::before {
  content: "";
  width: 24px;
  height: 24px;
  background: 50% 50%/contain no-repeat;
}
.archive .webinar__lists__link__dtl .date {
  margin-bottom: 8px;
}
.archive .webinar__lists__link__dtl .date::before {
  background-image: url(../images/icon/webinar-time.svg);
}
.archive .webinar__lists__link__dtl .venue {
  margin-bottom: 16px;
}
.archive .webinar__lists__link__dtl .venue::before {
  background-image: url(../images/icon/webinar-map.svg);
}
.archive .webinar__lists__link__dtl .category {
  display: grid;
  place-items: center;
  padding-inline: 8px;
  height: 24px;
  color: #3C8B86;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  border: 1px solid #3c8b86;
  border-radius: 4px;
}
.archive .webinar__lists__link p {
  margin-bottom: 10px;
  color: #707070;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.3;
}
.archive .webinar__lists__link__cat {
  justify-self: start;
  padding: 6px 8px;
  color: #3C8B86;
  font-size: 12px;
  line-height: 1;
  border: 1px solid #3C8B86;
  border-radius: 4px;
}
/*# sourceMappingURL=_sub.css.map */