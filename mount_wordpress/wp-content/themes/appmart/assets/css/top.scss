@import './variable';

.show_sp {
	display: none;
}

// ==============================================
// mv
// ==============================================
.mv {
	margin-top: 80px;

	&__bg {
		&__inner {
			position: relative;
			padding-top: 51%;
			.img {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				background: url(../images/illust/appmart-top.png) no-repeat center center / cover;
			}
		}
	}

	&__content {
		width: 100%;
		position: absolute;
		top: 12%;
		left: 0;
	}

	&__title {

		h1 {
			font-size: 50px;
			font-weight: bold;
			letter-spacing: 0.072em;
			color: #191919;
			line-height: 1;
			// line-height: (100 / 56);

			span {
				display: inline-block;
				background: #FFF;
				border-bottom: 10px solid $color--text--strong;
				padding: 15px 35px;
				margin-bottom: 10px;
			}

		}
	}
	&__scroll {
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;

		a, span {
			display: block;
			width: 50px;
		}
		a:hover {
			opacity: 0.6;
		}
		.txt {
			font-family: 'Open Sans', sans-serif;
			font-weight: 600;
			font-size: 16px;
			color: $color--text;
			writing-mode: vertical-lr;
			text-transform: uppercase;
			text-align: center;
			margin-bottom: 30px;
		}
		.bar {
			width: 2px;
			height: 120px;
			background: $color--text--strong;
			position: relative;
			left: 5px;
			&::before {
				content:'';
				@include absolute(7px, 7px, 0, initial, initial, 50%, $color--accent--orange, translateX(-50%), 2);
				border-radius: 50%;
			}
		}
	}
}

// ==============================================
// service
// ==============================================
.page01 {
	&__service {
		padding: 0 0 150px;

		&__cont {
			@include flex(initial, stretch, wrap);
			margin-top: 85px;
		}
		&__item {
			width: calc(33.333% - 20px);
			background: #FFF;
			box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.16);
			border-radius: 20px;
			position: relative;
			&::before {
				content:'';
				@include absolute(1px, 96px, -50px, initial, initial, 50%, $color--text, translateX(-50%), initial);
			}
			
			& + & {
				margin-left: 30px;
			}

			&__pic {
				height: 200px;
				overflow: hidden;
				img {
					width: 90%;
					margin: auto;
				}
			}

			&__title {
				@include flex(center, center, nowrap);
				margin: 25px 0 40px;
				h3 {
					font-size: 40px;
					font-weight: bold;
					margin-left: 20px;
				}
			}

			&__list {
				padding-left: 30px;
				padding-bottom: 40px;
				li {
					font-size: 18px;
					padding-left: 42px;
					position: relative;
					&::before {
						content:'';
						@include absolute(32px, 1px, 50%, initial, initial, 0, $color--text, translateY(-50%), initial);
					}
					a {
						display: block;
						color: $color--text;
						padding: 0.3em;
					}
				}
				li + li {
					margin-top: 30px;
				}
			}
		}
	}
}

// ==============================================
// mission
// ==============================================
.page01 {
	&__mission {
		background-image: url(../images/bg/appmart-logo-w.png);
		background-repeat: no-repeat;
		background-position: center center;
		background-size: 50%;
		padding: 150px 0;
		position: relative;
		overflow: hidden;

		&__bg {
			@include absolute(160vw, 100%, 50%, initial, initial, 50%, $color--base--green, translate(-50%, -50%), -1);
			border-radius: 50%;
		}
		&__lead {
			margin: 70px 0;
	
			h3 {
				text-align: center;
				font-size: 50px;
				font-weight: bold;
				color: $color--text--strong;
				letter-spacing: 0.064em;
			}
			
			span {
				font-size: 50px;
				background: $color--white;
			}
		}

		&__wrap {
			@include flex(initial, center, wrap);
			
			img {
				max-width: 400px;
				margin-left: auto;
			}
		}
		&__text {
			p {
				font-size: 18px;
				line-height: (36 / 18);
			}

			p + p {
				margin-top: 40px;
			}
			.fw_b {font-weight: bold;}
		}

	}


	
}

// ==============================================
// client
// ==============================================

.page01 {
	&__client {

		&__top {
			padding: 150px 0 0;
			position: relative;
			&::before {
				content:'';
				@include absolute(100%, initial, 500px, 0, 0, 0, $color--base--green, initial, -1);
			}

			&__bglogo {
				font-family: 'Open Sans', sans-serif;
				color: $color--white;
				font-size: 140px;
				font-weight: bold;
				text-align: center;
				text-transform: uppercase;
				opacity: 0.5;
				position: relative;
				top: 18px;
			}
		}

		&__bottom {
			padding: 80px 0;

			h3 {
				line-height: 1px;
				margin-bottom: 30px;
				position: relative;
				&::after {
					content:'';
					@include absolute(100%, 1px, 50%, 0, 0, 0, $color--text, translateY(-50%), -1);
				}

				span {
					display: initial;
					padding-right: 10px;
					background: $color--white;
					font-size: 24px;
					font-weight: bold;
					color: $color--text--strong;
					position: relative;
				}
			}
	
			&__cont {
				display: flex;
				align-items: center;
				flex-wrap: wrap;

				&__item {
					width: calc((100% - 320px) / 5);
					min-height: 40px;
					margin-top: 30px;
					margin-right: 80px;

					&:nth-of-type(5n) {
						margin-right: 0;
					}

					background-position: center center;
					background-size: contain;
					background-repeat: no-repeat;

					&:nth-of-type(1) {
						min-height: 27px;
						background-image: url(../images/logo/brand_sb.png);
					}
					&:nth-of-type(2) {
						background-image: url(../images/logo/brand_usen.png);
					}
					&:nth-of-type(3) {
						min-height: 27px;
						background-image: url(../images/logo/brand_dentsutec.png);
					}
					&:nth-of-type(4) {
						background-image: url(../images/logo/brand_channelginga.png);
					}
					&:nth-of-type(5) {
						background-image: url(../images/logo/brand_fuller.png);
					}
					&:nth-of-type(6) {
						background-image: url(../images/logo/brand_shobido.png);
					}
					&:nth-of-type(7) {
						background-image: url(../images/logo/brand_arara.png);
					}
					&:nth-of-type(8) {
						background-image: url(../images/logo/brand_freeway.png);
					}
					&:nth-of-type(9) {
						background-image: url(../images/logo/brand_aac.png);
					}
					&:nth-of-type(10) {
						background-image: url(../images/logo/brand_akokasei.png);
					}
					
				}
			}
		}
		
	}

	
}

// ==============================================
// blog
// ==============================================
.page01 {
	&__blog {
		padding: 150px 0;
		position: relative;
	
		&__bg {
			width: 48%;
			height: 100%;
			border-radius: 0 20px 20px 0;
			background: $color--base--blue;
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
		}
		> .container {
			display: flex;
		}
	
		&__right {
			flex-grow: 1;
			padding-left: 80px;
	
			&__item {

				& + & {
					margin-top: 40px;
				}

				a {
					display: flex;
					align-items: flex-start;
					&:hover {
						.page01__blog__right__item__eyecatch {
							.img {
								transform: scale(1.1);
							}
						}
					}
				}
	
				&__eyecatch {
					// height: 140px;
					flex-basis:40%;
					overflow: hidden;

					.img_inner {
						position: relative;
						padding-top: 56%;
						border-radius: 6px;
						overflow: hidden;
					}

					.img {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						transform: scale(1);
						transition: transform 0.5s;
						background-size: cover;
						background-repeat: no-repeat;
						background-position: center center;
					}
				}
	
				&__body {
					flex-basis: 60%;
					padding-left: 15px;
	
					h3 {
						font-size: 20px;
						font-weight: bold;
						line-height: 32px;
						margin-bottom: 35px;
					}
	
					time {
						font-size: 14px;
						color: $color--text;
					}
				}
			}
		}
	}
}
