@charset "utf-8";
/* 全体
------------------------------------------------------------*/
#wrapper{
margin:20px auto 0;
padding:0 1%;
width:98%;
position:relative;
}

.inner{
margin:0 auto;
width:100%;
justify-content: space-between;
display: flex;
align-items: center;
}

h2{
  font-size: 32px;
  font-weight: bold;
  margin-top: 8px;
}

h3{
  font-size: 32px;
  font-weight: bold;
}

#main-contents{
  margin: 0 auto;
  line-height:1.5;
  letter-spacing: 0.05;
}



/**************************
/* メイン画像（トップページ）
**************************/
#mainBanner{
  padding: 40px 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  z-index: 1;
  background-image: url(../images/webinar/seminar.jpg);
  margin-top: 72px;
}
.page__header {
  background-image: url(../images/webinar/seminar.jpg);
}

#mainBanner:before{
  content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(51, 51, 51, 0.5);
    transform: initial;
    z-index: 0;
}

#mainBanner .inner{
  position:relative;
  display: block;
  text-align: center;
  color: #fff;
}

#mainBanner img{
max-width:100%;
height:auto;
}

.slogan{
position:absolute;
max-width:100%;
height:auto;
bottom:0;
left:0;
padding:5px 10px;
line-height:1.4;
z-index:100;
}

.slogan h2{
padding-bottom:5px;
color:#333;
font-size:20px;
}

.slogan h3{
font-size:24px;
color:#666;
}

.slogan h4{
    font-size:20px;
}

.en-title{
  font-size: 14px;
  font-family: 'opensans';
  font-weight: 500;
}

.sub-title{
  font-size: 18px;
}

.main-title{
  background: linear-gradient(transparent 70%, #f3c11d 70%);
  display: inline-block;
  line-height: 1.25;
}

.sp-only{
  display: none;
}

/**************************
/* おすすめ
**************************/

#recommend{
  background: #ebf9f3;
  padding: 48px 0;
}

.banner .pc-only{
  width: 800px;
  height: auto;
  margin: 0 auto;
  margin-bottom: 32px;
  box-shadow: 0px 3px 15px 0px rgb(0 0 0 / 20%);
}

.recommend-box{
  /*box-shadow: 0 3px 6px rgb(0 0 0 / 16%);*/
  background-color: #ffffff;
  flex-wrap: wrap;
  align-content: space-between;
  border-radius: 8px;
  max-width: 1080px;
  margin: 32px auto 0;
}

.recommend-box-top{
  text-align: center;
  align-items: center;
  font-weight: bold;
}

.recommend-box h3{
  padding-top: 8px;
  padding-bottom: 8px;
  font-size: 32px;
  border-radius: 8px 8px 0 0;
  background: #3C8B86;
  color: #fff;
}

.youbi{
  height:40px;
  width:40px;
  border-radius:50%;
  line-height:40px;
  text-align:center;
  border: solid 1px #fff;
  padding: 5px 8px;
  margin: 0 16px;
  font-size: 24px;
}

.recommend-box-inner{
  padding: 32px 0;
  font-weight: bold;
  width: 750px;
  margin: 0 auto;
}

.recommend-box-inner ul{
  margin: 24px 0 48px;
}

.recommend-box-inner li:not(:last-of-type){
  margin-bottom: 12px;
}

.recommend-title{
  display: grid;
  grid-auto-flow: column;
  place-content: center;
  align-items: center;
}

.box-title{
  margin-left: 8px;
  font-size: 20px;
}

.btn{
  text-align: center;
}

.btn a{
  margin: 0 auto;
  padding: 18px 0;
  width: 360px;
  background-color: initial;
  box-shadow: initial;
  border-radius: 6px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
  text-align: center;
  position: relative;
  display: inline-block;
  background-color: #FA6B58;
  box-shadow: 0 4px 0 #dd523f;
}

.btn a:hover{
  background-color: #fff;
  color: #FA6B58;
  border: solid 1px #dd523f;
}

#main-contents .btn a {
  color: #fff !important;
  font-weight: 700;
}
#main-contents .btn a:hover {
  color: #FA6B58 !important;
}


/**************************
/* セミナー概要
**************************/

#overview{
  padding: 80px 0;
  margin: 0 auto;
  max-width: 1080px;
}

.heading .en-title{
  color: #3C8B86;
  margin-bottom: 12px;
}

.heading{
  margin-bottom: 32px;
  text-align: center;
}

.post{
  margin: 0 auto;
}

.post ul{
  margin: 32px 0;
  line-height: 2;
  font-weight: bold;
}

.overview{
  max-width: 1080px;
}

table {
    border-collapse: separate;  /* 表の線と線の間を空ける */
    border-spacing:  0.5px;       /* 表の線と線の間の幅 */
    width: 100%;
}

th,td {
    padding: 5px 10px;          /* 余白指定 */
    border: solid 1px #808080;
}

th {
    background-color:  #808080; /* 背景色指定 */
    color:  #fff;               /* 文字色指定 */
    font-weight:  normal;       /* 文字の太さ指定 */
}

th:not(:last-child) {
  border-bottom: solid 1px #fff;
}

td {
    border: solid 1px #808080; /* 背景色指定 */
}

/**************************
/* 講師紹介
**************************/

#lecture{
  padding-bottom: 80px;
}

.lecture{
  display: flex;
  max-width: 1080px;
}

.lecture-img{
  width: 300px;
  height: 300px;
  margin-right: 80px;
  border-radius: 1000px;
  object-fit: cover;
}

.lecture-name{
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 24px;
}

.lecture-name span{
  font-size: 16px;
}

.lecture-name p{
  margin-top: 8px;
}

.career-inner{
  margin-bottom: 24px;
}

.career-inner span{
  color: #3C8B86;
  font-size: 16px;
  font-weight: bold;
}

.career-inner p{
  margin-top: 8px;
}

/**************************
/* お申し込みフォーム
**************************/

#form{
  background: #F5F6F7;
  padding: 80px 0;
}

.form{
  max-width: 1080px;
}

/**************************
/* フッター
**************************/

#footer{
  background: #191919;
  padding: 48px 0;
  color: #fff;
}

.footer-inner{
  display: flex;
  width: 1080px;
  justify-content: space-between;
  margin: 0 auto;
}

.corporate .logo{
  margin-bottom: 24px;
}

.tel span{
  font-size: 14px;
}

.tel p{
  font-size: 36px;
  font-family: 'Opensans';
  font-weight: bold;
  margin-top: 10px;
  position: relative;
  padding-left: 42px;
}

.tel p:before{
  content: url(../images/webinar/tel.svg);
  position: absolute;
  left: 0;
}

#copyright{
  font-size: 12px;
  margin-top: 32px;
  text-align: center;
}

.pc-only{
  display: block;
}

@media screen and (max-width: 959px) {
  /* 959px以下に適用されるCSS（タブレット用） */

/**************************
/* おすすめ
**************************/

.recommend-box{
  max-width: 90%;
}

.recommend-box-inner{
  max-width: 90%;
}

/**************************
/* セミナー概要
**************************/

.overview{
  max-width: 90%;
}

/**************************
/* 講師紹介
**************************/

.lecture{
  max-width: 90%;
}

/**************************
/* お申し込みフォーム
**************************/

.form{
  max-width: 90%;
}




}
@media screen and (max-width: 480px) {
  /* 480px以下に適用されるCSS（スマホ用） */


/**************************
/* メイン画像（トップページ）
**************************/
#mainBanner{
margin-top: 64px;
padding: 24px 0;
}

h2{
  font-size: 24px;
}

.en-title{
  font-size: 12px;
}

.sub-title{
  font-size: 14px;
}

.sp-only{
  display: block;
}


/**************************
/* おすすめ
**************************/

#recommend{
  padding: 32px 0;
}

.banner .sp-only{
  width: 300px;
  height: auto;
  margin: 0 auto;
  margin-bottom: 24px;
}

.recommend-box h3{
  margin-bottom: 24px;
  font-size: 24px;
}

.youbi{
  margin: 0 10px;
  font-size: 20px;
}

.recommend-box-inner{
  padding: 20px 0;
  width: 300px;
}

.recommend-box-inner ul{
  margin: 24px 0 32px;
}

.box-title{
  font-size: 18px;
}

.btn{
  text-align: center;
}

.btn a{
  width: 300px !important;
}

/**************************
/* セミナー概要
**************************/

#overview{
  padding: 40px 0;
}

h3{
  font-size: 28px;
}

.heading{
  margin-bottom: 28px;
}

.post ul{
  margin: 24px 0;
}

.overview{
  max-width: 90%;
}

table {
   font-size: 14px;
}


/**************************
/* 講師紹介
**************************/

#lecture{
  padding-bottom: 40px;
}

.lecture{
  display: block;
  text-align: center;
}

.lecture-img{
  width: 220px;
  height: 220px;
  margin-inline: auto;
  margin-bottom: 20px;
}

.career-inner{
  text-align: left;
}


/**************************
/* お申し込みフォーム
**************************/

#form{
  padding: 40px 0;
}

.pc-only{
  display: none;
}



}