{"version": 3, "sources": ["top.scss", "top.css", "variable.scss"], "names": [], "mappings": "AAIA;EACC,0BAAA;EACA,iBAAA;ACHD;ADKC;EACC,WAAA;EACA,WAAA;EACA,mBEEa;ADLf;ADMC;EACC,WAAA;EACA,WAAA;EACA,6FAAA;ACJF;ADOC;EACC,oBAAA;EACA,kBAAA;EACA,WAAA;ACLF;ADOE;EACC,eAAA;EACA,iBAAA;EACA,uBAAA;EACA,mBAAA;ACLH;ADOG;EACC,cAAA;ACLJ;ADUE;EACC,WAAA;EEnBC,WFoBkB;EEnBlB,WFmBwB;EElBxB,kBAAA;EACA,YFiB6B;EEhB7B,cFgBsC;EEftC,SFe+C;EEd/C,OFckD;EEblD,mBAhBoB;EAiBpB,0BFY6E;UEZ7E,kBFY6E;EEX7E,gBFWsF;ACC1F;;ADQC;EACC,kBAAA;ACLF;ADOE;EEpBE,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,yBFoBa;MEpBb,sBFoBa;UEpBb,wBFoBa;EEnBb,0BFmBsB;MEnBtB,uBFmBsB;UEnBtB,oBFmBsB;EElBtB,mBFkB+B;MElB/B,eFkB+B;EAChC,gBAAA;ACFH;ADIE;EACC,2BAAA;EACA,gBAAA;EACA,mDAAA;UAAA,2CAAA;EACA,mBAAA;EACA,kBAAA;ACFH;ADGG;EACC,WAAA;EE3CA,UF4CkB;EE3ClB,YF2CuB;EE1CvB,kBAAA;EACA,UFyC6B;EExC7B,cFwCoC;EEvCpC,eFuC6C;EEtC7C,SFsCsD;EErCtD,mBApBU;EAqBV,mCFoCyE;UEpCzE,2BFoCyE;EEnCzE,gBFmC2F;ACQ/F;ADNG;EACC,iBAAA;ACQJ;ADJI;EACC,WAAA;ACML;ADFG;EE5CC,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,wBF4Cc;ME5Cd,qBF4Cc;UE5Cd,uBF4Cc;EE3Cd,yBF2CsB;ME3CtB,sBF2CsB;UE3CtB,mBF2CsB;EE1CtB,qBF0C8B;ME1C9B,iBF0C8B;EAC9B,mBAAA;ACOJ;ADNI;EACC,eAAA;EACA,iBAAA;EACA,iBAAA;ACQL;ADJG;EACC,kBAAA;EACA,oBAAA;ACMJ;ADLI;EACC,eAAA;EACA,kBAAA;EACA,kBAAA;ACOL;ADNK;EACC,WAAA;EE1EF,WF2EoB;EE1EpB,WF0E0B;EEzE1B,kBAAA;EACA,QFwE+B;EEvE/B,cFuEoC;EEtEpC,eFsE6C;EErE7C,OFqEsD;EEpEtD,mBApBU;EAqBV,mCFmEuE;UEnEvE,2BFmEuE;EElEvE,gBFkEyF;ACiB7F;ADdI;EACC,gBAAA;ACgBL;;ADLC;EACC,gDAAA;EACA,wBAAA;EACA,4BAAA;EACA,kCAAA;EACA,gBAAA;EACA,kBAAA;EACA,gBAAA;ACQF;ADNE;EEnGE,YFoGiB;EEnGjB,YFmGwB;EElGxB,kBAAA;EACA,QFiG8B;EEhG9B,cFgGmC;EE/FnC,eF+F4C;EE9F5C,SF8FqD;EE7FrD,mBAvBiB;EAwBjB,wCF4F+E;UE5F/E,gCF4F+E;EE3F/E,WF2FsG;EACvG,kBAAA;ACiBH;ADfE;EACC,cAAA;ACiBH;ADfG;EACC,kBAAA;EACA,eAAA;EACA,iBAAA;EACA,cEzHkB;EF0HlB,uBAAA;ACiBJ;ADdG;EACC,eAAA;EACA,mBE1HW;AD0If;ADZE;EE5GE,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,yBF4Ga;ME5Gb,sBF4Ga;UE5Gb,wBF4Ga;EE3Gb,yBF2GsB;ME3GtB,sBF2GsB;UE3GtB,mBF2GsB;EE1GtB,mBF0G8B;ME1G9B,eF0G8B;ACiBlC;ADfG;EACC,iBAAA;ACiBJ;ADbG;EACC,eAAA;EACA,cAAA;ACeJ;ADZG;EACC,gBAAA;ACcJ;ADZG;EAAO,iBAAA;ACeV;;ADCE;EACC,kBAAA;EACA,mJAAA;EAAA,uFAAA;ACEH;ADAG;EEhJC,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,yBFgJc;MEhJd,sBFgJc;UEhJd,wBFgJc;EE/Id,yBF+IuB;ME/IvB,sBF+IuB;UE/IvB,mBF+IuB;EE9IvB,mBF8I+B;ME9I/B,eF8I+B;EAC/B,gBAAA;ACKJ;ADHI;EACC,2BAAA;EACA,mBExKU;EFyKV,mBAAA;EACA,oBAAA;EACA,mBAAA;EACA,kBAAA;ACKL;ADJK;EACC,eAAA;ACMN;ADHK;EACC,gBAAA;EACA,kBAAA;ACKN;ADJM;EACC,WAAA;EACA,YAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,sBAAA;EACA,kCAAA;EACA,4BAAA;ACMP;ADFK;EACC,eAAA;EACA,iBAAA;EACA,kBAAA;EACA,mBAAA;ACIN;ADFK;EACC,eAAA;EACA,kBAAA;ACIN;ADCG;EACC,oCAAA;EACA,cE/MW;EFgNX,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,yBAAA;EACA,YAAA;EACA,kBAAA;EACA,SAAA;ACCJ;ADGE;EACC,eAAA;ACDH;ADGG;EE3MC,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,yBF2Mc;ME3Md,sBF2Mc;UE3Md,wBF2Mc;EE1Md,yBF0MuB;ME1MvB,sBF0MuB;UE1MvB,mBF0MuB;EEzMvB,qBFyM+B;MEzM/B,iBFyM+B;EAC/B,mBAAA;ACEJ;ADAI;EACC,4BAAA;MAAA,eAAA;EACA,oBAAA;MAAA,cAAA;EACA,qBAAA;EACA,eAAA;EACA,iBAAA;EACA,mBAAA;EACA,cE7OiB;AD+OtB;ADAI;EACC,cAAA;EACA,gBAAA;EACA,WAAA;EACA,mBErPS;EFsPT,iBAAA;ACEL;ADEG;EACC,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,mBAAA;MAAA,eAAA;ACAJ;ADEI;EACC,+BAAA;EACA,gBAAA;EACA,gBAAA;EACA,kBAAA;EAMA,kCAAA;EACA,wBAAA;EACA,4BAAA;ACLL;ADDK;EACC,eAAA;ACGN;ADIK;EACC,gBAAA;EACA,kDAAA;ACFN;ADIK;EACC,oDAAA;ACFN;ADIK;EACC,gBAAA;EACA,yDAAA;ACFN;ADIK;EACC,4DAAA;ACFN;ADIK;EACC,sDAAA;ACFN;ADIK;EACC,uDAAA;ACFN;ADIK;EACC,qDAAA;ACFN;ADIK;EACC,uDAAA;ACFN;ADIK;EACC,mDAAA;ACFN;ADIK;EACC,wDAAA;ACFN;;ADkBC;EACC,gBAAA;EACA,kBAAA;ACfF;ADiBE;EACC,UAAA;EACA,YAAA;EACA,4BAAA;EACA,mBEvUiB;EFwUjB,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;ACfH;ADiBE;EACC,oBAAA;EAAA,oBAAA;EAAA,aAAA;ACfH;ADkBE;EACC,mBAAA;MAAA,oBAAA;UAAA,YAAA;EACA,kBAAA;AChBH;ADoBI;EACC,gBAAA;AClBL;ADqBI;EACC,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;ACnBL;ADsBI;EACC,4BAAA;MAAA,eAAA;EACA,gBAAA;EACA,kBAAA;ACpBL;ADqBK;EACC,WAAA;EACA,YAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,sBAAA;EACA,kCAAA;EACA,kBAAA;ACnBN;ADuBI;EACC,4BAAA;MAAA,eAAA;EACA,kBAAA;ACrBL;ADuBK;EACC,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,mBAAA;ACrBN;ADwBK;EACC,eAAA;EACA,cEzXQ;ADmWd", "file": "top.css", "sourcesContent": ["@import './variable';\n// ==============================================\n// mv\n// ==============================================\n.mv {\n\theight: calc(100vh - 80px);\n\tpadding-top: 80px;\n\n\t&__spacer {\n\t\twidth: 100%;\n\t\theight: 30%;\n\t\tbackground: $color--white;\n\t}\n\n\t&__bg {\n\t\twidth: 100%;\n\t\theight: 70%;\n\t\tbackground: transparent url(../images/temp/temp-mainvisual.jpg) no-repeat center center / cover;\n\t}\n\n\t&__title {\n\t\tpadding-bottom: 40px;\n\t\tposition: relative;\n\t\ttop: -100px;\n\n\t\th1 {\n\t\t\tfont-size: 64px;\n\t\t\tfont-weight: bold;\n\t\t\tletter-spacing: 0.072em;\n\t\t\tline-height: (100 / 64);\n\n\t\t\tspan {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t}\n\n\t\t&::after {\n\t\t\tcontent:'';\n\t\t\t@include absolute (60px, 5px, initial, initial, 0, 0, $color--accent--orange, initial, initial);\n\t\t}\n\t}\n}\n\n// ==============================================\n// service\n// ==============================================\n.page01 {\n\t&__service {\n\t\tpadding: 0 0 150px;\n\n\t\t&__cont {\n\t\t\t@include flex(initial, stretch, wrap);\n\t\t\tmargin-top: 85px;\n\t\t}\n\t\t&__item {\n\t\t\twidth: calc(33.333% - 20px);\n\t\t\tbackground: #FFF;\n\t\t\tbox-shadow: 0px 6px 8px rgba(0, 0, 0, 0.16);\n\t\t\tborder-radius: 20px;\n\t\t\tposition: relative;\n\t\t\t&::before {\n\t\t\t\tcontent:'';\n\t\t\t\t@include absolute(1px, 96px, -50px, initial, initial, 50%, $color--text, translateX(-50%), initial);\n\t\t\t}\n\t\t\t& + & {\n\t\t\t\tmargin-left: 30px;\n\t\t\t}\n\n\t\t\t&__pic {\n\t\t\t\timg {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__title {\n\t\t\t\t@include flex(center, center, nowrap);\n\t\t\t\tmargin: 25px 0 40px;\n\t\t\t\th3 {\n\t\t\t\t\tfont-size: 40px;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tmargin-left: 20px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__list {\n\t\t\t\tpadding-left: 30px;\n\t\t\t\tpadding-bottom: 40px;\n\t\t\t\tli {\n\t\t\t\t\tfont-size: 18px;\n\t\t\t\t\tpadding-left: 42px;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\t&::before {\n\t\t\t\t\t\tcontent:'';\n\t\t\t\t\t\t@include absolute(32px, 1px, 50%, initial, initial, 0, $color--text, translateY(-50%), initial);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tli + li {\n\t\t\t\t\tmargin-top: 30px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// ==============================================\n// mission\n// ==============================================\n.page01 {\n\t&__mission {\n\t\tbackground-image: url(../svg/mission_bg_obj.svg);\n\t\tbackground-size: 80% 60%;\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center center;\n\t\tpadding: 150px 0;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&__bg {\n\t\t\t@include absolute(160vw, 100%, 50%, initial, initial, 50%, $color--base--green, translate(-50%, -50%), -1);\n\t\t\tborder-radius: 50%;\n\t\t}\n\t\t&__lead {\n\t\t\tmargin: 70px 0;\n\t\n\t\t\th3 {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 50px;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: $color--text--strong;\n\t\t\t\tletter-spacing: 0.064em;\n\t\t\t}\n\t\t\t\n\t\t\tspan {\n\t\t\t\tfont-size: 50px;\n\t\t\t\tbackground: $color--white;\n\t\t\t}\n\t\t}\n\n\t\t&__wrap {\n\t\t\t@include flex(initial, center, wrap);\n\t\t\t\n\t\t\timg {\n\t\t\t\tmargin-left: auto;\n\t\t\t}\n\t\t}\n\t\t&__text {\n\t\t\tp {\n\t\t\t\tfont-size: 18px;\n\t\t\t\tline-height: (36 / 18);\n\t\t\t}\n\n\t\t\tp + p {\n\t\t\t\tmargin-top: 40px;\n\t\t\t}\n\t\t\t.fw_b {font-weight: bold;}\n\t\t}\n\n\t}\n\n\n\t\n}\n\n// ==============================================\n// client\n// ==============================================\n\n.page01 {\n\t&__client {\n\n\t\t&__top {\n\t\t\tpadding: 150px 0 0;\n\t\t\tbackground: linear-gradient(transparent 0%, transparent 35%, $color--base--green 35%, $color--base--green 100%);\n\n\t\t\t&__cont {\n\t\t\t\t@include flex(initial, center, wrap);\n\t\t\t\tmargin-top: 85px;\n\t\t\t\t\n\t\t\t\t&__item {\n\t\t\t\t\twidth: calc(33.333% - 20px);\n\t\t\t\t\tbackground: $color--white;\n\t\t\t\t\tborder-radius: 20px;\n\t\t\t\t\tpadding-bottom: 30px;\n\t\t\t\t\tmargin-bottom: 30px;\n\t\t\t\t\tmargin-right: 30px;\n\t\t\t\t\t&:nth-of-type(3n) {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t&__eyecatch {\n\t\t\t\t\t\tpadding-top: 56%;\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t.img {\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\t\tbackground-size: cover;\n\t\t\t\t\t\t\tbackground-position: center center;\n\t\t\t\t\t\t\tborder-radius: 20px 20px 0 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\n\t\t\t\t\th4 {\n\t\t\t\t\t\tfont-size: 20px;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tmargin: 20px 0 25px;\n\t\t\t\t\t}\n\t\t\t\t\tp {\n\t\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__bglogo {\n\t\t\t\tfont-family: 'Open Sans', sans-serif;\n\t\t\t\tcolor: $color--white;\n\t\t\t\tfont-size: 140px;\n\t\t\t\tfont-weight: bold;\n\t\t\t\ttext-align: center;\n\t\t\t\ttext-transform: uppercase;\n\t\t\t\topacity: 0.5;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: 18px;\n\t\t\t}\n\t\t}\n\n\t\t&__bottom {\n\t\t\tpadding: 80px 0;\n\n\t\t\th3 {\n\t\t\t\t@include flex(initial, center, nowrap);\n\t\t\t\tmargin-bottom: 30px;\n\n\t\t\t\tspan {\n\t\t\t\t\tflex-basis: 45%;\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tfont-size: 24px;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\tcolor: $color--text--strong;\n\t\t\t\t}\n\t\t\t\tspan.line {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmax-width: 300px;\n\t\t\t\t\theight: 1px;\n\t\t\t\t\tbackground: $color--text;\n\t\t\t\t\tmargin-left: 15px;\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\t&__cont {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tflex-wrap: wrap;\n\n\t\t\t\t&__item {\n\t\t\t\t\twidth: calc((100% - 320px) / 5);\n\t\t\t\t\tmin-height: 40px;\n\t\t\t\t\tmargin-top: 30px;\n\t\t\t\t\tmargin-right: 80px;\n\n\t\t\t\t\t&:nth-of-type(5n) {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\tbackground-position: center center;\n\t\t\t\t\tbackground-size: contain;\n\t\t\t\t\tbackground-repeat: no-repeat;\n\n\t\t\t\t\t&:nth-of-type(1) {\n\t\t\t\t\t\tmin-height: 27px;\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_sb.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(2) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_usen.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(3) {\n\t\t\t\t\t\tmin-height: 27px;\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_dentsutec.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(4) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_channelginga.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(5) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_fuller.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(6) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_shobido.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(7) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_arara.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(8) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_freeway.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(9) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_aac.png);\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-of-type(10) {\n\t\t\t\t\t\tbackground-image: url(../images/logo/brand_akokasei.png);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t}\n\n\t\n}\n\n// ==============================================\n// blog\n// ==============================================\n.page01 {\n\t&__blog {\n\t\tpadding: 150px 0;\n\t\tposition: relative;\n\t\n\t\t&__bg {\n\t\t\twidth: 48%;\n\t\t\theight: 100%;\n\t\t\tborder-radius: 0 20px 20px 0;\n\t\t\tbackground: $color--base--blue;\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tz-index: -1;\n\t\t}\n\t\t> .container {\n\t\t\tdisplay: flex;\n\t\t}\n\t\n\t\t&__right {\n\t\t\tflex-grow: 1;\n\t\t\tpadding-left: 80px;\n\t\n\t\t\t&__item {\n\n\t\t\t\t& + & {\n\t\t\t\t\tmargin-top: 40px;\n\t\t\t\t}\n\n\t\t\t\ta {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: flex-start;\n\t\t\t\t}\n\t\n\t\t\t\t&__eyecatch {\n\t\t\t\t\tflex-basis: 40%;\n\t\t\t\t\tpadding-top: 23%;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\t.img {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tbackground-size: cover;\n\t\t\t\t\t\tbackground-position: center center;\n\t\t\t\t\t\tborder-radius: 6px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\n\t\t\t\t&__body {\n\t\t\t\t\tflex-basis: 60%;\n\t\t\t\t\tpadding-left: 15px;\n\t\n\t\t\t\t\th3 {\n\t\t\t\t\t\tfont-size: 20px;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tline-height: (24 / 20);\n\t\t\t\t\t\tmargin-bottom: 35px;\n\t\t\t\t\t}\n\t\n\t\t\t\t\ttime {\n\t\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\t\tcolor: $color--text;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", ".mv {\n  height: calc(100vh - 80px);\n  padding-top: 80px;\n}\n.mv__spacer {\n  width: 100%;\n  height: 30%;\n  background: #ffffff;\n}\n.mv__bg {\n  width: 100%;\n  height: 70%;\n  background: transparent url(../images/temp/temp-mainvisual.jpg) no-repeat center center/cover;\n}\n.mv__title {\n  padding-bottom: 40px;\n  position: relative;\n  top: -100px;\n}\n.mv__title h1 {\n  font-size: 64px;\n  font-weight: bold;\n  letter-spacing: 0.072em;\n  line-height: 1.5625;\n}\n.mv__title h1 span {\n  display: block;\n}\n.mv__title::after {\n  content: \"\";\n  width: 60px;\n  height: 5px;\n  position: absolute;\n  top: initial;\n  right: initial;\n  bottom: 0;\n  left: 0;\n  background: #FA6B58;\n  transform: initial;\n  z-index: initial;\n}\n\n.page01__service {\n  padding: 0 0 150px;\n}\n.page01__service__cont {\n  display: flex;\n  justify-content: initial;\n  align-items: stretch;\n  flex-wrap: wrap;\n  margin-top: 85px;\n}\n.page01__service__item {\n  width: calc(33.333% - 20px);\n  background: #FFF;\n  box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.16);\n  border-radius: 20px;\n  position: relative;\n}\n.page01__service__item::before {\n  content: \"\";\n  width: 1px;\n  height: 96px;\n  position: absolute;\n  top: -50px;\n  right: initial;\n  bottom: initial;\n  left: 50%;\n  background: #333333;\n  transform: translateX(-50%);\n  z-index: initial;\n}\n.page01__service__item + .page01__service__item {\n  margin-left: 30px;\n}\n.page01__service__item__pic img {\n  width: 100%;\n}\n.page01__service__item__title {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-wrap: nowrap;\n  margin: 25px 0 40px;\n}\n.page01__service__item__title h3 {\n  font-size: 40px;\n  font-weight: bold;\n  margin-left: 20px;\n}\n.page01__service__item__list {\n  padding-left: 30px;\n  padding-bottom: 40px;\n}\n.page01__service__item__list li {\n  font-size: 18px;\n  padding-left: 42px;\n  position: relative;\n}\n.page01__service__item__list li::before {\n  content: \"\";\n  width: 32px;\n  height: 1px;\n  position: absolute;\n  top: 50%;\n  right: initial;\n  bottom: initial;\n  left: 0;\n  background: #333333;\n  transform: translateY(-50%);\n  z-index: initial;\n}\n.page01__service__item__list li + li {\n  margin-top: 30px;\n}\n\n.page01__mission {\n  background-image: url(../svg/mission_bg_obj.svg);\n  background-size: 80% 60%;\n  background-repeat: no-repeat;\n  background-position: center center;\n  padding: 150px 0;\n  position: relative;\n  overflow: hidden;\n}\n.page01__mission__bg {\n  width: 160vw;\n  height: 100%;\n  position: absolute;\n  top: 50%;\n  right: initial;\n  bottom: initial;\n  left: 50%;\n  background: #E1EDE8;\n  transform: translate(-50%, -50%);\n  z-index: -1;\n  border-radius: 50%;\n}\n.page01__mission__lead {\n  margin: 70px 0;\n}\n.page01__mission__lead h3 {\n  text-align: center;\n  font-size: 50px;\n  font-weight: bold;\n  color: #006835;\n  letter-spacing: 0.064em;\n}\n.page01__mission__lead span {\n  font-size: 50px;\n  background: #ffffff;\n}\n.page01__mission__wrap {\n  display: flex;\n  justify-content: initial;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.page01__mission__wrap img {\n  margin-left: auto;\n}\n.page01__mission__text p {\n  font-size: 18px;\n  line-height: 2;\n}\n.page01__mission__text p + p {\n  margin-top: 40px;\n}\n.page01__mission__text .fw_b {\n  font-weight: bold;\n}\n\n.page01__client__top {\n  padding: 150px 0 0;\n  background: linear-gradient(transparent 0%, transparent 35%, #E1EDE8 35%, #E1EDE8 100%);\n}\n.page01__client__top__cont {\n  display: flex;\n  justify-content: initial;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-top: 85px;\n}\n.page01__client__top__cont__item {\n  width: calc(33.333% - 20px);\n  background: #ffffff;\n  border-radius: 20px;\n  padding-bottom: 30px;\n  margin-bottom: 30px;\n  margin-right: 30px;\n}\n.page01__client__top__cont__item:nth-of-type(3n) {\n  margin-right: 0;\n}\n.page01__client__top__cont__item__eyecatch {\n  padding-top: 56%;\n  position: relative;\n}\n.page01__client__top__cont__item__eyecatch .img {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  background-size: cover;\n  background-position: center center;\n  border-radius: 20px 20px 0 0;\n}\n.page01__client__top__cont__item h4 {\n  font-size: 20px;\n  font-weight: bold;\n  text-align: center;\n  margin: 20px 0 25px;\n}\n.page01__client__top__cont__item p {\n  font-size: 14px;\n  text-align: center;\n}\n.page01__client__top__bglogo {\n  font-family: \"Open Sans\", sans-serif;\n  color: #ffffff;\n  font-size: 140px;\n  font-weight: bold;\n  text-align: center;\n  text-transform: uppercase;\n  opacity: 0.5;\n  position: relative;\n  top: 18px;\n}\n.page01__client__bottom {\n  padding: 80px 0;\n}\n.page01__client__bottom h3 {\n  display: flex;\n  justify-content: initial;\n  align-items: center;\n  flex-wrap: nowrap;\n  margin-bottom: 30px;\n}\n.page01__client__bottom h3 span {\n  flex-basis: 45%;\n  flex-shrink: 0;\n  display: inline-block;\n  font-size: 24px;\n  font-weight: bold;\n  white-space: nowrap;\n  color: #006835;\n}\n.page01__client__bottom h3 span.line {\n  display: block;\n  max-width: 300px;\n  height: 1px;\n  background: #333333;\n  margin-left: 15px;\n}\n.page01__client__bottom__cont {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.page01__client__bottom__cont__item {\n  width: calc((100% - 320px) / 5);\n  min-height: 40px;\n  margin-top: 30px;\n  margin-right: 80px;\n  background-position: center center;\n  background-size: contain;\n  background-repeat: no-repeat;\n}\n.page01__client__bottom__cont__item:nth-of-type(5n) {\n  margin-right: 0;\n}\n.page01__client__bottom__cont__item:nth-of-type(1) {\n  min-height: 27px;\n  background-image: url(../images/logo/brand_sb.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(2) {\n  background-image: url(../images/logo/brand_usen.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(3) {\n  min-height: 27px;\n  background-image: url(../images/logo/brand_dentsutec.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(4) {\n  background-image: url(../images/logo/brand_channelginga.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(5) {\n  background-image: url(../images/logo/brand_fuller.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(6) {\n  background-image: url(../images/logo/brand_shobido.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(7) {\n  background-image: url(../images/logo/brand_arara.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(8) {\n  background-image: url(../images/logo/brand_freeway.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(9) {\n  background-image: url(../images/logo/brand_aac.png);\n}\n.page01__client__bottom__cont__item:nth-of-type(10) {\n  background-image: url(../images/logo/brand_akokasei.png);\n}\n\n.page01__blog {\n  padding: 150px 0;\n  position: relative;\n}\n.page01__blog__bg {\n  width: 48%;\n  height: 100%;\n  border-radius: 0 20px 20px 0;\n  background: #F5F6F7;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n.page01__blog > .container {\n  display: flex;\n}\n.page01__blog__right {\n  flex-grow: 1;\n  padding-left: 80px;\n}\n.page01__blog__right__item + .page01__blog__right__item {\n  margin-top: 40px;\n}\n.page01__blog__right__item a {\n  display: flex;\n  align-items: flex-start;\n}\n.page01__blog__right__item__eyecatch {\n  flex-basis: 40%;\n  padding-top: 23%;\n  position: relative;\n}\n.page01__blog__right__item__eyecatch .img {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  background-size: cover;\n  background-position: center center;\n  border-radius: 6px;\n}\n.page01__blog__right__item__body {\n  flex-basis: 60%;\n  padding-left: 15px;\n}\n.page01__blog__right__item__body h3 {\n  font-size: 20px;\n  font-weight: bold;\n  line-height: 1.2;\n  margin-bottom: 35px;\n}\n.page01__blog__right__item__body time {\n  font-size: 14px;\n  color: #333333;\n}", "\n// ----------- color -----------\n$color--base--blue: #F5F6F7; // ベースカラー\n$color--base--green: #E1EDE8; // ベースカラー\n$color--main: #3c8b86; // メインカラー\n\n$color--text: #333333; // テキストカラー\n$color--text--sub: #808080; // テキストカラー\n$color--text--strong: #006835; // テキストカラー\n\n$color--accent--orange: #FA6B58; // アクセントカラー\n$color--accent--yellow: #F3C11D; // アクセントカラー\n\n$color--white: #ffffff;\n$color--black: #000000;\n\n\n// ----------- mixin -----------\n@mixin absolute($width, $height, $top, $right, $bottom, $left, $bgc, $transform, $z) {\n    width: $width;\n    height: $height;\n    position: absolute;\n    top: $top;\n    right: $right;\n    bottom: $bottom;\n    left: $left;\n    background: $bgc;\n    transform: $transform;\n    z-index: $z;\n}\n@mixin flex($jc, $ai, $fw) {\n    display: flex;\n    justify-content: $jc;\n    align-items: $ai;\n    flex-wrap: $fw;\n}\n@mixin btn($pd, $width, $bg, $bshadow, $bradius) {\n    padding: $pd;\n    width: $width;\n    background-color: $bg;\n    box-shadow: $bshadow;\n    border-radius: $bradius;\n    box-sizing: border-box;\n}"]}