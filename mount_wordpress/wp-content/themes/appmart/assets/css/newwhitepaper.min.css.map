{"version": 3, "sources": ["newwhitepaper.min.css", "newwhitepaper.scss"], "names": [], "mappings": "AAAA,cCAA,YACE,CAAA,eAEF,eACE,CAAA,yBAKE,kDADJ,gBAEM,CAAA,CAAA,8BAKJ,oBAEE,CAAA,eACA,CAAA,6BACA,CAAA,qBACA,CAAA,8BACA,CAAA,eACA,CAAA,aACA,CAAA,gCAGF,aACE,CAAA,yBAEA,gCAHF,uBAII,CAAA,CAAA,gCAIJ,YACE,CAAA,yBAEA,gCAHF,aAIM,CAAA,CAAA,iCAIN,aACE,CAAA,uCAGF,iHACE,CADF,uDACE,CAAA,cACA,CAAA,iBACA,CAAA,SACA,CAAA,sCAGF,kCACE,CAAA,eACA,CAAA,cACA,CAAA,aACA,CAAA,iBACA,CAAA,aACA,CAAA,kBACA,CAAA,yBAEA,sCATF,cAUI,CAAA,CAAA,+BAIJ,cACE,CAAA,iBACA,CAAA,yBAEA,+BAJF,cAKI,CAAA,gBACA,CAAA,CAAA,gCAIJ,cACE,CAAA,gDAGF,kBACE,CAAA,iBACA,CAAA,yBACA,gDAHF,eAII,CAAA,CAAA,uDAGF,mBACE,CAAA,WACA,CAAA,UACA,CAAA,uDAEA,CAAA,2BACA,CAAA,qBACA,CAAA,iCACA,CAAA,iBACA,CAAA,yBAEA,uDAXF,0DAYI,CAAA,mBACA,CAAA,CAAA,6HAGF,UAEE,CAAA,iBACA,CAAA,MACA,CAAA,OACA,CAAA,WACA,CAAA,SACA,CAAA,WACA,CAAA,yBAEA,6HAVF,WAWI,CAAA,CAAA,+DAIJ,QACE,CAAA,qBACA,CAAA,8DAGF,YACE,CAAA,qBACA,CAAA,yBAEA,8DAJF,YAKI,CAAA,CAAA,iEAIJ,cACE,CAAA,eACA,CAAA,UACA,CAAA,aACA,CAAA,yBAEA,iEANF,cAOI,CAAA,CAAA,0DAIJ,cACE,CAAA,kBACA,CAAA,UACA,CAAA,oBACA,CAAA,yBAEA,0DANF,cAOI,CAAA,kBACA,CAAA,CAAA,kEAIJ,cACE,CAAA,UACA,CAAA,yBAEA,kEAJF,cAKI,CAAA,gBACA,CAAA,CAAA,2DAKN,iBACE,CAAA,UACA,CAAA,mEAEA,gBACE,CAAA,aACA,CAAA,kBACA,CAAA,yBAEA,mEALF,sBAMI,CAAA,CAAA,kFAKA,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,cACA,CAAA,yBAEA,kFALF,cAMI,CAAA,CAAA,qFAGF,eACE,CAAA,yGAEA,gBACE,CAAA,iBACA,CAAA,iHAEA,WACE,CAAA,iBACA,CAAA,KACA,CAAA,UACA,CAAA,cACA,CAAA,eACA,CAAA,yBAEA,iHARF,cASI,CAAA,CAAA,uFAKN,cACE,CAAA,eACA,CAAA,yBAEA,uFAJF,cAKI,CAAA,CAAA,2EAOV,eACE,CAAA,yBAEA,2EAHF,eAII,CAAA,CAAA,8EAGF,cACE,CAAA,oBACA,CAAA,iBACA,CAAA,yBAEA,8EALF,cAMI,CAAA,CAAA,2KAGF,UAEE,CAAA,iBACA,CAAA,KACA,CAAA,QACA,CAAA,WACA,CAAA,SACA,CAAA,WACA,CAAA,wBACA,CAAA,yBAEA,2KAXF,YAYI,CAAA,CAAA,sFAIJ,UACE,CAAA,gCACA,CADA,wBACA,CAAA,qFAGF,WACE,CAAA,+BACA,CADA,uBACA,CAAA,iFAIJ,YACE,CAAA,oCACA,CAAA,eACA,CAAA,yBAEA,iFALF,oCAMI,CAAA,eACA,CAAA,sMAIE,eAEE,CAAA,CAAA,6CAUhB,mBACE,CAAA,yBAEA,6CAHF,qBAII,CAAA,CAAA,wDAGF,gBACE,CAAA,aACA,CAAA,yBACE,wDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,yBAOA,oEAFF,cAGI,CAAA,aACA,CAAA,CAAA,iEAKN,kBACE,CAAA,eACA,CAAA,yBAEA,iEAJF,eAKI,CAAA,CAAA,uEAGF,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,2FAEA,eACE,CAAA,yBAEA,2FAHF,eAII,CAAA,CAAA,6EAIJ,UACE,CAAA,iBACA,CAAA,yBAEA,6EAJF,cAKI,CAAA,cACA,CAAA,iBACA,CAAA,CAAA,wFAMF,kCACE,CAAA,eACA,CAAA,cACA,CAAA,iBACA,CAAA,aACA,CAAA,yBAEA,wFAPF,cAQI,CAAA,CAAA,gGAGF,UACE,CAAA,iBACA,CAAA,KACA,CAAA,QACA,CAAA,WACA,CAAA,YACA,CAAA,qBACA,CAAA,WACA,CAAA,UACA,CAAA,iFAIJ,cACE,CAAA,eACA,CAAA,yBAEA,iFAJF,cAKI,CAAA,gBACA,CAAA,CAAA,6CASd,cACE,CAAA,wBACA,CAAA,yBAEA,6CAJF,qBAKI,CAAA,CAAA,wDAGF,gBACE,CAAA,aACA,CAAA,yBACE,wDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,iEAIJ,eACE,CAAA,mBACA,CADA,mBACA,CADA,YACA,CAAA,wBACA,CADA,qBACA,CADA,6BACA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,yBAEA,iEANF,aAOI,CAAA,CAAA,uEAGF,WACE,CAAA,iBACA,CAAA,eACA,CAAA,yBAEA,uEALF,WAMI,CAAA,mBACA,CADA,mBACA,CADA,YACA,CAAA,wBAEA,CAFA,qBAEA,CAFA,6BAEA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,2FACA,eACE,CAAA,CAAA,mLAOF,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,iBAEA,CAAA,WACA,CAAA,WACA,CAAA,qBACA,CAAA,2BACA,CAAA,cAEA,CAAA,eACA,CAAA,kBACA,CAAA,yBACA,mLAbF,WAcI,CAAA,WACA,CAAA,cACA,CAAA,CAAA,2MAEF,gBACE,CAAA,yBACA,2MAFF,aAGI,CAAA,CAAA,mMAIJ,UACE,CAAA,iBACA,CAAA,OACA,CAAA,YACA,CAAA,OACA,CAAA,QACA,CAAA,kBACA,CAAA,0BACA,CAAA,2DACA,CAAA,yBAEA,mMAXF,YAYI,CAAA,0BACA,CAAA,CAAA,yBAOJ,2FAFF,2BAGI,CAAA,mGACA,MACE,CAAA,gCACA,CADA,wBACA,CAAA,CAAA,kFAKN,cACE,CAAA,UACA,CAAA,yBACA,kFAHF,cAII,CAAA,CAAA,2EAON,WACE,CAAA,aACA,CAAA,eACA,CAAA,yBAEA,2EALF,UAMI,CAAA,iBACA,CAAA,CAAA,yBAIJ,oFAEE,6BACE,CADF,6BACE,CADF,8BACE,CADF,0BACE,CAAA,+FAIE,2BACE,CAAA,uGAEA,WACE,CAAA,MACA,CAAA,0BACA,CAAA,2DACA,CAAA,wFAKN,iBACE,CAAA,CAAA,8CASd,cACE,CAAA,yBAEA,8CAHF,qBAII,CAAA,CAAA,yDAGF,gBACE,CAAA,aACA,CAAA,yBAEE,yDAJJ,kBAKM,CAAA,iBACA,CAAA,CAAA,iEAIJ,iBACE,CAAA,kBACA,CAAA,yBAEA,iEAJF,kBAKI,CAAA,CAAA,uEAGF,cACE,CAAA,aACA,CAAA,gBACA,CAAA,yBAEA,uEALF,cAMI,CAAA,gBACA,CAAA,CAAA,6EAGF,UACE,CAAA,sEAIJ,cACE,CAAA,gBACA,CAAA,aACA,CAAA,yBAEA,sEALF,cAMI,CAAA,gBACA,CAAA,CAAA,mEAKN,eACE,CAAA,yBAEA,mEAHF,eAII,CAAA,CAAA,yEAGF,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,6BACA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,yBAEA,yEALF,aAMI,CAAA,CAAA,6FAGF,gBACE,CAAA,yBAEA,6FAHF,eAII,CAAA,CAAA,kFAIJ,iBACE,CAAA,iBACA,CAAA,yBAEA,kFAJF,QAKI,CAAA,CAAA,4FAGF,kCACE,CAAA,eACA,CAAA,cACA,CAAA,iBACA,CAAA,aACA,CAAA,yBAEA,4FAPF,cAQI,CAAA,CAAA,oGAGF,UACE,CAAA,iBACA,CAAA,KACA,CAAA,QACA,CAAA,WACA,CAAA,YACA,CAAA,qBACA,CAAA,WACA,CAAA,UACA,CAAA,qFAIJ,oBACE,CAAA,cACA,CAAA,aACA,CAAA,eACA,CAAA,yBAEA,qFANF,cAOI,CAAA,kBACA,CAAA,oBACA,CAAA,CAAA,0FAGF,cACE,CAAA,kGAEA,UACE,CAAA,yBAEA,kGAHF,WAII,CAAA,CAAA,yBAIJ,0FAXF,cAYI,CAAA,CAAA,wFAKN,cACE,CAAA,gBACA,CAAA,eACA,CAAA,0FAGF,+BACE,CAAA,eACA,CAAA,iBACA,CAAA,eACA,CAAA,aACA,CAAA,KACA,CAAA,OAEA,CAAA,gBACA,CAAA,0BACA,CADA,iBACA,CAAA,yBAEA,0FAZF,KAaI,CAAA,cACA,CAAA,CAAA,gFAKN,eACE,CAAA,yBAEA,gFAHF,eAII,CAAA,eACA,CAAA,CAAA,2FAIJ,6BACE,CADF,6BACE,CADF,8BACE,CADF,0BACE,CAAA,oGAEA,cACE,CAAA,gBACA,CAAA,yBAEA,oGAJF,QAKI,CAAA,CAAA,0CASd,cACE,CAAA,wBACA,CAAA,yBAEA,0CAJF,qBAKI,CAAA,CAAA,qDAGF,gBACE,CAAA,aACA,CAAA,yBACE,qDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,gEAIJ,eACE,CAAA,cACA,CAAA,gBACA,CAAA,eACA,CAAA,iBACA,CAAA,yBAEA,gEAPF,eAQI,CAAA,eACA,CAAA,CAAA,2DAIJ,eACE,CAAA,YACA,CAAA,oCACA,CAAA,aACA,CAAA,yBAEA,2DANF,aAOI,CAAA,CAAA,iEAGF,sBACE,CAAA,uBACA,CADA,mBACA,CAAA,yBAIE,qFAFF,eAGI,CAAA,CAAA,yEAIJ,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,6BACA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,yBAEA,yEALF,wBAMI,CANJ,qBAMI,CANJ,6BAMI,CAAA,CAAA,6EAGF,WACE,CAAA,4CACA,CADA,oCACA,CAAA,yBAIE,iGAFF,gBAGI,CAAA,CAAA,yBAIJ,6EAXF,WAYI,CAAA,CAAA,mEAKN,cACE,CAAA,iBACA,CAAA,eACA,CAAA,yBAEA,mEALF,eAMI,CAAA,CAAA,0FAQA,WACE,CAAA,yBAEA,0FAHF,WAII,CAAA,CAAA,2CAUhB,cACE,CAAA,yBAEA,2CAHF,qBAII,CAAA,CAAA,sDAGF,gBACE,CAAA,aACA,CAAA,yBACE,sDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,6DAIJ,eACE,CAAA,mBACA,CADA,mBACA,CADA,YACA,CAAA,wBACA,CADA,qBACA,CADA,6BACA,CAAA,yBAEA,6DALF,aAMI,CAAA,CAAA,mEAGF,YACE,CAAA,iBACA,CAAA,qBACA,CAAA,iBACA,CAAA,4CACA,CADA,oCACA,CAAA,WACA,CAAA,yBAEA,mEARF,iBASI,CAAA,UACA,CAAA,CAAA,yBAKA,uFAFF,eAGI,CAAA,CAAA,yBAMF,4EAFF,mBAGI,CAHJ,mBAGI,CAHJ,YAGI,CAAA,wBACA,CADA,qBACA,CADA,6BACA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,cACA,CAAA,CAAA,+EAGF,cACE,CAAA,yBAEA,+EAHF,cAII,CAAA,CAAA,gFAIJ,UACE,CAAA,kBACA,CAAA,iBACA,CAAA,gBACA,CAAA,yBAEA,gFANF,UAOI,CAAA,QACA,CAAA,CAAA,wEAKN,cACE,CAAA,eACA,CAAA,yBAEA,wEAJF,eAKI,CAAA,CAAA,yEAIJ,eACE,CAAA,cACA,CAAA,gBACA,CAAA,eACA,CAAA,eACA,CAAA,yBAEA,yEAPF,eAQI,CAAA,CAAA,2CAQZ,cACE,CAAA,wBACA,CAAA,yBAEA,2CAJF,qBAKI,CAAA,CAAA,sDAGF,gBACE,CAAA,aACA,CAAA,yBACE,sDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,2DAIJ,eACE,CAAA,8DAEA,kBACE,CAAA,UACA,CAAA,iBACA,CAAA,cACA,CAAA,cACA,CAAA,yBACA,CAAA,yBAEA,8DARF,cASI,CAAA,CAAA,mEAGF,UACE,CAAA,yBAEA,mEAHF,cAII,CAAA,CAAA,oEAKN,mBACE,CAAA,qBACA,CAAA,yBACA,CAAA,iBACA,CAAA,yBAEA,oEANF,SAQI,CAAA,mBACA,CAAA,CAAA,0EAGF,mBACE,CADF,mBACE,CADF,YACE,CAAA,uBACA,CADA,oBACA,CADA,sBACA,CAAA,yBAEA,0EAJF,aAKI,CAAA,CAAA,gFAGF,WACE,CAAA,cACA,CAAA,yBAEA,gFAJF,cAKI,CAAA,aACA,CAAA,CAAA,oGAGF,0BACE,CAAA,yBAEA,oGAHF,gBAII,CAAA,yBACA,CAAA,CAAA,yBAMF,yFAFF,mBAGI,CAHJ,mBAGI,CAHJ,YAGI,CAAA,uBACA,CADA,oBACA,CADA,sBACA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,CAAA,oFAIF,WACE,CAAA,aACA,CAAA,yBAEA,oFAJF,QAKI,CAAA,iBACA,CAAA,UACA,CAAA,CAAA,mFAIF,cACE,CAAA,cACA,CAAA,yBAEA,mFAJF,cAKI,CAAA,QACA,CAAA,CAAA,kFAIR,eACE,CAAA,cACA,CAAA,gBACA,CAAA,eACA,CAAA,yBAEA,kFANF,cAOI,CAAA,aACA,CAAA,CAAA,gFAMR,kBACE,CAAA,mBACA,CADA,mBACA,CADA,YACA,CAAA,uBACA,CADA,oBACA,CADA,sBACA,CAAA,qBACA,CADA,kBACA,CADA,oBACA,CAAA,yBAEA,gFANF,eAOI,CAAA,CAAA,sFAGF,cACE,CAAA,gBACA,CAAA,iBACA,CAAA,UACA,CAAA,uFAGF,cACE,CAAA,gBACA,CAAA,4FAEA,cACE,CAAA,gBACA,CAAA,0EAKN,iBACE,CAAA,kBACA,CAAA,cACA,CAAA,UACA,CAAA,yBAEA,0EANF,kBAOI,CAAA,cACA,CAAA,CAAA,wEAIJ,cACE,CAAA,oBACA,CAAA,yBAEA,wEAJF,cAKI,CAAA,CAAA,qFAKA,UACE,CAAA,QACA,CAAA,0CASd,cACE,CAAA,yBAEA,0CAHF,qBAII,CAAA,CAAA,qDAGF,gBACE,CAAA,aACA,CAAA,yBACE,qDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,2DAIJ,eACE,CAAA,yBAEA,2DAHF,eAII,CAAA,CAAA,iEAGF,mBACE,CADF,mBACE,CADF,YACE,CAAA,qFAEA,eACE,CAAA,yBAEA,qFAHF,gBAII,CAAA,gBACA,CAAA,CAAA,oFAIJ,iBACE,CAAA,4FAEA,UACE,CAAA,iBACA,CAAA,SACA,CAAA,UACA,CAAA,wBACA,CAAA,SACA,CAAA,yBACA,CAAA,yBAEA,4FATF,SAUI,CAAA,wBACA,CAAA,CAAA,2FAIJ,UACE,CAAA,iBACA,CAAA,SACA,CAAA,UACA,CAAA,OACA,CAAA,QACA,CAAA,kBACA,CAAA,6BACA,CAAA,8DACA,CAAA,yBAEA,2FAXF,SAYI,CAAA,CAAA,gFAKN,iBACE,CAAA,WACA,CAAA,YACA,CAAA,iBACA,CAAA,mBACA,CADA,mBACA,CADA,YACA,CAAA,2BACA,CADA,4BACA,CADA,yBACA,CADA,qBACA,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,uBACA,CADA,oBACA,CADA,sBACA,CAAA,wBACA,CAAA,yBAEA,gFAXF,cAYI,CAAA,cACA,CAAA,eACA,CAAA,eACA,CAAA,iBACA,CAAA,CAAA,sFAGF,kCACE,CAAA,eACA,CAAA,cACA,CAAA,eACA,CAAA,UACA,CAAA,yBAEA,sFAPF,cAQI,CAAA,CAAA,wFAIJ,kCACE,CAAA,eACA,CAAA,cACA,CAAA,eACA,CAAA,UACA,CAAA,aACA,CAAA,yBAEA,wFARF,cASI,CAAA,cACA,CAAA,CAAA,0EAMN,mBACE,CAAA,+BACA,CAAA,kBACA,CADA,mBACA,CADA,WACA,CAAA,mFAEA,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,iBACA,CAAA,2FAEA,UACE,CAAA,iBACA,CAAA,SACA,CAAA,YACA,CAAA,qBACA,CAAA,SACA,CAAA,WACA,CAAA,yBAEA,2FATF,YAUI,CAAA,CAAA,uFAIJ,WACE,CAAA,iBACA,CAAA,yBAEA,uFAJF,UAKI,CAAA,iBACA,CAAA,CAAA,sFAIJ,cACE,CAAA,yBAEA,sFAHF,cAII,CAAA,CAAA,gFAKN,cACE,CAAA,eACA,CAAA,gBACA,CAAA,eACA,CAAA,yBAEA,gFANF,eAOI,CAAA,cACA,CAAA,CAAA,2DAOV,eACE,CAAA,cACA,CAAA,gBACA,CAAA,eACA,CAAA,UACA,CAAA,yCAKN,qBACE,CAAA,wBACA,CAAA,yBAEA,yCAJF,qBAKI,CAAA,CAAA,oDAGF,gBACE,CAAA,aACA,CAAA,yBACE,oDAHJ,kBAIM,CAAA,iBACA,CAAA,CAAA,yDAIJ,eACE,CAAA,gEAEA,mBACE,CADF,mBACE,CADF,YACE,CAAA,2BACA,CADA,4BACA,CADA,yBACA,CADA,qBACA,CAAA,iFAIA,eACE,CAAA,yBACA,iFAFF,eAGI,CAAA,CAAA,gFAIJ,+BACE,CAAA,iEAGF,mBACE,CADF,mBACE,CADF,YACE,CAAA,wBACA,CADA,qBACA,CADA,kBACA,CAAA,kBACA,CAAA,yBACA,iEAJF,kBAKI,CAAA,CAAA,uEAGF,UACE,CAAA,WACA,CAAA,yBACA,uEAHF,UAII,CAAA,WACA,CAAA,CAAA,uEAIJ,cACE,CAAA,eACA,CAAA,iBACA,CAAA,yBACA,uEAJF,cAKI,CAAA,iBACA,CAAA,CAAA,iEAKN,kBACE,CAAA,yBACA,iEAFF,kBAGI,CAAA,CAAA,uEAGF,cACE,CAAA,eACA,CAAA,kEAIJ,eACE,CAAA,gEASF,mBACE,CAAA,+BACA,CAAA,yBAEA,gEAJF,oBAKI,CAAA,mBACA,CAAA,CAAA,oFAGF,eACE,CAAA,yBAEA,oFAHF,iBAII,CAAA,eACA,CAAA,CAAA,mEAIJ,iBACE,CAAA,cACA,CAAA,iBACA,CAAA,eACA,CAAA,yBAEA,mEANF,kBAOI,CAAA,cACA,CAAA,CAAA,2EAGF,UACE,CAAA,mDACA,CAAA,uBACA,CAAA,2BACA,CAAA,gBACA,CAAA,UACA,CAAA,WACA,CAAA,iBACA,CAAA,KACA,CAAA,QACA,CAAA,MACA,CAAA,WACA,CAAA,yBAEA,2EAdF,UAeI,CAAA,WACA,CAAA,QACA,CAAA,CAAA,mEAKN,eACE,CAAA,cACA,CAAA,gBACA,CAAA,eACA,CAAA,yBAEA,mEANF,eAOI,CAAA", "file": "newwhitepaper.min.css"}