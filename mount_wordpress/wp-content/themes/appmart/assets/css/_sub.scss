@import './variable';

.show_sp {
  display: none;
}

// ==============================================
// page
// ==============================================
.company-overview {
  &__item {
    @include flex(initial, flex-start, wrap);

    border-top: 1px solid #808080;

    &:last-of-type {
      border-bottom: 1px solid #808080;
    }

    &:nth-of-type(7) dd {
      line-height: 1.8;
    }

    dt {
      box-sizing: border-box;
      width: 300px;
      padding: 30px 0 30px 30px;
      font-weight: bold;
      line-height: (19 / 16);
      color: #333;
    }

    dd {
      width: calc(100% - 300px);
      padding: 30px 0;
      line-height: (19 / 16);
      color: #333;

      .heading {
        font-weight: bold;
      }

      .hq_map {
        position: relative;
        width: 100%;
        padding-top: 28.66%;
        margin: 25px 0;

        iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }

      a[href^='tel'] {
        color: $color--text;
        pointer-events: none;
      }

      address {
        line-height: 1.8;
      }
    }
  }
}

// ==============================================
// page section
// ==============================================
.section {
  &__service__intro {
    padding-bottom: 150px;
    background: linear-gradient(
      transparent 0%,
      transparent 30%,
      $color--base--green 30%,
      $color--base--green 100%
    );

    .service__intro__lead {
      h2 {
        font-size: 40px;
        font-weight: bold;
        color: $color--text--strong;
        text-align: center;
      }
    }

    .service__intro__cont {
      @include flex(initial, stretch, wrap);

      margin-top: 85px;

      &__item {
        position: relative;
        width: calc(33.333% - 20px);
        margin-right: 30px;
        background: $color--white;
        border-radius: 20px;
        box-shadow: 0 6px 8px rgb(0 0 0 / 16%);

        &::before {
          content: '';

          @include absolute(
            1px,
            96px,
            -50px,
            initial,
            initial,
            50%,
            $color--text,
            translateX(-50%),
            initial
          );
        }

        &:nth-child(3n) {
          margin-right: 0;
        }

        &__pic {
          height: 200px;
          overflow: hidden;

          img {
            width: 90%;
            margin: auto;
          }
        }

        &__title {
          @include flex(center, center, nowrap);

          margin: 25px 0 40px;

          h3 {
            margin-left: 20px;
            font-size: 40px;
            font-weight: bold;
          }
        }

        &__list {
          padding-bottom: 40px;
          padding-left: 30px;

          li {
            position: relative;
            padding-left: 42px;

            &::before {
              content: '';

              @include absolute(
                32px,
                1px,
                50%,
                initial,
                initial,
                0,
                $color--text,
                translateY(-50%),
                initial
              );
            }

            a {
              font-size: 18px;
              color: $color--text;
            }
          }

          li + li {
            margin-top: 30px;
          }
        }
      }
    }
  }

  &__service-detail {
    padding-top: 150px;

    .section__title {
      box-sizing: border-box;
      max-width: 1050px;
      padding-left: 50px;
      margin: auto;
      font-size: 32px;
      font-weight: bold;
    }

    &__item {
      & + & {
        margin-top: -3px;
      }

      .outerbox {
        position: relative;
        width: calc(100% - 50px);
        max-width: 1050px;
        margin: 0 auto;

        &::after {
          position: absolute;
          top: 50%;
          width: 56px;
          height: 56px;
          content: '';
          background: transparent url('../images/icon/round-arrow_orange.png') no-repeat center
            center / cover;
          transform: translateY(-50%);
        }

        &--strategy {
          border-radius: 0 0 0 70px;

          &::before {
            content: '';

            @include absolute(
              56px,
              56px,
              -28px,
              initial,
              initial,
              -28px,
              initial,
              translateY(-50%),
              initial
            );

            background: transparent url('../images/icon/round-arrow_black.png') no-repeat center
              center / cover;
          }

          &::after {
            left: -28px;
          }
        }

        &--creative {
          border-radius: 0 70px 0 0;

          &::after {
            right: -28px;
          }
        }

        &--analysis {
          &::after {
            left: -28px;
          }
        }
      }

      .middlebox {
        width: calc(100% - 70px);
        padding: 150px 0;
        border-bottom: 3px solid $color--text;
        border-left: 3px solid $color--text;
        border-radius: 0 0 0 70px;

        &--creative {
          margin-right: 0;
          margin-left: auto;
          border-top: 3px solid $color--text;
          border-right: 3px solid $color--text;
          border-bottom: 3px solid $color--text;
          border-left: 0;
          border-radius: 0 70px 70px 0;
        }

        &--analysis {
          border-top: 3px solid $color--text;
          border-bottom: 0;
          border-radius: 70px 0 0;
        }
      }

      .innerbox {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        max-width: 1050px;
        padding: 80px 100px;
        border-radius: 20px;
        box-shadow: 0 0 12px rgb(0 0 0 / 16%);

        &::before {
          position: absolute;
          top: 40px;
          left: 50%;
          z-index: -1;
          display: block;
          width: 100%;
          font-family: 'Open Sans', sans-serif;
          font-size: 140px;
          font-weight: bold;
          color: rgb(255 126 118 / 20%);
          text-align: center;
          text-transform: uppercase;
          content: attr(data-en);
          transform: translateX(-50%);
        }

        &--strategy {
          right: -75px;
        }

        &--creative {
          left: -75px;

          &::before {
            color: rgb(51 186 210 / 20%);
          }
        }

        &--analysis {
          right: -75px;

          &::before {
            color: rgb(161 228 197 / 20%);
          }
        }
      }

      .item__title {
        position: relative;
        display: flex;
        align-items: center;

        h3 {
          margin-left: 20px;
          font-size: 40px;
          font-weight: bold;
        }

        &::before {
          position: absolute;
          top: 50%;
          width: 120px;
          height: 3px;
          content: '';
          background: $color--text;
          transform: translateY(-50%);
        }

        &::after {
          position: absolute;
          top: 50%;
          z-index: 2;
          width: 15px;
          height: 15px;
          content: '';
          background: $color--accent--yellow;
          border-radius: 50%;
          transform: translateY(-50%);
        }
      }

      .wrap {
        display: flex;
        flex-direction: row-reverse;
        align-items: center;

        img {
          width: 45%;
          margin-left: auto;
        }

        p {
          width: 50%;

          span {
            display: inline-block;
          }
        }
      }

      .innerbox--strategy {
        .item__title {
          &::before {
            left: -175px;
          }

          &::after {
            left: -182px;
          }
        }
      }

      .innerbox--creative {
        .wrap {
          flex-direction: row;

          img {
            margin-right: auto;
            margin-left: 0;
          }
        }

        .item__title {
          justify-content: flex-end;

          &::before {
            right: -175px;
          }

          &::after {
            right: -183px;
          }
        }
      }

      .innerbox--analysis {
        .item__title {
          &::before {
            left: -175px;
          }

          &::after {
            left: -182px;
          }
        }
      }

      ul {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 45px;

        li {
          width: calc(50% - 25px);
          margin-top: 40px;

          // width: calc(50% - 45px);
          margin-right: 50px;

          &:nth-child(2n) {
            margin-right: 0;
          }

          .head {
            padding: 30px 20px;
            background: $color--text;
            border-radius: 20px 20px 0 0;

            h4 {
              display: flex;
              align-items: center;

              .icon {
                flex-shrink: 0;
                width: 72px;
                height: 72px;
                margin-right: 15px;
                background: $color--white;
                background-repeat: no-repeat;
                background-position: center center;
                background-size: 40px 40px;
                border-radius: 50%;
              }

              span:not(.icon) {
                display: inline-block;
                font-size: 20px;
                font-weight: bold;
                color: $color--white;
              }
            }
          }

          .body {
            padding: 30px;
            background: #fafafa;
            border-radius: 0 0 20px 20px;

            p {
              font-size: 14px;

              // line-height: (17 / 14);
              line-height: 25px;

              span {
                display: inline-block;
              }
            }

            .more_btn {
              margin-top: 25px;
              text-align: right;

              a {
                position: relative;
                display: inline-block;
                padding-right: 30px;
                font-size: 16px;
                color: $color--text;

                &::after {
                  content: '';

                  @include absolute(
                    18px,
                    11px,
                    50%,
                    0,
                    initial,
                    initial,
                    transparent,
                    translateY(-50%),
                    initial
                  );

                  background-image: url('../images/svg/right_orange.svg');
                  background-position: center;
                  background-size: contain;
                }
              }
            }
          }
        }
      }
    }

    &__footer {
      width: 100%;
      padding: 50px 0;
      background: $color--main;

      > .container {
        position: relative;
      }

      p {
        font-size: 28px;
        font-weight: bold;
        color: $color--white;
        text-align: center;

        span {
          display: inline-block;
        }
      }

      img {
        position: absolute;
        top: 50%;
        right: -225px;
        width: 450px;
        height: auto;
        transform: translateY(-50%);
      }
    }
  }

  &__search {
    > .container {
      @include flex(initial, flex-start, wrap);
    }

    .section_title {
      h2 {
        font-size: 40px;
        color: $color--text--strong;
      }
    }
  }

  &__not-found {
    > .container {
      @include flex(initial, flex-start, wrap);
    }

    .section_title {
      margin-bottom: 30px;

      h2 {
        font-size: 40px;
        color: $color--text--strong;
      }
    }
  }
}

// ==============================================
// single
// ==============================================
.single {
  &__wp {
    &__cont {
      @include flex(initial, flex-start, wrap);
    }

    &__left {
      width: 48%;
    }

    &__eyecatch {
      position: relative;
      padding-top: 76%;
      margin-bottom: 30px;
      border: 1px solid #808080;
      border-radius: 6px;

      .img {
        @include absolute(100%, 100%, 0, 0, 0, 0, transparent, initial, initial);

        background-position: center center;
        background-size: cover;
        border-radius: 6px;
      }
    }

    &__title {
      h1 {
        margin-bottom: 40px;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.7;
      }
    }

    &__content {
      p {
        line-height: 1.7;
      }

      p + p {
        margin-top: 30px;
      }

      &__box {
        margin: 50px 0 30px;

        .head {
          padding: 15px 0;
          font-size: 20px;
          font-weight: bold;
          color: $color--white;
          text-align: center;
          background: $color--text;
          border-radius: 20px 20px 0 0;
        }

        .body {
          padding: 30px 25px;
          background: #fafafa;
          border-radius: 0 0 20px 20px;

          p {
            position: relative;
            padding-left: 34px;
            font-size: 18px;

            &::before {
              content: '';

              @include absolute(
                24px,
                24px,
                50%,
                initial,
                initial,
                0,
                initial,
                translateY(-50%),
                initial
              );

              background-image: url('../images/svg/check.svg');
              background-size: cover;
            }
          }

          p + p {
            margin-top: 23px;
          }
        }
      }
    }

    &__right {
      width: 48%;
      margin-left: auto;

      p {
        margin-bottom: 45px;
      }
    }
  }

  &__blog {
    &__cont {
      display: flex;
    }

    &__top {
      h1 {
        position: relative;
        padding-top: 25px;
        margin-bottom: 32px;
        font-size: 32px;
        font-weight: bold;
        line-height: 50px;

        &::before {
          content: '';

          @include absolute(
            64px,
            5px,
            0,
            initial,
            initial,
            0,
            $color--accent--orange,
            translateY(-50%),
            1
          );

          border-radius: 20px;
        }
      }

      time {
        display: block;
        margin-bottom: 15px;
        font-size: 14px;
        text-align: right;
      }

      &--update {
        margin-right: 12px;
      }
    }

    &__eyecatch {
      width: 100%;
      margin-top: 80px;

      img {
        width: 100%;
        height: auto;
      }
    }

    &__share {
      padding: 60px 0 40px;

      p {
        margin-bottom: 30px;
        font-size: 28px;
        font-weight: bold;
        text-align: center;

        span {
          position: relative;
          display: inline-block;
          padding-left: 44px;

          &::before {
            content: '';

            @include absolute(
              32px,
              32px,
              50%,
              initial,
              initial,
              0,
              initial,
              translateY(-50%),
              initial
            );

            background-image: url('../images/icon/share.png');
            background-position: center;
            background-size: contain;
          }
        }
      }

      .sns__list {
        display: flex;
        flex-wrap: wrap;

        li {
          width: calc((100% - 60px) / 4);
        }

        li + li {
          margin-left: 20px;
        }

        a {
          display: block;
          padding: 17px 10px;
          background: #1da1f2;
          border-radius: 6px;

          @include flex(center, center, nowrap);

          &:hover {
            opacity: 0.6 !important;
          }

          img {
            display: inline-block;
            width: 20px;
            height: 20px;
          }

          span {
            display: inline-block;
            margin-left: 8px;
            font-size: 14px;
            color: $color--white;
          }

          &.facebook {
            background: #305097;
          }

          &.hatena {
            background: #00a4de;
          }

          &.line {
            background: #00b900;

            span {
              text-transform: uppercase;
            }
          }
        }
      }
    }

    &__author {
      padding: 20px;
      background: $color--base--blue;

      @include flex(center, flex-start, wrap);

      .pic {
        width: 150px;
        height: 150px;

        img {
          border-radius: 50%;
        }
      }

      .body {
        width: calc(100% - 182px);
        margin-left: auto;

        .name {
          margin-bottom: 25px;
          color: $color--text--strong;

          span {
            font-weight: bold;
          }
        }
      }
    }

    &__related-posts {
      margin-top: 40px;

      h2 {
        position: relative;
        padding-top: 16px;
        margin-bottom: 30px;
        font-size: 28px;
        font-weight: bold;

        &::before {
          content: '';

          @include absolute(
            60px,
            5px,
            0,
            initial,
            initial,
            0,
            $color--accent--orange,
            initial,
            initial
          );

          border-radius: 20px;
        }
      }

      &__list {
        @include flex(initial, initial, wrap);

        > li {
          width: calc((100% - 80px) / 3);
          margin-right: 40px;
          margin-bottom: 25px;

          &:nth-of-type(3n) {
            margin-right: 0;
          }

          a {
            display: block;

            &:hover {
              .related-posts__eyecatch .img {
                transform: scale(1.1);
              }
            }

            .related-posts__eyecatch {
              position: relative;
              padding-top: 55%;
              overflow: hidden;
              border-radius: 6px;

              .img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-repeat: no-repeat;
                background-position: center center;
                background-size: cover;
                transition: transform 0.5s;
                transform: scale(1);
              }
            }

            h3 {
              margin-top: 18px;
              font-size: 13px;
              font-weight: bold;
              line-height: (16 / 13);
            }

            time {
              display: none;
            }
          }
        }
      }
    }
  }
}

// ==============================================
// archive
// ==============================================
.archive {
  &__blog {
    &__cont {
      @include flex(initial, flex-start, wrap);
    }
  }

  &__wp {
    &__tab {
      margin-bottom: 80px;

      ul {
        @include flex(center, center, nowrap);

        li {
          box-sizing: border-box;

          // width: calc((100% - 30px) / 3);
          width: calc((100% - 30px) / 3);
          padding: 10px 0;
          margin-right: 15px;
          font-size: 20px;
          font-weight: bold;
          color: #808080;
          text-align: center;
          border: 1px solid #808080;
          border-radius: 6px;

          &:nth-of-type(4n) {
            margin-right: 0;
          }

          &:hover {
            cursor: pointer;
          }
        }

        li.is-active {
          color: $color--white;
          background: $color--text--strong;
          border-color: $color--text--strong;
        }

        li.all-button {
          width: calc((100% - 30px) / 4 - 100px);
        }
      }
    }

    &__cont {
      &__title {
        margin-bottom: 50px;
        font-size: 40px;
        font-weight: bold;
        color: $color--text--strong;
        text-align: center;
      }

      .group {
        display: none;

        &.is-show {
          display: block;
        }
      }

      &__list {
        @include flex(initial, stretch, wrap);
      }

      &__item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: calc((100% - 120px) / 3);
        margin-right: 60px;
        margin-bottom: 60px;

        &:nth-of-type(3n) {
          margin-right: 0;
        }

        &__top {
          h2 {
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: bold;
            line-height: 1.7;
          }

          p {
            font-size: 14px;
            line-height: 1.7;
          }
        }

        &__eyecatch {
          position: relative;
          padding-top: 75%;
          margin-bottom: 10px;
          overflow: hidden;
          border: 1px solid #808080;
          border-radius: 6px;

          img {
            @include absolute(100%, 100%, 0, 0, 0, 0, initial, initial, initial);

            border-radius: 6px;
          }
        }
      }
    }
  }
}

// ==============================================
// common parts
// ==============================================
.primary {
  width: 70%;
}

.sidebar {
  width: 23%;
  margin-left: auto;

  .category {
    margin-bottom: 50px;

    h4 {
      position: relative;
      padding-top: 20px;
      padding-left: 40px;
      font-size: 28px;
      font-weight: bold;

      &::before {
        content: '';

        @include absolute(
          60px,
          5px,
          0,
          initial,
          initial,
          0,
          $color--accent--orange,
          initial,
          initial
        );

        border-radius: 20px;
      }

      &::after {
        content: '';

        @include absolute(28px, 28px, initial, initial, 0, 0, initial, initial, initial);

        background-image: url('../images/icon/tag.png');
        background-position: center center;
        background-size: cover;
      }
    }

    &__list {
      margin-top: 30px;

      li {
        position: relative;
        padding-left: 15px;

        &::before {
          /* width: 5px; */

          /* height: 1px; */
          position: absolute;
          top: 50%;
          right: initial;
          bottom: initial;
          left: 0;
          z-index: initial;
          color: #333;
          content: '-';
          transform: translateY(-50%);
        }
      }

      li + li {
        margin-top: 15px;
      }

      a {
        color: $color--text;
      }
    }
  }

  .banner {
    // margin-top: 64px;
    margin-top: 24px;

    &__list {
      li + li {
        margin-top: 40px;
      }

      li a {
        display: block;

        &:hover {
          opacity: 0.6 !important;
        }

        img {
          width: 100%;
        }
      }
    }
  }
}

.is_fixed {
  position: fixed;
  top: 80px;
  right: calc((100% - 1080px) / 2);
  z-index: 990;
  width: calc(1080px * 0.23);
}

.tr_reset {
  transform: initial;
}

.search_box_area {
  .searchform {
    position: relative;
    max-width: 100%;
    height: 100px;
  }

  input.searchfield {
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    height: 64px;
    padding: 0 10px 0 25px;
    appearance: none;
    border: 1px solid $color--text;
    border-radius: 6px;
    outline: 0;
  }

  input.searchsubmit {
    position: absolute;
    top: 20px;
    right: 24px;
    width: 24px;
    height: 24px;
    padding: 0;
    background: none;
  }
}

.category_label {
  display: none;
  width: 100%;
  overflow: auto;
  white-space: nowrap;

  li {
    position: relative;
    display: inline-block;
    padding: 11px 13px 11px 40px;
    margin-right: 10px;
    font-size: 14px;
    color: $color--white;
    background: #3d3d3f;
    border-radius: 6px;

    &::before {
      content: '';

      @include absolute(
        20px,
        20px,
        50%,
        initial,
        initial,
        13px,
        transparent,
        translateY(-50%),
        initial
      );

      background-image: url('../images/icon/tag-w.png');
      background-position: center center;
      background-size: cover;
    }

    a {
      color: $color--white;
    }
  }
}

.pagination {
  h2.screen-reader-text {
    display: none;
  }

  .nav-links {
    @include flex(center, center, wrap);

    padding: 50px 0 30px;

    span,
    a {
      display: inline-block;
      padding: 10px 15px;
      margin: 0 10px;
      font-size: 16px;
      color: #333;
      background: $color--white;
      border: 1px solid #808080;
      border-radius: 6px;
      box-shadow: 0 0 3px rgb(128 128 128 / 16%);

      &:hover {
        opacity: 0.6 !important;
      }

      &.current {
        color: $color--white;
        background: #333;
      }
    }
  }
}

// ==============================================
// entry style
// ==============================================
.entry {
  padding: 40px 0 60px;

  h2 {
    padding: 15px 0;
    margin: 60px 0 20px;
    font-size: 24px;
    font-weight: bold;
    border-top: 3px solid $color--main;
    border-bottom: 3px solid $color--main;
  }

  h3 {
    padding: 5px 20px 5px 10px;
    margin: 40px 0 15px;
    font-size: 22px;
    border-left: 7px solid $color--main;
  }

  h4 {
    position: relative;
    padding-left: 25px;
    margin: 30px 0 10px;
    font-size: 20px;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 18px;
      height: 18px;
      content: '';
      background: url('../images/svg/tick.svg') no-repeat center center / cover;
      transform: translateY(-50%);
    }
  }

  h5 {
    margin: 30px 0 10px;
    font-size: 18px;
  }

  p {
    margin-bottom: 20px;
    line-height: 1.9em;
    text-align: justify;
    letter-spacing: 1.5px;
  }

  a {
    // color: $color--text--strong;
    // text-decoration: underline;
    color: #fa6b58;
    text-decoration: none;

    &:hover {
      color: #dd523f;
    }
  }

  ul {
    padding-left: 20px;
    margin-top: 30px;
    margin-bottom: 20px;

    & > li {
      margin-bottom: 10px;
      margin-left: 20px;
      font-size: 15px;
      line-height: 1.5;
      color: $color--text;
      list-style-type: disc;
    }
  }

  ol {
    padding-left: 20px;
    margin-top: 30px;
    margin-bottom: 20px;

    & > li {
      margin-bottom: 10px;
      margin-left: 20px;
      font-size: 15px;
      line-height: 1.5;
      color: $color--text;
      list-style-type: decimal;
    }
  }

  li {
    margin-bottom: 10px;
    margin-left: 20px;
    font-size: 15px;
    line-height: 1.5;
    color: $color--text;
    list-style-type: disc;
  }

  a.cta-link {
    box-sizing: border-box;
    display: table;
    width: initial;
    padding: 15px 30px;
    margin: 30px auto;
    font-weight: bold;
    color: $color--white !important;
    text-align: center;
    background-color: #fa6b58;
    border-radius: 6px;
    box-shadow: 0 4px 0 #dd523f;

    &:hover {
      box-shadow: none !important;
      opacity: 1 !important;
      transform: translateY(4px);
    }
  }

  img {
    max-width: 100%;
    height: auto;
    margin: 20px auto;
    pointer-events: auto;
  }

  .marker-yellow {
    background: linear-gradient(to bottom, transparent 70%, #f3c11d 70%);
  }

  .marker-green {
    background: linear-gradient(to bottom, transparent 70%, #3c8b86 70%);
  }

  strong {
    font-weight: bold;
  }

  table {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #333;

    thead th {
      padding: 10px 15px;
      color: $color--white;
      background: $color--text--strong;
      border-right: $color--white solid 1px;
      border-bottom: $color--white solid 1px;
    }

    thead th:last-child {
      border-right: $color--text--strong solid 1px;
    }

    tbody {
      td {
        padding: 10px 15px;
        vertical-align: top;
        background: $color--white;
        border-right: $color--text--strong solid 1px;
        border-bottom: $color--text--strong solid 1px;
        border-left: $color--text--strong solid 1px;
      }

      th {
        padding: 10px 15px;
        color: $color--white;
        vertical-align: top;
        background: $color--text--strong;
        border-bottom: $color--white solid 1px;
      }
    }
  }

  table.darkgreen {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #333;
    border-collapse: collapse;

    th {
      color: $color--white;
      background: $color--text--strong;
    }

    th,
    td {
      padding: 6px 10px;
      border: solid 1px #ededed;
    }
  }

  > div.pointbox {
    position: relative;
    padding: 1em 1.5em;
    margin: 2em 0 1.5em;
    border: solid 3px $color--text--strong;

    > ul {
      margin-top: 20px;
    }

    .box-title {
      position: absolute;
      top: -27px;
      left: -3px;
      display: inline-block;
      height: 25px;
      padding: 3px 10px;
      font-size: 17px;
      font-weight: bold;
      line-height: 25px;
      color: $color--white;
      vertical-align: middle;
      background: $color--text--strong;
      border-radius: 5px 5px 0 0;
    }

    p {
      padding: 0;
      margin: 0;
    }
  }

  .mokuji {
    position: relative;
    padding: 25px 10px 7px;
    margin: 2em 0;
    border: solid 2px $color--text--strong;

    > ul {
      margin-top: 20px;
    }

    .box-title {
      position: absolute;
      top: -2px;
      left: -2px;
      display: inline-block;
      height: 25px;
      padding: 3px 10px;
      font-size: 17px;
      font-weight: bold;
      line-height: 25px;
      color: $color--white;
      vertical-align: middle;
      background: $color--text--strong;
    }

    p {
      padding: 0;
      margin: 0;
    }
  }

  iframe {
    width: 100%;
  }
}

.blog-widget {
  display: flex;
  justify-content: space-around;

  &__ele {
    margin: 0 10px;

    a {
      position: relative;
      z-index: 2;
    }
  }
}

.entry__list {
  margin-top: 45px;

  > li {
    padding: 30px 0;
    border-top: 1px solid #808080;

    &:last-of-type {
      border-bottom: 1px solid #808080;
    }
  }

  a {
    display: block;

    @include flex(initial, stretch, wrap);

    &:hover {
      .entry__eyecatch .img {
        transform: scale(1.1);
      }
    }

    .left {
      width: 33%;
    }

    .right {
      width: 63%;
      margin-left: auto;
    }
  }
}

.entry__eyecatch {
  position: relative;
  padding-top: 56%;

  // width: 33%;
  overflow: hidden;
  border-radius: 6px;

  .img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    transition: transform 0.5s;
    transform: scale(1);
  }
}

.entry__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  h3 {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    line-height: 32px;
  }

  time {
    display: block;
    font-size: 14px;
    font-style: normal;
    color: $color--text;
  }

  &--update {
    margin-right: 12px;
  }
}

.category_label.sp {
  display: none;
}

// ==============================================
// casestudy
// ==============================================
.archive {
  .casestudy {
    &.content__main {
      padding-top: 35px;

      @media (width <= 767px) {
        padding-top: 27px;
      }
    }

    .archive__wp {
      p.center {
        margin-bottom: 44px;
        line-height: 32px;

        @media (width >= 768px) {
          text-align: center;
        }

        @media (width <= 767px) {
          margin-bottom: 31px;
        }
      }

      &__tab {
        @media (width >= 768px) {
          margin-bottom: 48px;
        }

        ul li {
          &.is-active {
            border-color: #3c8b86;

            @media (width >= 768px) {
              background-color: #3c8b86;
            }
          }

          @media (width <= 767px) {
            &.tab {
              color: #3c8b86;
              border-color: #3c8b86;

              .tab_toggle {
                background-image: url('../images/icon/round-arrow_green.svg');
              }
            }
          }
        }
      }

      &__cont__title {
        color: #3c8b86;

        @media (width >= 768px) {
          margin-bottom: 38px;
          font-size: 32px;
        }
      }
    }

    &__lists {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      grid-template-columns: repeat(auto-fill, minmax(min(250px, 100%), 1fr));
      grid-gap: 32px 28px;
      justify-content: center;
      width: 100%;

      @media (width <= 768px) {
        grid-gap: 24px 15px;
      }

      &__item {
        width: 100%;
        padding-bottom: 20px;
        overflow: hidden;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgb(0 0 0 / 29%);

        &__link {
          display: grid;
          grid-template-columns: 20px 1fr 20px;

          > *:not(img) {
            grid-column: 2;
          }

          &__img {
            grid-column: 1/4;
            width: 100%;
            height: auto;
            margin-bottom: 16px;
          }

          h3 {
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: bold;
            line-height: 24px;
          }

          p {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 700;
            line-height: 1.3;
            color: #707070;
          }

          .casestudy__cat-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            &__label {
              justify-self: start;
              padding: 6px 8px;
              font-size: 12px;
              line-height: 1;
              color: #3c8b86;
              border: 1px solid #3c8b86;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
}

.single-casestudy .breadcrumb-inner {
  max-width: 756px;
  padding-block: 0 58px;
  margin-inline: auto;
}

.single__casestudy {
  width: 100%;
  max-width: 756px;
  margin-inline: auto;

  &__cont {
    h1 {
      position: relative;
      padding-top: 25px;
      margin-bottom: 32px;
      font-size: 32px;
      font-weight: bold;
      line-height: 50px;

      &::before {
        content: '';

        @include absolute(
          64px,
          5px,
          0,
          initial,
          initial,
          0,
          $color--accent--orange,
          translateY(-50%),
          1
        );

        border-radius: 20px;
      }

      @media (width <= 768px) {
        margin-bottom: 25px;
        font-size: 22px;
        line-height: 35px;
      }
    }

    &__cat {
      display: inline-block;
      padding: 6px 8px;
      margin-bottom: 40px;
      font-size: 12px;
      line-height: 1;
      color: #3c8b86;
      border: 1px solid #3c8b86;
      border-radius: 4px;

      @media (width <= 768px) {
        margin-bottom: 30px;
      }
    }

    &__img {
      display: block;
      width: 100%;
      height: auto;
      margin-bottom: 60px;

      @media (width <= 768px) {
        margin-bottom: 50px;
      }
    }
  }
}

// ==============================================
// webinar
// ==============================================
.archive {
  .webinar {
    &.content__main {
      padding-top: 35px;

      @media (width <= 767px) {
        padding-top: 27px;
      }
    }

    .archive__wp {
      p {
        margin-bottom: 44px;
        line-height: 32px;

        @media (width <= 767px) {
          margin-bottom: 31px;
        }
      }
    }

    &__lists {
      padding-block: 30px;
      border: 0 solid #dcdcdc;
      border-top-width: 1px;

      @media (width >= 769px) {
        padding-inline: 40px;
      }

      &:last-of-type {
        border-bottom-width: 1px;
      }

      &__link {
        display: grid;
        grid-gap: 32px;
        justify-content: start;

        @media (width >= 769px) {
          grid-template-columns: 500px 1fr;
        }

        @media (width <= 768px) {
          grid-gap: 25px;
        }

        &:hover {
          opacity: 0.8;
        }

        &__img {
          width: 100%;
          height: auto;
        }

        &__dtl {
          display: grid;
          place-content: start;
          place-items: start;

          &__status {
            display: grid;
            place-items: center;
            height: 28px;
            padding-inline: 10px;
            margin-bottom: 10px;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            border-radius: 4px;

            &.open {
              background-color: #3c8b86;
            }

            &.close {
              background-color: #707070;
            }
          }

          h3 {
            margin-bottom: 16px;
            font-size: 20px;
            font-weight: bold;
            line-height: 1.5;
          }

          .date,
          .venue {
            display: grid;
            grid-auto-flow: column;
            grid-gap: 16px;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
            color: #191919;

            &::before {
              width: 24px;
              height: 24px;
              content: '';
              background: 50% 50% / contain no-repeat;
            }
          }

          .date {
            margin-bottom: 8px;

            &::before {
              background-image: url('../images/icon/webinar-time.svg');
            }
          }

          .venue {
            margin-bottom: 16px;

            &::before {
              background-image: url('../images/icon/webinar-map.svg');
            }
          }

          .category {
            display: grid;
            place-items: center;
            height: 24px;
            padding-inline: 8px;
            font-size: 12px;
            font-weight: 600;
            line-height: 1;
            color: #3c8b86;
            border: 1px solid #3c8b86;
            border-radius: 4px;
          }
        }

        p {
          margin-bottom: 10px;
          font-size: 14px;
          font-weight: 700;
          line-height: 1.3;
          color: #707070;
        }

        &__cat {
          justify-self: start;
          padding: 6px 8px;
          font-size: 12px;
          line-height: 1;
          color: #3c8b86;
          border: 1px solid #3c8b86;
          border-radius: 4px;
        }
      }
    }
  }
}
