@import './variable';
@import './_sp';

@media screen and (max-width: 1200px) {
    .container {
        padding-right: 20px;
        padding-left: 20px;
    }
    .header {
        &__right {
            nav {
                display: none;
                padding: 60px 100px;
                background-color: $color--white;
                position: fixed;
                top: 80px;
                right: 0;
                bottom: 0;
                left: 0;
                overflow-y: scroll;
                ul {
                    display: block;
                    li {
                        margin-left: 0;
                        padding: 20px 0;
                        width: 100%;
                        height: initial;
                    }
                }
            }
        }
        &__service {
            display: block !important;
            & > a {
                display: none;
            }
            &__sp {
                display: flex;
            }
            &__modal {
                margin-top: 20px;
                padding: 0;
                top: initial;
                background: none;
                position: initial;
                .container ul {
                    justify-content: flex-start;
                    li {
                        padding: 5px 0;
                        width: 25%;
                        &:before {
                            display: none;
                        }
                    }
                }
            }
        }
        &__recruit {
            display: block !important;
        }
        &__sp__bottom {
            margin-top: 20px;
            width: initial !important;
            display: block !important;
            p {
                margin-top: 30px;
                color: $color--text--sub;
            }
        }
    }
    .spMenu {
        display: block;
    }
    .footer {
        &__left {
            margin-right: 30px;
            width: 300px;
        }
        &__right {
            width: calc(100% - 330px);
        }
    }
    .section {
        &__intro {
            &__left {
                margin-right: 50px;
            }
            &__right {
                width: calc(100% - 650px);
            }
        }
        &__concerns {
            &__top {
                ul {
                    li {
                        width: 350px;
                    }
                }
            }
        }
        &__system {
            &__left {
                margin-right: 50px;
                width: 400px;
            }
            &__right {
                width: calc(100% - 450px);
            }
        }
        &__compare {
            &__item {
                &:first-child {
                    left: -20px;
                    &:before {
                        left: 0;
                    }
                    &:after {
                        right: -400px;
                        font-size: 100px;
                    }
                }
                &:last-child {
                    right: -20px;
                    &::before {
                        right: 0;
                    }
                    &:after {
                        left: -330px;
                        font-size: 100px;
                    }
                }
            }
        }
        &__about {
            &__cont a {
                width: calc(50% - 15px);
                &:first-child {
                    margin-right: 30px;
                }
            }
        }
        &__contact {
            &__cont a {
                width: calc(50% - 15px);
                &:first-child {
                    margin-right: 30px;
                }
            }
            &__logo {
                width: 100%;
            }
        }
    }
    .solution__title {
        padding-right: 0;
        padding-left: 0;
        width: 100%;
    }
    .footer__link__main {
        a {
            font-size: 15px !important;
        }
    }
    .content-marketing {
        .section {
            &__plan {
                &__desc {
                    br {
                        display: none;
                    }
                }
                h4 span:nth-child(2) {
                    font-size: 30px;
                }
            }
        }
    }
    .saiyou-ownedmedia {
        .section {
            &__point__desc {
                width: 100%;
            }
            &__plan__warn span {
                display: inline-block;
            }
        }
    }
}

@media screen and (max-width: 979px) {
    .header {
        &__right {
            nav {
                padding: 60px 50px;
            }
        }
        &__service {
            &__modal .container ul li {
                width: 33.3333%;
            }
        }
    }
    nav {
        display: none;
    }
    .section {
        &__intro {
            padding-bottom: 120px;
            &__cont {
                display: block;
            }
            &__left {
                margin-right: 50px;
                width: 100%;
                h2 {
                    margin-bottom: 40px;
                }
            }
            &__right {
                margin: 50px auto 0;
                width: 70%;
            }
        }
        &__concerns {
            &__top {
                margin-top: 0;
                margin-bottom: 30px;
                padding-top: 0;
                ul {
                    position: relative;
                    top: initial;
                    right: initial;
                    bottom: initial;
                    left: initial;
                    li {
                        margin-bottom: 20px;
                        padding: 25px 0;
                        width: 100%;
                        position: relative !important;
                        top: initial !important;
                        right: initial !important;
                        bottom: initial !important;
                        left: initial !important;
                        transform: initial !important;
                        border-radius: 10px;
                        &:last-child {
                            margin-bottom: 25px;
                        }
                    }
                }
            }
        }
        &__system {
            background-image: none;
            position: relative;
            overflow: hidden;
            &__bg__top,
            &__bg__bottom {
                display: block;
            }
            &__cont {
                flex-direction: column-reverse;
            }
            &__left {
                margin-right: 0;
            }
            &__right {
                margin-bottom: 40px;
                width: 100%;
            }
        }
        &__feature {
            &__cont {
                ul li {
                    margin-right: 0;
                    width: 100%;
                    &:nth-child(5) {
                        margin-bottom: 60px;
                    }
                }
            }
        }
        &__flow {
            padding-top: 120px;
        }
        &__compare {
            &__item {
                width: 90%;
                &::after {
                    display: none;
                }
            }
        }
        &__point {
            &__item {
                &.full__bg {
                    .section__point__img ul li img {
                        width: 140px;
                    }
                }
                &__inner {
                    display: block;
                    .section__point__img {
                        margin: 0 auto;
                        width: 250px;
                    }
                }
            }
            &__left {
                width: 100%;
            }
        }
        &__plan {
            padding-top: 120px;
            &__item {
                margin-right: 0;
                margin-bottom: 80px !important;
                width: 100%;
                &:last-child {
                    margin-bottom: 0 !important;
                }
            }
        }
        &__case {
            &__cont {
                ul li {
                    width: calc(50% - 15px);
                    &:nth-child(3n) {
                        margin-right: 30px;
                    }
                    &:nth-child(2n) {
                        margin-right: 0;
                    }
                }
            }
        }
        &__about {

        }
        &__contact {
            &__cont {
                display: block;
                a {
                    width: 100%;
                    &:first-child {
                        margin-right: 0;
                        margin-bottom: 25px;
                    }
                }
            }
        }
    }
    .solution__title h2 {
        & > span {
            font-size: 36px;
            span {
                font-size: 46px;
            }
        }
        img {
            width: 330px;
        }
    }
    .footer {
        .container {
            padding-top: 50px;
            padding-bottom: 50px;
            display: block;
        }
        &__left {
            margin: 0 auto;
            text-align: center;
            &__top {
                margin-bottom: 0;
            }
            &__bottom {
                display: none;
            }
        }
    }
    .content__main {
        padding: 120px 0;
    }
    .page__main {
        padding: 80px 0 60px;
    }
    .page__default {
        padding: 40px 0 20px;
    }
    .content-marketing {
        .section {
            &__plan {
                &__desc {
                    text-align: center;
                }
            }
        }
    }
    .attribution {
        .section {
            &__plan {
                &__item__inner {
                    padding: 40px;
                    ul {
                        padding: 0 0 0 30px;
                        width: calc(100% - 280px);
                        li {
                            margin-bottom: 20px;
                            &:last-child {
                                margin-bottom: 0;
                            }
                            p {
                                font-size: 16px;
                            }
                        }
                    }
                }
                &__left {
                    padding-right: 30px;
                    width: 280px;
                    box-sizing: border-box;
                    img {
                        width: 100%;
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 767px) {

    // ----------- common -----------
    .container {
        padding-right: 15px;
        padding-left: 15px;
    }
    .btn {
        margin-top: 40px;
        a {
            padding-top: 20px;
            padding-bottom: 17px;
            width: 100% !important;
            span {
                font-size: 14px;
            }
            img {
                width: 15px;
            }
        }
        &.col2 a:first-child {
            margin-bottom: 32px;
        }
    }
    .pageLoader__logo img {
        width: 80px;
    }
    .header {
        height: 50px;
        &__left {
            a {
                h1 {
                    width: 160px;
                }
            }
        }
        &__right {
            nav {
                top: 50px;
                ul {
                    li {
                        margin-bottom: 30px;
                        padding: 0;
                        &:nth-child(5) {
                            margin-bottom: 60px;
                        }
                    }
                }
            }
        }
        &__service {
            &__sp {
                justify-content: space-between;
            }
            &__modal .container ul li {
                margin-bottom: 10px !important;
                width: 100%;
            }
        }
        &__contact {
            padding-top: 25px !important;
            border-top: 1px solid $color--text;
            a {
                width: 100%;
                text-align: center;
                font-size: 14px;
            }
        }
        &__sp__bottom {
            p {
                font-size: 12px;
            }
        }
    }
    .menu__trigger {
        width: initial;
        height: 50px;
        &.active {
            .hamburger span {
                &:nth-of-type(1) {
                    transform: translateY(9px) rotate(-45deg);
                }
                &:nth-of-type(3) {
                    transform: translateY(-7px) rotate(45deg);
                }
            }
        }
    }
    .hamburger {
        width: 28px;
        height: 18px;
        span:nth-of-type(2) {
            top: 8px;
        }
    }
    .section {
        &__title {
            &__left,
            &__centered {
                img {
                    width: 50px;
                }
                & > span {
                    margin-bottom: 12px;
                    padding-left: 22px;
                    font-size: 12px !important;
                    &:before {
                        width: 16px;
                    }
                }
                h3, h2 {
                    font-size: 22px !important;
                    line-height: 1.5;
                }
            }
        }
        &__more__btn {
            span {
                font-size: 15px;
            }
        }
        &__intro {
            padding-bottom: 60px;
            &__left {
                h2 {
                    margin-bottom: 30px;
                    font-size: 20px;
                }
                ul li {
                    margin-bottom: 20px;
                    p {
                        font-size: 16px;
                    }
                }
            }
        }
        &__concerns {
            padding-top: 40px;
            padding-bottom: 100px;
            &__cont {
                margin-top: 40px;
            }
            &__top {
                img {
                    width: 100px;
                }
            }
            &__bottom p {
                font-size: 16px;
            }
            &__item {
                margin-right: 0;
                margin-bottom: 30px;
                width: 100%;
                &:last-child {
                    margin-bottom: 0;
                }
                img {
                    margin-bottom: 30px;
                    width: 80px;
                }
                &__desc {
                    min-height: initial !important;
                    padding: 20px;
                    &:before {
                        height: 30px;
                        top: -15px;
                    }
                }
            }
            &__solution {
                padding-bottom: 60px !important;
                border-radius: 0 0 30px 30px;
                .section__title__left {
                    display: block;
                }
                h3 {
                    font-size: 20px;
                    span {
                        &:before {
                            height: 8px;
                        }
                    }
                }
                &__cont {
                    margin-top: 40px;
                }
                &__item {
                    margin-right: 0;
                    margin-bottom: 40px !important;
                    width: 100%;
                    &:last-child {
                        margin-bottom: 0;
                    }
                    &__icon {
                        width: 80px;
                        height: 80px;
                        &:before {
                            height: 25px;
                            bottom: -12px;
                        }
                        img {
                            height: 50px;
                        }
                    }
                    h3, h4 {
                        font-size: 18px;
                    }
                }
            }
            &__line {
                height: 50px;
                bottom: -25px;
            }
        }
        &__system {
            padding-top: 60px;
            padding-bottom: 60px;
            .section__title__centered {
                h2 {
                    text-align: center;
                    display: grid;
                }
            }
            &__bg__top {
                width: 120%;
                left: -80px;
            }
            &__cont {
                margin-top: 40px;
            }
            &__right {
                margin-bottom: 30px;
                ul li {
                    margin-bottom: 25px;
                    padding-right: 50px;
                    padding-left: 20px;
                    width: 100%;
                    border-radius: 10px;
                    & > span {
                        margin-right: 12px;
                        font-size: 32px;
                    }
                    p {
                        font-size: 16px;
                        text-align: center;
                    }
                }
            }
            &__left {
                width: 150px;
            }
        }
        &__feature {
            &__cont {
                margin-top: 40px;
                ul li {
                    margin-bottom: 25px !important;
                    &:last-child {
                        margin-bottom: 0 !important;
                    }
                }
            }
            &__num {
                padding: 15px 8px 5px;
                width: 40px;
                border-radius: 10px 0 10px 0;
                &::before {
                    height: calc(100% + 15px);
                }
                span {
                    font-size: 22px;
                }
            }
            &__top {
                h4 {
                    margin-right: 20px;
                    padding: 13px 13px 5px;
                    width: calc(100% - 40px);
                    font-size: 18px;
                }
            }
            &__bottom {
                padding: 20px;
            }
            &__icon {
                width: 80px;
                img {
                    width: 60px;
                }
            }
            &__desc {
                width: calc(100% - 80px);
                p {
                    font-size: 14px;
                }
            }
        }
        &__case {
            padding-top: 60px;
            padding-bottom: 60px;
            &::before {
                top: 400px;
            }
            &__cont {
                margin-top: 40px;
                ul li {
                    margin-right: 0 !important;
                    margin-bottom: 25px;
                    width: 100%;
                    border-radius: 10px;
                    &:last-child {
                        margin-bottom: 0 !important;
                    }
                div {
                    border-radius: 10px 10px 0 0;
                }
                    h3 {
                        margin-bottom: 15px;
                    }
                }
            }
        }
        &__compare {
            padding-top: 60px;
            padding-bottom: 0;
            &__cont {
                margin-top: 40px;
            }
            &__item {
                padding: 30px 0 !important;
                width: 100%;
                &::before {
                    left: -15px !important;
                    right: -15px !important;
                    border-radius: 0 !important;
                }
                &:first-child {
                    margin-bottom: 25px;
                    left: 0;
                }
                &:last-child {
                    right: 0;
                }
                .container {
                    padding-right: 0;
                    padding-left: 0;
                }
                ul {
                    margin-top: 30px;
                    li {
                        margin-bottom: 20px;
                        font-size: 16px;
                    }
                }
                img {
                    width: 110px;
                    top: 30px;
                    right: 15px;
                    transform: initial;
                }
            }
        }
        &__flow {
            padding-top: 60px;
            &__cont {
                margin-top: 40px;
            }
            &__item {
                &__left {
                    margin-right: 20px;
                    padding-top: 15px;
                    width: 50px;
                    &:before {
                        width: 2px;
                        top: 65px;
                    }
                    &:after {
                        border-top: 5px solid $color--main;
                        border-right: 4px solid transparent;
                        border-left: 4px solid transparent;
                    }
                }
                &__right {
                    padding: 15px 0;
                    width: calc(100% - 70px);
                    &__top {
                        margin-bottom: 17px;
                        h3 {
                            width: calc(100% - 70px);
                            font-size: 17px;
                            line-height: 1.6;
                        }
                    }
                    &__bottom p {
                        font-size: 14px;
                    }
                }
            }
            &__num {
                width: 50px;
                height: 50px;
                div {
                    span {
                        &:first-child {
                            margin-bottom: 2px;
                            font-size: 10px;
                        }
                        &:last-child {
                            font-size: 18px;
                        }
                    }
                }
            }
            &__icon {
                margin-right: 20px;
                width: 50px;
                height: 50px;
                img {
                    width: 30px;
                }
                &:before {
                    height: 16px;
                    bottom: -12px;
                }
            }
        }
        &__point {
            padding-top: 60px;
            &__cont {
                margin-top: 30px;
            }
            &__item {
                padding: 40px 0 30px;
                .section__title__left.ttl__sm h3 {
                    font-size: 18px !important;
                    &:before {
                        height: 8px;
                        bottom: 4px;
                    }
                }
                &.half__bg:before {
                    height: 80%;
                }
                &.full__bg .section__point__img ul li {
                    width: 50%;
                    &:nth-child(1),
                    &:nth-child(2) {
                        margin-bottom: 20px;
                    }
                    img {
                        margin-bottom: 15px;
                        width: 100px;
                    }
                    p {
                        font-size: 14px;
                    }
                }
            }
            &__desc {
                margin: 15px 0;
                width: 100%;
            }
        }
        &__voice {
            padding-top: 60px;
            &__cont {
                margin-top: 35px;
            }
            &__item {
                margin-bottom: 25px;
                padding: 30px;
                &__left {
                    margin-bottom: 20px;
                    width: 100%;
                    @include flex(center, center, initial);
                    img {
                        margin: 0 25px 0 0;
                        width: 60px;
                    }
                    p {
                        font-size: 16px;
                    }
                }
                &__right {
                    width: 100%;
                    h3 {
                        margin-bottom: 15px;
                        img {
                            margin-right: 10px;
                        }
                        & > span {
                            span {
                                font-size: 16px;
                                &::before {
                                    height: 6px;
                                    bottom: 3px;
                                }
                            }

                        }
                    }
                    p {
                        font-size: 14px;
                    }
                }
            }
        }
        &__case__slide {
            padding-top: 60px;
            &__cont {
                margin-top: 40px;
            }
            .slick-slide {
                img {
                    width: 100%;
                    height: 250px;
                }
            }
            .slick-dots {
                bottom: -40px;
                li {
                    margin: 0 10px;
                    button:before {
                        font-size: 12px;
                    }
                }
            }
        }
        &__plan {
            padding-top: 60px;
            &__cont {
                margin-top: 40px;
            }
            h3 {
                padding-top: 12px;
                padding-bottom: 12px;
                font-size: 18px !important;
                border-radius: 10px 10px 0 0;
            }
            h4 {
                padding-top: 12px;
                padding-bottom: 12px;
                border-radius: 0 0 10px 10px;
                span {
                    &:first-child {
                        font-size: 16px !important;
                    }
                    &:nth-child(2) {
                        font-size: 24px !important;
                    }
                }
            }
            &__item {
                margin-bottom: 40px !important;
                border-radius: 10px;
                &__inner {
                    padding: 30px 25px;
                    ul li {
                        margin-bottom: 20px;
                        img {
                            width: 20px;
                        }
                        p {
                            font-size: 16px;
                        }
                    }
                }
            }
            &__desc {
                padding-bottom: 20px;
                font-size: 16px;
            }
            &__warn {
                margin-top: 25px;
                text-align: left;
                font-size: 16px;
            }
        }
        &__faq {
            padding-top: 60px;
            &__cont {
                margin-top: 40px;
                ul li {
                    margin-bottom: 30px;
                    h3 {
                        margin-bottom: 15px;
                        span {
                            margin-right: 15px;
                            width: 40px;
                            height: 40px;
                            font-weight: normal;
                            font-size: 20px;
                        }
                        p {
                            width: calc(100% - 55px);
                            font-size: 16px;
                            br {
                                display: none;
                            }
                        }
                    }
                    p {
                        font-size: 14px;
                    }
                    a {
                        margin-top: 15px;
                        font-size: 15px;
                    }
                }
            }
        }
        &__about {
            padding-top: 60px;
            padding-bottom: 60px;
            &__title {
                h2 {
                    font-size: 22px;
                }
            }
            &__cont {
                margin-top: 30px;
                display: block;
                a {
                    padding: 20px 30px;
                    width: 100%;
                    display: block;
                    &:first-child {
                        margin-right: 0;
                        margin-bottom: 30px;
                    }
                    .section__title__left {
                        margin-bottom: 25px;
                    }
                }
            }
        }
        &__contact {
            padding-top: 60px;
            padding-bottom: 50px;
            &__title {
                span {
                    font-size: 12px;
                }
                h2 {
                    margin-top: 10px;
                    font-size: 22px;
                }
            }
            &__logo {
                margin-bottom: -3px;
                width: 70%;
            }
            &__cont {
                margin-top: 40px;
                padding-top: 30px;
                a {
                    padding: 15px 40px;
                    border-radius: 8px;
                    img {
                        width: 40px;
                    }
                    p {
                        &:first-child {
                            font-size: 24px;
                        }
                        &:last-child {
                            font-size: 12px;
                        }
                    }
                }
            }
            &__arrow {
                width: 20px !important;
            }
        }
    }
    .solution__title {
        top: -37px;
        h2 {
            & > span {
                font-size: 17px;
                span {
                    font-size: 22px;
                }
            }
            img {
                margin-right: 5px;
                width: 160px;
            }
        }
    }
    .footer {
        .container {
            padding-top: 35px;
            padding-bottom: 35px;
        }
        &__left {
            a {
                margin: 0 auto 20px;
                width: 200px;
                display: inline-block;
            }
            &__top {
                p {
                    font-size: 12px;
                }
            }
        }
    }
    .copyright {
        padding: 10px 0;
    }
    #topBtn {
        width: 40px;
        height: 40px;
        right: 15px;
        bottom: 30px;
    }
    .content__main {
        padding: 70px 0;
    }
    .page__main {
        padding: 40px 0;
    }
    .single-document,
    .single-blog,
    .search {
        .content__main {
            margin-top: 50px;
        }
        .breadcrumb-inner {
            padding-top: 25px;
            margin-bottom: 30px;
        }
    }

    // ----------- sub -----------
    .page {
        &__header {
            margin-top: 50px;
            padding-top: 30px;
            padding-bottom: 45px;
            &__title {
                & > span {
                    font-size: 12px;
                }
                h1 {
                    margin-top: 12px;
                    line-height: 1.4;
                }
            }
            &__main {
                font-size: 25px;
            }
            &__catch {
                font-size: 22px;
            }
        }

        &__scroll {
            height: 40px;
            bottom: -20px;
        }
        &__title {
            margin-bottom: 40px;
            span {
                font-size: 14px;
            }
            h1 {
                margin-top: 10px;
                font-size: 26px;
                &:before {
                    height: 8px;
                }
            }
        }
    }
    .content-marketing {
        .section {
            &__plan {
                &__cont {
                    margin-top: 80px;
                }
                &__item {
                    &:nth-child(1) {
                        &:before {
                            padding-top: 10px;
                            padding-bottom: 10px;
                            font-size: 16px;
                            top: -47px;
                            border-radius: 10px;
                        }
                    }
                }
            }
        }
    }
    .owned-media {
        .section {
            &__concerns {
                padding-bottom: 40px;
                &__solution__cont {
                    margin-bottom: 60px;
                }
            }
        }
    }
    .attribution {
        .section {
            &__plan {
                h3 > span {
                    &:first-child {
                        margin-right: 10px;
                        font-size: 18px;
                    }
                    &:last-child {
                        span {
                            font-size: 24px;
                        }
                    }
                }
                &__item__inner {
                    display: block;
                    padding: 25px 30px;
                    ul {
                        padding: 0;
                        width: 100%;
                        li {

                            p {
                                font-size: 16px;
                            }
                        }
                    }
                }
                &__left {
                    margin-bottom: 25px;
                    padding-right: 0;
                    padding-bottom: 20px;
                    width: 100%;
                    border-right: none;
                    border-bottom: 1px solid $color--text;
                    img {
                        margin: 0 auto;
                        width: 160px;
                    }
                }
            }
        }
    }
    .saiyou-ownedmedia {
        .section {
            &__point {
                &__item {
                    &:first-child {
                        margin-bottom: 25px;
                    }
                }
            }
        }
    }
    .breadcrumb-inner {
        padding-top: 35px;
    }
    // ==============================================
// wpcf7
// ==============================================
.entry {
    .wpcf7 {
        input[type="text"],
        input[type="email"] {
            height: 40px;
        }
        textarea {
            min-height: 100px;
        }
        select {
            width: 100%;
            height: 40px;
            font-size: 14px;
        }
        input[type="submit"] {
            width: 100%;
        }
    }
}
}