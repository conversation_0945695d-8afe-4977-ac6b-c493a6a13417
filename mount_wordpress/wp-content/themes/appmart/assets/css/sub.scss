@import './variable';
@import './_service';
@import './_sub';

// ==============================================
// page
// ==============================================
.page {
  &__header {
    margin-top: 80px;
    padding: 75px 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    z-index: 1;
    &:before {
      content: '';
      @include absolute(
        100%,
        100%,
        0,
        0,
        0,
        0,
        rgba(51, 51, 51, 0.5),
        initial,
        0
      );
    }

    &__title {
      position: relative;
      text-align: center;
      & > span {
        color: $color--white;
        font-size: 20px;
        text-transform: uppercase;
      }
      h1,
      h2 {
        margin-top: 25px;
        font-weight: bold;
        color: $color--white;
        line-height: 1.7;
        span {
          display: inline-block;
        }
      }
    }
    &__main {
      font-size: 50px;
    }
    &__catch {
      font-size: 40px;
    }
  }

  &__scroll {
    @include absolute(
      1px,
      80px,
      initial,
      initial,
      -40px,
      50%,
      linear-gradient(
        to bottom,
        $color--white 0%,
        $color--white 50%,
        $color--text 50%,
        $color--text 100%
      ),
      initial,
      1
    );
  }

  &__title {
    margin-bottom: 80px;
    text-align: center;
    span {
      display: block;
      font-size: 20px;
      text-transform: uppercase;
    }
    h1 {
      margin-top: 25px;
      font-size: 50px;
      line-height: 1.2;
      font-weight: bold;
      display: inline-block;
      position: relative;
      z-index: 2;
      &:before {
        content: '';
        @include absolute(
          100%,
          15px,
          initial,
          0,
          4px,
          0,
          $color--accent--yellow,
          initial,
          -1
        );
      }
    }
  }
}
.movie {
  .section {
    &__concerns__item__desc,
    &__flow__item__right__bottom {
      min-height: initial;
    }
    &__plan__item:nth-child(2) h3,
    &__plan__item:nth-child(3) h3 {
      font-size: 24px;
    }
  }
}
.owned-media {
  .section {
    &__concerns {
      padding-bottom: 90px;
    }
    &__concerns__solution {
      padding-bottom: 80px;
      .section__title__left {
        display: block;
      }
      &__cont {
        margin-bottom: 150px;
      }
    }
  }
}
.content-marketing {
  .section {
    &__concerns__item__desc {
      min-height: 120px;
    }
    &__plan {
      &__cont {
        margin-top: 150px;
      }
      &__item {
        margin-bottom: 60px;
        &:nth-child(1) {
          position: relative;
          &:before {
            content: '検索上位実績多数！';
            padding: 15px 0;
            @include absolute(
              100%,
              initial,
              -61px,
              0,
              initial,
              0,
              $color--accent--orange,
              initial,
              1
            );
            border-radius: 20px;
            font-size: 20px;
            font-weight: bold;
            color: $color--white;
            text-align: center;
          }
          &:after {
            content: '';
            @include absolute(
              0,
              0,
              -11px,
              initial,
              initial,
              50%,
              initial,
              translateX(-50%),
              1
            );
            border-top: 20px solid $color--accent--orange;
            border-right: 12px solid transparent;
            border-left: 12px solid transparent;
          }
        }
        &:nth-child(3n) {
          margin-right: 0;
        }
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(6) {
          margin-bottom: 0;
        }
      }
    }
  }
}
.attribution {
  .section {
    &__concerns {
      padding-bottom: 80px;
      &__item__desc {
        min-height: 120px;
      }
    }
    &__plan {
      &__item {
        width: 100%;
        margin-right: 0;
        &__inner {
          padding: 40px 90px;
          @include flex(initial, stretch, initial);
          ul {
            padding: 30px 0 30px 65px;
            width: calc(100% - 380px);
          }
        }
      }
      h3 {
        background-color: $color--text--strong;
        & > span {
          &:first-child {
            margin-right: 30px;
            font-size: 28px;
          }
          &:last-child {
            font-size: 24px;
            span {
              font-size: 40px;
            }
          }
        }
      }
      &__left {
        width: 380px;
        border-right: 1px solid $color--text;
        @include flex(flex-start, center, initial);
        img {
          width: 320px;
          height: auto;
        }
      }
    }
  }
}
.saiyou-ownedmedia {
  .section {
    &__concerns {
      &__item__desc {
        min-height: initial;
      }
      &__solution__item {
        &:nth-child(3n) {
          margin-right: 0;
        }
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          margin-bottom: 60px;
        }
      }
    }
    &__point__item:first-child {
      margin-bottom: 60px;
    }
  }
}
.contact {
  &__form {
    margin-top: 60px;
  }
}
.post-type-archive-document {
  .btn {
    margin-top: 40px;
    height: 48px;
  }
}
.breadcrumb-inner {
  padding-top: 25px;
  font-size: 12px;
  color: $color--text;
  span {
    font-size: 12px;
    line-height: 20px;
    color: $color--text;
  }
  a {
    text-decoration: underline;
    text-decoration-color: $color--text;
    &:visited {
      color: $color--text;
      opacity: 1;
    }
  }
}

// ==============================================
// page section
// ==============================================
.section {
  // ------------- section__intro -------------
  &__intro {
    padding-bottom: 150px;

    &__cont {
      @include flex(space-between, center, initial);
    }

    &__left {
      width: 600px;
      h2 {
        margin-bottom: 80px;
        font-size: 30px;
        font-weight: bold;
        line-height: 1.6;
      }
      ul li {
        margin-bottom: 25px;
        @include flex(flex-start, center, initial);
        img {
          margin-right: 15px;
          width: 25px;
          height: auto;
        }
        p {
          font-size: 18px;
        }
      }
    }

    &__desc p {
      margin-bottom: 20px;
      text-align: justify;
    }

    &__right {
      width: 430px;
      img {
        width: 100%;
        height: auto;
      }
    }
  }

  // ------------- section__concerns -------------
  &__concerns {
    padding-top: 80px;
    padding-bottom: 210px;
    background-color: $color--base--blue;
    position: relative;

    &__cont {
      margin-top: 80px;
      @include flex(initial, initial, wrap);
    }

    &__item {
      margin-right: 30px;
      width: calc(33.3333% - 20px);
      &:last-child {
        margin-right: 0;
      }
      img {
        margin: 0 auto 75px;
        width: 140px;
        height: auto;
      }

      &__desc {
        min-height: 150px;
        padding: 30px;
        border-radius: 20px;
        box-sizing: border-box;
        background-color: $color--white;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
        position: relative;
        &:before {
          content: '';
          @include absolute(
            1px,
            80px,
            -60px,
            initial,
            initial,
            50%,
            $color--text,
            translateX(-50%),
            1
          );
        }
      }
    }

    &__top {
      margin-bottom: 85px;
      padding-top: 150px;
      width: 100%;
      position: relative;

      ul {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        li {
          padding: 35px 0;
          width: 420px;
          box-sizing: border-box;
          color: #333333;
          text-align: center;
          font-size: 16px;
          background-color: #ffffff;
          border-radius: 20px 20px 0 20px;
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
          position: absolute;
          &:before {
            content: '';
            position: absolute;
            right: 0;
            bottom: -16px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 16px 16px 0;
            border-color: transparent #ffffff transparent transparent;
          }
          &:nth-child(1) {
            top: 0;
            left: 50%;
            transform: translateX(-50%);
          }
          &:nth-child(2) {
            top: 150px;
            left: 0;
          }
          &:nth-child(3) {
            top: 150px;
            right: 0;
          }
          &:nth-child(4) {
            top: 276px;
            left: 0;
          }
          &:nth-child(5) {
            top: 276px;
            right: 0;
          }
        }
      }
      img {
        margin: 0 auto;
        width: 200px;
        height: auto;
      }
    }

    &__bottom {
      p {
        margin-bottom: 20px;
        font-size: 18px;
        span {
          font-weight: bold;
        }
      }
    }

    &__solution {
      padding-bottom: 80px;
      position: relative;
      background-color: $color--base--green;
      border-radius: 0 0 60px 60px;
      .section__title__left {
        display: none;
      }
      h3 {
        margin-top: 20px;
        text-align: center;
        font-size: 32px;
        font-weight: bold;
        span {
          display: inline-block;
          position: relative;
          z-index: 2;
          line-height: 1.8;
          &:before {
            content: '';
            @include absolute(
              100%,
              15px,
              initial,
              0,
              7px,
              0,
              $color--accent--yellow,
              initial,
              -1
            );
          }
        }
      }

      &__cont {
        margin-top: 80px;
        @include flex(initial, initial, wrap);
      }

      &__item {
        margin-right: 30px;
        width: calc(33.3333% - 20px);
        &:last-child {
          margin-right: 0;
        }
        h4,
        h3 {
          font-weight: bold;
          font-size: 20px;
          text-align: center;
        }

        &__icon {
          margin: 0 auto 30px;
          width: 125px;
          height: 125px;
          border-radius: 50%;
          background-color: $color--white;
          @include flex(center, center, initial);
          position: relative;
          &:before {
            content: '';
            @include absolute(
              1px,
              40px,
              initial,
              initial,
              -20px,
              50%,
              $color--text,
              translateX(-50%),
              1
            );
          }
          img {
            width: auto;
            height: 70px;
          }
        }

        &__desc {
          margin-top: 20px;
        }
      }
    }

    &__line {
      @include absolute(
        1px,
        120px,
        initial,
        initial,
        -60px,
        50%,
        $color--text,
        translateX(-50%),
        1
      );
    }
  }

  // ------------- section__system -------------
  &__system {
    padding-top: 150px;
    padding-bottom: 210px;
    background-image: url(../images/bg/shikumi-bg.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;

    &__bg__top {
      display: none;
      @include absolute(
        100%,
        auto,
        15px,
        0,
        initial,
        -40px,
        initial,
        initial,
        -1
      );
    }

    &__bg__bottom {
      display: none;
      @include absolute(100%, auto, initial, 0, 35px, 0, initial, initial, -1);
    }

    &__cont {
      margin-top: 80px;
      @include flex(initial, center, wrap);
    }

    &__left {
      margin-right: 100px;
      width: 500px;
      img {
        width: 100%;
        height: auto;
      }
    }

    &__right {
      width: calc(100% - 600px);
      ul li {
        margin-bottom: 30px;
        padding: 16px 30px;
        background-color: $color--base--green;
        border-radius: 20px;
        box-sizing: border-box;
        @include flex(initial, center, initial);
        &:last-child {
          margin-bottom: 0;
        }
        & > span {
          margin-right: 30px;
          color: $color--white;
          font-size: 44px;
          font-weight: bold;
        }
        p {
          font-size: 20px;
          font-weight: bold;
          span {
            display: inline-block;
          }
        }
      }
    }
  }

  // ------------- section__compare -------------
  &__compare {
    padding-top: 150px;
    padding-bottom: 50px;
    overflow: hidden;

    &__cont {
      margin-top: 80px;
    }

    &__item {
      padding-top: 50px;
      padding-bottom: 50px;
      width: 70%;
      box-sizing: border-box;
      position: relative;
      &:before {
        content: '';
        @include absolute(
          initial,
          initial,
          0,
          0,
          0,
          0,
          $color--base--blue,
          initial,
          -1
        );
      }
      &:after {
        color: $color--base--blue;
        font-size: 180px;
        text-transform: uppercase;
        font-weight: bold;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: -1;
      }
      &:first-child {
        margin-bottom: 90px;
        padding-right: 50px;
        &:before {
          left: calc(((100vw - 1080px) / 2) * -1);
          border-radius: 0 20px 20px 0;
        }
        &:after {
          content: 'before';
          right: -740px;
        }
      }
      &:last-child {
        margin-left: auto;
        padding-left: 50px;
        &:before {
          right: calc(((100vw - 1080px) / 2) * -1);
          border-radius: 20px 0 0 20px;
        }
        &:after {
          content: 'after';
          left: -600px;
        }
      }

      .section__title__left {
        & > span {
          color: $color--text--strong;
        }
        h3 {
          color: $color--text;
        }
      }

      ul {
        margin-top: 50px;
        li {
          margin-bottom: 25px;
          color: $color--text;
          font-size: 18px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      img {
        @include absolute(
          230px,
          auto,
          50%,
          40px,
          initial,
          initial,
          none,
          translateY(-50%),
          1
        );
      }
    }
  }

  // ------------- section__feature -------------
  &__feature {
    padding-bottom: 150px;
    background-color: $color--base--green;

    &__cont {
      margin-top: 80px;
      ul {
        @include flex(flex-start, stretch, wrap);
        li {
          margin-right: 60px;
          margin-bottom: 60px;
          width: calc(50% - 30px);
          border-radius: 20px;
          background-color: $color--white;
          &:nth-child(2n) {
            margin-right: 0;
          }
          &:nth-child(5),
          &:nth-child(6) {
            margin-bottom: 0;
          }
        }
      }
    }

    &__top {
      @include flex(flex-start, stretch, initial);
      h4 {
        margin-right: 25px;
        width: calc(100% - 80px);
        padding: 15px 25px 10px;
        font-size: 28px;
        font-weight: bold;
        color: $color--text--strong;
        border-bottom: 1px solid $color--text;
        line-height: initial;
      }
    }

    &__num {
      padding: 17px 15px 10px;
      width: 80px;
      box-sizing: border-box;
      position: relative;
      &:before {
        content: '';
        @include absolute(
          100%,
          calc(100% + 20px),
          0,
          0,
          0,
          0,
          $color--text,
          initial,
          1
        );
        border-radius: 20px 0 20px 0;
      }
      span {
        color: $color--white;
        font-size: 40px;
        font-weight: bold;
        position: relative;
        z-index: 1;
      }
    }

    &__bottom {
      padding: 40px;
      @include flex(flex-start, flex-start, initial);
    }

    &__icon {
      width: 180px;
      img {
        width: 120px;
        height: auto;
      }
    }
    &__desc {
      width: calc(100% - 180px);
      p {
        font-size: 18px;
      }
    }
  }

  // ------------- section__flow -------------
  &__flow {
    padding-top: 150px;

    &__cont {
      margin-top: 80px;
    }

    &__item {
      @include flex(initial, stretch, initial);
      &:first-child {
        .section__flow__item__right {
          border-top: 1px solid #e0e0e0;
        }
      }
      &:last-child {
        .section__flow__item__left {
          &:before,
          &:after {
            display: none;
          }
        }
      }

      &__left {
        margin-right: 65px;
        padding-top: 30px;
        width: 110px;
        position: relative;
        &:before {
          content: '';
          @include absolute(
            3px,
            initial,
            140px,
            initial,
            2px,
            50%,
            $color--main,
            translateX(-50%),
            1
          );
        }
        &:after {
          content: '';
          @include absolute(
            0,
            0,
            initial,
            initial,
            0,
            50%,
            none,
            translateX(-50%),
            1
          );
          border-top: 10px solid $color--main;
          border-right: 7.5px solid transparent;
          border-left: 7.5px solid transparent;
        }
      }

      &__right {
        padding: 30px 0;
        width: calc(100% - 175px);
        border-bottom: 1px solid #e0e0e0;

        &__top {
          margin-bottom: 40px;
          @include flex(flex-start, center, initial);
          h3 {
            width: calc(100% - 150px);
            font-size: 24px;
            font-weight: bold;
          }
        }

        &__bottom {
          min-height: 75px;
        }
      }
    }

    &__num {
      width: 110px;
      height: 110px;
      background-color: $color--main;
      border-radius: 50%;
      @include flex(center, center, initial);
      div {
        span {
          display: block;
          text-align: center;
          font-weight: bold;
          color: $color--white;
          &:first-child {
            margin-bottom: 10px;
            font-size: 18px;
            text-transform: uppercase;
          }
          &:last-child {
            font-size: 32px;
          }
        }
      }
    }

    &__icon {
      margin-right: 40px;
      width: 110px;
      height: 110px;
      background-color: #efefef;
      border-radius: 50%;
      @include flex(center, center, initial);
      position: relative;
      &:before {
        content: '';
        @include absolute(
          1px,
          32px,
          initial,
          initial,
          -25px,
          50%,
          $color--text,
          translateX(-50%),
          1
        );
      }
      img {
        width: 65px;
        height: auto;
      }
    }
  }

  // ------------- section__voice -------------
  &__voice {
    padding-top: 80px;

    &__cont {
      margin-top: 80px;
    }

    &__item {
      margin-bottom: 55px;
      padding: 50px 40px;
      background-color: $color--base--blue;
      border-radius: 20px;
      position: relative;
      z-index: -2;
      @include flex(initial, flex-start, wrap);
      &:last-child {
        margin-bottom: 0;
      }

      &__left {
        margin-right: 25px;
        width: 200px;
        text-align: center;
        img {
          margin: 0 auto 20px;
          width: 140px;
          height: auto;
        }
        p {
          font-size: 18px;
          font-weight: bold;
        }
      }
      &__right {
        width: calc(100% - 225px);
        h3 {
          margin-bottom: 20px;
          @include flex(initial, center, initial);
          img {
            margin-right: 20px;
            width: 30px;
          }
          & > span {
            display: flex;
            flex-wrap: wrap;
            span {
              font-size: 24px;
              font-weight: bold;
              display: inline-block;
              position: relative;
              &:before {
                content: '';
                @include absolute(
                  100%,
                  12px,
                  initial,
                  0,
                  -1px,
                  0,
                  $color--accent--yellow,
                  initial,
                  -1
                );
              }
            }
          }
        }
      }
    }
  }

  // ------------- section__point -------------
  &__point {
    padding-top: 100px;

    &__cont {
      margin-top: 80px;
    }

    &__item {
      padding: 55px 0;
      position: relative;
      z-index: -1;

      .section__title__left.ttl__sm {
        span {
          display: block;
        }
        h3 {
          font-size: 24px;
          color: $color--text;
          display: inline-block;
          position: relative;
          &:before {
            content: '';
            @include absolute(
              100%,
              12px,
              initial,
              0,
              0,
              0,
              $color--accent--yellow,
              initial,
              -1
            );
          }
        }
      }

      &.half__bg {
        &:before {
          content: '';
          @include absolute(
            100%,
            70%,
            0,
            0,
            initial,
            0,
            $color--base--blue,
            initial,
            -1
          );
        }
      }

      &.full__bg {
        background-color: $color--base--blue;
        .section__point__img {
          ul {
            @include flex(center, flex-start, wrap);
            li {
              width: 25%;
              img {
                margin: 0 auto 25px;
                width: 200px;
                height: auto;
              }
              p {
                font-size: 20px;
                text-align: center;
              }
            }
          }
        }
      }

      &__inner {
        @include flex(initial, center, wrap);
        .section__point__img {
          margin-left: 160px;
          width: 300px;
        }
      }
    }

    &__left {
      width: calc(100% - 460px);
    }

    &__desc {
      margin-top: 30px;
      margin-bottom: 45px;
      width: 740px;
    }

    &__img {
      img {
        width: 100%;
        height: auto;
      }
    }
  }

  // ------------- section__plan -------------
  &__plan {
    padding-top: 150px;
    padding-bottom: 80px;
    position: relative;
    &:before {
      content: '';
      @include absolute(
        100%,
        50%,
        initial,
        0,
        0,
        0,
        $color--base--blue,
        initial,
        -1
      );
    }

    &__cont {
      margin-top: 80px;
      @include flex(center, stretch, wrap);
    }

    &__item {
      margin-right: 30px;
      width: calc(33.3333% - 20px);
      border-radius: 20px;
      box-shadow: 0 6px 8px rgba(0, 0, 0, 0.16);
      background-color: $color--white;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;
      &:last-child {
        margin-right: 0;
      }

      &__inner {
        padding: 45px 25px;
        ul li {
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          &:last-child {
            margin-bottom: 0;
          }
          img {
            margin-right: 10px;
            width: 25px;
            height: auto;
          }
          p {
            font-size: 16px;
            span {
              font-weight: bold;
            }
          }
        }
      }

      &__top {
        width: 100%;
      }
    }

    h3 {
      padding-top: 20px;
      padding-bottom: 20px;
      border-radius: 20px 20px 0 0;
      background-color: $color--text;
      font-size: 28px;
      color: $color--white;
      font-weight: bold;
      text-align: center;
      span {
        display: inline-block;
      }
    }

    &__desc {
      margin-bottom: 30px;
      padding-bottom: 30px;
      border-bottom: 1px solid $color--text;
      font-size: 18px;
      font-weight: bold;
      color: $color--accent--orange;
      span {
        display: inline-block;
      }
      &.center {
        text-align: center;
      }
    }

    h4 {
      padding-top: 20px;
      padding-bottom: 20px;
      width: 100%;
      border-radius: 0 0 20px 20px;
      color: #191919;
      border-top: 1px solid #808080;
      font-weight: bold;
      text-align: center;
      span {
        &:first-child {
          font-size: 24px;
        }
        &:nth-child(2) {
          font-size: 32px;
          text-transform: uppercase;
          span {
            font-size: 28px;
          }
        }
        &:nth-child(3) {
          font-size: 18px;
        }
      }
    }

    &__warn {
      margin-top: 40px;
      font-size: 18px;
      text-align: right;
    }
  }

  // ------------- section__case -------------
  &__case {
    padding-top: 80px;
    padding-bottom: 80px;
    position: relative;
    &:before {
      content: '';
      @include absolute(
        100%,
        initial,
        500px,
        0,
        0,
        0,
        $color--base--green,
        initial,
        -1
      );
    }

    &__cont {
      margin-top: 80px;
      position: relative;
    }
  }

  // ------------- section__faq -------------
  &__case__slide {
    padding-top: 80px;
    &__cont {
      margin-top: 80px;
    }
    // .movieslider {
    //     &__item {
    //         position: relative;
    //         &__box {
    //             background: $color--black;
    //             img {
    //                 transition: all .3s;
    //                 opacity: .7;
    //             }
    //         }
    //     }
    //     a {
    //         &:hover {
    //             img {
    //                 opacity: .9;
    //             }
    //         }
    //     }
    //     .play {
    //         position: absolute;
    //         top: 50%;
    //         left: 50%;
    //         transform: translate(-50%, -50%);
    //         -webkit-transform: translate(-50%, -50%);
    //         -ms-transform: translate(-50%, -50%);
    //         img {
    //             width: 60px;
    //             height: 100%;
    //             opacity: 1;
    //         }
    //     }
    // }
    .movieslider__item__box {
      position: relative;
      background: #000;
    }
    .movieslider__item__box:hover {
      cursor: pointer;
      cursor: hand;
    }

    .movieslider__item__box img {
      opacity: 0.6;
    }

    .movieslider__item__box:hover img {
      opacity: 0.8;
      transition: filter 1s cubic-bezier(0, 2.5, 0.2, 2.5);
    }

    .play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      img {
        width: 60px !important;
        height: 100%;
        opacity: 1;
      }
    }
    .slick-dots {
      bottom: -50px;
      z-index: 1;
      li {
        margin: 0 20px;
        button:before {
          font-size: 16px;
          color: $color--base--blue;
          opacity: 1;
        }
        &.slick-active {
          button:before {
            opacity: 1;
            color: $color--text--sub;
          }
        }
      }
    }
  }

  // ------------- section__faq -------------
  &__faq {
    padding-top: 150px;

    &__cont {
      margin-top: 80px;
      ul li {
        margin-bottom: 50px;
        &:last-child {
          margin-bottom: 0;
        }
        h3 {
          margin-bottom: 20px;
          @include flex(initial, center, initial);
          span {
            margin-right: 20px;
            width: 80px;
            height: 80px;
            background-color: $color--text;
            border-radius: 50%;
            text-transform: uppercase;
            color: $color--white;
            font-size: 30px;
            @include flex(center, center, initial);
          }
          p {
            width: calc(100% - 100px);
            font-size: 24px;
            font-weight: bold;
          }
        }
        p {
          font-size: 18px;
          span {
            font-weight: bold;
          }
        }
        a {
          margin-top: 25px;
          display: inline-block;
          font-size: 18px;
          color: $color--accent--orange;
          font-weight: bold;
        }
      }
    }
  }
}
.solution__title {
  margin: 0 auto;
  padding: 25px 100px;
  display: table;
  border-radius: 20px;
  background-color: $color--text;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  position: relative;
  top: -60px;

  h2 {
    @include flex(center, center, initial);
    img {
      margin-right: 15px;
      width: 380px;
      height: auto;
    }
    & > span {
      font-size: 44px;
      font-weight: bold;
      color: $color--white;
      letter-spacing: 2px;
      span {
        font-size: 56px;
      }
    }
  }
}

// ==============================================
// single
// ==============================================
.single {
}
.single-document,
.single-blog,
.search,
.error404 {
  .content__main {
    margin-top: 80px;
  }
}

.single-document,
.single-blog,
.search,
.error404 {
  .breadcrumb-inner {
    margin-bottom: 60px;
  }
  .content__main {
    padding-top: 0px;
  }
}

// ==============================================
// archive
// ==============================================
.archive {
}

// ==============================================
// wpcf7
// ==============================================
.entry {
  .wpcf7 {
    max-width: 700px;
    margin: 0 auto;
    p {
      &:last-of-type {
        margin-bottom: 0;
      }
    }
    label {
      font-weight: bold;
      font-size: 15px;
      .required {
        color: #fff;
        margin-right: 0.3em;
        font-size: 0.8em;
        padding: 0.05em 0.25em 0.1em 0.25em;
        border-radius: 0.3em;
        white-space: nowrap;
        text-align: center;
        font-weight: normal;
        background-color: #df6f5f;
        &.not {
          background-color: #f5b555;
        }
      }
    }
    input[type='text'],
    input[type='email'] {
      margin-top: 5px;
      width: 100%;
      height: 45px;
      font-size: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 2px;
      padding: 6px 12px;
      display: block;
      color: #555;
      line-height: 1.42857143;
      box-sizing: border-box;
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
      transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    }
    textarea {
      min-height: 150px;
      margin-top: 5px;
      padding: 6px 12px;
      width: 100%;
      display: block;
      font-size: 15px;
      color: #555;
      border: 1px solid #e0e0e0;
      border-radius: 2px;
      line-height: 1.42857143;
      box-sizing: border-box;
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
      transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    }
    select {
      margin-top: 5px;
      width: 50%;
      height: 45px;
      font-size: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 2px;
      padding: 6px 8px;
      display: block;
      color: #555;
      line-height: 1.42857143;
      box-sizing: border-box;
      box-shadow: none !important;
      background: $color--white;
      cursor: pointer;
    }
    input[type='submit'] {
      margin: 40px auto 0;
      @include btn(16px 0, 360px, initial, initial, 6px);
      @include flex(center, center, initial);
      text-align: center;
      position: relative;
      color: $color--white;
      font-size: 18px;
      background-color: #428bca !important;
      box-shadow: 0 4px 0 #2b6699 !important;
      appearance: none;
      -webkit-appearance: none;
      transition: all 0.2s;
      cursor: pointer;
      &:hover {
        transform: translateY(6px);
        box-shadow: none !important;
      }
    }
  }
}
