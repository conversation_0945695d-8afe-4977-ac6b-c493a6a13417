// 変数
$tooltip-max-width: 450px;
$tooltip-bg-color: #fff;
$tooltip-border-color: #ddd;
$tooltip-shadow: 0 2px 10px rgb(0 0 0 / 10%);
$tooltip-padding: 12px;
$tooltip-border-radius: 4px;
$tooltip-term-color: #45908c;
$tooltip-description-color: #666;
$tooltip-link-color: #06c;
$tooltip-term-font-size: 16px;
$tooltip-description-font-size: 14px;
$tooltip-link-font-size: 12px;

// 用語のスタイル
a.glossary-term {
  font-weight: 600;
  color: $tooltip-term-color;
  cursor: help;
  border-bottom: 1px solid $tooltip-term-color;

  &:hover {
    border-bottom-style: solid;
  }
}

// ツールチップのベーススタイル
.glossary-tooltip {
  position: fixed;
  z-index: 1000;
  display: none;
  max-width: $tooltip-max-width;
  pointer-events: auto;
  background: $tooltip-bg-color;
  border: 1px solid $tooltip-border-color;
  border-radius: $tooltip-border-radius;
  box-shadow: $tooltip-shadow;
  opacity: 0;
  transition: opacity 0.2s ease;

  &.active {
    display: block;
    opacity: 1;
  }

  // コンテンツ領域
  &__content {
    display: flex;
    padding: $tooltip-padding;
  }

  &__image {
    max-width: 140px;
    margin-right: 18px;
    object-fit: contain;

    img {
      width: 100%;
      height: auto;
    }
  }

  // 用語見出し
  &__term {
    margin: 0 0 8px;
    font-size: $tooltip-term-font-size;
    font-weight: bold;
    line-height: 1.2;
    color: $tooltip-term-color;
  }

  // 説明文
  &__description {
    margin: 0 0 8px;
    font-size: $tooltip-description-font-size;
    line-height: 1.5;
    color: $tooltip-description-color;
    word-break: break-word;
    white-space: pre-line;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // リンク
  &__link {
    display: inline-block;
    font-size: $tooltip-link-font-size;
    color: $tooltip-link-color;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.glossary-archive {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 970px;
  margin: 0 auto;

  .glossary {
    &-title {
      width: 100%;
      padding-bottom: 24px;
      margin-bottom: 32px;
      font-size: 32px;
      font-weight: bold;
      color: #006835;
      text-align: center;
      border-bottom: 1px solid #006835;
    }

    &-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      justify-content: flex-start;
      margin-top: 24px;

      &__item {
        position: relative;
        display: flex;
        width: calc((100% - 48px) / 3);
        min-width: 280px;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          content: '- ';
          transform: translateY(-50%);
        }

        .item-title {
          padding-left: 16px;
          font-size: 16px;
          color: $tooltip-description-color;
          text-align: left;
        }
      }
    }
  }
}

.glossary-single {
  display: flex;
  max-width: 1080px;
  margin: 0 auto;

  .sidebar {
    margin-top: 56px;
  }

  &__content {
    width: 70%;
    padding: 32px 0;
  }

  &__title {
    padding-bottom: 24px;
    margin-bottom: 32px;
    font-size: 32px;
    font-weight: bold;
    color: #006835;
    text-align: center;
    border-bottom: 1px solid $tooltip-term-color;
  }

  &__thumbnail {
    width: 100%;
    margin: 0 auto;
    margin-top: 48px;
    margin-bottom: 32px;

    img {
      max-width: 100%;
      height: auto;
    }
  }

  &__bunner {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    margin-top: 48px;

    img {
      width: fit-content;
      max-width: 100%;
      height: auto;
    }
  }

  &__description {
    margin-bottom: 32px;
    font-size: 16px;
    line-height: 1.5;
    color: #666;
  }
}

@media screen and (width <= 979px) {
  a.glossary-term {
    &:hover {
    }
  }

  // ツールチップのベーススタイル
  .glossary-tooltip {
    &.active {
    }

    // コンテンツ領域
    &__content {
    }

    &__image {
      img {
      }
    }

    // 用語見出し
    &__term {
    }

    // 説明文
    &__description {
      &:last-child {
      }
    }

    // リンク
    &__link {
      &:hover {
      }
    }
  }

  .glossary-archive {
    .glossary {
      &-title {
        width: unset;
        padding: 8px 16px;
        margin: 0 16px;
      }

      &-list {
        gap: 12px;
        justify-content: flex-start;
        padding: 0 16px;
        margin-top: 24px;

        &__item {
          width: calc((100% - 48px) / 2);
          min-width: 280px;

          &::before {
          }

          .item-title {
          }
        }
      }
    }
  }

  .glossary-single {
    padding: 0 16px;

    .sidebar {
    }

    &__content {
    }

    &__title {
    }

    &__bunner {
      img {
      }
    }

    &__description {
    }
  }
}

@media screen and (width <= 769px) {
  a.glossary-term {
    &:hover {
    }
  }

  // ツールチップのベーススタイル
  .glossary-tooltip {
    &.active {
    }

    // コンテンツ領域
    &__content {
    }

    &__image {
      img {
      }
    }

    // 用語見出し
    &__term {
    }

    // 説明文
    &__description {
      &:last-child {
      }
    }

    // リンク
    &__link {
      &:hover {
      }
    }
  }

  .glossary-archive {
    .glossary {
      &-title {
        font-size: 22px;
      }

      &-list {
        &__item {
          width: 100%;

          &::before {
          }

          .item-title {
          }
        }
      }
    }
  }

  .glossary-single {
    flex-direction: column;
    justify-content: center;
    overflow: hidden;

    .sidebar {
      margin-top: unset;
    }

    &__content {
      width: 100%;
      padding: 16px 0;
    }

    &__title {
    }

    &__bunner {
      img {
      }
    }

    &__description {
    }
  }
}
