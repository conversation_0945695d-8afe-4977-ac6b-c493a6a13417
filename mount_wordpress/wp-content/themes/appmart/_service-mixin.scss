@mixin service-above($img,$sp-img) {
  padding: 40px 0 77px;
  height: auto;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  position: relative;
  background-image: url($img);
  
  @media (max-width: 768px) {
    background-image: url($sp-img);
    padding: 38px 0 40px;
  }

  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    width: 1px;
    height: 40px;
  
    @media (max-width: 768px) {
      height: 20px;
    }
  }

  &::before {
    bottom: 0;
    background-color: #fff;
  }

  &::after {
    bottom: -40px;
    background-color: #333333;
  
    @media (max-width: 768px) {
      bottom: -20px;
    }
  }

  .h1_above {
    font-size: 14px;
    font-weight: 400;
    color: #fff;
    display: block;

    @media (max-width: 768px) {
      font-size: 12px;
    }
  }

  h1 {
    font-size: 40px;
    margin: 13px 0 31px;
    color: #fff;
    display: inline-block;

    @media (max-width: 768px) {
      font-size: 24px;
      margin: 10px 0 26px;
    }
  }

  .h1_bottom {
    font-size: 20px;
    color: #fff;

    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 200%;
    }
  }
}

@mixin section-padding($td:48px) {
    padding: 80px 0 80px;
  
    @media (max-width: 768px) {
      padding: $td 0 $td 0;
    }
}