<!DOCTYPE html>
<html <?php language_attributes(); ?>>

  <head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <?php if (is_front_page()) : ?>
    <title>Appmart株式会社（アップマート） | オウンドメディア構築から運営代行までサポート</title>
    <?php elseif (is_post_type_archive('blog')) : ?>
    <title>ブログ | Appmart株式会社（アップマート）</title>
    <?php elseif (is_singular('blog')) : ?>
    <title><?php the_title(); ?> | Appmart株式会社（アップマート）</title>
    <?php
            $noindex = get_field('column_noindex');
            $nofollow = get_field('column_nofollow');
        if ($noindex || $nofollow) {
            $robots = array();
            if ($noindex) {
$robots[] = 'noindex';
            }
            if ($nofollow) {
$robots[] = 'nofollow';
            }
            echo '<meta name="robots" content="' . implode(',', $robots) . '" />';
        }
        ?>
    <?php elseif (is_post_type_archive('document')) : ?>
    <title>お役立ち資料 | Appmart株式会社（アップマート）</title>
    <?php elseif (is_post_type_archive('casesutudy')) : ?>
    <title>導入事例 | Appmart株式会社（アップマート）</title>
    <?php elseif (is_post_type_archive('glossary')) : ?>
    <title>用語集 | Appmart株式会社（アップマート）</title>
    <?php elseif (is_post_type_archive('webinar')) : ?>
    <title>ウェビナー予約 | Appmart株式会社（アップマート）</title>
    <?php elseif (is_tax()) : ?>
    <title><?php single_term_title(); ?> | Appmart株式会社（アップマート）</title>
    <?php elseif (is_search()) : ?>
    <title>検索結果 | Appmart株式会社（アップマート）</title>
    <?php elseif (is_404()) : ?>
    <title>ページが見つかりませんでした | Appmart株式会社（アップマート）</title>
    <?php else : ?>
    <title><?php the_title() ; ?> | Appmart株式会社（アップマート）</title>
    <?php endif; ?>

    <?php
        $noIndex = get_field('noindex_nofollow');
    if ($noIndex == 1) {
        echo '<meta name="robots" content="noindex,nofollow" />';
    }
    ?>

    <?php wp_head(); ?>

    <meta name="google-site-verification" content="6vTMHyQEfSII3LYJtOJlc4koUy6yuFSExMSrIyXPTmw" />

    <!-- Google Tag Manager -->
    <script>
    (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src =
        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-M2ZZ662');
    </script>
    <!-- End Google Tag Manager -->

    <!-- Optimize Next -->
    <script>
    (function(p, r, o, j, e, c, t, g) {
      p['_' + t] = {};
      g = r.createElement('script');
      g.src = 'https://www.googletagmanager.com/gtm.js?id=GTM-' + t;
      r[o].prepend(g);
      g = r.createElement('style');
      g.innerText = '.' + e + t + '{visibility:hidden!important}';
      r[o].prepend(g);
      r[o][j].add(e + t);
      setTimeout(function() {
        if (r[o][j].contains(e + t)) {
          r[o][j].remove(e + t);
          p['_' + t] = 0
        }
      }, c)
    })(window, document, 'documentElement', 'classList', 'loading', 2000, 'KVXTWRCL')
    </script>
    <!-- End Optimize Next -->

    <script>
    document.addEventListener('wpcf7mailsent', function(event) {
      location = 'https://appmart.co.jp/lp-thanks/';
    }, false);
    </script>

  </head>

  <body <?php body_class(); ?>>

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M2ZZ662" height="0"
        width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Start pageLoader -->
    <!--
    <div class="pageLoader">
        <div class="pageLoader__inner">
            <div class="pageLoader__cont">

                <div class="spinner">
                    <div class="rect1"></div>
                    <div class="rect2"></div>
                    <div class="rect3"></div>
                    <div class="rect4"></div>
                    <div class="rect5"></div>
                </div>

                <div class="pageLoader__logo">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/logo/appmart-symbol.png" alt="Appmart株式会社">
                </div>

            </div>
        </div>
    </div>
    -->
    <!-- End page_loader -->

    <!-- Start header -->
    <header class="header">
      <div class="container">

        <div class="header__left">
          <a href="<?php echo esc_url(home_url()); ?>">
            <h1>
              <img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/logo/appmart-logo.png"
                alt="Appmart株式会社">
            </h1>
          </a>
        </div>

        <div class="header__right">
          <nav>
            <ul>
              <li class="header__service">
                <a href="<?php echo esc_url(home_url('/service')); ?>" <?php if (is_page('service') || is_page('seo') || is_page('content-marketing') || is_page('whitepaper') || is_page('owned-media') || is_page('saiyou-ownedmedia') || is_page('movie') || is_page('attribution')) {
echo 'class="current"';
                         } ?>>
                  <span>サービス</span>
                  <img
                    src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/triangle_bottom.svg"
                    alt="↓">
                </a>
                <div class="header__service__sp">
                  <a href="<?php echo esc_url(home_url('/service')); ?>">サービス</a>
                  <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/plus.svg"
                    alt="+">
                </div>
                <div class="header__service__modal">
                  <div class="container">
                    <ul>
                      <li>
                        <a href="<?php echo esc_url(home_url('/comonseo')); ?>" <?php if (is_page('comonseo')) {
echo 'class="current"';
                                 } ?>>顧問SEO</a>
                      </li>
                      <li>
                        <a href="<?php echo esc_url(home_url('/content-marketing')); ?>" <?php if (is_page('content-marketing')) {
echo 'class="current"';
                                 } ?>>コンテンツマーケティング</a>
                      </li>
                      <li>
                        <a href="<?php echo esc_url(home_url('/whitepaper')); ?>" <?php if (is_page('whitepaper')) {
echo 'class="current"';
                                 } ?>>ホワイトペーパー制作</a>
                      </li>
                      <li>
                        <a href="<?php echo esc_url(home_url('/writing')); ?>" <?php if (is_page('writing')) {
echo 'class="current"';
                                 } ?>>記事制作</a>
                      </li>
                      <li>
                        <a href="<?php echo esc_url(home_url('/owned-media')); ?>" <?php if (is_page('owned-media')) {
echo 'class="current"';
                                 } ?>>オウンドメディア制作</a>
                      </li>
                      <li>
                        <a href="<?php echo esc_url(home_url('/movie')); ?>" <?php if (is_page('movie')) {
echo 'class="current"';
                                 } ?>>動画制作</a>
                      </li>
                      <li>
                        <a href="<?php echo esc_url(home_url('/attribution')); ?>" <?php if (is_page('attribution')) {
echo 'class="current"';
                                 } ?>>アトリビューション分析</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </li>
              <li>
                <a href="<?php echo esc_url(home_url('/casestudy')); ?>" <?php if (is_singular('casestudy') || is_post_type_archive('casestudy')) {
echo 'class="current"';
                         } ?>>導入事例</a>
              </li>
              <li>
                <a href="<?php echo esc_url(home_url('/document')); ?>" <?php if (is_singular('document') || is_post_type_archive('document')) {
echo 'class="current"';
                         } ?>>お役立ち資料</a>
              </li>
              <li>
                <a href="<?php echo esc_url(home_url('/blog')); ?>" <?php if (is_singular('blog') || is_post_type_archive('blog')) {
echo 'class="current"';
                         } ?>>ブログ</a>
              </li>
              <li>
                <a href="<?php echo esc_url(home_url('/webinar')); ?>" <?php if (is_singular('webinar') || is_post_type_archive('webinar')) {
echo 'class="current"';
                         } ?>>ウェビナー</a>
              </li>
              <li>
                <a href="<?php echo esc_url(home_url('/company')); ?>" <?php if (is_page('company')) {
echo 'class="current"';
                         } ?>>会社概要</a>
              </li>
              <li class="header__recruit">
                <a href="https://recruit.appmart.co.jp/" target="_blank">採用情報</a>
              </li>
              <li class="header__contact">
                <a href="<?php echo esc_url(home_url('/contact')); ?>">お問い合わせ</a>
              </li>
              <li class="header__sp__bottom">
                <a href="<?php echo esc_url(home_url('/privacy-policy')); ?>" <?php if (is_page('privacy-policy')) {
echo 'class="current"';
                         } ?>>プライバシーポリシー</a>
                <p>Copyright (c) Appmart All Rights Reserved.</p>
              </li>
            </ul>
          </nav>

          <div class="spMenu">
            <a class="menu__trigger">
              <span>
                <div class="hamburger">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </span>
            </a>
          </div>

        </div>

      </div>
    </header>
    <!-- End header -->

    <div class="fadein__cont">
      <!-- Start fadein__cont -->
