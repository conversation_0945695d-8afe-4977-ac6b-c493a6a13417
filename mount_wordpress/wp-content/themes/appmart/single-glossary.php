<?php
/*
Template Name: 用語集シングル
Template Post Type: post,page

*/
?>

<?php get_header();
$bunner = get_field('bunner');

?>

<!-- Start main -->
<main class="content__main">
  <?php echo get_template_part('template-parts/breadcrumb'); ?>
  <section class="section">
    <div class="glossary-single">
      <div class="glossary-single__content">
        <h2 class="glossary-single__title"><?php the_title(); ?></h2>
        <!-- サムネイルが設定されている場合は表示 -->
          <?php if (has_post_thumbnail()) : ?>
            <div class="glossary-single__thumbnail">
              <?php the_post_thumbnail('full'); ?>
            </div>
          <?php endif; ?>
        <?php the_content(); ?>
        <?php if ($bunner) : ?>
          <a class="glossary-single__bunner" href="<?php echo $bunner['url']; ?>">
            <img src="<?php echo $bunner['image'] ?>">
          </a>
        <?php endif; ?>
      </div>

      <?php get_template_part('template-parts/sidebar'); ?>
    </div>


  </section>

</main>
<!-- End main -->

<div id="widget-1" class="blog-widget">
  <?php dynamic_sidebar('blog-widget-1'); ?>
</div>

<div id="widget-2" class="blog-widget">
  <?php dynamic_sidebar('blog-widget-2'); ?>
</div>

<div id="widget-3" class="blog-widget">
  <?php dynamic_sidebar('blog-widget-3'); ?>
</div>

<script>
jQuery(function($) {
  var widget1Target = document.getElementById('toc_container');
  var widget2Target = document.getElementsByTagName('h2')[1];
  var widget3Target = document.getElementsByClassName('entry');
  console.log(widget2Target);
  $('#widget-1').insertAfter(widget1Target);
  $('#widget-2').insertBefore(widget2Target);
  $('#widget-3').insertAfter(widget3Target);
});
</script>

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>
