@charset "UTF-8";
.content__main--service {
  padding-top: 40px !important;
}

@media (max-width: 768px) {
  body > div.fadein__cont.active > div.breadcrumb-inner {
    padding-top: 30px;
  }
}

a.cta-link {
  display: table;
  margin: 30px auto;
  text-align: center;
  font-weight: bold;
  padding: 15px 30px;
  width: initial;
  background-color: #fa6b58;
  box-shadow: 0 4px 0 #dd523f;
  border-radius: 6px;
  box-sizing: border-box;
  color: #ffffff !important;
}
a.cta-link:hover {
  transform: translateY(4px);
  box-shadow: none !important;
  opacity: 1 !important;
  color: #ffffff !important;
}

.page-template-service * {
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
  font-family: "游ゴシック", sans-serif;
  font-weight: 700;
  color: #191919;
}
.page-template-service .pc {
  display: block;
}
@media (max-width: 768px) {
  .page-template-service .pc {
    display: none !important;
  }
}
.page-template-service .sp {
  display: none;
}
@media (max-width: 768px) {
  .page-template-service .sp {
    display: block;
  }
}
.page-template-service .red {
  color: #FA6B58;
}
.page-template-service .underline {
  background: linear-gradient(transparent 70%, #F3C11D 0%);
  display: inline;
  position: relative;
  z-index: 2;
}
.page-template-service .h2_above {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #3C8B86;
  text-align: center;
  display: block;
  margin-bottom: 12px;
}
@media (max-width: 768px) {
  .page-template-service .h2_above {
    font-size: 12px;
  }
}
.page-template-service h2 {
  font-size: 32px;
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service h2 {
    font-size: 28px;
    line-height: 42px;
  }
}
.page-template-service img {
  max-width: 100%;
}
.page-template-service .mainvisual-section {
  padding: 40px 0 0 0;
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section {
    padding-top: 0px;
  }
}
.page-template-service .mainvisual-section .service-above {
  padding: 40px 0 77px;
  height: 240px;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  position: relative;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .service-above {
    padding: 38px 0 40px;
  }
}
.page-template-service .mainvisual-section .service-above .scroll-line {
  width: 1px;
  height: 80px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: -40px;
  left: 50%;
  background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #333333 50%, #333333 100%);
  transform: initial;
  z-index: 1;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .service-above .scroll-line {
    width: 1px;
    height: 40px;
    position: absolute;
    top: initial;
    right: initial;
    bottom: -20px;
    left: 50%;
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #333333 50%, #333333 100%);
    transform: initial;
    z-index: 1;
  }
}
.page-template-service .mainvisual-section .service-above:before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(51, 51, 51, 0.5);
  transform: initial;
  z-index: 0;
}
.page-template-service .mainvisual-section .service-above .h1_above {
  position: relative;
  z-index: 2;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  display: block;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .service-above .h1_above {
    font-size: 12px;
  }
}
.page-template-service .mainvisual-section .service-above h1 {
  font-size: 40px;
  margin: 13px 0 31px;
  color: #fff;
  display: inline-block;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .service-above h1 {
    font-size: 24px;
    margin: 10px 0 26px;
  }
}
.page-template-service .mainvisual-section .service-above .h1_bottom {
  z-index: 2;
  position: relative;
  font-size: 20px;
  color: #fff;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .service-above .h1_bottom {
    font-size: 16px;
    line-height: 200%;
  }
}
.page-template-service .mainvisual-section .catch-copy {
  font-size: 24px;
  display: inline-block;
  position: relative;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .catch-copy {
    font-size: 20px;
  }
}
.page-template-service .mainvisual-section .catch-copy::before, .page-template-service .mainvisual-section .catch-copy::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 2px;
  height: 26px;
  background-color: #191919;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section .catch-copy::before, .page-template-service .mainvisual-section .catch-copy::after {
    display: none;
  }
}
.page-template-service .mainvisual-section .catch-copy::before {
  left: -20px;
  transform: rotate(-20deg);
}
.page-template-service .mainvisual-section .catch-copy::after {
  right: -20px;
  transform: rotate(20deg);
}
.page-template-service .mainvisual-section__container {
  text-align: center;
  width: 100%;
}
.page-template-service .mainvisual-section__container .bottom {
  max-width: 1080px;
  margin: 0 auto;
  padding: 25px 0 0 0;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section__container .bottom {
    padding: 32px 20px 24px;
  }
}
.page-template-service .mainvisual-section__container .bottom .breadcrumb-inner {
  padding-top: 0;
  text-align: left;
}
.page-template-service .mainvisual-section__container .bottom .breadcrumb-inner > .container > .breadcrumb-area > span > a > span {
  font-weight: 400;
  font-size: 14px;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section__container .bottom .breadcrumb-inner > .container > .breadcrumb-area > span > a > span {
    font-size: 12px;
  }
}
.page-template-service .mainvisual-section__container .bottom .breadcrumb-inner > .container > .breadcrumb-area > span > span {
  font-weight: 400;
  font-size: 14px;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section__container .bottom .breadcrumb-inner > .container > .breadcrumb-area > span > span {
    font-size: 12px;
  }
}
.page-template-service .mainvisual-section__container .bottom .client {
  margin-top: 48px;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section__container .bottom .client {
    margin-top: 32px;
  }
}
.page-template-service .mainvisual-section__container .bottom .client__list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.page-template-service .mainvisual-section__container .bottom .client__img {
  width: 14.285%;
}
@media (max-width: 768px) {
  .page-template-service .mainvisual-section__container .bottom .client__img {
    width: 33.3%;
  }
}
.page-template-service .benefit-section {
  padding: 80px 0 80px;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .benefit-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container h2 .sp_mini {
    font-size: 16px;
    display: block;
  }
}
.page-template-service .benefit-section .container .benefit {
  margin: 48px auto 0;
  max-width: 880px;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container .benefit {
    margin-top: 32px;
  }
}
.page-template-service .benefit-section .container .benefit .item {
  display: flex;
  align-items: center;
}
.page-template-service .benefit-section .container .benefit .item:not(:first-of-type) {
  margin-top: 42px;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container .benefit .item:not(:first-of-type) {
    margin-top: 40px;
  }
}
.page-template-service .benefit-section .container .benefit .item .left {
  width: 73px;
  margin-right: 20px;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container .benefit .item .left {
    min-width: 48px;
    max-width: 48px;
    margin-right: 17px;
  }
}
.page-template-service .benefit-section .container .benefit .item .right .h3_above {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  color: #3C8B86;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container .benefit .item .right .h3_above {
    font-size: 12px;
  }
}
.page-template-service .benefit-section .container .benefit .item .right .h3_above::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  right: -120px;
  background-color: #808080;
  width: 100px;
  height: 1px;
}
.page-template-service .benefit-section .container .benefit .item .right h3 {
  font-size: 24px;
  margin-top: 12px;
}
@media (max-width: 768px) {
  .page-template-service .benefit-section .container .benefit .item .right h3 {
    font-size: 16px;
    line-height: 200%;
  }
}
.page-template-service .problem-section {
  padding: 80px 0 80px;
  background-color: #F5F6F7;
}
@media (max-width: 768px) {
  .page-template-service .problem-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .problem-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .problem-section .container .problem {
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem {
    display: block;
  }
}
.page-template-service .problem-section .container .problem .item {
  width: 340px;
  text-align: center;
  margin-top: 36px;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item {
    width: unset;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .page-template-service .problem-section .container .problem .item:not(:first-of-type) {
    margin-top: 36px;
  }
}
.page-template-service .problem-section .container .problem .item .thought .bubble, .page-template-service .problem-section .container .problem .item .thought .bubble-rv {
  display: flex;
  align-items: center;
  position: relative;
  width: 320px;
  height: 77px;
  background-color: #fff;
  border-radius: 8px 8px 0 8px;
  padding: 0 25px;
  text-align: left;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item .thought .bubble, .page-template-service .problem-section .container .problem .item .thought .bubble-rv {
    width: 220px;
    height: 80px;
    padding: 0 15px;
  }
}
.page-template-service .problem-section .container .problem .item .thought .bubble:first-child, .page-template-service .problem-section .container .problem .item .thought .bubble-rv:first-child {
  margin-left: 20px;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item .thought .bubble:first-child, .page-template-service .problem-section .container .problem .item .thought .bubble-rv:first-child {
    margin-left: 0;
  }
}
.page-template-service .problem-section .container .problem .item .thought .bubble::before, .page-template-service .problem-section .container .problem .item .thought .bubble-rv::before {
  content: "";
  position: absolute;
  right: 0;
  bottom: -16px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 16px 16px 0;
  border-color: transparent #ffffff transparent transparent;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item .thought .bubble::before, .page-template-service .problem-section .container .problem .item .thought .bubble-rv::before {
    bottom: -12px;
    border-width: 0 12px 12px 0;
  }
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item .thought .bubble-rv {
    border-radius: 8px 8px 8px 0;
  }
  .page-template-service .problem-section .container .problem .item .thought .bubble-rv::before {
    left: 0;
    transform: rotate(-90deg);
  }
}
.page-template-service .problem-section .container .problem .item .thought p {
  font-size: 15px;
  color: #808080;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item .thought p {
    font-size: 14px;
  }
}
.page-template-service .problem-section .container .problem .item img {
  width: 110px;
  margin: 0 auto;
  margin-top: 30px;
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item img {
    width: 80px;
    margin: 0 0 0 36px;
  }
}
@media (max-width: 768px) {
  .page-template-service .problem-section .container .problem .item:nth-child(2) {
    flex-direction: row-reverse;
  }
  .page-template-service .problem-section .container .problem .item:nth-child(2) .thought p {
    border-radius: 8px 8px 8px 0;
  }
  .page-template-service .problem-section .container .problem .item:nth-child(2) .thought p::before {
    right: unset;
    left: 0;
    border-width: 16px 16px 0 0;
    border-color: #ffffff transparent transparent transparent;
  }
  .page-template-service .problem-section .container .problem .item:nth-child(2) img {
    margin: 0 36px 0 0;
  }
}


/*system-section*/
.page-template-service .sysytem-section {
  padding: 80px 0 80px;
  background-image: url(../images/bg/shikumi-bg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.page-template-service .sysytem-section .h2_above{
  text-align: left;
}
.page-template-service .sysytem-section .system-top{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-template-service .sysytem-section .system-top img{
  width: 450px;
  height: auto;
}
.page-template-service .sysytem-section h2{
  text-align: left;
  line-height: 1.5;
}
.page-template-service .sysytem-section h2 .red{
  color: #FA6B58;
  font-size: 48px;
}
.page-template-service .sysytem-section .container .system{
  margin-top: 32px;
  width: 100%;
}
.page-template-service .sysytem-section .container .system ul .system-column{
  display: flex;
  justify-content: space-around;
}
.page-template-service .sysytem-section .container .system ul li {
    padding: 35px 0;
    width: 320px;
    box-sizing: border-box;
    text-align: center;
}
.page-template-service .sysytem-section .container .system ul li span{
    color: #FA6B58;
    font-family: "Open Sans" ;
    font-size: 32px;
    font-weight: 700;
    position: relative;
}
.page-template-service .sysytem-section .container .system ul li p{
  font-size: 20px;
  margin-top: 32px;
}
.page-template-service .sysytem-section .container .system ul li span:before{
  content: '';
  width: 1px;
  height: 20px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: -20px;
  left: 50%;
  background: #FA6B58;
  transform: translateX(-50%);
  z-index: 1;
}

@media (max-width: 768px) {
.page-template-service .sysytem-section {
  padding: 48px 0;
}
.page-template-service .sysytem-section .system-top{
  display: block;
}
.page-template-service .sysytem-section .system-top img{
  width: 80%;
  margin: 32px auto 0;
}
.page-template-service .sysytem-section h2{
  text-align: center;
  font-size: 25px;
}
.page-template-service .sysytem-section h2 .red{
  font-size: 32px;
}
.page-template-service .sysytem-section .h2_above{
  text-align: center;
}
.page-template-service .sysytem-section .container .system ul .system-column{
  display: block;
}
.page-template-service .sysytem-section .container .system ul li {
    padding: 16px 0;
    width: 335px;
    text-align: left;
    display: flex;
    align-items: center;
}
.page-template-service .sysytem-section .container .system ul li p{
  font-size: 18px;
  margin-top: 0;
  margin-left: 48px;
}
.page-template-service .sysytem-section .container .system ul li span:before{
  content: '';
  width: 20px;
  height: 1px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: 50%;
  left: 60px;
  background: #FA6B58;
  transform: translateX(-50%);
  z-index: 1;
}
}

.page-template-service .strength-section {
  padding: 80px 0 80px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .strength-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .strength-section .container .middle {
  text-align: center;
  margin-bottom: 48px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .middle {
    margin-bottom: 32px;
  }
}
.page-template-service .strength-section .container .middle .mini {
  font-size: 24px;
  display: block;
  line-height: 48px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .middle .mini {
    font-size: 16px;
    line-height: 42px;
  }
}
.page-template-service .strength-section .container .middle .mini .gray {
  color: #808080;
}
.page-template-service .strength-section .container .middle .big {
  font-size: 32px;
  line-height: 48px;
  display: block;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .middle .big {
    font-size: 28px;
    line-height: 42px;
  }
}
.page-template-service .strength-section .container .strength {
  margin-top: 80px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength {
    margin-top: 53px;
  }
}
.page-template-service .strength-section .container .strength .item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item {
    display: block;
  }
}
.page-template-service .strength-section .container .strength .item:not(:first-of-type) {
  margin-top: 100px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item:not(:first-of-type) {
    margin-top: 68px;
  }
}
.page-template-service .strength-section .container .strength .item .content {
  margin-right: 64px;
  position: relative;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .content {
    margin: 0;
  }
}
.page-template-service .strength-section .container .strength .item .content .h3_above {
  display: inline;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  color: #3C8B86;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .content .h3_above {
    font-size: 12px;
  }
}
.page-template-service .strength-section .container .strength .item .content .h3_above::after {
  content: "\a";
  white-space: pre;
}
.page-template-service .strength-section .container .strength .item .content .h3_above::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  right: -120px;
  background-color: #808080;
  width: 100px;
  height: 1px;
}
.page-template-service .strength-section .container .strength .item .content h3 {
  display: inline-block;
  font-size: 24px;
  margin: 32px 0;
  line-height: 1.5;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .content h3 {
    font-size: 20px;
    margin: 24px 0 32px;
    display: inline-block;
  }
}
.page-template-service .strength-section .container .strength .item .content h3 .red {
  font-size: 32px;
}
.page-template-service .strength-section .container .strength .item .content h3 .red::before {
  bottom: 5px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .content h3 .red::before {
    bottom: 0.5px;
  }
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .content h3 .red {
    font-size: 24px;
  }
}
.page-template-service .strength-section .container .strength .item .content .desc {
  font-size: 16px;
  line-height: 200%;
  font-weight: 400;
}
.page-template-service .strength-section .container .strength .item .content .number {
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  position: absolute;
  font-size: 120px;
  color: #F0F0F0;
  top: 0;
  right: 0;
  line-height: 48px;
  align-self: center;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .content .number {
    top: 0;
    font-size: 80px;
  }
}
.page-template-service .strength-section .container .strength .item .image {
  min-width: 476px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item .image {
    min-width: unset;
    margin-top: 40px;
  }
}
.page-template-service .strength-section .container .strength .item:nth-of-type(even) {
  flex-direction: row-reverse;
}
.page-template-service .strength-section .container .strength .item:nth-of-type(even) .content {
  margin-right: 0;
  margin-left: 64px;
}
@media (max-width: 768px) {
  .page-template-service .strength-section .container .strength .item:nth-of-type(even) .content {
    margin: 0;
  }
}
.page-template-service .work-section {
  padding: 80px 0 80px;
  background-color: #EBF9F3;
}
@media (max-width: 768px) {
  .page-template-service .work-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .work-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .work-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .work-section .container .h2_bottom {
  margin-top: 32px;
  font-size: 16px;
  line-height: 200%;
  font-weight: 400;
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .h2_bottom {
    margin-top: 40px;
    text-align: left;
  }
}
.page-template-service .work-section .container .work {
  margin-top: 32px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32px 48px;
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work {
    display: block;
  }
}
.page-template-service .work-section .container .work .item {
  -ms-grid-row-align: end;
  align-self: flex-end;
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work .item:not(:first-of-type) {
    margin-top: 24px;
  }
}
.page-template-service .work-section .container .work .item .images {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work .item .images {
    justify-content: space-between;
  }
}
.page-template-service .work-section .container .work .item .images .work__img--2 {
  width: 250px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work .item .images .work__img--2:not(:first-of-type) {
    margin-left: 18px;
  }
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work .item .images .work__img--2 {
    width: 160px;
  }
}
.page-template-service .work-section .container .work .item .images .work__img--3 {
  width: 132px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work .item .images .work__img--3 {
    width: 100px;
  }
}
.page-template-service .work-section .container .work .item p {
  font-size: 16px;
  text-align: center;
  margin-top: 16px;
}
@media (max-width: 768px) {
  .page-template-service .work-section .container .work .item p {
    margin-top: 12px;
  }
}
.page-template-service .voice-section {
  padding: 80px 0;
}
@media (max-width: 768px) {
  .page-template-service .voice-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .voice-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .voice-section .container .voice {
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice {
    display: block;
  }
}
.page-template-service .voice-section .container .voice .item {
  padding: 24px;
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  width: 320px;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item {
    padding: 20px 24px;
    width: 100%;
  }
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item:not(:first-of-type) {
    margin-top: 32px;
  }
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item .heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
  }
}
.page-template-service .voice-section .container .voice .item .heading h3 {
  font-size: 16px;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item .heading h3 {
    font-size: 14px;
  }
}
.page-template-service .voice-section .container .voice .item .heading img {
  width: 92px;
  margin: 16px 0 19px;
  margin-right: auto;
  margin-left: auto;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item .heading img {
    width: 67px;
    margin: 0;
  }
}
.page-template-service .voice-section .container .voice .item .red {
  font-size: 18px;
  text-align: left;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item .red {
    margin-top: 20px;
  }
}
.page-template-service .voice-section .container .voice .item .desc {
  text-align: left;
  font-size: 14px;
  line-height: 200%;
  font-weight: 400;
  margin-top: 21px;
}
@media (max-width: 768px) {
  .page-template-service .voice-section .container .voice .item .desc {
    margin-top: 25px;
  }
}
.page-template-service .price-section {
  padding: 80px 0;
  background-color: #EBF9F3;
}
@media (max-width: 768px) {
  .page-template-service .price-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .price-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .price-section .container .box {
  margin-top: 32px;
}
.page-template-service .price-section .container .box h3 {
  background: #191919;
  color: #fff;
  text-align: center;
  font-size: 24px;
  padding: 18px 0;
  border-radius: 8px 8px 0 0;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box h3 {
    font-size: 18px;
  }
}
.page-template-service .price-section .container .box h3 span {
  color: #fff;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box h3 span {
    font-size: 14px;
  }
}
.page-template-service .price-section .container .box .content {
  padding: 24px 0 52px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content {
    padding: 0;
    padding-bottom: 37px;
  }
}
.page-template-service .price-section .container .box .content .flex {
  display: flex;
  justify-content: center;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex {
    display: block;
  }
}
.page-template-service .price-section .container .box .content .flex .item {
  width: 280px;
  padding: 0 40px;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex .item {
    padding: 16px 0;
    margin: 0 auto;
  }
}
.page-template-service .price-section .container .box .content .flex .item:not(:first-of-type) {
  border-left: 1px solid #808080;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex .item:not(:first-of-type) {
    border-left: none;
    border-top: 1px solid #808080;
  }
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex .item .heading {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.page-template-service .price-section .container .box .content .flex .item img {
  width: 120px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex .item img {
    margin: 0;
    margin-right: 18px;
    width: 80px;
  }
}
.page-template-service .price-section .container .box .content .flex .item h4 {
  margin-top: 0px;
  font-size: 18px;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex .item h4 {
    font-size: 16px;
    margin: 0;
  }
}
.page-template-service .price-section .container .box .content .flex .item .example {
  margin-top: 24px;
  text-align: left;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .flex .item .example {
    margin-top: 8px;
    padding: 0 9px;
  }
}
.page-template-service .price-section .container .box .content .flex .item .example > li {
  font-size: 14px;
  line-height: 2;
}
.page-template-service .price-section .container .box .content .price-wrap {
  margin: 28px 0 16px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .price-wrap {
    margin: 0 0 16px;
  }
}
.page-template-service .price-section .container .box .content .price-wrap .term {
  font-size: 18px;
  line-height: 28px;
  margin-right: 28px;
  color: #808080;
}
.page-template-service .price-section .container .box .content .price-wrap .price {
  font-size: 18px;
  line-height: 28px;
}
.page-template-service .price-section .container .box .content .price-wrap .price .red {
  font-size: 32px;
  line-height: 28px;
}
.page-template-service .price-section .container .box .content .rice {
  text-align: center;
  margin-bottom: 24px;
  font-size: 12px;
  color: #808080;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .rice {
    margin-bottom: 16px;
    font-size: 11px;
  }
}
.page-template-service .price-section .container .box .content .ul {
  font-size: 20px;
  display: inline-block;
}
@media (max-width: 768px) {
  .page-template-service .price-section .container .box .content .ul {
    font-size: 16px;
  }
}
.page-template-service .price-section .container .box .content .ul span::before {
  height: 6px;
  bottom: 0;
}
.page-template-service .flow-section {
  padding: 80px 0;
}
@media (max-width: 768px) {
  .page-template-service .flow-section {
    padding: 48px 0 48px 0;
  }
}
.page-template-service .flow-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .flow-section .container .flow {
  margin-top: 42px;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow {
    margin-top: 32px;
  }
}
.page-template-service .flow-section .container .flow .item {
  display: flex;
}
.page-template-service .flow-section .container .flow .item:not(:first-of-type) {
  margin-top: 25px;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item:not(:first-of-type) {
    margin-top: unset;
    padding-top: 15px;
  }
}
.page-template-service .flow-section .container .flow .item:not(:last-of-type) {
  position: relative;
}
.page-template-service .flow-section .container .flow .item:not(:last-of-type)::before {
  content: "";
  position: absolute;
  left: 53px;
  bottom: 7px;
  background-color: #3C8B86;
  width: 2px;
  height: calc(100% - 110px);
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item:not(:last-of-type)::before {
    left: 24px;
    height: calc(100% - 50px);
  }
}
.page-template-service .flow-section .container .flow .item:not(:last-of-type)::after {
  content: "";
  position: absolute;
  left: 50px;
  bottom: 1px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6.86px 4px 0 4px;
  border-color: #3C8B86 transparent transparent transparent;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item:not(:last-of-type)::after {
    left: 21px;
  }
}
.page-template-service .flow-section .container .flow .item .number_circle {
  margin-right: 40px;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #3C8B86;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .number_circle {
    min-width: 50px;
    max-width: 50px;
    min-height: 50px;
    max-height: 50px;
    margin-right: 20px;
  }
}
.page-template-service .flow-section .container .flow .item .number_circle .step {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .number_circle .step {
    font-size: 10px;
  }
}
.page-template-service .flow-section .container .flow .item .number_circle .number {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  font-size: 32px;
  font-weight: 500;
  color: #fff;
  line-height: 1;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .number_circle .number {
    font-size: 18px;
    margin-top: 0px;
  }
}
.page-template-service .flow-section .container .flow .item .content {
  padding-bottom: 25px;
  border-bottom: 1px solid #E0E0E0;
  flex-grow: 1;
}
.page-template-service .flow-section .container .flow .item .content .heading {
  display: flex;
  align-items: center;
  position: relative;
}
.page-template-service .flow-section .container .flow .item .content .heading::before {
  content: "";
  position: absolute;
  left: 53px;
  bottom: -10px;
  background-color: #808080;
  width: 1px;
  height: 20px;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .content .heading::before {
    display: none;
  }
}
.page-template-service .flow-section .container .flow .item .content .heading img {
  width: 110px;
  margin-right: 48px;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .content .heading img {
    width: 32px;
    margin-right: 20px;
  }
}
.page-template-service .flow-section .container .flow .item .content .heading h3 {
  font-size: 24px;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .content .heading h3 {
    font-size: 17px;
  }
}
.page-template-service .flow-section .container .flow .item .content .desc {
  font-size: 16px;
  font-weight: 400;
  line-height: 28px;
  margin-top: 20px;
}
@media (max-width: 768px) {
  .page-template-service .flow-section .container .flow .item .content .desc {
    margin-top: 17px;
    font-size: 14px;
  }
}
.page-template-service .flow-section .container .rice {
  margin-top: 32px;
  font-size: 14px;
  line-height: 200%;
  font-weight: 400;
  color: #808080;
}
.page-template-service .faq-section {
  padding: 80px 0 80px 0;
  background-color: #F5F6F7;
}
@media (max-width: 768px) {
  .page-template-service .faq-section {
    padding: 48px 0 20px 0;
  }
}
.page-template-service .faq-section .container {
  max-width: 1080px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service .faq-section .container .faq {
  margin-top: 32px;
}
.page-template-service .faq-section .container .faq__items {
  display: flex;
  flex-direction: column;
}
.page-template-service .faq-section .container .faq__item:not(:first-child) {
  margin-top: 32px;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq__item:not(:first-child) {
    margin-top: 20px;
  }
}
.page-template-service .faq-section .container .faq__item:not(:last-child) {
  border-bottom: 1px solid #E0E0E0;
}
.page-template-service .faq-section .container .faq__item-q {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq__item-q {
    margin-bottom: 24px;
  }
}
.page-template-service .faq-section .container .faq__item-q--icon {
  width: 64px;
  height: 64px;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq__item-q--icon {
    width: 40px;
    height: 40px;
  }
}
.page-template-service .faq-section .container .faq__item-q--text {
  font-size: 20px;
  line-height: 1.5;
  padding-left: 24px;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq__item-q--text {
    font-size: 16px;
    padding-left: 10px;
  }
}
.page-template-service .faq-section .container .faq__item-a {
  margin-bottom: 32px;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq__item-a {
    margin-bottom: 20px;
  }
}
.page-template-service .faq-section .container .faq__item-a--text {
  font-size: 16px;
  font-weight: 400;
}
.page-template-service .faq-section .container .faq__item-mc {
  margin-bottom: 0;
}
.page-template-service .faq-section .container .faq dl div {
  padding-bottom: 25px;
  border-bottom: 1px solid #E0E0E0;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq dl div {
    padding-bottom: unset;
    border-bottom: unset;
  }
}
.page-template-service .faq-section .container .faq dl div:not(:first-of-type) {
  margin-top: 39px;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq dl div:not(:first-of-type) {
    padding-top: unset;
    margin-top: 45px;
  }
}
.page-template-service .faq-section .container .faq dl div dt {
  padding-left: 80px;
  font-size: 20px;
  position: relative;
  line-height: 1.5;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq dl div dt {
    padding: 0 0 0 50px;
    font-size: 16px;
  }
}
.page-template-service .faq-section .container .faq dl div dt::before {
  content: "";
  background-image: url(../images/newwhitepaper/q.png);
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 64px;
  width: 64px;
  height: 64px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq dl div dt::before {
    width: 40px;
    height: 40px;
    top: -5px;
  }
}
.page-template-service .faq-section .container .faq dl div dd {
  margin-top: 44px;
  font-size: 16px;
  line-height: 200%;
  font-weight: 400;
}
@media (max-width: 768px) {
  .page-template-service .faq-section .container .faq dl div dd {
    margin-top: 29px;
  }
}
.page-template-service.writing .h2_above {
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .page-template-service.writing br {
    line-height: 0;
  }
  .page-template-service.writing .h2_above {
    margin-bottom: 12px;
  }
  .page-template-service.writing .mainvisual-section .catch-copy {
    margin-bottom: 15px;
    line-height: 30px;
  }
}
.page-template-service.writing .seo-section {
  margin: 80px auto 0;
  padding: 48px;
  max-width: 1080px;
  border: 8px solid #FA6B58;
  border-radius: 8px;
}
@media (max-width: 768px) {
  .page-template-service.writing .seo-section {
    margin-right: 20px;
    margin-left: 20px;
    padding: 32px 20px;
    border-width: 4px;
  }
  .page-template-service.writing .seo-section .container {
    padding: 0;
  }
}
.page-template-service.writing .seo-section .h2_above {
  color: #FA6B58;
  font-size: 16px;
}
.page-template-service.writing .seo-section h2 {
  margin-bottom: 55px;
  line-height: 1.5;
}
@media (max-width: 768px) {
  .page-template-service.writing .seo-section h2 {
    margin-bottom: 25px;
    font-size: 24px;
  }
  .page-template-service.writing .seo-section h2 .mini {
    font-size: 20px;
  }
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .seo-section .writing {
    display: flex;
    align-items: center;
  }
}
.page-template-service.writing .seo-section .writing p {
  margin: 0 47px 20px 0;
  max-width: 651px;
  font-weight: 400;
  line-height: 2.2;
}
@media (max-width: 768px) {
  .page-template-service.writing .seo-section .writing p {
    margin-right: 0;
  }
}
.page-template-service.writing .seo-section .writing img {
  flex-shrink: 0;
  width: auto;
  height: 243px;
}
@media (max-width: 768px) {
  .page-template-service.writing .seo-section .writing img {
    margin: 0 auto;
    width: 200px;
    height: auto;
  }
}
.page-template-service.writing .benefit-section .container .item .left {
  flex-shrink: 0;
  margin-right: 32px;
}
.page-template-service.writing .benefit-section .container .item .right p {
  margin-top: 15px;
  font-weight: 400;
  line-height: 2;
}
.page-template-service.writing .problem-section .container .problem {
  margin-bottom: 67px;
}
.page-template-service.writing .problem-section .container .problem .bubble, .page-template-service.writing .problem-section .container .problem .item .thought .bubble-rv {
  justify-content: center;
}
.page-template-service.writing .problem-section .container .middle {
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service.writing .problem-section .container .middle {
    margin-bottom: 32px;
  }
}
.page-template-service.writing .problem-section .container .middle .mini {
  font-size: 24px;
  display: block;
  line-height: 48px;
}
@media (max-width: 768px) {
  .page-template-service.writing .problem-section .container .middle .mini {
    font-size: 16px;
    line-height: 42px;
  }
}
.page-template-service.writing .problem-section .container .middle .mini .gray {
  color: #808080;
}
.page-template-service.writing .problem-section .container .middle .big {
  font-size: 32px;
  line-height: 48px;
  display: block;
}
@media (max-width: 768px) {
  .page-template-service.writing .problem-section .container .middle .big {
    font-size: 28px;
    line-height: 42px;
  }
}
.page-template-service.writing .support-section {
  padding-top: 80px;
  padding-bottom: 80px;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section {
    padding-top: 48px;
    padding-bottom: 48px;
  }
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.page-template-service.writing .support-section .container h2 {
  margin-bottom: 60px;
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container h2 {
    margin-bottom: 35px;
  }
}
.page-template-service.writing .support-section .container h2 span {
  display: block;
  line-height: 1.5;
}
.page-template-service.writing .support-section .container h2 span.mini {
  margin-bottom: 5px;
  font-size: 32px;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container h2 span.mini {
    font-size: 24px;
  }
}
.page-template-service.writing .support-section .container h2 span.big {
  font-size: 40px;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container h2 span.big {
    font-size: 28px;
  }
}
.page-template-service.writing .support-section .container .flex {
  display: flex;
  justify-content: center;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex {
    display: block;
  }
}
.page-template-service.writing .support-section .container .flex .item {
  display: grid;
  grid-gap: 32px 0;
  padding: 10px 0;
  width: 380px;
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item {
    grid-template-rows: 125px 50px 144px 1fr;
  }
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item {
    grid-gap: 0;
    padding: 0;
    margin: 0 auto;
    width: 100%;
  }
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item:not(:first-of-type) {
    padding-left: 30px;
    border-left: 1px solid #808080;
  }
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item:not(:first-of-type) {
    padding-top: 32px;
    border-left: none;
    border-top: 1px solid #808080;
  }
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item:not(:last-of-type) {
    padding-right: 30px;
  }
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item:not(:last-of-type) {
    padding-bottom: 31px;
  }
}
.page-template-service.writing .support-section .container .flex .item .heading {
  position: relative;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item .heading {
    margin-bottom: 25px;
  }
}
.page-template-service.writing .support-section .container .flex .item .heading::after {
  content: "";
  position: absolute;
  transform: translateX(-50%);
  bottom: -10px;
  left: 50%;
  background-color: #808080;
  width: 1px;
  height: 20px;
}
.page-template-service.writing .support-section .container .flex .item .heading img {
  margin: 0 auto;
  width: auto;
  height: 100%;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item .heading img {
    height: 100px;
  }
}
.page-template-service.writing .support-section .container .flex .item h3 {
  place-self: center;
  margin-top: 0px;
  font-size: 20px;
  line-height: 1.5;
  text-align: center;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item h3 {
    margin-bottom: 14px;
  }
}
.page-template-service.writing .support-section .container .flex .item .example {
  place-self: center;
  max-width: 290px;
  width: 100%;
  text-align: left;
}
@media (max-width: 768px) {
  .page-template-service.writing .support-section .container .flex .item .example {
    margin-bottom: 20px;
    padding: 0 20px;
    max-width: none;
  }
}
.page-template-service.writing .support-section .container .flex .item .example > li {
  display: flex;
  font-size: 16px;
  line-height: 2;
}
.page-template-service.writing .support-section .container .flex .item .example > li::before {
  content: "";
  flex-shrink: 0;
  margin: 5px 8px 0 0;
  width: 20px;
  height: 20px;
  background: url(../images/service/check-icon.png) 0 0/contain no-repeat;
}
.page-template-service.writing .support-section .container .flex .item p {
  font-weight: 400;
  text-align: justify;
}
.page-template-service.writing .strength-section {
  background-color: #EBF9F3;
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .content {
    width: 545px;
  }
}
.page-template-service.writing .strength-section .container .strength .content .number {
  top: 14px;
  color: #fff;
}
@media (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .content .number {
    top: 5px;
  }
}
.page-template-service.writing .strength-section .container .strength .content h3 {
  margin-bottom: 15px;
}
.page-template-service.writing .strength-section .container .strength .item {
  flex-direction: row;
}
.page-template-service.writing .strength-section .container .strength .item:nth-of-type(-n+2) {
  display: block;
}
.page-template-service.writing .strength-section .container .strength .item .content {
  margin: 0 auto 50px;
}
@media (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .item .content {
    margin-bottom: 35px;
  }
}
@media (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .item .h3_above::before {
    right: -88px;
    width: 80px;
  }
}
.page-template-service.writing .strength-section .container .strength .item .images {
  display: grid;
  grid-gap: 36px;
}
@media (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .item .images {
    grid-gap: 16px;
  }
}
.page-template-service.writing .strength-section .container .strength .item .images:not(:last-of-type) {
  margin-bottom: 50px;
}
@media (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .item .images:not(:last-of-type) {
    margin-bottom: 30px;
  }
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .item .images {
    grid-template-rows: repeat(2, auto);
    grid-template-columns: repeat(2, 1fr);
  }
  .page-template-service.writing .strength-section .container .strength .item .images h4 {
    display: flex;
    align-items: center;
    grid-column: 1/3;
  }
  .page-template-service.writing .strength-section .container .strength .item .images h4::before {
    content: "";
    margin-right: 17px;
    width: 50px;
    height: 1px;
    background-color: #808080;
  }
}
.page-template-service.writing .strength-section .container .strength .item .images h4 {
  font-size: 20px;
  line-height: 32px;
}
@media (max-width: 768px) {
  .page-template-service.writing .strength-section .container .strength .item .images h4 {
    font-size: 18px;
  }
}
.page-template-service.writing .strength-section .container .strength .item .images img {
  justify-self: center;
}
.page-template-service.writing .strength-section .container .strength .item .image img {
  margin-inline: auto;
}
.page-template-service.writing .difference-section {
  padding-top: 80px;
  padding-bottom: 80px;
  overflow: hidden;
}
@media (max-width: 768px) {
  .page-template-service.writing .difference-section {
    padding-top: 48px;
    padding-bottom: 48px;
  }
}
.page-template-service.writing .difference-section h2 {
  margin-bottom: 60px;
}
.page-template-service.writing .difference-section .scroll {
  overflow-x: scroll;
}
@media not screen and (max-width: 768px) {
  .page-template-service.writing .difference-section .scroll {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .page-template-service.writing .difference-section .scroll::-webkit-scrollbar {
    display: none;
  }
}
.page-template-service.writing .difference-section .scroll .image {
  margin: 0 auto;
  width: 1000px;
}
@media (max-width: 768px) {
  .page-template-service.writing .difference-section .scroll .image {
    width: 730px;
  }
}
.page-template-service.writing .difference-section .scroll .image img {
  padding: 0 20px;
}
.page-template-service.writing .voice-section {
  background-color: #EBF9F3;
}
.page-template-service.writing .voice-section .voice {
  margin-top: 42px;
}
.page-template-service.writing .flow-section .number_circle {
  flex-shrink: 0;
}
.page-template-service.writing .faq-section .faq__item {
  margin-inline: auto;
  width: 100%;
  max-width: 800px;
}
.page-template-service.writing .faq-section .faq__item:not(:last-of-type) {
  padding-top: 20px;
  border-bottom: 1px solid #E0E0E0;
}
.page-template-service.writing .faq-section .faq__item-a {
  margin-left: auto;
  max-width: 712px;
}
.page-template-service.writing .faq-section .faq__item-a a {
  margin-top: 25px;
  display: inline-block;
  font-size: 18px;
  color: #FA6B58;
  font-weight: bold;
}

/*--------ownedmedia--------*/

.page-template-service .intro-section {
  margin: 80px auto 80px;
  padding: 48px;
  max-width: 1080px;
  border: 8px solid #FA6B58;
  border-radius: 8px;
}
@media (max-width: 768px) {
  .page-template-service .intro-section {
    margin: 48px 20px;
    padding: 32px 20px;
    border-width: 4px;
  }
  .page-template-service .intro-section .container {
    padding: 0;
  }
}
.page-template-service .intro-section .h2_above {
  color: #FA6B58;
  font-size: 16px;
}
.page-template-service .intro-section h2 {
  margin-bottom: 55px;
  line-height: 1.5;
}
@media (max-width: 768px) {
  .page-template-service .intro-section h2 {
    margin-bottom: 25px;
    font-size: 24px;
  }
  .page-template-service .intro-section h2 .mini {
    font-size: 20px;
  }
}
@media not screen and (max-width: 768px) {
  .page-template-service .intro-section .ownedmedia {
    display: flex;
    align-items: center;
  }
}
.page-template-service .intro-section .ownedmedia p {
  margin: 0 47px 20px 0;
  max-width: 651px;
  font-weight: 400;
  line-height: 2.2;
}
@media (max-width: 768px) {
  .page-template-service .intro-section .ownedmedia p {
    margin-right: 0;
  }
}
.page-template-service .intro-section .ownedmedia img {
  flex-shrink: 0;
  width: auto;
  height: 243px;
}
@media (max-width: 768px) {
  .page-template-service .intro-section .ownedmedia img {
    margin: 0 auto;
    width: 200px;
    height: auto;
  }
}

.show_sp {
  display: none;
}

.company-overview__item {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
  border-top: 1px solid #808080;
}
.company-overview__item:last-of-type {
  border-bottom: 1px solid #808080;
}
.company-overview__item:nth-of-type(7) dd {
  line-height: 1.8;
}
.company-overview__item dt {
  width: 300px;
  font-weight: bold;
  line-height: 1.1875;
  padding: 30px 0 30px 30px;
  box-sizing: border-box;
  color: #333;
}
.company-overview__item dd {
  width: calc(100% - 300px);
  line-height: 1.1875;
  padding: 30px 0;
  color: #333;
}
.company-overview__item dd .heading {
  font-weight: bold;
}
.company-overview__item dd .hq_map {
  width: 100%;
  padding-top: 28.66%;
  margin: 25px 0;
  position: relative;
}
.company-overview__item dd .hq_map iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.company-overview__item dd a[href^=tel] {
  pointer-events: none;
  color: #333333;
}
.company-overview__item dd address {
  line-height: 1.8;
}

.section__service__intro {
  background: linear-gradient(transparent 0%, transparent 30%, #E1EDE8 30%, #E1EDE8 100%);
  padding-bottom: 150px;
}
.section__service__intro .service__intro__lead h2 {
  color: #006835;
  font-size: 40px;
  font-weight: bold;
  text-align: center;
}
.section__service__intro .service__intro__cont {
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: wrap;
  margin-top: 85px;
}
.section__service__intro .service__intro__cont__item {
  width: calc(33.333% - 20px);
  background: #ffffff;
  box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.16);
  border-radius: 20px;
  margin-right: 30px;
  position: relative;
}
.section__service__intro .service__intro__cont__item::before {
  content: "";
  width: 1px;
  height: 96px;
  position: absolute;
  top: -50px;
  right: initial;
  bottom: initial;
  left: 50%;
  background: #333333;
  transform: translateX(-50%);
  z-index: initial;
}
.section__service__intro .service__intro__cont__item:nth-child(3n) {
  margin-right: 0;
}
.section__service__intro .service__intro__cont__item__pic {
  height: 200px;
  overflow: hidden;
}
.section__service__intro .service__intro__cont__item__pic img {
  width: 90%;
  margin: auto;
}
.section__service__intro .service__intro__cont__item__title {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  margin: 25px 0 40px;
}
.section__service__intro .service__intro__cont__item__title h3 {
  font-size: 40px;
  font-weight: bold;
  margin-left: 20px;
}
.section__service__intro .service__intro__cont__item__list {
  padding-left: 30px;
  padding-bottom: 40px;
}
.section__service__intro .service__intro__cont__item__list li {
  padding-left: 42px;
  position: relative;
}
.section__service__intro .service__intro__cont__item__list li::before {
  content: "";
  width: 32px;
  height: 1px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  background: #333333;
  transform: translateY(-50%);
  z-index: initial;
}
.section__service__intro .service__intro__cont__item__list li a {
  color: #333333;
  font-size: 18px;
}
.section__service__intro .service__intro__cont__item__list li + li {
  margin-top: 30px;
}
.section__service-detail {
  padding-top: 150px;
}
.section__service-detail .section__title {
  max-width: 1050px;
  margin: auto;
  font-size: 32px;
  font-weight: bold;
  padding-left: 50px;
  box-sizing: border-box;
}
.section__service-detail__item + .section__service-detail__item {
  margin-top: -3px;
}
.section__service-detail__item .outerbox {
  width: calc(100% - 50px);
  max-width: 1050px;
  margin: 0 auto;
  position: relative;
}
.section__service-detail__item .outerbox::after {
  content: "";
  width: 56px;
  height: 56px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: transparent url(../images/icon/round-arrow_orange.png) no-repeat center center/cover;
}
.section__service-detail__item .outerbox--strategy {
  border-radius: 0 0 0 70px;
}
.section__service-detail__item .outerbox--strategy::before {
  content: "";
  width: 56px;
  height: 56px;
  position: absolute;
  top: -28px;
  right: initial;
  bottom: initial;
  left: -28px;
  background: initial;
  transform: translateY(-50%);
  z-index: initial;
  background: transparent url(../images/icon/round-arrow_black.png) no-repeat center center/cover;
}
.section__service-detail__item .outerbox--strategy::after {
  left: -28px;
}
.section__service-detail__item .outerbox--creative {
  border-radius: 0 70px 0 0;
}
.section__service-detail__item .outerbox--creative::after {
  right: -28px;
}
.section__service-detail__item .outerbox--analysis::after {
  left: -28px;
}
.section__service-detail__item .middlebox {
  width: calc(100% - 70px);
  border-left: 3px solid #333333;
  border-bottom: 3px solid #333333;
  border-radius: 0 0 0 70px;
  padding: 150px 0;
}
.section__service-detail__item .middlebox--creative {
  border-left: 0;
  border-top: 3px solid #333333;
  border-right: 3px solid #333333;
  border-bottom: 3px solid #333333;
  border-radius: 0 70px 70px 0;
  margin-right: 0;
  margin-left: auto;
}
.section__service-detail__item .middlebox--analysis {
  border-top: 3px solid #333333;
  border-bottom: 0;
  border-radius: 70px 0 0 0;
}
.section__service-detail__item .innerbox {
  width: 100%;
  max-width: 1050px;
  border-radius: 20px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.16);
  padding: 80px 100px;
  position: relative;
  box-sizing: border-box;
}
.section__service-detail__item .innerbox::before {
  display: block;
  width: 100%;
  content: attr(data-en);
  font-family: "Open Sans", sans-serif;
  font-size: 140px;
  font-weight: bold;
  color: rgba(255, 126, 118, 0.2);
  text-transform: uppercase;
  text-align: center;
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.section__service-detail__item .innerbox--strategy {
  right: -75px;
}
.section__service-detail__item .innerbox--creative {
  left: -75px;
}
.section__service-detail__item .innerbox--creative::before {
  color: rgba(51, 186, 210, 0.2);
}
.section__service-detail__item .innerbox--analysis {
  right: -75px;
}
.section__service-detail__item .innerbox--analysis::before {
  color: rgba(161, 228, 197, 0.2);
}
.section__service-detail__item .item__title {
  display: flex;
  align-items: center;
  position: relative;
}
.section__service-detail__item .item__title h3 {
  font-size: 40px;
  font-weight: bold;
  margin-left: 20px;
}
.section__service-detail__item .item__title::before {
  content: "";
  width: 120px;
  height: 3px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #333333;
}
.section__service-detail__item .item__title::after {
  content: "";
  border-radius: 50%;
  width: 15px;
  height: 15px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #F3C11D;
  z-index: 2;
}
.section__service-detail__item .wrap {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
.section__service-detail__item .wrap img {
  width: 45%;
  margin-left: auto;
}
.section__service-detail__item .wrap p {
  width: 50%;
}
.section__service-detail__item .wrap p span {
  display: inline-block;
}
.section__service-detail__item .innerbox--strategy .item__title::before {
  left: -175px;
}
.section__service-detail__item .innerbox--strategy .item__title::after {
  left: -182px;
}
.section__service-detail__item .innerbox--creative .wrap {
  flex-direction: row;
}
.section__service-detail__item .innerbox--creative .wrap img {
  margin-left: 0;
  margin-right: auto;
}
.section__service-detail__item .innerbox--creative .item__title {
  justify-content: flex-end;
}
.section__service-detail__item .innerbox--creative .item__title::before {
  right: -175px;
}
.section__service-detail__item .innerbox--creative .item__title::after {
  right: -183px;
}
.section__service-detail__item .innerbox--analysis .item__title::before {
  left: -175px;
}
.section__service-detail__item .innerbox--analysis .item__title::after {
  left: -182px;
}
.section__service-detail__item ul {
  margin-top: 45px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.section__service-detail__item ul li {
  margin-right: 50px;
  width: calc(50% - 25px);
  margin-top: 40px;
}
.section__service-detail__item ul li:nth-child(2n) {
  margin-right: 0;
}
.section__service-detail__item ul li .head {
  background: #333333;
  padding: 30px 20px;
  border-radius: 20px 20px 0 0;
}
.section__service-detail__item ul li .head h4 {
  display: flex;
  align-items: center;
}
.section__service-detail__item ul li .head h4 .icon {
  flex-shrink: 0;
  background: #ffffff;
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background-position: center center;
  background-size: 40px 40px;
  background-repeat: no-repeat;
  margin-right: 15px;
}
.section__service-detail__item ul li .head h4 span:not(.icon) {
  display: inline-block;
  color: #ffffff;
  font-size: 20px;
  font-weight: bold;
}
.section__service-detail__item ul li .body {
  background: #FAFAFA;
  border-radius: 0 0 20px 20px;
  padding: 30px;
}
.section__service-detail__item ul li .body p {
  font-size: 14px;
  line-height: 25px;
}
.section__service-detail__item ul li .body p span {
  display: inline-block;
}
.section__service-detail__item ul li .body .more_btn {
  text-align: right;
  margin-top: 25px;
}
.section__service-detail__item ul li .body .more_btn a {
  display: inline-block;
  font-size: 16px;
  color: #333333;
  padding-right: 30px;
  position: relative;
}
.section__service-detail__item ul li .body .more_btn a::after {
  content: "";
  width: 18px;
  height: 11px;
  position: absolute;
  top: 50%;
  right: 0;
  bottom: initial;
  left: initial;
  background: transparent;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/svg/right_orange.svg);
  background-position: center;
  background-size: contain;
}
.section__service-detail__footer {
  width: 100%;
  padding: 50px 0;
  background: #3c8b86;
}
.section__service-detail__footer > .container {
  position: relative;
}
.section__service-detail__footer p {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
}
.section__service-detail__footer p span {
  display: inline-block;
}
.section__service-detail__footer img {
  width: 450px;
  height: auto;
  position: absolute;
  right: -225px;
  top: 50%;
  transform: translateY(-50%);
}
.section__search > .container {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.section__search .section_title h2 {
  color: #006835;
  font-size: 40px;
}
.section__not-found > .container {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.section__not-found .section_title {
  margin-bottom: 30px;
}
.section__not-found .section_title h2 {
  color: #006835;
  font-size: 40px;
}

.single__wp__cont {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.single__wp__left {
  width: 48%;
}
.single__wp__eyecatch {
  padding-top: 76%;
  position: relative;
  margin-bottom: 30px;
  border-radius: 6px;
  border: 1px solid #808080;
}
.single__wp__eyecatch .img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: transparent;
  transform: initial;
  z-index: initial;
  background-position: center center;
  background-size: cover;
  border-radius: 6px;
}
.single__wp__title h1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.7;
  margin-bottom: 40px;
}
.single__wp__content p {
  line-height: 1.7;
}
.single__wp__content p + p {
  margin-top: 30px;
}
.single__wp__content__box {
  margin: 50px 0 30px;
}
.single__wp__content__box .head {
  font-size: 20px;
  font-weight: bold;
  background: #333333;
  color: #ffffff;
  text-align: center;
  border-radius: 20px 20px 0 0;
  padding: 15px 0;
}
.single__wp__content__box .body {
  background: #FAFAFA;
  border-radius: 0 0 20px 20px;
  padding: 30px 25px;
}
.single__wp__content__box .body p {
  font-size: 18px;
  padding-left: 34px;
  position: relative;
}
.single__wp__content__box .body p::before {
  content: "";
  width: 24px;
  height: 24px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  background: initial;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/svg/check.svg);
  background-size: cover;
}
.single__wp__content__box .body p + p {
  margin-top: 23px;
}
.single__wp__right {
  width: 48%;
  margin-left: auto;
}
.single__wp__right p {
  margin-bottom: 45px;
}
.single__blog__cont {
  display: flex;
}
.single__blog__top h1 {
  font-size: 32px;
  font-weight: bold;
  line-height: 50px;
  padding-top: 25px;
  margin-bottom: 32px;
  position: relative;
}
.single__blog__top h1:before {
  content: "";
  width: 64px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 20px;
}
.single__blog__top time {
  display: block;
  font-size: 14px;
  text-align: right;
  margin-bottom: 15px;
}
.single__blog__top--update {
  margin-right: 12px;
}
.single__blog__eyecatch {
  width: 100%;
  margin-top: 80px;
}
.single__blog__eyecatch img {
  width: 100%;
  height: auto;
}
.single__blog__share {
  padding: 60px 0 40px;
}
.single__blog__share p {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
}
.single__blog__share p span {
  display: inline-block;
  padding-left: 44px;
  position: relative;
}
.single__blog__share p span::before {
  content: "";
  width: 32px;
  height: 32px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  background: initial;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/icon/share.png);
  background-position: center;
  background-size: contain;
}
.single__blog__share .sns__list {
  display: flex;
  flex-wrap: wrap;
}
.single__blog__share .sns__list li {
  width: calc((100% - 60px) / 4);
}
.single__blog__share .sns__list li + li {
  margin-left: 20px;
}
.single__blog__share .sns__list a {
  display: block;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  padding: 17px 10px;
  border-radius: 6px;
  background: #1DA1F2;
}
.single__blog__share .sns__list a:hover {
  opacity: 0.6 !important;
}
.single__blog__share .sns__list a img {
  display: inline-block;
  width: 20px;
  height: 20px;
}
.single__blog__share .sns__list a span {
  display: inline-block;
  color: #ffffff;
  margin-left: 8px;
  font-size: 14px;
}
.single__blog__share .sns__list a.facebook {
  background: #305097;
}
.single__blog__share .sns__list a.hatena {
  background: #00A4DE;
}
.single__blog__share .sns__list a.line {
  background: #00B900;
}
.single__blog__share .sns__list a.line span {
  text-transform: uppercase;
}
.single__blog__author {
  background: #F5F6F7;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
}
.single__blog__author .pic {
  width: 150px;
  height: 150px;
}
.single__blog__author .pic img {
  border-radius: 50%;
}
.single__blog__author .body {
  width: calc(100% - 182px);
  margin-left: auto;
}
.single__blog__author .body .name {
  color: #006835;
  margin-bottom: 25px;
}
.single__blog__author .body .name span {
  font-weight: bold;
}
.single__blog__related-posts {
  margin-top: 40px;
}
.single__blog__related-posts h2 {
  font-size: 28px;
  font-weight: bold;
  padding-top: 16px;
  margin-bottom: 30px;
  position: relative;
}
.single__blog__related-posts h2::before {
  content: "";
  width: 60px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: initial;
  z-index: initial;
  border-radius: 20px;
}
.single__blog__related-posts__list {
  display: flex;
  justify-content: initial;
  align-items: initial;
  flex-wrap: wrap;
}
.single__blog__related-posts__list > li {
  width: calc((100% - 80px) / 3);
  margin-right: 40px;
  margin-bottom: 25px;
}
.single__blog__related-posts__list > li:nth-of-type(3n) {
  margin-right: 0;
}
.single__blog__related-posts__list > li a {
  display: block;
}
.single__blog__related-posts__list > li a:hover .related-posts__eyecatch .img {
  transform: scale(1.1);
}
.single__blog__related-posts__list > li a .related-posts__eyecatch {
  overflow: hidden;
  padding-top: 55%;
  position: relative;
  border-radius: 6px;
}
.single__blog__related-posts__list > li a .related-posts__eyecatch .img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1);
  transition: transform 0.5s;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.single__blog__related-posts__list > li a h3 {
  font-size: 13px;
  font-weight: bold;
  line-height: 1.2307692308;
  margin-top: 18px;
}
.single__blog__related-posts__list > li a time {
  display: none;
}

.archive__blog__cont {
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.archive__wp__tab {
  margin-bottom: 80px;
}
.archive__wp__tab ul {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.archive__wp__tab ul li {
  width: calc((100% - 30px) / 3);
  border: 1px solid #808080;
  color: #808080;
  font-size: 20px;
  font-weight: bold;
  border-radius: 6px;
  text-align: center;
  padding: 10px 0;
  margin-right: 15px;
  box-sizing: border-box;
}
.archive__wp__tab ul li:nth-of-type(3n) {
  margin-right: 0;
}
.archive__wp__tab ul li:hover {
  cursor: pointer;
}
.archive__wp__tab ul li.is-active {
  border-color: #006835;
  background: #006835;
  color: #ffffff;
}
.archive__wp__cont__title {
  font-size: 40px;
  font-weight: bold;
  color: #006835;
  text-align: center;
  margin-bottom: 50px;
}
.archive__wp__cont .group {
  display: none;
}
.archive__wp__cont .group.is-show {
  display: block;
}
.archive__wp__cont__list {
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: wrap;
}
.archive__wp__cont__item {
  width: calc((100% - 120px) / 3);
  margin-bottom: 60px;
  margin-right: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.archive__wp__cont__item:nth-of-type(3n) {
  margin-right: 0;
}
.archive__wp__cont__item__top h2 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.7;
  margin-bottom: 10px;
}
.archive__wp__cont__item__top p {
  font-size: 14px;
  line-height: 1.7;
}
.archive__wp__cont__item__eyecatch {
  padding-top: 75%;
  position: relative;
  margin-bottom: 10px;
  border: 1px solid #808080;
  border-radius: 6px;
  overflow: hidden;
}
.archive__wp__cont__item__eyecatch img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: initial;
  transform: initial;
  z-index: initial;
  border-radius: 6px;
}

.primary {
  width: 70%;
}

.sidebar {
  width: 23%;
  margin-left: auto;
}
.sidebar .category {
  margin-bottom: 50px;
}
.sidebar .category h4 {
  font-size: 28px;
  font-weight: bold;
  padding-top: 20px;
  padding-left: 40px;
  position: relative;
}
.sidebar .category h4::before {
  content: "";
  width: 60px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: initial;
  z-index: initial;
  border-radius: 20px;
}
.sidebar .category h4::after {
  content: "";
  width: 28px;
  height: 28px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: 0;
  left: 0;
  background: initial;
  transform: initial;
  z-index: initial;
  background-image: url(../images/icon/tag.png);
  background-position: center center;
  background-size: cover;
}
.sidebar .category__list {
  margin-top: 30px;
}
.sidebar .category__list li {
  padding-left: 15px;
  position: relative;
}
.sidebar .category__list li::before {
  content: "-";
  /* width: 5px; */
  /* height: 1px; */
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 0;
  color: #333333;
  transform: translateY(-50%);
  z-index: initial;
}
.sidebar .category__list li + li {
  margin-top: 15px;
}
.sidebar .category__list a {
  color: #333333;
}
.sidebar .banner {
  margin-top: 24px;
}
.sidebar .banner__list li + li {
  margin-top: 40px;
}
.sidebar .banner__list li a {
  display: block;
}
.sidebar .banner__list li a:hover {
  opacity: 0.6 !important;
}
.sidebar .banner__list li a img {
  width: 100%;
}

.is_fixed {
  position: fixed;
  width: 248.4px;
  top: 80px;
  right: calc((100% - 1080px) / 2);
  z-index: 990;
}

.tr_reset {
  transform: initial;
}

.search_box_area .searchform {
  height: 100px;
  position: relative;
  max-width: 100%;
}
.search_box_area input.searchfield {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 100%;
  height: 64px;
  padding: 0 10px 0 25px;
  position: absolute;
  top: 0;
  left: 0;
  outline: 0;
  border: 1px solid #333333;
  border-radius: 6px;
  box-sizing: border-box;
}
.search_box_area input.searchsubmit {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 20px;
  right: 24px;
  background: none;
  padding: 0;
}

.category_label {
  display: none;
  width: 100%;
  overflow: auto;
  white-space: nowrap;
}
.category_label li {
  display: inline-block;
  font-size: 14px;
  color: #ffffff;
  background: #3D3D3F;
  border-radius: 6px;
  padding: 11px 13px 11px 40px;
  margin-right: 10px;
  position: relative;
}
.category_label li:before {
  content: "";
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  right: initial;
  bottom: initial;
  left: 13px;
  background: transparent;
  transform: translateY(-50%);
  z-index: initial;
  background-image: url(../images/icon/tag-w.png);
  background-position: center center;
  background-size: cover;
}
.category_label li a {
  color: #ffffff;
}

.pagination h2.screen-reader-text {
  display: none;
}
.pagination .nav-links {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 50px 0 30px;
}
.pagination .nav-links span, .pagination .nav-links a {
  display: inline-block;
  color: #333;
  background: #ffffff;
  font-size: 16px;
  border: 1px solid #808080;
  box-shadow: 0px 0px 3px rgba(128, 128, 128, 0.16);
  border-radius: 6px;
  padding: 10px 15px;
  margin: 0 10px;
}
.pagination .nav-links span:hover, .pagination .nav-links a:hover {
  opacity: 0.6 !important;
}
.pagination .nav-links span.current, .pagination .nav-links a.current {
  background: #333;
  color: #ffffff;
}

.entry {
  padding: 40px 0 60px;
}
.entry h2 {
  font-size: 24px;
  border-top: 3px solid #3c8b86;
  border-bottom: 3px solid #3c8b86;
  padding: 15px 0;
  margin: 60px 0 20px;
  font-weight: bold;
  letter-spacing: 0.05em;
}
.entry h3 {
  font-size: 22px;
  border-left: 7px solid #3c8b86;
  padding: 5px 20px 5px 10px;
  margin: 40px 0 15px;
  font-weight: bold;
  letter-spacing: 0.05em;
}
.entry h4 {
  font-size: 20px;
  margin: 30px 0 10px;
  position: relative;
  padding-left: 25px;
  font-weight: bold;
  letter-spacing: 0.05em;
}
.entry h4::before {
  content: "";
  width: 18px;
  height: 18px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: url(../images/svg/tick.svg) no-repeat center center/cover;
}
.entry h5 {
  font-size: 18px;
  margin: 30px 0 10px;
  letter-spacing: 0.05em;
}
.entry p {
  margin-bottom: 20px;
  line-height: 2;
  text-align: justify;
  font-weight: 500;
  letter-spacing: 0.05em;
}
.entry a {
  color: #FA6B58;
  text-decoration: none;
}
.entry a:hover {
  color: #DD523F;
}
.entry ul {
  padding-left: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
}
.entry ul > li {
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 1.5;
  list-style-type: disc;
  color: #333333;
}
.entry ol {
  padding-left: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
}
.entry ol > li {
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 1.5;
  list-style-type: decimal;
  color: #333333;
}
.entry li {
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 1.5;
  list-style-type: disc;
  color: #333333;
}
.entry a.cta-link {
  display: table;
  margin: 30px auto;
  text-align: center;
  font-weight: bold;
  padding: 15px 30px;
  width: initial;
  background-color: #fa6b58;
  box-shadow: 0 4px 0 #dd523f;
  border-radius: 6px;
  box-sizing: border-box;
  color: #ffffff !important;
}
.entry a.cta-link:hover {
  transform: translateY(4px);
  box-shadow: none !important;
  opacity: 1 !important;
}
.entry img {
  margin: 20px auto;
  max-width: 100%;
  height: auto;
}
.entry .marker-yellow {
  background: linear-gradient(to bottom, transparent 70%, #f3c11d 70%);
}
.entry .marker-green {
  background: linear-gradient(to bottom, transparent 70%, #3C8B86 70%);
}
.entry strong {
  font-weight: bold;
}
.entry table {
  color: #333;
  margin-bottom: 20px;
  line-height: 1.7;
}
.entry table thead th {
  background: #006835;
  color: #ffffff;
  padding: 10px 15px;
  border-right: #ffffff solid 1px;
  border-bottom: #ffffff solid 1px;
}
.entry table thead th:last-child {
  border-right: #006835 solid 1px;
}
.entry table tbody td {
  background: #ffffff;
  padding: 10px 15px;
  border-left: #006835 solid 1px;
  border-bottom: #006835 solid 1px;
  border-right: #006835 solid 1px;
  vertical-align: top;
}
.entry table tbody th {
  background: #006835;
  padding: 10px 15px;
  color: #ffffff;
  border-bottom: #ffffff solid 1px;
  vertical-align: top;
}
.entry table.darkgreen {
  border-collapse: collapse;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.7;
}
.entry table.darkgreen th {
  background: #006835;
  color: #ffffff;
}
.entry table.darkgreen th, .entry table.darkgreen td {
  border: solid 1px #ededed;
  padding: 6px 10px;
}
.entry > div.pointbox {
  position: relative;
  margin: 2em 0 1.5em;
  padding: 1em 1.5em;
  border: solid 3px #006835;
}
.entry > div.pointbox > ul {
  margin-top: 20px;
}
.entry > div.pointbox .box-title {
  position: absolute;
  display: inline-block;
  top: -27px;
  left: -3px;
  padding: 3px 10px;
  height: 25px;
  line-height: 25px;
  vertical-align: middle;
  font-size: 17px;
  background: #006835;
  color: #ffffff;
  font-weight: bold;
  border-radius: 5px 5px 0 0;
}
.entry > div.pointbox p {
  padding: 0;
  margin: 0;
}
.entry .mokuji {
  position: relative;
  margin: 2em 0;
  padding: 25px 10px 7px;
  border: solid 2px #006835;
}
.entry .mokuji > ul {
  margin-top: 20px;
}
.entry .mokuji .box-title {
  position: absolute;
  display: inline-block;
  top: -2px;
  left: -2px;
  padding: 3px 10px;
  height: 25px;
  line-height: 25px;
  vertical-align: middle;
  font-size: 17px;
  background: #006835;
  color: #ffffff;
  font-weight: bold;
}
.entry .mokuji p {
  padding: 0;
  margin: 0;
}
.entry iframe {
  width: 100%;
}

.blog-widget {
  display: flex;
  justify-content: space-around;
}
.blog-widget__ele {
  margin: 0 10px;
}
.blog-widget__ele a {
  position: relative;
  z-index: 2;
}

.entry__list {
  margin-top: 45px;
}
.entry__list > li {
  border-top: 1px solid #808080;
  padding: 30px 0;
}
.entry__list > li:last-of-type {
  border-bottom: 1px solid #808080;
}
.entry__list a {
  display: block;
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: wrap;
}
.entry__list a:hover .entry__eyecatch .img {
  transform: scale(1.1);
}
.entry__list a .left {
  width: 33%;
}
.entry__list a .right {
  width: 63%;
  margin-left: auto;
}

.entry__eyecatch {
  overflow: hidden;
  padding-top: 56%;
  position: relative;
  border-radius: 6px;
}
.entry__eyecatch .img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1);
  transition: transform 0.5s;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.entry__body {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.entry__body h3 {
  font-size: 20px;
  font-weight: bold;
  line-height: 32px;
  margin-bottom: 20px;
}
.entry__body time {
  display: block;
  font-size: 14px;
  color: #333333;
  font-style: normal;
}
.entry__body--update {
  margin-right: 12px;
}

.category_label.sp {
  display: none;
}

.archive .casestudy.content__main {
  padding-top: 35px;
}
@media (max-width: 767px) {
  .archive .casestudy.content__main {
    padding-top: 27px;
  }
}
.archive .casestudy .archive__wp p.center {
  margin-bottom: 44px;
  line-height: 32px;
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp p.center {
    text-align: center;
  }
}
@media (max-width: 767px) {
  .archive .casestudy .archive__wp p.center {
    margin-bottom: 31px;
  }
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp__tab {
    margin-bottom: 48px;
  }
}
.archive .casestudy .archive__wp__tab ul li.is-active {
  border-color: #3C8B86;
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp__tab ul li.is-active {
    background-color: #3C8B86;
  }
}
@media (max-width: 767px) {
  .archive .casestudy .archive__wp__tab ul li.tab {
    color: #3C8B86;
    border-color: #3C8B86;
  }
  .archive .casestudy .archive__wp__tab ul li.tab .tab_toggle {
    background-image: url(../images/icon/round-arrow_green.svg);
  }
}
.archive .casestudy .archive__wp__cont__title {
  color: #3C8B86;
}
@media (min-width: 768px) {
  .archive .casestudy .archive__wp__cont__title {
    margin-bottom: 38px;
    font-size: 32px;
  }
}
.archive .casestudy__lists {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-template-columns: repeat(auto-fill, minmax(min(250px, 100%), 1fr));
  grid-gap: 32px 28px;
  justify-content: center;
  width: 100%;
}
@media (max-width: 768px) {
  .archive .casestudy__lists {
    grid-gap: 24px 15px;
  }
}
.archive .casestudy__lists__item {
  padding-bottom: 20px;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.29);
  border-radius: 6px;
  overflow: hidden;
}
.archive .casestudy__lists__item__link {
  display: grid;
  grid-template-columns: 20px 1fr 20px;
}
.archive .casestudy__lists__item__link > *:not(img) {
  grid-column: 2;
}
.archive .casestudy__lists__item__link__img {
  grid-column: 1/4;
  margin-bottom: 16px;
  width: 100%;
  height: auto;
}
.archive .casestudy__lists__item__link h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
}
.archive .casestudy__lists__item__link p {
  margin-bottom: 10px;
  color: #707070;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.3;
}
.archive .casestudy__lists__item__link__cat {
  justify-self: start;
  padding: 6px 8px;
  color: #3C8B86;
  font-size: 12px;
  line-height: 1;
  border: 1px solid #3C8B86;
  border-radius: 4px;
}

.single-casestudy .breadcrumb-inner {
  margin-inline: auto;
  padding-block: 0 58px;
  max-width: 756px;
}

.single__casestudy {
  margin-inline: auto;
  width: 100%;
  max-width: 756px;
}
.single__casestudy__cont h1 {
  position: relative;
  margin-bottom: 32px;
  padding-top: 25px;
  font-size: 32px;
  font-weight: bold;
  line-height: 50px;
}
.single__casestudy__cont h1:before {
  content: "";
  width: 64px;
  height: 5px;
  position: absolute;
  top: 0;
  right: initial;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 20px;
}
@media (max-width: 768px) {
  .single__casestudy__cont h1 {
    margin-bottom: 25px;
    font-size: 22px;
    line-height: 35px;
  }
}
.single__casestudy__cont__cat {
  display: inline-block;
  margin-bottom: 40px;
  padding: 6px 8px;
  color: #3C8B86;
  font-size: 12px;
  line-height: 1;
  border: 1px solid #3C8B86;
  border-radius: 4px;
}
@media (max-width: 768px) {
  .single__casestudy__cont__cat {
    margin-bottom: 30px;
  }
}
.single__casestudy__cont__img {
  display: block;
  margin-bottom: 60px;
  width: 100%;
  height: auto;
}
@media (max-width: 768px) {
  .single__casestudy__cont__img {
    margin-bottom: 50px;
  }
}

.archive .webinar.content__main {
  padding-top: 35px;
}
@media (max-width: 767px) {
  .archive .webinar.content__main {
    padding-top: 27px;
  }
}
.archive .webinar .archive__wp p {
  margin-bottom: 44px;
  line-height: 32px;
}
@media (max-width: 767px) {
  .archive .webinar .archive__wp p {
    margin-bottom: 31px;
  }
}
.archive .webinar__lists {
  padding-block: 30px;
  border: 0 solid #DCDCDC;
  border-top-width: 1px;
}
@media (min-width: 769px) {
  .archive .webinar__lists {
    padding-inline: 40px;
  }
}
.archive .webinar__lists:last-of-type {
  border-bottom-width: 1px;
}
.archive .webinar__lists__link {
  display: grid;
  justify-content: start;
  grid-gap: 32px;
}
@media (min-width: 769px) {
  .archive .webinar__lists__link {
    grid-template-columns: 500px 1fr;
  }
}
@media (max-width: 768px) {
  .archive .webinar__lists__link {
    grid-gap: 25px;
  }
}
.archive .webinar__lists__link:hover {
  opacity: 0.8;
}
.archive .webinar__lists__link__img {
  width: 100%;
  height: auto;
}
.archive .webinar__lists__link__dtl {
  display: grid;
  place-content: start;
  place-items: start;
}
.archive .webinar__lists__link__dtl__status {
  display: grid;
  place-items: center;
  margin-bottom: 10px;
  padding-inline: 10px;
  height: 28px;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  border-radius: 4px;
}
.archive .webinar__lists__link__dtl__status.open {
  background-color: #3C8B86;
}
.archive .webinar__lists__link__dtl__status.close {
  background-color: #707070;
}
.archive .webinar__lists__link__dtl h3 {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: bold;
  line-height: 1.5;
}
.archive .webinar__lists__link__dtl .date, .archive .webinar__lists__link__dtl .venue {
  display: grid;
  align-items: center;
  grid-auto-flow: column;
  grid-gap: 16px;
  color: #191919;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}
.archive .webinar__lists__link__dtl .date::before, .archive .webinar__lists__link__dtl .venue::before {
  content: "";
  width: 24px;
  height: 24px;
  background: 50% 50%/contain no-repeat;
}
.archive .webinar__lists__link__dtl .date {
  margin-bottom: 8px;
}
.archive .webinar__lists__link__dtl .date::before {
  background-image: url(../images/icon/webinar-time.svg);
}
.archive .webinar__lists__link__dtl .venue {
  margin-bottom: 16px;
}
.archive .webinar__lists__link__dtl .venue::before {
  background-image: url(../images/icon/webinar-map.svg);
}
.archive .webinar__lists__link__dtl .category {
  display: grid;
  place-items: center;
  padding-inline: 8px;
  height: 24px;
  color: #3C8B86;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  border: 1px solid #3c8b86;
  border-radius: 4px;
}
.archive .webinar__lists__link p {
  margin-bottom: 10px;
  color: #707070;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.3;
}
.archive .webinar__lists__link__cat {
  justify-self: start;
  padding: 6px 8px;
  color: #3C8B86;
  font-size: 12px;
  line-height: 1;
  border: 1px solid #3C8B86;
  border-radius: 4px;
}

.page__header {
  margin-top: 80px;
  padding: 75px 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  z-index: 1;
}
.page__header:before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(51, 51, 51, 0.5);
  transform: initial;
  z-index: 0;
}
.page__header__title {
  position: relative;
  text-align: center;
}
.page__header__title > span {
  color: #ffffff;
  font-size: 20px;
  text-transform: uppercase;
}
.page__header__title h1, .page__header__title h2 {
  margin-top: 25px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.7;
}
.page__header__title h1 span, .page__header__title h2 span {
  display: inline-block;
}
.page__header__main {
  font-size: 50px;
}
.page__header__catch {
  font-size: 40px;
}
.page__scroll {
  width: 1px;
  height: 80px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: -40px;
  left: 50%;
  background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #333333 50%, #333333 100%);
  transform: initial;
  z-index: 1;
}
.page__title {
  margin-bottom: 80px;
  text-align: center;
}
.page__title span {
  display: block;
  font-size: 20px;
  text-transform: uppercase;
}
.page__title h1 {
  margin-top: 25px;
  font-size: 50px;
  line-height: 1.2;
  font-weight: bold;
  display: inline-block;
  position: relative;
  z-index: 2;
}
.page__title h1:before {
  content: "";
  width: 100%;
  height: 15px;
  position: absolute;
  top: initial;
  right: 0;
  bottom: 4px;
  left: 0;
  background: #F3C11D;
  transform: initial;
  z-index: -1;
}

.movie .section__concerns__item__desc, .movie .section__flow__item__right__bottom {
  min-height: initial;
}
.movie .section__plan__item:nth-child(2) h3, .movie .section__plan__item:nth-child(3) h3 {
  font-size: 24px;
}

.owned-media .section__concerns {
  padding-bottom: 90px;
}
.owned-media .section__concerns__solution {
  padding: 80px 0;
}
.owned-media .section__concerns__solution .section__title__left {
  display: block;
}
.owned-media .section__concerns__solution__cont {
  margin-bottom: 100px;
}
.owned-media .section__concerns__solution .container .solution {
  font-size: 24px;
  margin-bottom: 32px;
  text-align: center;
}
.owned-media .section__concerns__solution .container .solution .red{
  font-size: 32px;
  color: #FA6B58;
  background: linear-gradient(transparent 70%, #F3C11D 0%);
}
/*オウンドメディア構築・運営代行のサポート内容*/
.owned-media .section__concerns__solution .support-title{
  text-align: center;
}
.owned-media .section__concerns__solution .container .support {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: wrap;
  margin-top: 72px;
}
.owned-media .section__concerns__solution .container .support .item {
  width: 500px;
}
.owned-media .section__concerns__solution .container .support .item .content{
  margin-bottom: 48px;
}
.owned-media .section__concerns__solution .container .support .item .content img {
  width: 140px;
  height: auto;
  margin: 24px auto;
}
.owned-media .section__concerns__solution .container .support .item .content .h3_above{
  display: inline;
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  color: #3C8B86;
}
.owned-media .section__concerns__solution .container .support .item .content .h3_above::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  right: -120px;
  background-color: #808080;
  width: 100px;
  height: 1px;
}
.owned-media .section__concerns__solution .container .support .item .content h4{
  line-height: 1.5;
}
.owned-media .section__concerns__solution .container .support .item .content .desc{
  font-weight: 400;
}
.owned-media .section__concerns__solution .container .btn{
  margin-top: 8px;
}

@media (max-width: 768px) {
  .owned-media .section__concerns__solution {
    padding: 48px 0;
  }
  .owned-media .section__concerns__solution .container .support .item .content h4{
    font-size: 20px;
    text-align: center;
    margin-bottom: 16px;
  }
  .owned-media .section__concerns__solution .container .support .item .content img {
    width: 120px;
  }
}

.content-marketing .section__concerns__item__desc {
  min-height: 120px;
}
.content-marketing .section__plan__cont {
  margin-top: 150px;
}
.content-marketing .section__plan__item {
  margin-bottom: 60px;
}
.content-marketing .section__plan__item:nth-child(1) {
  position: relative;
}
.content-marketing .section__plan__item:nth-child(1):before {
  content: "検索上位実績多数！";
  padding: 15px 0;
  width: 100%;
  height: initial;
  position: absolute;
  top: -61px;
  right: 0;
  bottom: initial;
  left: 0;
  background: #FA6B58;
  transform: initial;
  z-index: 1;
  border-radius: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
}
.content-marketing .section__plan__item:nth-child(1):after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: -11px;
  right: initial;
  bottom: initial;
  left: 50%;
  background: initial;
  transform: translateX(-50%);
  z-index: 1;
  border-top: 20px solid #FA6B58;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
}
.content-marketing .section__plan__item:nth-child(3n) {
  margin-right: 0;
}
.content-marketing .section__plan__item:nth-child(4), .content-marketing .section__plan__item:nth-child(5), .content-marketing .section__plan__item:nth-child(6) {
  margin-bottom: 0;
}

.attribution .section__concerns {
  padding-bottom: 80px;
}
.attribution .section__concerns__item__desc {
  min-height: 120px;
}
.attribution .section__plan__item {
  width: 100%;
  margin-right: 0;
}
.attribution .section__plan__item__inner {
  padding: 40px 90px;
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: initial;
}
.attribution .section__plan__item__inner ul {
  padding: 30px 0 30px 65px;
  width: calc(100% - 380px);
}
.attribution .section__plan h3 {
  background-color: #006835;
}
.attribution .section__plan h3 > span:first-child {
  margin-right: 30px;
  font-size: 28px;
}
.attribution .section__plan h3 > span:last-child {
  font-size: 24px;
}
.attribution .section__plan h3 > span:last-child span {
  font-size: 40px;
}
.attribution .section__plan__left {
  width: 380px;
  border-right: 1px solid #333333;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: initial;
}
.attribution .section__plan__left img {
  width: 320px;
  height: auto;
}

.saiyou-ownedmedia .section__concerns__item__desc {
  min-height: initial;
}
.saiyou-ownedmedia .section__concerns__solution__item:nth-child(3n) {
  margin-right: 0;
}
.saiyou-ownedmedia .section__concerns__solution__item:nth-child(1), .saiyou-ownedmedia .section__concerns__solution__item:nth-child(2), .saiyou-ownedmedia .section__concerns__solution__item:nth-child(3) {
  margin-bottom: 60px;
}
.saiyou-ownedmedia .section__point__item:first-child {
  margin-bottom: 60px;
}

.contact__form {
  margin-top: 60px;
}

.post-type-archive-document .btn {
  margin-top: 40px;
  height: 48px;
}

.breadcrumb-inner {
  padding-top: 25px;
  font-size: 12px;
  color: #333333;
}
.breadcrumb-inner span {
  font-size: 12px;
  line-height: 20px;
  color: #333333;
}
.breadcrumb-inner a {
  text-decoration: underline;
  -webkit-text-decoration-color: #333333;
          text-decoration-color: #333333;
}
.breadcrumb-inner a:visited {
  color: #333333;
  opacity: 1;
}

.section__intro {
  padding-bottom: 150px;
}
.section__intro__cont {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: initial;
}
.section__intro__left {
  width: 600px;
}
.section__intro__left h2 {
  margin-bottom: 80px;
  font-size: 30px;
  font-weight: bold;
  line-height: 1.6;
}
.section__intro__left ul li {
  margin-bottom: 25px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: initial;
}
.section__intro__left ul li img {
  margin-right: 15px;
  width: 25px;
  height: auto;
}
.section__intro__left ul li p {
  font-size: 18px;
}
.section__intro__desc p {
  margin-bottom: 20px;
  text-align: justify;
}
.section__intro__right {
  width: 430px;
}
.section__intro__right img {
  width: 100%;
  height: auto;
}
.section__concerns {
  padding-top: 80px;
  padding-bottom: 210px;
  background-color: #F5F6F7;
  position: relative;
}
.section__concerns__cont {
  margin-top: 80px;
  display: flex;
  justify-content: initial;
  align-items: initial;
  flex-wrap: wrap;
}
.section__concerns__item {
  margin-right: 30px;
  width: calc(33.3333% - 20px);
}
.section__concerns__item:last-child {
  margin-right: 0;
}
.section__concerns__item img {
  margin: 0 auto 75px;
  width: 140px;
  height: auto;
}
.section__concerns__item__desc {
  min-height: 150px;
  padding: 30px;
  border-radius: 20px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  position: relative;
}
.section__concerns__item__desc:before {
  content: "";
  width: 1px;
  height: 80px;
  position: absolute;
  top: -60px;
  right: initial;
  bottom: initial;
  left: 50%;
  background: #333333;
  transform: translateX(-50%);
  z-index: 1;
}
.section__concerns__top {
  margin-bottom: 85px;
  padding-top: 150px;
  width: 100%;
  position: relative;
}
.section__concerns__top ul {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.section__concerns__top ul li {
  padding: 35px 0;
  width: 420px;
  box-sizing: border-box;
  color: #333333;
  text-align: center;
  font-size: 16px;
  background-color: #ffffff;
  border-radius: 20px 20px 0 20px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  position: absolute;
}
.section__concerns__top ul li:before {
  content: '';
  position: absolute;
  right: 0;
  bottom: -16px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 16px 16px 0;
  border-color: transparent #ffffff transparent transparent;
}
.section__concerns__top ul li:nth-child(1) {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
.section__concerns__top ul li:nth-child(2) {
  top: 150px;
  left: 0;
}
.section__concerns__top ul li:nth-child(3) {
  top: 150px;
  right: 0;
}
.section__concerns__top ul li:nth-child(4) {
  top: 276px;
  left: 0;
}
.section__concerns__top ul li:nth-child(5) {
  top: 276px;
  right: 0;
}
.section__concerns__top img {
  margin: 0 auto;
  width: 200px;
  height: auto;
}
.section__concerns__bottom p {
  margin-bottom: 20px;
  font-size: 18px;
}
.section__concerns__bottom p span {
  font-weight: bold;
}
.section__concerns__solution {
  padding-bottom: 80px;
  position: relative;
  background-color: #E1EDE8;
  border-radius: 0 0 60px 60px;
}
.section__concerns__solution .section__title__left {
  display: none;
}
.section__concerns__solution h3 {
  margin-top: 20px;
  text-align: center;
  font-size: 32px;
  font-weight: bold;
}
.section__concerns__solution h3 span {
  display: inline-block;
  position: relative;
  z-index: 2;
  line-height: 1.8;
}
.section__concerns__solution h3 span:before {
  content: "";
  width: 100%;
  height: 15px;
  position: absolute;
  top: initial;
  right: 0;
  bottom: 7px;
  left: 0;
  background: #F3C11D;
  transform: initial;
  z-index: -1;
}
.section__concerns__solution__cont {
  margin-top: 80px;
  display: flex;
  justify-content: initial;
  align-items: initial;
  flex-wrap: wrap;
}
.section__concerns__solution__item {
  margin-right: 30px;
  width: calc(33.3333% - 20px);
}
.section__concerns__solution__item:last-child {
  margin-right: 0;
}
.section__concerns__solution__item h4, .section__concerns__solution__item h3 {
  font-weight: bold;
  font-size: 20px;
  text-align: center;
}
.section__concerns__solution__item__icon {
  margin: 0 auto 30px;
  width: 125px;
  height: 125px;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
  position: relative;
}
.section__concerns__solution__item__icon:before {
  content: "";
  width: 1px;
  height: 40px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: -20px;
  left: 50%;
  background: #333333;
  transform: translateX(-50%);
  z-index: 1;
}
.section__concerns__solution__item__icon img {
  width: auto;
  height: 70px;
}
.section__concerns__solution__item__desc {
  margin-top: 20px;
}
.section__concerns__solution__item__desc p{
  font-weight: 400;
}
.section__concerns__line {
  width: 1px;
  height: 120px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: -60px;
  left: 50%;
  background: #333333;
  transform: translateX(-50%);
  z-index: 1;
}
.section__system {
  padding-top: 150px;
  padding-bottom: 210px;
  background-image: url(../images/bg/shikumi-bg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.section__system__bg__top {
  display: none;
  width: 100%;
  height: auto;
  position: absolute;
  top: 15px;
  right: 0;
  bottom: initial;
  left: -40px;
  background: initial;
  transform: initial;
  z-index: -1;
}
.section__system__bg__bottom {
  display: none;
  width: 100%;
  height: auto;
  position: absolute;
  top: initial;
  right: 0;
  bottom: 35px;
  left: 0;
  background: initial;
  transform: initial;
  z-index: -1;
}
.section__system__cont {
  margin-top: 80px;
  display: flex;
  justify-content: initial;
  align-items: center;
  flex-wrap: wrap;
}
.section__system__left {
  margin-right: 100px;
  width: 500px;
}
.section__system__left img {
  width: 100%;
  height: auto;
}
.section__system__right {
  width: calc(100% - 600px);
}
.section__system__right ul li {
  margin-bottom: 30px;
  padding: 16px 30px;
  background-color: #E1EDE8;
  border-radius: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: initial;
  align-items: center;
  flex-wrap: initial;
}
.section__system__right ul li:last-child {
  margin-bottom: 0;
}
.section__system__right ul li > span {
  margin-right: 30px;
  color: #ffffff;
  font-size: 44px;
  font-weight: bold;
}
.section__system__right ul li p {
  font-size: 20px;
  font-weight: bold;
}
.section__system__right ul li p span {
  display: inline-block;
}
.section__compare {
  padding-top: 150px;
  padding-bottom: 50px;
  overflow: hidden;
}
.section__compare__cont {
  margin-top: 80px;
}
.section__compare__item {
  padding-top: 50px;
  padding-bottom: 50px;
  width: 70%;
  box-sizing: border-box;
  position: relative;
}
.section__compare__item:before {
  content: "";
  width: initial;
  height: initial;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #F5F6F7;
  transform: initial;
  z-index: -1;
}
.section__compare__item:after {
  color: #F5F6F7;
  font-size: 180px;
  text-transform: uppercase;
  font-weight: bold;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
}
.section__compare__item:first-child {
  margin-bottom: 90px;
  padding-right: 50px;
}
.section__compare__item:first-child:before {
  left: calc((100vw - 1080px) / 2 * -1);
  border-radius: 0 20px 20px 0;
}
.section__compare__item:first-child:after {
  content: "before";
  right: -740px;
}
.section__compare__item:last-child {
  margin-left: auto;
  padding-left: 50px;
}
.section__compare__item:last-child:before {
  right: calc((100vw - 1080px) / 2 * -1);
  border-radius: 20px 0 0 20px;
}
.section__compare__item:last-child:after {
  content: "after";
  left: -600px;
}
.section__compare__item .section__title__left > span {
  color: #006835;
}
.section__compare__item .section__title__left h3 {
  color: #333333;
}
.section__compare__item ul {
  margin-top: 50px;
}
.section__compare__item ul li {
  margin-bottom: 25px;
  color: #333333;
  font-size: 18px;
}
.section__compare__item ul li:last-child {
  margin-bottom: 0;
}
.section__compare__item img {
  width: 230px;
  height: auto;
  position: absolute;
  top: 50%;
  right: 40px;
  bottom: initial;
  left: initial;
  background: none;
  transform: translateY(-50%);
  z-index: 1;
}
.section__feature {
  padding-bottom: 150px;
  background-color: #E1EDE8;
}
.section__feature__cont {
  margin-top: 80px;
}
.section__feature__cont ul {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  flex-wrap: wrap;
}
.section__feature__cont ul li {
  margin-right: 60px;
  margin-bottom: 60px;
  width: calc(50% - 30px);
  border-radius: 20px;
  background-color: #ffffff;
}
.section__feature__cont ul li:nth-child(2n) {
  margin-right: 0;
}
.section__feature__cont ul li:nth-child(5), .section__feature__cont ul li:nth-child(6) {
  margin-bottom: 0;
}
.section__feature__top {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  flex-wrap: initial;
}
.section__feature__top h4 {
  margin-right: 25px;
  width: calc(100% - 80px);
  padding: 15px 25px 10px;
  font-size: 28px;
  font-weight: bold;
  color: #006835;
  border-bottom: 1px solid #333333;
  line-height: initial;
}
.section__feature__num {
  padding: 17px 15px 10px;
  width: 80px;
  box-sizing: border-box;
  position: relative;
}
.section__feature__num:before {
  content: "";
  width: 100%;
  height: calc(100% + 20px);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #333333;
  transform: initial;
  z-index: 1;
  border-radius: 20px 0 20px 0;
}
.section__feature__num span {
  color: #ffffff;
  font-size: 40px;
  font-weight: bold;
  position: relative;
  z-index: 1;
}
.section__feature__bottom {
  padding: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: initial;
}
.section__feature__icon {
  width: 180px;
}
.section__feature__icon img {
  width: 120px;
  height: auto;
}
.section__feature__desc {
  width: calc(100% - 180px);
}
.section__feature__desc p {
  font-size: 18px;
}
.section__flow {
  padding-top: 150px;
}
.section__flow__cont {
  margin-top: 80px;
}
.section__flow__item {
  display: flex;
  justify-content: initial;
  align-items: stretch;
  flex-wrap: initial;
}
.section__flow__item:first-child .section__flow__item__right {
  border-top: 1px solid #E0E0E0;
}
.section__flow__item:last-child .section__flow__item__left:before, .section__flow__item:last-child .section__flow__item__left:after {
  display: none;
}
.section__flow__item__left {
  margin-right: 65px;
  padding-top: 30px;
  width: 110px;
  position: relative;
}
.section__flow__item__left:before {
  content: "";
  width: 3px;
  height: initial;
  position: absolute;
  top: 140px;
  right: initial;
  bottom: 2px;
  left: 50%;
  background: #3c8b86;
  transform: translateX(-50%);
  z-index: 1;
}
.section__flow__item__left:after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: initial;
  right: initial;
  bottom: 0;
  left: 50%;
  background: none;
  transform: translateX(-50%);
  z-index: 1;
  border-top: 10px solid #3c8b86;
  border-right: 7.5px solid transparent;
  border-left: 7.5px solid transparent;
}
.section__flow__item__right {
  padding: 30px 0;
  width: calc(100% - 175px);
  border-bottom: 1px solid #E0E0E0;
}
.section__flow__item__right__top {
  margin-bottom: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: initial;
}
.section__flow__item__right__top h3 {
  width: calc(100% - 150px);
  font-size: 24px;
  font-weight: bold;
}
.section__flow__item__right__bottom {
  min-height: 75px;
}
.section__flow__item__right__bottom p {
  font-weight: 400;
}
.section__flow__num {
  width: 110px;
  height: 110px;
  background-color: #3c8b86;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
}
.section__flow__num div span {
  display: block;
  text-align: center;
  font-weight: bold;
  color: #ffffff;
}
.section__flow__num div span:first-child {
  margin-bottom: 10px;
  font-size: 18px;
  text-transform: uppercase;
}
.section__flow__num div span:last-child {
  font-size: 32px;
}
.section__flow__icon {
  margin-right: 40px;
  width: 110px;
  height: 110px;
  background-color: #EFEFEF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
  position: relative;
}
.section__flow__icon:before {
  content: "";
  width: 1px;
  height: 32px;
  position: absolute;
  top: initial;
  right: initial;
  bottom: -25px;
  left: 50%;
  background: #333333;
  transform: translateX(-50%);
  z-index: 1;
}
.section__flow__icon img {
  width: 65px;
  height: auto;
}
.section__voice {
  padding-top: 80px;
}
.section__voice__cont {
  margin-top: 80px;
}
.section__voice__item {
  margin-bottom: 55px;
  padding: 50px 40px;
  background-color: #F5F6F7;
  border-radius: 20px;
  position: relative;
  z-index: -2;
  display: flex;
  justify-content: initial;
  align-items: flex-start;
  flex-wrap: wrap;
}
.section__voice__item:last-child {
  margin-bottom: 0;
}
.section__voice__item__left {
  margin-right: 25px;
  width: 200px;
  text-align: center;
}
.section__voice__item__left img {
  margin: 0 auto 20px;
  width: 140px;
  height: auto;
}
.section__voice__item__left p {
  font-size: 18px;
  font-weight: bold;
}
.section__voice__item__right {
  width: calc(100% - 225px);
}
.section__voice__item__right h3 {
  margin-bottom: 20px;
  display: flex;
  justify-content: initial;
  align-items: center;
  flex-wrap: initial;
}
.section__voice__item__right h3 img {
  margin-right: 20px;
  width: 30px;
}
.section__voice__item__right h3 > span {
  display: flex;
  flex-wrap: wrap;
}
.section__voice__item__right h3 > span span {
  font-size: 24px;
  font-weight: bold;
  display: inline-block;
  position: relative;
}
.section__voice__item__right h3 > span span:before {
  content: "";
  width: 100%;
  height: 12px;
  position: absolute;
  top: initial;
  right: 0;
  bottom: -1px;
  left: 0;
  background: #F3C11D;
  transform: initial;
  z-index: -1;
}
.section__point {
  padding-top: 100px;
}
.section__point__cont {
  margin-top: 80px;
}
.section__point__item {
  padding: 55px 0;
  position: relative;
  z-index: -1;
}
.section__point__item .section__title__left.ttl__sm span {
  display: block;
}
.section__point__item .section__title__left.ttl__sm h3 {
  font-size: 24px;
  color: #333333;
  display: inline-block;
  position: relative;
}
.section__point__item .section__title__left.ttl__sm h3:before {
  content: "";
  width: 100%;
  height: 12px;
  position: absolute;
  top: initial;
  right: 0;
  bottom: 0;
  left: 0;
  background: #F3C11D;
  transform: initial;
  z-index: -1;
}
.section__point__item.half__bg:before {
  content: "";
  width: 100%;
  height: 70%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: initial;
  left: 0;
  background: #F5F6F7;
  transform: initial;
  z-index: -1;
}
.section__point__item.full__bg {
  background-color: #F5F6F7;
}
.section__point__item.full__bg .section__point__img ul {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
}
.section__point__item.full__bg .section__point__img ul li {
  width: 25%;
}
.section__point__item.full__bg .section__point__img ul li img {
  margin: 0 auto 25px;
  width: 200px;
  height: auto;
}
.section__point__item.full__bg .section__point__img ul li p {
  font-size: 20px;
  text-align: center;
}
.section__point__item__inner {
  display: flex;
  justify-content: initial;
  align-items: center;
  flex-wrap: wrap;
}
.section__point__item__inner .section__point__img {
  margin-left: 160px;
  width: 300px;
}
.section__point__left {
  width: calc(100% - 460px);
}
.section__point__desc {
  margin-top: 30px;
  margin-bottom: 45px;
  width: 740px;
}
.section__point__img img {
  width: 100%;
  height: auto;
}
.section__plan {
  padding-top: 150px;
  padding-bottom: 80px;
  position: relative;
}
.section__plan:before {
  content: "";
  width: 100%;
  height: 50%;
  position: absolute;
  top: initial;
  right: 0;
  bottom: 0;
  left: 0;
  background: #F5F6F7;
  transform: initial;
  z-index: -1;
}
.section__plan__cont {
  margin-top: 80px;
  display: flex;
  justify-content: center;
  align-items: stretch;
  flex-wrap: wrap;
}
.section__plan__item {
  margin-right: 30px;
  width: calc(33.3333% - 20px);
  border-radius: 20px;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.16);
  background-color: #ffffff;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
}
.section__plan__item:last-child {
  margin-right: 0;
}
.section__plan__item__inner {
  padding: 45px 25px;
}
.section__plan__item__inner ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
.section__plan__item__inner ul li:last-child {
  margin-bottom: 0;
}
.section__plan__item__inner ul li img {
  margin-right: 10px;
  width: 25px;
  height: auto;
}
.section__plan__item__inner ul li p {
  font-size: 16px;
}
.section__plan__item__inner ul li p span {
  font-weight: bold;
}
.section__plan__item__top {
  width: 100%;
}
.section__plan h3 {
  padding-top: 20px;
  padding-bottom: 20px;
  border-radius: 20px 20px 0 0;
  background-color: #333333;
  font-size: 28px;
  color: #ffffff;
  font-weight: bold;
  text-align: center;
}
.section__plan h3 span {
  display: inline-block;
}
.section__plan__desc {
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #333333;
  font-size: 18px;
  font-weight: bold;
  color: #FA6B58;
}
.section__plan__desc span {
  display: inline-block;
}
.section__plan__desc.center {
  text-align: center;
}
.section__plan h4 {
  padding-top: 20px;
  padding-bottom: 20px;
  width: 100%;
  border-radius: 0 0 20px 20px;
  color: #191919;
  border-top: 1px solid #808080;
  font-weight: bold;
  text-align: center;
}
.section__plan h4 span:first-child {
  font-size: 24px;
}
.section__plan h4 span:nth-child(2) {
  font-size: 32px;
  text-transform: uppercase;
  color: #FA6B58;
}
.section__plan h4 span:nth-child(2) span {
  font-size: 28px;
}
.section__plan h4 span:nth-child(3) {
  font-size: 18px;
}
.section__plan__warn {
  margin-top: 40px;
  font-size: 18px;
  text-align: right;
}
.section__case {
  padding-top: 80px;
  padding-bottom: 80px;
  position: relative;
}
.section__case:before {
  content: "";
  width: 100%;
  height: initial;
  position: absolute;
  top: 500px;
  right: 0;
  bottom: 0;
  left: 0;
  background: #E1EDE8;
  transform: initial;
  z-index: -1;
}
.section__case__cont {
  margin-top: 80px;
  position: relative;
}
.section__case__slide {
  padding-top: 80px;
}
.section__case__slide__cont {
  margin-top: 80px;
}
.section__case__slide .movieslider__item__box {
  position: relative;
  background: #000;
}
.section__case__slide .movieslider__item__box:hover {
  cursor: pointer;
  cursor: hand;
}
.section__case__slide .movieslider__item__box img {
  opacity: 0.6;
}
.section__case__slide .movieslider__item__box:hover img {
  opacity: 0.8;
  transition: filter 1s cubic-bezier(0, 2.5, 0.2, 2.5);
}
.section__case__slide .play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}
.section__case__slide .play img {
  width: 60px !important;
  height: 100%;
  opacity: 1;
}
.section__case__slide .slick-dots {
  bottom: -50px;
  z-index: 1;
}
.section__case__slide .slick-dots li {
  margin: 0 20px;
}
.section__case__slide .slick-dots li button:before {
  font-size: 16px;
  color: #F5F6F7;
  opacity: 1;
}
.section__case__slide .slick-dots li.slick-active button:before {
  opacity: 1;
  color: #808080;
}
.section__faq {
  padding-top: 150px;
}
.section__faq__cont {
  margin-top: 80px;
}
.section__faq__cont ul li {
  margin-bottom: 50px;
}
.section__faq__cont ul li:last-child {
  margin-bottom: 0;
}
.section__faq__cont ul li h3 {
  margin-bottom: 20px;
  display: flex;
  justify-content: initial;
  align-items: center;
  flex-wrap: initial;
}
.section__faq__cont ul li h3 span {
  margin-right: 20px;
  width: 80px;
  height: 80px;
  background-color: #333333;
  border-radius: 50%;
  text-transform: uppercase;
  color: #ffffff;
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
}
.section__faq__cont ul li h3 p {
  width: calc(100% - 100px);
  font-size: 24px;
  font-weight: bold;
}
.section__faq__cont ul li p {
  font-size: 18px;
}
.section__faq__cont ul li p span {
  font-weight: bold;
}
.section__faq__cont ul li a {
  margin-top: 25px;
  display: inline-block;
  font-size: 18px;
  color: #FA6B58;
  font-weight: bold;
}

.solution__title {
  margin: 0 auto;
  padding: 25px 100px;
  display: table;
  border-radius: 20px;
  background-color: #333333;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  position: relative;
  top: -60px;
}
.solution__title h2 {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
}
.solution__title h2 img {
  margin-right: 15px;
  width: 380px;
  height: auto;
}
.solution__title h2 > span {
  font-size: 44px;
  font-weight: bold;
  color: #ffffff;
  letter-spacing: 2px;
}
.solution__title h2 > span span {
  font-size: 56px;
}

.single-document .content__main,
.single-blog .content__main,
.search .content__main,
.error404 .content__main {
  margin-top: 80px;
}

.single-document .breadcrumb-inner,
.single-blog .breadcrumb-inner,
.search .breadcrumb-inner,
.error404 .breadcrumb-inner {
  margin-bottom: 60px;
}
.single-document .content__main,
.single-blog .content__main,
.search .content__main,
.error404 .content__main {
  padding-top: 0px;
}

.entry .wpcf7 {
  max-width: 700px;
  margin: 0 auto;
}
.entry .wpcf7 p:last-of-type {
  margin-bottom: 0;
}
.entry .wpcf7 label {
  font-weight: bold;
  font-size: 15px;
}
.entry .wpcf7 label .required {
  color: #fff;
  margin-right: 0.3em;
  font-size: 0.8em;
  padding: 0.05em 0.25em 0.1em 0.25em;
  border-radius: 0.3em;
  white-space: nowrap;
  text-align: center;
  font-weight: normal;
  background-color: #df6f5f;
}
.entry .wpcf7 label .required.not {
  background-color: #f5b555;
}
.entry .wpcf7 input[type=text],
.entry .wpcf7 input[type=email] {
  margin-top: 5px;
  width: 100%;
  height: 45px;
  font-size: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 2px;
  padding: 6px 12px;
  display: block;
  color: #555;
  line-height: 1.42857143;
  box-sizing: border-box;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.entry .wpcf7 textarea {
  min-height: 150px;
  margin-top: 5px;
  padding: 6px 12px;
  width: 100%;
  display: block;
  font-size: 15px;
  color: #555;
  border: 1px solid #e0e0e0;
  border-radius: 2px;
  line-height: 1.42857143;
  box-sizing: border-box;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.entry .wpcf7 select {
  margin-top: 5px;
  width: 50%;
  height: 45px;
  font-size: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 2px;
  padding: 6px 8px;
  display: block;
  color: #555;
  line-height: 1.42857143;
  box-sizing: border-box;
  box-shadow: none !important;
  background: #ffffff;
  cursor: pointer;
}
.entry .wpcf7 input[type=submit] {
  margin: 40px auto 0;
  padding: 16px 0;
  width: 360px;
  background-color: initial;
  box-shadow: initial;
  border-radius: 6px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: initial;
  text-align: center;
  position: relative;
  color: #ffffff;
  font-size: 18px;
  background-color: #428BCA !important;
  box-shadow: 0 4px 0 #2b6699 !important;
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  transition: all 0.2s;
  cursor: pointer;
}
.entry .wpcf7 input[type=submit]:hover {
  transform: translateY(6px);
  box-shadow: none !important;
}
/*# sourceMappingURL=sub.css.map */