<?php
$casestudyLists = get_posts(array(
    'post_type' => 'casestudy',
    'posts_per_page' => -1,
));
foreach ($casestudyLists as $casestudy) {
    $cats = get_the_terms($casestudy->ID, 'casestudy-cat');
    $cat = $cats ? $cats[0] : null;
    $cat_names = array();
    if ($cats) {
        foreach ($cats as $cat_item) {
            $cat_names[] = $cat_item->name;
        }
    }
    $dispList = (object)[
        'title' => get_field('casestudy_title', $casestudy->ID),
        'category' => implode(', ', $cat_names),
        'company' => $casestudy->post_title,
        'image' => get_the_post_thumbnail_url($casestudy->ID),
        'link' => get_the_permalink($casestudy->ID)
    ];
    $getLists[] = $dispList;
    if ($cat) {
        $getLists_cat[$cat->slug][] = $dispList;
    }
}
$dispOpt = get_option('dispCheck');
?>
<?php get_header(); ?>

<div class="page__header" style="background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/photo/casestudy.jpg);">
    <div class="container">
        <div class="page__header__title">
            <span>CASE STUDY</span>
            <h1 class="page__header__main">導入事例</h1>
        </div>
    </div>
    <div class="page__scroll"></div>
</div>

<?php echo get_template_part('template-parts/breadcrumb'); ?>

<main class="content__main casestudy">
    <section class="section">
        <div class="archive__wp">
            <div class="container">
                <p class="center">様々な業界・業種のお客様の導入事例をご紹介いたします。</p>

                <?php if ($dispOpt === '1'): ?>
                    <div class="archive__wp__tab">
                        <ul class="tab__list">
                            <li class="tab all is-active">
                                すべて
                                <span class="tab_toggle"></span>
                            </li>
                            <li class="tab wp">ホワイトペーパー制作</li>
                            <li class="tab seo">記事・SEO</li>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="archive__wp__cont">

                    <div class="group is-show">
                        <?php if ($dispOpt === '1'): ?>
                            <h2 class="archive__wp__cont__title">すべて</h2>
                        <?php endif; ?>

                        <?php if (isset($getLists[0])): ?>

                            <ul class="casestudy__lists">

                                <?php foreach ($getLists as $list): ?>
                                    <li class="casestudy__lists__item">
                                        <a class="casestudy__lists__item__link" href="<?php echo $list->link; ?>">
                                            <img class="casestudy__lists__item__link__img" src="<?php echo $list->image; ?>" alt="<?php echo $list->company . ' ' . $list->title; ?>">

                                            <h3><?php echo $list->title; ?></h3>
                                            <p><?php echo $list->company; ?></p>
                                            <div class="casestudy__cat-list">
                                                <?php
                                                $cat_array = explode(', ', $list->category);
                                                foreach ($cat_array as $cat_name): ?>
                                                    <div class="casestudy__cat-list__label"><?php echo $cat_name; ?></div>
                                                <?php endforeach; ?>
                                            </div>
                                        </a>
                                    </li>
                                <?php endforeach; ?>

                            </ul>

                        <?php else: ?>
                            <p style="text-align:center; ">投稿がありません。</p>
                        <?php endif; ?>
                    </div>

                    <?php if ($dispOpt === '1'): ?>
                        <div class="group">
                            <h2 class="archive__wp__cont__title">ホワイトペーパー制作</h2>

                            <?php if (isset($getLists_cat['whitepaper'][0])) : ?>

                                <ul class="casestudy__lists">

                                    <?php foreach ($getLists_cat['whitepaper'] as $list): ?>
                                        <li class="casestudy__lists__item">
                                            <a class="casestudy__lists__item__link" href="<?php echo $list->link; ?>">
                                                <img class="casestudy__lists__item__link__img" src="<?php echo $list->image; ?>" alt="<?php echo $list->company . ' ' . $list->title; ?>">

                                                <h3><?php echo $list->title; ?></h3>
                                                <p><?php echo $list->company; ?></p>
                                                <div class="casestudy__lists__item__link__cats">
                                                    <?php
                                                    $cat_array = explode(', ', $list->category);
                                                    foreach ($cat_array as $cat_name): ?>
                                                        <div class="casestudy__lists__item__link__cat"><?php echo $cat_name; ?></div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>

                                </ul>

                            <?php else: ?>
                                <p style="text-align:center; ">投稿がありません。</p>
                            <?php endif; ?>
                        </div>

                        <div class="group">
                            <h2 class="archive__wp__cont__title">記事・SEO</h2>

                            <?php if (isset($getLists_cat['writing-seo'][0])) : ?>

                                <ul class="casestudy__lists">

                                    <?php foreach ($getLists_cat['writing-seo'] as $list): ?>
                                        <li class="casestudy__lists__item">
                                            <a class="casestudy__lists__item__link" href="<?php echo $list->link; ?>">
                                                <img class="casestudy__lists__item__link__img" src="<?php echo $list->image; ?>" alt="<?php echo $list->company . ' ' . $list->title; ?>">

                                                <h3><?php echo $list->title; ?></h3>
                                                <p><?php echo $list->company; ?></p>
                                                <div class="casestudy__lists__item__link__cats">
                                                    <?php
                                                    $cat_array = explode(', ', $list->category);
                                                    foreach ($cat_array as $cat_name): ?>
                                                        <div class="casestudy__lists__item__link__cat"><?php echo $cat_name; ?></div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>

                                </ul>

                            <?php else: ?>
                                <p style="text-align:center; ">投稿がありません。</p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</main>

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>