<?php
/*
Template Name: トップ
*/
?>

<?php echo get_header(); ?>

<!-- Start mv -->
<div class="mv">
  <div class="mv__bg">
    <div class="mv__bg__inner">
      <div class="img"></div>
      <div class="mv__content">
        <div class="container">
          <div class="mv__title">
            <h1>
              <span>コア業務に集中できる</span><br>
              <span>コンテンツマーケティングの</span><br>
              <span>支援会社</span>
            </h1>
          </div>
        </div>
      </div>
      <div class="mv__scroll">
        <div class="container">
          <a href="#scroll_anchor" class="smooth__scroll">
            <span class="txt">Scroll</span>
            <span class="bar"></span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- End mv -->

<!-- for the main contents -->
<main class="content__main" id="scroll_anchor">

  <section class="section">
    <div class="page01__service">
      <div class="container">
        <div class="section__title__left">
          <span>service</span>
          <h2>サービス</h2>
        </div>
        <div class="page01__service__cont">
          <div class="page01__service__item">
            <div class="page01__service__item__pic"><img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/illust/marketing-senryaku.png"
                alt="マーケティングの戦略設計"></div>
            <div class="page01__service__item__title">
              <img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/senryaku-icon.png"
                alt="戦略">
              <h3>戦略</h3>
            </div>
            <ul class="page01__service__item__list">
              <li><a href="<?php echo esc_url(home_url('/comonseo')); ?>">顧問SEO</a></li>
            </ul>
          </div>
          <div class="page01__service__item">
            <div class="page01__service__item__pic"><img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/illust/content-seisaku.png"
                alt="コンテンツ制作"></div>
            <div class="page01__service__item__title">
              <img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/seisaku-icon.png"
                alt="制作">
              <h3>制作</h3>
            </div>
            <ul class="page01__service__item__list">
              <li><a href="<?php echo esc_url(home_url('/content-marketing')); ?>">コンテンツマーケティング</a>
              </li>
              <li><a href="<?php echo esc_url(home_url('/whitepaper')); ?>">ホワイトペーパー制作</a></li>
              <li><a href="<?php echo esc_url(home_url('/writing')); ?>">記事制作</a></li>
              <li><a href="<?php echo esc_url(home_url('/movie')); ?>">動画制作</a></li>
              <li><a href="<?php echo esc_url(home_url('/owned-media')); ?>">オウンドメディア制作</a></li>
            </ul>
          </div>
          <div class="page01__service__item">
            <div class="page01__service__item__pic"><img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/illust/marketing-bpo.png"
                alt="運用と分析"></div>
            <div class="page01__service__item__title">
              <img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/icon/bunseki-icon.png"
                alt="分析">
              <h3>分析</h3>
            </div>
            <ul class="page01__service__item__list">
              <li><a href="<?php echo esc_url(home_url('/attribution')); ?>">アトリビューション分析</a></li>
            </ul>
          </div>
        </div>
        <div class="btn">
          <a href="<?php echo esc_url(home_url('/service')); ?>" class="btn__black">
            <span>サービス一覧を見る</span>
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg"
              alt="→">
          </a>
        </div>
      </div>
    </div>
  </section>

  <section class="section">
    <div class="page01__mission">
      <div class="page01__mission__bg"></div>

      <div class="container">
        <div class="section__title__left">
          <span>our mission</span>
          <h2>私たちの想い</h2>
        </div>
        <div class="page01__mission__cont">
          <div class="page01__mission__lead">
            <h3>
              <span>コンテンツで<br class="show_sp">価値ある出会いを。</span>
            </h3>
          </div>
          <div class="page01__mission__wrap">

            <div class="page01__mission__text">
              <p>
                Appmartは考えます。<br><span
                  class="fw_b">「せっかくいい商品があっても、<br>広告を出し続けないと見つけてもらえないなんて、そんなの寂しい。<br>皆がいいものを作ることに集中できたら、世の中が豊かになるのに」</span>
              </p>
              <p>
                <span
                  class="fw_b">「企業の想いは、ちゃんと"伝えたい相手"に伝わっているだろうか。<br>双方にとって価値のある出会いが、もっと増えたらいいのに」</span>
              </p>
              <p>
                私たちは、コンテンツマーケティングを通じて<br>企業とまだ見ぬ未来の顧客をつなげる架け橋になりたいと考えています。
              </p>
              <p>
                そう思うからこそ、日々変わりゆく市場における<br>マーケティング戦略やコンテンツ施策をアップデートし続けています。
              </p>
            </div>

            <img
              src="<?php echo get_template_directory_uri(); ?>/assets/images/illust/appmart-omoi.png"
              alt="Appmartの想い">
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="section">
    <div class="page01__client__top">
      <div class="container">
        <div class="section__title__left">
          <span>client</span>
          <h2>お取引先企業様</h2>
        </div>

        <?php get_template_part('template-parts/works_posts'); ?>

        <div class="page01__client__top__bglogo">client</div>
      </div>
    </div>
    <div class="page01__client__bottom">
      <div class="container">
        <h3>
          <span>多くの企業様と<br class="show_sp">お取引をいただいております。</span>
        </h3>
        <div class="page01__client__bottom__cont">
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
          <div class="page01__client__bottom__cont__item"></div>
        </div>
      </div>
    </div>
  </section>

  <section class="section">
    <div class="page01__blog">

      <div class="page01__blog__bg"></div>

      <div class="container">
        <div class="page01__blog__left">
          <div class="section__title__left">
            <span>blog</span>
            <h2>ブログ</h2>
          </div>
          <div class="btn hide_sp">
            <a href="<?php echo esc_url(home_url('/blog')); ?>" class="btn__black">
              <span>ブログ一覧を見る</span>
              <img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg"
                alt="→">
            </a>
          </div>
        </div>

        <ul class="page01__blog__right">

          <?php
                        $args = array(
                        'post_type' => 'blog',
                        'posts_per_page' => '3'
                        );
                        ?>

          <?php
                        $new_post = new WP_Query($args);
            if ($new_post->have_posts()) :
                while ($new_post->have_posts()) :
$new_post->the_post();
                    ?>

          <li class="page01__blog__right__item">
            <a href="<?php the_permalink(); ?>">
              <div class="page01__blog__right__item__eyecatch">
                <div class="img_inner">
                  <div class="img"
                    style="background-image: url(<?php the_post_thumbnail_url('full'); ?>);"></div>
                </div>
              </div>
              <div class="page01__blog__right__item__body">
                <h3><?php the_title(); ?></h3>
                <time datetime="<?php the_time('Y-m-d'); ?>"><?php the_time('Y.m.d'); ?></time>
              </div>
            </a>
          </li>

          <?php
                endwhile; ?>

          <?php else : ?>
          <p>投稿がありません</p>
          <?php endif; ?>

          <div class="btn show_sp">
            <a href="<?php echo esc_url(home_url('/blog')); ?>" class="btn__black">
              <span>ブログ一覧を見る</span>
              <img
                src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg"
                alt="→">
            </a>
          </div>

        </ul>
      </div>
    </div>
  </section>

  <!-- news -->
  <?php
  $news_args = array(
    'post_type' => 'news',
    'posts_per_page' => '5',
    'orderby' => 'date',
    'order' => 'DESC',
    'post_status' => 'publish'
  );
  $news_post = new WP_Query($news_args);

  // ニュース投稿が存在する場合のみセクションを表示
    if ($news_post->have_posts()) :
        ?>
  <section class="section">
    <div class="page01__news">
      <div class="container">
        <div class="section__title__left">
          <span>news</span>
          <h2>お知らせ</h2>
        </div>

        <ul class="news-list">
          <?php
            while ($news_post->have_posts()) :
                $news_post->the_post();
                ?>
          <li class="news-list__item">
            <div class="news-list__item--date">
              <time datetime="<?php the_time('Y-m-d'); ?>"><?php the_time('Y/m/d'); ?></time>
            </div>
            <div class="news-list__item--title">
              <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </div>
          </li>
          <?php
            endwhile;
            ?>
        </ul>

        <div class="btn hide_sp">
          <a href="<?php echo esc_url(home_url('/news')); ?>" class="btn__black">
            <span>お知らせ一覧を見る</span>
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg"
              alt="→">
          </a>
        </div>
        <div class="btn show_sp">
          <a href="<?php echo esc_url(home_url('/news')); ?>" class="btn__black">
            <span>お知らせ一覧を見る</span>
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg"
              alt="→">
          </a>
        </div>

      </div>
    </div>
  </section>
  <?php
    endif;
  wp_reset_postdata();
    ?>
</main>
<!-- main till here -->

<?php get_template_part('template-parts/section_about'); ?>

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>