@import './_service-mixin';
@import './variable';

// .service__page--header {
//   display: none;
// }

.content__main--service {
  padding-top: 40px !important;
}

body > div.fadein__cont.active > div.breadcrumb-inner {
    @media (max-width: 768px) {
      padding-top: 30px;
    }
}

//CTAボタン用
a.cta-link {
  display: table;
  margin: 30px auto;
  text-align: center;
  font-weight: bold;
  padding: 15px 30px;
  width: initial;
  background-color: #fa6b58;
  box-shadow: 0 4px 0 #dd523f;
  border-radius: 6px;
  box-sizing: border-box;
  color: $color--white !important;
  &:hover {
    transform: translateY(4px);
    box-shadow: none !important;
    opacity: 1 !important;
    color: #ffffff !important;
  }
}


.page-template-service {
  * {
    text-decoration: none;
    list-style: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-family: '游ゴシック', sans-serif;
    font-weight: 700;
    color: #191919;
  }

  .pc {
    display: block;

    @media (max-width: 768px) {
      display: none!important;
    }
  }

  .sp {
    display: none;

    @media (max-width: 768px) {
        display: block;
    }
  }

  .red {
    color: #FA6B58;
  }

  .underline {
    background: linear-gradient(transparent 70%, #F3C11D 0%);
    display: inline;
    position: relative;
    z-index: 2;
  }

  .h2_above {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 14px;
    color: #3C8B86;
    text-align: center;
    display: block;
    margin-bottom: 12px;

    @media (max-width: 768px) {
      font-size: 12px;
    }
  }

  h2 {
    font-size: 32px;
    text-align: center;

    @media (max-width: 768px) {
      font-size: 28px;
      line-height: 42px;
    }
  }

  img {
    max-width: 100%;
  }

  .mainvisual-section {
    padding: 40px 0 0 0;
    text-align: center;
    @media (max-width: 768px) {
      padding-top: 0px;
    }

    .service-above {
      padding: 40px 0 77px;
      height: 240px;
      width: 100%;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center center;
      position: relative;

      @media (max-width: 768px) {
        padding: 38px 0 40px;
      }

      .scroll-line {
        @include absolute(1px, 80px, initial, initial, -40px, 50%, linear-gradient(to bottom, $color--white 0%, $color--white 50%, $color--text 50%, $color--text 100%), initial, 1);
        @media (max-width: 768px) {
          @include absolute(1px, 40px, initial, initial, -20px, 50%, linear-gradient(to bottom, $color--white 0%, $color--white 50%, $color--text 50%, $color--text 100%), initial, 1);
        }
      }

      &:before {
          content: '';
          @include absolute(100%, 100%, 0, 0, 0, 0, rgba(51, 51, 51, .5), initial, 0);
      }

      .h1_above {
        position: relative;
        z-index: 2;
        font-size: 14px;
        font-weight: 400;
        color: #fff;
        display: block;

        @media (max-width: 768px) {
          font-size: 12px;
        }
      }

      h1 {
        font-size: 40px;
        margin: 13px 0 31px;
        color: #fff;
        display: inline-block;

        @media (max-width: 768px) {
          font-size: 24px;
          margin: 10px 0 26px;
        }
      }

      .h1_bottom {
        z-index: 2;
        position: relative;
        font-size: 20px;
        color: #fff;

        @media (max-width: 768px) {
          font-size: 16px;
          line-height: 200%;
        }
      }
    }

    .catch-copy {
      font-size: 24px;
      display: inline-block;
      position: relative;

      @media (max-width: 768px) {
        font-size: 20px;
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        width: 2px;
        height: 26px;
        background-color: #191919;

        @media (max-width: 768px) {
          display: none;
        }
      }

      &::before {
        left: -20px;
        transform: rotate(-20deg);
      }

      &::after {
        right: -20px;
        transform: rotate(20deg);
      }
    }

    &__container {
      text-align: center;
      width: 100%;

      .bottom {
        max-width: 1080px;
        margin: 0 auto;
        padding: 25px 0 0 0;

        @media (max-width: 768px) {
          padding: 32px 20px 24px;
        }

        .breadcrumb-inner {
          padding-top: 0;
          text-align: left;
          &>.container>.breadcrumb-area {
            &>span>a>span {
              font-weight: 400;
              font-size: 14px;

              @media (max-width: 768px) {
                font-size: 12px;
              }
            }
            &>span>span {
              font-weight: 400;
              font-size: 14px;

              @media (max-width: 768px) {
                font-size: 12px;
              }
            }
          }
        }


        .client {
          margin-top: 48px;

          @media (max-width: 768px) {
            margin-top: 32px;
          }

          &__list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;

          }
          &__img {
            width: 14.285%;
            @media (max-width: 768px) {
              width: 33.3%;
            }
          }

        }

      }
    }
  }

  .benefit-section {
    @include section-padding();

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }

      h2 {

        .sp_mini {

          @media (max-width: 768px) {
            font-size: 16px;
            display: block;
          }
        }
      }

      .benefit {
        margin: 48px auto 0;
        max-width: 880px;

        @media (max-width: 768px) {
          margin-top: 32px;
        }

        .item {
          display: flex;
          align-items: center;

          &:not(:first-of-type) {
            margin-top: 42px;

            @media (max-width: 768px) {
              margin-top: 40px;
            }
          }

          .left {
            width: 73px;
            margin-right: 20px;

            @media (max-width: 768px) {
              min-width: 48px;
              max-width: 48px;
              margin-right: 17px;
            }
          }

          .right {

            .h3_above {
              font-family: 'Open Sans', sans-serif;
              font-weight: 600;
              font-size: 14px;
              position: relative;
              color: #3C8B86;

              @media (max-width: 768px) {
                font-size: 12px;
              }

              &::before {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                margin: auto;
                right: -120px;
                background-color: #808080;
                width: 100px;
                height: 1px;
              }
            }

            h3 {
              font-size: 24px;
              margin-top: 12px;

              @media (max-width: 768px) {
                font-size: 16px;
                line-height: 200%;
              }
            }
          }
        }
      }
    }
  }

  .problem-section {
    @include section-padding();
    background-color: #F5F6F7;

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .problem {
        margin-top: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media (max-width: 768px) {
          display: block;
        }

        .item {
          width: 340px;
          text-align: center;
          margin-top: 36px;

          @media (max-width: 768px) {
            width: unset;
            display: flex;
            justify-content: space-between;
            align-items: center;
            &:not(:first-of-type) {
              margin-top: 36px;
            }
          }


          .thought {

            .bubble {
              display: flex;
              align-items: center;
              // justify-content: center;
              position: relative;
              width: 320px;
              height: 77px;
              background-color: #fff;
              border-radius: 8px 8px 0 8px;
              // padding: 16px 25px 0;
              padding: 0 25px;
              text-align: left;
              margin-bottom: 20px;
              @media (max-width: 768px) {
                width: 220px;
                height: 80px;
                padding: 0 15px;
              }
              &:first-child {
                margin-left: 20px;
                @media (max-width: 768px) {
                  margin-left: 0;
                }

              }
              &::before {
                content: '';
                position: absolute;
                right: 0;
                bottom: -16px;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 0 16px 16px 0;
                border-color: transparent #ffffff transparent transparent;

                @media (max-width: 768px) {
                  bottom: -12px;
                  border-width: 0 12px 12px 0;
                }
              }
            }

            .bubble-rv {
              @extend .bubble;
              @media (max-width: 768px) {
                border-radius: 8px 8px 8px 0;
                &::before {
                  left: 0;
                  transform: rotate(-90deg);
                }
              }
            }

            p {
              font-size: 15px;
              color: #808080;
              @media (max-width: 768px) {
                font-size: 14px;
              }

              }

          }

          img {
            width: 110px;
            margin: 0 auto;
            margin-top: 30px;

            @media (max-width: 768px) {
              width: 80px;
              margin: 0 0 0 36px;
            }
          }

          @media (max-width: 768px) {

            &:nth-child(2) {
              flex-direction: row-reverse;

              .thought {

                p {
                  border-radius: 8px 8px 8px 0;

                  &::before {
                    right: unset;
                    left: 0;
                    border-width: 16px 16px 0 0;
                    border-color: #ffffff transparent transparent transparent;
                  }
                }
              }

              img {
                margin: 0 36px 0 0;
              }
            }
          }
        }
      }
    }
  }

  .strength-section {
    @include section-padding();

    .container {
      max-width: 1080px;
      margin: 0 auto;

        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .middle {
        text-align: center;
        margin-bottom: 48px;

        @media (max-width: 768px) {
          margin-bottom: 32px;
        }

        .mini {
          font-size: 24px;
          display: block;
          line-height: 48px;

          @media (max-width: 768px) {
            font-size: 16px;
            line-height: 42px;
          }

          .gray {
            color: #808080;
          }
        }

        .big {
          font-size: 32px;
          line-height: 48px;
          display: block;

          @media (max-width: 768px) {
            font-size: 28px;
            line-height: 42px;
          }
        }
      }

      .strength {
        margin-top: 80px;

        @media (max-width: 768px) {
          margin-top: 53px;
        }

        .item {
          display: flex;
          justify-content: space-between;
          align-items: center;

          @media (max-width: 768px) {
            display: block;
          }

          &:not(:first-of-type) {
            margin-top: 100px;

            @media (max-width: 768px) {
              margin-top: 68px;
            }
          }

          .content {
            margin-right: 64px;
            position: relative;

            @media (max-width: 768px) {
              margin: 0;
            }

            .h3_above {
              display: inline;
              font-family: 'Open Sans', sans-serif;
              font-weight: 600;
              font-size: 14px;
              position: relative;
              color: #3C8B86;

              @media (max-width: 768px) {
                font-size: 12px;
              }

              &::after {
                content: '\A';
                white-space: pre;
              }

              &::before {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                margin: auto;
                right: -120px;
                background-color: #808080;
                width: 100px;
                height: 1px;
              }
            }

            h3 {
              display: inline-block;
              font-size: 24px;
              margin: 32px 0;
              line-height: 1.5;

              @media (max-width: 768px) {
                font-size: 20px;
                margin: 24px 0 32px;
                display: inline-block;
              }

              .red {
                font-size: 32px;

                &::before {
                  bottom: 5px;

                  @media (max-width: 768px) {
                    bottom: .5px;
                  }
                }

                @media (max-width: 768px) {
                  font-size: 24px;
                }
              }
            }

            .desc {
              font-size: 16px;
              line-height: 200%;
              font-weight: 400;
            }

            .number {
              font-family: 'Roboto', sans-serif;
              font-weight: 500;
              position: absolute;
              font-size: 120px;
              color: #F0F0F0;
              top: 0;
              // top: 25%;
              right: 0;
              line-height: 48px;
              align-self: center;

              @media (max-width: 768px) {
                top: 0;
                font-size: 80px;
              }
            }
          }

          .image {
            min-width: 476px;

            @media (max-width: 768px) {
              min-width: unset;
              margin-top: 40px;
            }
          }

          &:nth-of-type(even) {
            flex-direction: row-reverse;

            .content {
              margin-right: 0;
              margin-left: 64px;

              @media (max-width: 768px) {
                margin: 0;
              }
            }
          }
        }
      }
    }
  }

  .work-section {
    @include section-padding();
    background-color: #EBF9F3;

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .h2_bottom {
        margin-top: 32px;
        font-size: 16px;
        line-height: 200%;
        font-weight: 400;
        text-align: center;

        @media (max-width: 768px) {
          margin-top: 40px;
          text-align: left;
        }
      }

      .work {
        margin-top: 32px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 32px 48px;

        @media (max-width: 768px) {
          display: block;
        }

        .item {
          -ms-grid-row-align: end;
          align-self: flex-end;

          &:not(:first-of-type) {

            @media (max-width: 768px) {
              margin-top: 24px;
            }
          }

          .images {
            display: flex;
            justify-content: space-between;
            align-items: center;

            @media (max-width: 768px) {
              justify-content: space-between;
            }

            .work__img--2 {
              width: 250px;
              box-shadow: 0 3px 6px rgba($color: #000000, $alpha: 0.16);

              &:not(:first-of-type) {

                @media (max-width: 768px) {
                  margin-left: 18px;
                }
              }

              @media (max-width: 768px) {
                width: 160px;
              }
            }

            .work__img--3 {
              width: 132px;
              box-shadow: 0 3px 6px rgba($color: #000000, $alpha: 0.16);

              @media (max-width: 768px) {
                width: 100px;
              }
            }
          }

          p {
            font-size: 16px;
            text-align: center;
            margin-top: 16px;

            @media (max-width: 768px) {
              margin-top: 12px;
            }
          }

        }
      }
    }
  }

  .voice-section {
    padding: 80px 0;

    @media (max-width: 768px) {
      padding: 48px 0 48px 0;
    }

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .voice {
        margin-top: 32px;
        display: flex;
        justify-content: space-between;

        @media (max-width: 768px) {
          display: block;
        }

        .item {
          padding: 24px;
          text-align: center;
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 3px 6px rgba($color: #000000, $alpha: 0.16);
          width: 320px;

          @media (max-width: 768px) {
            padding: 20px 24px;
            width: 100%;
          }

          &:not(:first-of-type) {

            @media (max-width: 768px) {
              margin-top: 32px;
            }
          }

          .heading {

            @media (max-width: 768px) {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 20px;
            }

            h3 {
              font-size: 16px;

              @media (max-width: 768px) {
                font-size: 14px;
              }
            }

            img {
              width: 92px;
              margin: 16px 0 19px;
              margin-right: auto;
              margin-left: auto;

              @media (max-width: 768px) {
                width: 67px;
                margin: 0;
              }
            }
          }

          .red {
            font-size: 18px;
            text-align: left;

            @media (max-width: 768px) {
              margin-top: 20px;
            }
          }

          .desc {
            text-align: left;
            font-size: 14px;
            line-height: 200%;
            font-weight: 400;
            margin-top: 21px;

            @media (max-width: 768px) {
              margin-top: 25px;
            }
          }
        }
      }
    }
  }

  .price-section {
    padding: 80px 0;
    background-color: #EBF9F3;

    @media (max-width: 768px) {
      padding: 48px 0 48px 0;
    }

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .box {
        margin-top: 32px;

        h3 {
          background: #191919;
          color: #fff;
          text-align: center;
          font-size: 24px;
          padding: 18px 0;
          border-radius: 8px 8px 0 0;

          @media (max-width: 768px) {
            font-size: 18px;
          }

          span {
            color: #fff;

            @media (max-width: 768px) {
              font-size: 14px;
            }
          }
        }

        .content {
          padding: 24px 0 52px;
          background-color: #fff;
          border-radius: 0 0 8px 8px;
          text-align: center;

          @media (max-width: 768px) {
            // padding: 0 17.5px;
            padding: 0;
            padding-bottom: 37px;
          }

          .flex {
            display: flex;
            justify-content: center;

            @media (max-width: 768px) {
              display: block;
            }

            .item {
              width: 280px;
              padding: 0 40px;

              @media (max-width: 768px) {
                padding: 16px 0;
                margin: 0 auto;
              }

              &:not(:first-of-type) {
                border-left: 1px solid #808080;

                @media (max-width: 768px) {
                  border-left: none;
                  border-top: 1px solid #808080;
                }
              }

              .heading {

                @media (max-width: 768px) {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                }
                }

                img {
                  width: 120px;
                  margin: 0 auto;

                  @media (max-width: 768px) {
                    margin: 0;
                    margin-right: 18px;
                    width: 80px;
                  }
                }

                  h4 {
                    margin-top: 0px;
                    font-size: 18px;

                    @media (max-width: 768px) {
                      font-size: 16px;
                      margin: 0;
                    }
                  }

              .example {
                margin-top: 24px;
                text-align: left;
                @media (max-width: 768px) {
                  margin-top: 8px;
                  padding: 0 9px;
                }
                &>li {
                  font-size: 14px;
                  line-height: 2;
                }
              }
            }
          }

          .price-wrap {
            margin: 28px 0 16px;
            display: flex;
            justify-content: center;
            align-items: flex-end;

            @media (max-width: 768px) {
              margin: 0 0 16px;
            }

            .term {
              font-size: 18px;
              line-height: 28px;
              margin-right: 28px;
              color: #808080;
            }

            .price {
              font-size: 18px;
              line-height: 28px;

              .red {
                font-size: 32px;
                line-height: 28px;
              }
            }
          }

          .rice {
            text-align: center;
            margin-bottom: 24px;
            font-size: 12px;
            color: #808080;

            @media (max-width: 768px) {
              margin-bottom: 16px;
              font-size: 11px;
            }
          }

          .ul {
            font-size: 20px;
            display: inline-block;

            @media (max-width: 768px) {
              font-size: 16px;
            }

            span {

              &::before {
                height: 6px;
                bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  .flow-section {
    padding: 80px 0;

    @media (max-width: 768px) {
      padding: 48px 0 48px 0;
    }

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .flow {
        margin-top: 42px;

        @media (max-width: 768px) {
          margin-top: 32px;
        }

        .item {
          display: flex;

          &:not(:first-of-type) {
            margin-top: 25px;

            @media (max-width: 768px) {
              margin-top: unset;
              padding-top: 15px;
            }
          }

          &:not(:last-of-type) {
            position: relative;

            &::before {
              content: '';
              position: absolute;
              left: 53px;
              bottom: 7px;
              background-color: #3C8B86;
              width: 2px;
              height: calc(100% - 110px);

              @media (max-width: 768px) {
                left: 24px;
                height: calc(100% - 50px);
              }
            }

            &::after {
              content: '';
              position: absolute;
              left: 50px;
              bottom: 1px;
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 6.86px 4px 0 4px;
              border-color: #3C8B86 transparent transparent transparent;

              @media (max-width: 768px) {
                left: 21px;
              }
            }
          }

          .number_circle {
            margin-right: 40px;
            width: 110px;
            height: 110px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #3C8B86;

            @media (max-width: 768px) {
              min-width: 50px;
              max-width: 50px;
              min-height: 50px;
              max-height: 50px;
              margin-right: 20px;
            }

            .step {
              font-family: "Open Sans", sans-serif;
              font-weight: 600;
              font-size: 14px;
              font-weight: 500;
              color: #fff;

              @media (max-width: 768px) {
                font-size: 10px;
              }
            }

            .number {
              font-family: "Open Sans", sans-serif;
              font-weight: 600;
              font-size: 32px;
              font-weight: 500;
              color: #fff;
              line-height: 1;

              @media (max-width: 768px) {
                font-size: 18px;
                margin-top: 0px;
              }
            }

          }

          .content {
            padding-bottom: 25px;
            border-bottom: 1px solid #E0E0E0;
            flex-grow: 1;

            .heading {
              display: flex;
              align-items: center;
              position: relative;

              &::before {
                content: '';
                position: absolute;
                left: 53px;
                bottom: -10px;
                background-color: #808080;
                width: 1px;
                height: 20px;

                @media (max-width: 768px) {
                  display: none;
                }
              }

              img {
                width: 110px;
                margin-right: 48px;

                @media (max-width: 768px) {
                  width: 32px;
                  margin-right: 20px;
                }
              }

              h3 {
                font-size: 24px;

                @media (max-width: 768px) {
                  font-size: 17px;
                }
              }
            }

            .desc {
              font-size: 16px;
              font-weight: 400;
              line-height: 28px;
              margin-top: 20px;

              @media (max-width: 768px) {
                margin-top: 17px;
                font-size: 14px;
              }
            }
          }
        }
      }

      .rice {
        margin-top: 32px;
        font-size: 14px;
        line-height: 200%;
        font-weight: 400;
        color: #808080;
      }
    }
  }

  .faq-section {
    padding: 80px 0 80px 0;
    background-color: #F5F6F7;

    @media (max-width: 768px) {
      padding: 48px 0 20px 0;
    }

    .container {
      max-width: 1080px;
      margin: 0 auto;
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }


      .faq {
        margin-top: 32px;

        &__items {
          display: flex;
          flex-direction: column;
        }

        &__item {
          &:not(:first-child) {
            margin-top: 32px;
            @media (max-width: 768px) {
              margin-top: 20px;
            }
          }

          &:not(:last-child) {
            border-bottom: 1px solid #E0E0E0;
          }

          &-q {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            @media (max-width: 768px) {
              margin-bottom: 24px;
            }

            &--icon {
              width: 64px;
              height: 64px;
              @media (max-width: 768px) {
                width: 40px;
                height: 40px;
              }
            }

            &--text {
              font-size: 20px;
              line-height: 1.5;
              padding-left: 24px;
              @media (max-width: 768px) {
                font-size: 16px;
                padding-left: 10px;
              }
            }
          }

          &-a {
            margin-bottom: 32px;
            @media (max-width: 768px) {
              margin-bottom: 20px;
            }

            &--text {
              font-size: 16px;
              font-weight: 400;
            }
          }

          &-mc {
            margin-bottom: 0;
          }

        }

        dl {

          div {
            padding-bottom: 25px;
            border-bottom: 1px solid #E0E0E0;

            @media (max-width: 768px) {
              padding-bottom: unset;
              border-bottom: unset;
            }

            &:not(:first-of-type) {
              margin-top: 39px;

              @media (max-width: 768px) {
                padding-top: unset;
                margin-top: 45px;
              }
            }

            dt {
              padding-left: 80px;
              font-size: 20px;
              position: relative;
              line-height: 1.5;

              @media (max-width: 768px) {
                padding: 0 0 0 50px;
                font-size: 16px;
              }

              &::before {
                content: '';
                background-image: url(../images/newwhitepaper/q.png);
                background-size: contain;
                background-repeat: no-repeat;
                padding-top: 64px;
                width: 64px;
                height: 64px;
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                margin: auto;

                @media (max-width: 768px) {
                  width: 40px;
                  height: 40px;
                  top: -5px;
                }
              }
            }

            dd {
              margin-top: 44px;
              font-size: 16px;
              line-height: 200%;
              font-weight: 400;

              @media (max-width: 768px) {
                margin-top: 29px;
              }
            }
          }
        }
      }
    }
  }



  &.writing {
    .h2_above {
      margin-bottom: 20px;
    }

    @media (max-width: 768px) {
      br {
        line-height: 0;
      }

      .h2_above {
        margin-bottom: 12px;
      }

      .mainvisual-section .catch-copy {
        margin-bottom: 15px;
        line-height: 30px;
      }
    }

    .seo-section {
      margin: 80px auto 0;
      padding: 48px;
      max-width: 1080px;
      border: 8px solid #FA6B58;
      border-radius: 8px;

      @media (max-width: 768px) {
        margin-right: 20px;
        margin-left: 20px;
        padding: 32px 20px;
        border-width: 4px;

        .container {
          padding: 0;
        }
      }


      .h2_above {
        color: #FA6B58;
        font-size: 16px;
      }
      h2 {
        margin-bottom: 55px;
        line-height: 1.5;

        @media (max-width: 768px) {
          margin-bottom: 25px;
          font-size: 24px;

          .mini {
            font-size: 20px;
          }
        }
      }

      .writing {
        @media not screen and (max-width: 768px) {
          display: flex;
          align-items: center;
        }

        p {
          margin: 0 47px 20px 0;
          max-width: 651px;
          font-weight: 400;
          line-height: 2.2;

          @media (max-width: 768px) {
            margin-right: 0;
          }
        }
        img {
          flex-shrink: 0;
          width: auto;
          height: 243px;

          @media (max-width: 768px) {
            margin: 0 auto;
            width: 200px;
            height: auto;
          }
        }
      }
    }

    .benefit-section {

      .container {

        .item {

          .left {
            flex-shrink: 0;
            margin-right: 32px;
          }

          .right {

            p {
              margin-top: 15px;
              font-weight: 400;
              line-height: 2;
            }
          }
        }
      }
    }

    .problem-section {

      .container {

        .problem {
          margin-bottom: 67px;

          .bubble {
            justify-content: center;;
          }
        }

        .middle {
          text-align: center;

          @media (max-width: 768px) {
            margin-bottom: 32px;
          }

          .mini {
            font-size: 24px;
            display: block;
            line-height: 48px;

            @media (max-width: 768px) {
              font-size: 16px;
              line-height: 42px;
            }

            .gray {
              color: #808080;
            }
          }

          .big {
            font-size: 32px;
            line-height: 48px;
            display: block;

            @media (max-width: 768px) {
              font-size: 28px;
              line-height: 42px;
            }
          }
        }
      }
    }

    .support-section {
      padding-top: 80px;
      padding-bottom: 80px;

      @media (max-width: 768px) {
        padding-top: 48px;
        padding-bottom: 48px;
      }

      .container {
        @media (max-width: 768px) {
          padding-right: 20px;
          padding-left: 20px;
        }

        h2 {
          margin-bottom: 60px;
          text-align: center;

          @media (max-width: 768px) {
            margin-bottom: 35px;
          }

          span {
            display: block;
            line-height: 1.5;

            &.mini {
              margin-bottom: 5px;
              font-size: 32px;

              @media (max-width: 768px) {
                font-size: 24px;
              }
            }

            &.big {
              font-size: 40px;

              @media (max-width: 768px) {
                font-size: 28px;
              }
            }
          }
        }

        .flex {
          display: flex;
          justify-content: center;

          @media (max-width: 768px) {
            display: block;
          }

          .item {
            display: grid;
            grid-gap: 32px 0;
            padding: 10px 0;
            width: 380px;

            @media not screen and (max-width: 768px) {
              grid-template-rows: 125px 50px 144px 1fr;
            }
            @media (max-width: 768px) {
              grid-gap: 0;
              padding: 0;
              margin: 0 auto;
              width: 100%;
            }

            &:not(:first-of-type) {
              @media not screen and (max-width: 768px) {
                padding-left: 30px;
                border-left: 1px solid #808080;
              }

              @media (max-width: 768px) {
                padding-top: 32px;
                border-left: none;
                border-top: 1px solid #808080;
              }
            }
            &:not(:last-of-type) {
              @media not screen and (max-width: 768px) {
                padding-right: 30px;
              }
              @media (max-width: 768px) {
                padding-bottom: 31px;
              }
            }

            .heading {
              position: relative;

              @media (max-width: 768px) {
                margin-bottom: 25px;
              }

              &::after {
                content: '';
                position: absolute;
                transform: translateX(-50%);
                bottom: -10px;
                left: 50%;
                background-color: #808080;
                width: 1px;
                height: 20px;
              }


              img {
                margin: 0 auto;
                width: auto;
                height: 100%;

                @media (max-width: 768px) {
                  height: 100px;
                }
              }

            }

            h3 {
              place-self: center;
              margin-top: 0px;
              font-size: 20px;
              line-height: 1.5;
              text-align: center;

              @media (max-width: 768px) {
                margin-bottom: 14px;
              }
            }

            .example {
              place-self: center;
              max-width: 290px;
              width: 100%;
              text-align: left;

              @media (max-width: 768px) {
                margin-bottom: 20px;
                padding: 0 20px;
                max-width: none;
              }


              &>li {
                display: flex;
                font-size: 16px;
                line-height: 2;

                &::before {
                  content: '';
                  flex-shrink: 0;
                  margin: 5px 8px 0 0;
                  width: 20px;
                  height: 20px;
                  background: url(../images/service/check-icon.png) 0 0/contain no-repeat;
                }
              }
            }

            p {
              font-weight: 400;
              text-align: justify;
            }
          }
        }
      }
    }

    .strength-section {
      background-color: #EBF9F3;
      .container {

        .strength {

          .content {
            @media not screen and (max-width: 768px) {
              width: 545px;
            }

            .number {
              top: 14px;
              color: #fff;

              @media (max-width: 768px) {
                top: 5px;
              }
            }

            h3 {
              margin-bottom: 15px;
            }
          }

          .item {
            flex-direction: row;

            &:nth-of-type(-n+2) {
              display: block;
            }

            .content {
              margin: 0 auto 50px;

              @media (max-width: 768px) {
                margin-bottom: 35px;
              }
            }

            @media (max-width: 768px) {
              .h3_above::before {
                right: -88px;
                width: 80px;
              }
            }

            .images {
              display: grid;
              grid-gap: 36px;

              @media (max-width: 768px) {
                grid-gap: 16px;
              }

              &:not(:last-of-type) {
                margin-bottom: 50px;

                @media (max-width: 768px) {
                  margin-bottom: 30px;
                }
              }

              @media not screen and (max-width: 768px) {
                grid-template-rows: repeat(2,auto);
                grid-template-columns: repeat(2,1fr);

                h4 {
                  display: flex;
                  align-items: center;
                  grid-column: 1/3;

                  &::before {
                    content: "";
                    margin-right: 17px;
                    width: 50px;
                    height: 1px;
                    background-color: #808080;
                  }
                }
              }

              h4 {
                font-size: 20px;
                line-height: 32px;

                @media (max-width: 768px) {
                  font-size: 18px;
                }
              }

              img {
                justify-self: center;
              }
            }

            .image {
              img {
                margin-inline: auto;
              }
            }
          }
        }
      }
    }

    .difference-section {
      padding-top: 80px;
      padding-bottom: 80px;
      overflow: hidden;

      @media (max-width: 768px) {
        padding-top: 48px;
        padding-bottom: 48px;
      }

      h2 {
        margin-bottom: 60px;
      }

      .scroll {
        overflow-x: scroll;
        
        @media not screen and (max-width: 768px){
          scrollbar-width: none;
          -ms-overflow-style: none;
        
          &::-webkit-scrollbar {  
            display: none;
          }
        }

        .image {
          margin: 0 auto;
          width: 1000px;
          
          @media (max-width: 768px){
            width: 730px;
          }

          img {
            padding: 0 20px;
          }
        }
      }
    }

    .voice-section {
      background-color: #EBF9F3;

      .voice {
        margin-top: 42px;
      }
    }

    .flow-section {

      .number_circle {
        flex-shrink: 0;
      }
    }

    .faq-section {

      .faq__item {
        margin-inline: auto;
        width: 100%;
        max-width: 800px;

        &:not(:last-of-type) {
          padding-top: 20px;
          border-bottom: 1px solid #E0E0E0;
        }

        &-a {
          margin-left: auto;
          max-width: 712px;

          a {
            margin-top: 25px;
            display: inline-block;
            font-size: 18px;
            color: $color--accent--orange;
            font-weight: bold;
          }
        }
      }
    }
  }
}