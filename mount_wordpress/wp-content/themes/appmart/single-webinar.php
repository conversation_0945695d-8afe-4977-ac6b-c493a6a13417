<?php get_header(); ?>
<?php
	// $title = get_the_title();
	$webinar = get_field('webinar');

	foreach ($webinar as $key => $value) {
		$imgs[$key] = $value;
	}

	$weekList = ['日','月','火','水','木','金','土',];
	$week = $weekList[date('w',strtotime($webinar['date']))];
	$time = $webinar['stime'] . '～' . $webinar['etime'];
	$webinar['recommend'] = str_replace(array("\r\n", "\r", "\n"), "\n", $webinar['recommend']);

	$recommend = explode("\n",$webinar['recommend']);

	$form = 'sid_' . $webinar['formid'];
?>
	<div class="page__header">
        <div class="container">
            <div class="page__header__title">
                <span>WEBINAR</span>
                <h1 class="page__header__main">Appmartウェビナー</h1>
            </div>
        </div>
    </div>

	<div id="main-contents">
		<section id="recommend">
			<div class="banner">
				<img class="pc-only" src="<?php echo $imgs['pc']; ?>">
				<img class="sp-only" src="<?php echo $imgs['sp']; ?>">
			</div>
			<div class="btn">
				<a href="#form">無料ウェビナーに申し込む</a>
			</div>
			<div class="recommend-box">
				<div class="recommend-box-top">
					<h3><?php echo date('n/j',strtotime($webinar['date'])); ?><span class="youbi"><?php echo $week; ?></span><?php echo $time; ?></h3>
				</div>
				<div class="recommend-box-inner">
					<div class="recommend-title">
						<img src="<?php echo get_template_directory_uri(); ?>/assets/images/webinar/recommend.svg">
						<span class="box-title">こんな方におすすめ</span>
					</div>
					<p>
						<ul>
						<?php foreach ($recommend as $cont) : ?>
							<li><strong>・<?php echo $cont; ?>
							</strong></li>
						<?php endforeach; ?>
						</ul>
					</p>
					<div class="btn">
						<a href="#form">無料ウェビナーに申し込む</a>
					</div>
				</div>
			</div>
		</section>

		<section id="overview">
			<h3 class="heading">
				<span class="en-title">OVERVIEW</span><br>
				<span class="main-title">セミナー概要</span>
			</h3>
			<article class="post overview">
				<?php echo $webinar['overview'] ?>
				<br>
				<br>
				<table>
				<tr>
					<th>日時</th>
					<td><?php echo date('Y年n月j日',strtotime($webinar['date'])); ?>(<?php echo $week; ?>) <?php echo $time; ?></td>
				</tr>
				<tr>
					<th>講演者</th>
					<td><?php if ($webinar['speaker_dtl']) { echo $webinar['speaker_dtl'] . '　<br class="sp-only">'; } ?><?php echo $webinar['speaker']; ?></td>
				</tr>
				<tr>
					<th>会場</th>
					<td><?php echo $webinar['venue']; ?></td>
				</tr>
				<tr>
					<th>費用</th>
					<td><?php echo $webinar['cost']; ?></td>
				</tr>
				<tr>
					<th>主催</th>
					<td><?php echo $webinar['organizer']; ?></td>
				</tr>
				</table>
			</article>
		</section>

		<section id="lecture">
			<h3 class="heading">
				<span class="en-title">LECTURE</span><br>
				<span class="main-title">講師紹介</span>
			</h3>

			<article class="post lecture">
				<img class="lecture-img" src="<?php echo $webinar['speaker_img']; ?>">
				<div class="lecture-info">
					<div class="lecture-name">
						<?php if ($webinar['speaker_dtl']) { echo '<span>' . $webinar['speaker_dtl'] . '</span>'; } ?>
						<p><?php echo $webinar['speaker']; ?></p>
					</div>
					<div class="career">
						<?php echo $webinar['speaker_career']; ?>
					</div>
				</div>
			</article>
		</section>

		<section id="form">
			<h3 class="heading">
				<span class="en-title">FORM</span><br>
				<span class="main-title">お申し込みフォーム</span>
			</h3>
			<article class="post form">
			<?php if (isset($webinar['formend'][0])) : ?>
				<p>本ウェビナーは終了いたしました。
				<br>ご視聴いただきありがとうございました。</p>
			<?php else: ?>
				<!-- Bownow -->
				<script id="_bownow_cs_<?php echo $form; ?>">
					var _bownow_cs_<?php echo $form; ?> = document.createElement('script');
					_bownow_cs_<?php echo $form; ?>.charset = 'utf-8';
					_bownow_cs_<?php echo $form; ?>.src = 'https://contents.bownow.jp/forms/<?php echo $form; ?>/trace.js';
					document.getElementsByTagName('head')[0].appendChild(_bownow_cs_<?php echo $form; ?>);
				</script>
			<?php endif; ?>
			</article>
		</section>
	</div>
	<!-- <script>
		$(function(){$("nav div.panel").hide();$(".menu").click(function(){$(this).toggleClass("menuOpen").next().slideToggle();});})
	</script> -->

	<?php get_footer(); ?>
</body>
</html>