<?php
/*
Template Name: ホワイトペーパー一覧
Template Post Type: post,page

*/
?>

<?php get_header(); ?>

<!-- Start page__header -->
<div class="page__header" style="background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/photo/whitepaper.jpg);">
  <div class="container">
    <div class="page__header__title">
      <span>whitepaper</span>
      <h1 class="page__header__main">ホワイトペーパー一覧</h1>
    </div>
  </div>
  <div class="page__scroll"></div>
</div>
<!-- End page__header -->

<?php echo get_template_part('template-parts/breadcrumb'); ?>

<!-- Start main -->
<main class="content__main">

  <section class="section"><!-- archive wp -->
    <div class="archive__wp">
      <div class="container">

        <div class="archive__wp__tab">
          <ul class="tab__list">
            <li class="tab all is-active all-button">
              すべて
              <span class="tab_toggle"></span>
            </li>
            <li class="tab cm">コンテンツマーケティング</li>
            <li class="tab om">オウンドメディア運営</li>
            <li class="tab wp">ホワイトペーパー</li>
          </ul>
        </div>

        <div class="archive__wp__cont">

          <div class="group is-show">
            <h2 class="archive__wp__cont__title">すべて</h2>

            <?php
            $white_post = get_posts(array(
              'post_type' => 'document',
              'posts_per_page' => -1
            ));
            ?>
            <?php if ($white_post) : ?>

              <ul class="archive__wp__cont__list">

                <?php foreach ($white_post as $post) : setup_postdata($post);  ?>

                  <?php
                  $white_img = get_field('white_img');
                  $white_title = get_the_title();
                  $white_excerpt = get_field('white_excerpt');
                  ?>

                  <li class="archive__wp__cont__item">
                    <div class="archive__wp__cont__item__top">
                      <div class="archive__wp__cont__item__eyecatch">
                        <img src="<?php echo $white_img; ?>" alt="<?php echo $white_title; ?>">

                      </div>
                      <h2><?php echo $white_title; ?></h2>
                      <p><?php echo $white_excerpt; ?></p>
                    </div>
                    <div class="btn">
                      <a href="<?php the_permalink(); ?>" class="btn__orange btn__dl">
                        <span>ダウンロード</span>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg" alt="→">
                      </a>
                    </div>
                  </li>

                <?php
                endforeach;
                wp_reset_postdata();
                ?>
              </ul>
            <?php else : ?>
              <p style="text-align:center; ">投稿がありません。</p>
            <?php endif; ?>
          </div>

          <div class="group">
            <?php
            $white_cm_post = get_posts(array(
              'post_type' => 'document',
              'meta_key' => 'white_category',
              'meta_value' => 'コンテンツマーケティング',
              'posts_per_page' => -1
            ));
            ?>

            <h2 class="archive__wp__cont__title">コンテンツマーケティング</h2>

            <?php if ($white_cm_post) : ?>
              <ul class="archive__wp__cont__list">

                <?php foreach ($white_cm_post as $post) : setup_postdata($post);  ?>
                  <?php
                  $white_img = get_field('white_img');
                  $white_title = get_the_title();
                  $white_excerpt = get_field('white_excerpt');
                  ?>

                  <li class="archive__wp__cont__item">
                    <div class="archive__wp__cont__item__top">
                      <div class="archive__wp__cont__item__eyecatch">
                        <img src="<?php echo $white_img; ?>" alt="<?php echo $white_title; ?>">

                      </div>
                      <h2><?php echo $white_title; ?></h2>
                      <p><?php echo $white_excerpt; ?></p>
                    </div>
                    <div class="btn">
                      <a href="<?php the_permalink(); ?>" class="btn__orange btn__dl">
                        <span>ダウンロード</span>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg" alt="→">
                      </a>
                    </div>
                  </li>
                <?php endforeach;
                wp_reset_postdata();
                ?>

              </ul>
            <?php else : ?>
              <p style="text-align:center; ">投稿がありません。</p>
            <?php endif; ?>

          </div>

          <div class="group">
            <?php
            $white_om_post = get_posts(array(
              'post_type' => 'document',
              'posts_per_page' => -1,
              'meta_key' => 'white_category',
              'meta_value' => 'オウンドメディア運営'
            ));
            ?>

            <h2 class="archive__wp__cont__title">オウンドメディア運営</h2>

            <?php if ($white_om_post) : ?>

              <ul class="archive__wp__cont__list">

                <?php foreach ($white_om_post as $post) : setup_postdata($post);  ?>

                  <?php
                  $white_img = get_field('white_img');
                  $white_title = get_the_title();
                  $white_excerpt = get_field('white_excerpt');
                  ?>

                  <li class="archive__wp__cont__item">
                    <div class="archive__wp__cont__item__top">
                      <div class="archive__wp__cont__item__eyecatch">
                        <img src="<?php echo $white_img; ?>" alt="<?php echo $white_title; ?>">

                      </div>
                      <h2><?php echo $white_title; ?></h2>
                      <p><?php echo $white_excerpt; ?></p>
                    </div>
                    <div class="btn">
                      <a href="<?php the_permalink(); ?>" class="btn__orange btn__dl">
                        <span>ダウンロード</span>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg" alt="→">
                      </a>
                    </div>
                  </li>
                <?php endforeach;
                wp_reset_postdata();
                ?>

              </ul>
            <?php else : ?>
              <p style="text-align:center; ">投稿がありません。</p>
            <?php endif; ?>

          </div>

          <div class="group">
            <?php
            $white_cm_post = get_posts(array(
              'post_type' => 'document',
              'meta_key' => 'white_category',
              'meta_value' => 'ホワイトペーパー',
              'posts_per_page' => -1
            ));
            ?>

            <h2 class="archive__wp__cont__title">ホワイトペーパー</h2>

            <?php if ($white_cm_post) : ?>
              <ul class="archive__wp__cont__list">

                <?php foreach ($white_cm_post as $post) : setup_postdata($post);  ?>
                  <?php
                  $white_img = get_field('white_img');
                  $white_title = get_the_title();
                  $white_excerpt = get_field('white_excerpt');
                  ?>

                  <li class="archive__wp__cont__item">
                    <div class="archive__wp__cont__item__top">
                      <div class="archive__wp__cont__item__eyecatch">
                        <img src="<?php echo $white_img; ?>" alt="<?php echo $white_title; ?>">

                      </div>
                      <h2><?php echo $white_title; ?></h2>
                      <p><?php echo $white_excerpt; ?></p>
                    </div>
                    <div class="btn">
                      <a href="<?php the_permalink(); ?>" class="btn__orange btn__dl">
                        <span>ダウンロード</span>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/right_white.svg" alt="→">
                      </a>
                    </div>
                  </li>
                <?php endforeach;
                wp_reset_postdata();
                ?>

              </ul>
            <?php else : ?>
              <p style="text-align:center; ">投稿がありません。</p>
            <?php endif; ?>

          </div>
        </div>
      </div>
    </div>
  </section><!-- archive wp -->

</main>
<!-- End main -->

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>