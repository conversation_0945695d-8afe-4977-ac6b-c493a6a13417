<?php

namespace functions;

/**
 * 用語集による本文処理を制御するクラス
 */
class GlossaryContentProcessor
{
    /**
     * 処理対象の投稿タイプ配列
     *
     * @var array
     */
    private const TARGET_POST_TYPES = [
        'blog',
        'glossary'
    ];

    /**
     * カスタム投稿タイプ: 用語集 のスラッグ
     */
    private const POST_TYPE_GLOSSARY = 'glossary';

/**
 * 処理オプション
 *
 * @var array
 */
private $options = [
    'case_sensitive' => false,
    'exact_match' => false,
    'link_type' => 'both',
    'exclude_tags' => self::EXCLUDE_TAGS
];

    /**
     * 変更前の用語データ
     *
     * @var array|null
     */
    private $old_term_data = null;

    /**
     * 用語変換から除外するHTMLタグ配列
     *
     * @var array
     */
    private const EXCLUDE_TAGS = [
        'a',        // リンク
        'h1',       // 見出し
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'img',
        'pre',      // 整形済みテキスト
        'code',     // コード
        'script',   // スクリプト
        'style',    // スタイル
        'textarea', // テキストエリア
        'input',    // 入力フィールド
        'button',   // ボタン
    ];

    /**
     * HTML解析用のDOMDocument
     *
     * @var \DOMDocument
     */
    // private $dom;

    /**
     * 初期化処理とフックの登録
     */
    public function __construct()
    {
        // save_postフックに単一のハンドラーを登録
        add_action('save_post', [$this, 'handle_post_save'], 10, 3);

        // 用語削除時
        add_action('before_delete_post', [$this, 'handle_term_deletion'], 10, 1);
        add_action('wp_trash_post', [$this, 'handle_term_deletion'], 10, 1);

        // 用語更新前のデータを保存
        add_action('pre_post_update', [$this, 'store_old_term_data']);

        // オプション初期化
        $this->init_options();
    }
    /**
     * 投稿保存時の共通ハンドラー
     *
     * @param int $post_id 投稿ID
     * @param \WP_Post $post 投稿オブジェクト
     * @param bool $update 更新かどうか
     */
    public function handle_post_save(int $post_id, \WP_Post $post, bool $update): void
    {
        // 投稿タイプに応じて適切なハンドラーを呼び出す
        if ($post->post_type === self::POST_TYPE_GLOSSARY) {
            $this->handle_glossary_save($post_id, $post, $update);
        } elseif (in_array($post->post_type, self::TARGET_POST_TYPES, true)) {
            $this->handle_blog_save($post_id, $post, $update);
        }
    }

/**
 * オプション初期化
 *
 * @return void
 */
    private function init_options(): void
    {
        $this->options = [
        'case_sensitive' => false,
        'exact_match' => false,
        'link_type' => 'both',
        'exclude_tags' => self::EXCLUDE_TAGS
        ];
    }
    /**
     * 用語更新前のデータを保存
     *
     * @param int $post_id 投稿ID
     */
    public function store_old_term_data(int $post_id): void
    {
        $post = get_post($post_id);
        if ($post && $post->post_type === self::POST_TYPE_GLOSSARY) {
            $this->old_term_data = [
                'ID' => $post->ID,
                'term' => $post->post_title,
                'description' => $post->post_excerpt ?: wp_trim_words($post->post_content, 55),
                'permalink' => get_permalink($post->ID)
            ];
        }
    }

    /**
     * スキップ判定のベース処理
     *
     * @param integer $post_id
     * @param \WP_Post $post
     * @return boolean
     */
    private function should_skip_base(int $post_id, \WP_Post $post): bool
    {
        // 自動保存
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return true;
        }

        // リビジョン
        if (wp_is_post_revision($post_id)) {
            return true;
        }

        // 公開状態ではない
        if ($post->post_status !== 'publish') {
            return true;
        }

        return false;
    }

    private function should_skip_save(int $post_id, \WP_Post $post): bool
    {
        if ($this->should_skip_base($post_id, $post)) {
            return true;
        }
        return !in_array($post->post_type, self::TARGET_POST_TYPES, true);
    }

    private function should_skip_glossary_save(int $post_id, \WP_Post $post): bool
    {
        if ($this->should_skip_base($post_id, $post)) {
            return true;
        }
        return $post->post_type !== self::POST_TYPE_GLOSSARY;
    }

    /**
     * 処理対象の投稿タイプ配列を取得
     *
     * @access public
     * @return array 処理対象の投稿タイプ配列
     */
    public function get_target_post_types(): array
    {
        return self::TARGET_POST_TYPES;
    }

    /**
     * 処理対象のブログ投稿を取得する
     *
     * @return array 処理対象の投稿オブジェクトの配列
     */
    private function get_target_blog_posts(): array
    {
        return get_posts([
        'post_type' => self::TARGET_POST_TYPES,
        'post_status' => 'publish',
        'posts_per_page' => -1,
        ]);
    }

    /**
     * ブログ投稿保存時の処理
     *
     * @param int $post_id 投稿ID
     * @param \WP_Post $post 投稿オブジェクト
     * @param bool $update 更新かどうか
     */
    public function handle_blog_save(int $post_id, \WP_Post $post, bool $update): void
    {
        // 自動保存、リビジョン、対象外の投稿タイプはスキップ
        if ($this->should_skip_save($post_id, $post)) {
            return;
        }

        // 現在の投稿内容を取得
        $content = $post->post_content;

        try {
            // 用語変換処理を実行
            $processed_content = $this->process_content($content);

            // 内容が変更された場合のみ更新
            if ($processed_content !== $content) {
                // フックの一時的な削除（無限ループ防止）
                remove_action('save_post', [$this, 'handle_blog_save'], 10);

                // 更新日時を保持したまま内容を更新
                global $wpdb;
                $wpdb->update(
                    $wpdb->posts,
                    ['post_content' => $processed_content],
                    ['ID' => $post_id],
                    ['%s'],
                    ['%d']
                );

                // キャッシュのクリア
                clean_post_cache($post_id);

                // フックの再追加
                add_action('save_post', [$this, 'handle_blog_save'], 10, 3);
            }
        } catch (\Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Glossary processing error during save: ' . $e->getMessage());
            }
        }
    }
    /**
     * 用語保存時の処理
     *
     * @param int $post_id 投稿ID
     * @param \WP_Post $post 投稿オブジェクト
     * @param bool $update 更新かどうか
     */
    public function handle_glossary_save(int $post_id, \WP_Post $post, bool $update): void
    {
        // スキップ判定
        if ($this->should_skip_glossary_save($post_id, $post)) {
            return;
        }

        try {
            if ($update && $this->old_term_data) {
                // 用語変更時の処理
                $this->handle_term_update($post, $this->old_term_data);
            } else {
                // 新規用語追加時の処理
                $this->update_all_blog_posts();
            }
        } catch (\Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Glossary term save error: ' . $e->getMessage());
            }
        } finally {
            // 古いデータをクリア
            $this->old_term_data = null;
        }
    }
    /**
     * 用語変更時の処理
     *
     * @param \WP_Post $new_post 新しい投稿データ
     * @param array $old_data 古い用語データ
     */
    private function handle_term_update(\WP_Post $new_post, array $old_data): void
    {
        // 新しい用語データを作成
        $new_term_data = [
        'ID' => $new_post->ID,
        'term' => $new_post->post_title,
        'description' => $new_post->post_excerpt ?: wp_trim_words($new_post->post_content, 55),
        'permalink' => get_permalink($new_post->ID)
        ];

        try {
            $posts = $this->get_target_blog_posts();
            foreach ($posts as $post) {
                try {
                    // 処理対象がglossaryの自身の投稿ならスキップ
                    if ($post->ID === $new_term_data['ID']) {
                        continue;
                    }
                    // 古い用語のタグを削除
                    $content_without_old_tags = $this->remove_term_tags($post->post_content, $old_data);

                    // 新しい用語のみを処理
                    $final_content = $this->process_content($content_without_old_tags, $new_term_data);

                    if ($post->post_content !== $final_content) {
                        $this->save_post_content($post->ID, $final_content);
                    }
                } catch (\Exception $e) {
                    $this->log_error('Post processing error', [
                    'post_id' => $post->ID,
                    'error' => $e->getMessage()
                    ]);
                }
            }
        } finally {
            libxml_clear_errors();
        }
    }

     /**
     * データベース更新処理
     */
    private function save_post_content(int $post_id, string $content): void
    {
        global $wpdb;

        $result = $wpdb->update(
            $wpdb->posts,
            ['post_content' => $content],
            ['ID' => $post_id],
            ['%s'],
            ['%d']
        );

        if ($result === false) {
            throw new \Exception("Failed to update post {$post_id}");
        }

        clean_post_cache($post_id);
    }

    /**
     * エラーログ出力
     */
    private function log_error(string $message, array $context = []): void
    {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf(
                '[GlossaryContentProcessor] %s: %s',
                $message,
                json_encode($context, JSON_UNESCAPED_UNICODE)
            ));
        }
    }

  /**
  * 全ブログ投稿の更新を実行
  *
  * @throws \Exception 更新処理に失敗した場合
  */
    private function update_all_blog_posts(): void
    {
    global $wpdb;

    // 処理中フラグを設定（無限ループ防止）
    static $processing = false;
        if ($processing) {
          return;
        }
    $processing = true;

        try {
        $posts = $this->get_target_blog_posts();

            foreach ($posts as $post) {
                  $content = $post->post_content;
                  $processed_content = $this->process_content($content);

                  // 内容が変更された場合のみ更新
                if ($processed_content !== $content) {
          $wpdb->update(
              $wpdb->posts,
              ['post_content' => $processed_content],
              ['ID' => $post->ID],
              ['%s'],
              ['%d']
          );

          // キャッシュのクリア
          clean_post_cache($post->ID);
                }
            }
        } finally {
        // 処理中フラグをリセット
        $processing = false;
        }
    }

/**
 * 投稿本文内の用語を検出してツールチップ用のHTMLに変換する
 *
 * @param string $content 投稿本文
 * @param array|null $specific_term 特定の用語データ（指定がない場合は全用語を処理）
 * @param array $custom_options カスタムオプション
 * @return string 変換後の本文
 */
    private function process_content(string $content, ?array $specific_term = null, array $custom_options = []): string
    {
        // カスタムオプションがある場合は一時的にマージ
        $options = array_merge($this->options, $custom_options);

        // 特定の用語のみを処理する場合
        if ($specific_term !== null) {
            $terms = [$specific_term];
        } else {
            // 全用語を取得
            $terms = $this->get_glossary_terms();
        }

        if (empty($terms)) {
            return $content;
        }

        // 除外タグ内のコンテンツを退避
        $preserved = $this->preserve_html_tags($content, $options['exclude_tags']);
        $processing_content = $preserved['processed_content'];

        // 用語の処理
        foreach ($terms as $term_data) {
            $processing_content = $this->wrap_term_with_span(
                $processing_content,
                $term_data,
                $options
            );
        }

        return $this->restore_preserved_content(
            $processing_content,
            $preserved['replacements']
        );
    }

    // private function process_content(string $content, ?array $specific_term = null, array $custom_options = []): string
    // {
    //     // カスタムオプションがある場合は一時的にマージ
    //     $options = array_merge($this->options, $custom_options);

    //     // 特定の用語のみを処理する場合
    //     if ($specific_term !== null) {
    //         $terms = [$specific_term];
    //     } else {
    //         // 全用語を取得
    //         $terms = $this->get_glossary_terms();
    //     }

    //     if (empty($terms)) {
    //         return $content;
    //     }

    //     // カスタムオプションから現在の投稿情報を取得
    //     $current_post = isset($custom_options['current_post']) ? $custom_options['current_post'] : null;
    //     $is_glossary = $current_post && $current_post->post_type === self::POST_TYPE_GLOSSARY;

    //     // 除外タグ内のコンテンツを退避
    //     $preserved = $this->preserve_html_tags($content, $options['exclude_tags']);
    //     $processing_content = $preserved['processed_content'];

    //     // 用語の処理
    //     foreach ($terms as $term_data) {
    //         // 用語集ページで自身の用語の場合はスキップ
    //         if ($is_glossary && $current_post && $current_post->ID === $term_data['ID']) {
    //             continue;
    //         }

    //         $processing_content = $this->wrap_term_with_span(
    //             $processing_content,
    //             $term_data,
    //             $options
    //         );
    //     }

    //     return $this->restore_preserved_content(
    //         $processing_content,
    //         $preserved['replacements']
    //     );
    // }
  /**
  * 用語集の投稿から検索対象の用語リストを取得する
  *
  * @access private
  * @return array 用語情報の配列
  */
    private function get_glossary_terms(): array
    {
        $args = [
        'post_type' => self::POST_TYPE_GLOSSARY,
        'post_status' => 'publish',
        'posts_per_page' => -1,
        ];

        $glossary_posts = get_posts($args);
        $terms = [];

        foreach ($glossary_posts as $post) {
            $terms[] = [
            'ID' => $post->ID,
            'term' => $post->post_title,
            'description' => $post->post_excerpt ?: wp_trim_words($post->post_content),
            'permalink' => get_permalink($post->ID),
            'length' => mb_strlen($post->post_title)  // 用語の長さを追加
            ];
        }

        // 文字列長でソート（長い順）
        usort($terms, function ($a, $b) {
            return $b['length'] - $a['length'];
        });

        return $terms;
    }

/**
 * 本文中の用語を検出してツールチップ用のspanタグで囲む
 *
 * @access private
 * @param string $content 処理対象の文字列
 * @param array $term_data 用語情報
 * @param array $options 処理オプション
 * @return string 変換後の文字列
 */
    // private function wrap_term_with_span(string $content, array $term_data, array $custom_options = []): string
    // {
    //     // デフォルトのオプションとカスタムオプションをマージ
    //     $options = array_merge($this->options, $custom_options);

    //     $term = $term_data['term'];
    //     $patterns = $this->get_term_patterns($term, $options['exact_match']);

    //     // ツールチップ用のHTML生成
    //     $replacement = sprintf(
    //         '<span class="glossary-term" data-term="%s" data-description="%s" data-permalink="%s">$1</span>',
    //         esc_attr($term),
    //         esc_attr($term_data['description']),
    //         esc_url($term_data['permalink'])
    //     );

    //     $modified_content = $content;
    //     foreach ($patterns as $pattern) {
    //         if (!$options['case_sensitive']) {
    //             $pattern .= 'i';
    //         }
    //         $modified_content = preg_replace($pattern, $replacement, $modified_content);
    //     }

    //     return $modified_content;
    // }
    private function wrap_term_with_span(string $content, array $term_data, array $custom_options = []): string
    {
    // デフォルトのオプションとカスタムオプションをマージ
    $options = array_merge($this->options, $custom_options);

    // すでにタグ付けされている用語をチェックするパターン
    $tagged_pattern = '/<span[^>]*class="glossary-term"[^>]*>.*?<\/span>/i';

    // コンテンツを分割して処理
    $segments = preg_split($tagged_pattern, $content, -1, PREG_SPLIT_DELIM_CAPTURE);
    $result = '';

        foreach ($segments as $segment) {
            // すでにタグ付けされている部分はそのまま使用
            if (preg_match($tagged_pattern, $segment)) {
                $result .= $segment;
                continue;
            }

            // タグ付けされていない部分に対して用語を検索
            $term = $term_data['term'];
            $patterns = $this->get_term_patterns($term, $options['exact_match']);

            // 先にエスケープした属性値を準備
            $escaped_attributes = [
            'term' => esc_attr($term),
            'description' => esc_attr($term_data['description']),
            'permalink' => esc_url($term_data['permalink'])
            ];

            // ツールチップ用のHTML生成
            $replacement = sprintf(
                '<span class="glossary-term" data-term="%s" data-description="%s" data-permalink="%s">$1</span>',
                $escaped_attributes['term'],
                $escaped_attributes['description'],
                $escaped_attributes['permalink']
            );

            // 用語の置換
            $processed_segment = $segment;
            foreach ($patterns as $pattern) {
                if (!$options['case_sensitive']) {
                    $pattern .= 'i';
                }
                $processed_segment = preg_replace($pattern, $replacement, $processed_segment);
            }

            $result .= $processed_segment;
        }

    return $result;
    }

/**
 * HTMLエンティティを考慮した用語検索パターンを生成
 *
 * @param string $term 検索する用語
 * @param bool $exact_match 完全一致で検索するか
 * @return array 通常パターンとエンティティ変換パターンの配列
 */
    private function get_term_patterns(string $term, bool $exact_match): array
    {
        $patterns = [];

        // 通常パターン
        $quoted_term = preg_quote($term, '/');
        if ($exact_match) {
            $patterns[] = '/(?<![^\s\p{P}])(' . $quoted_term . ')(?![^\s\p{P}])/u';
        } else {
            $patterns[] = '/(' . $quoted_term . ')/u';
        }

        // HTMLエンティティ変換パターン
        $entity_term = preg_quote(htmlspecialchars($term, ENT_QUOTES, 'UTF-8'), '/');
        if ($entity_term !== $quoted_term) {
            if ($exact_match) {
                $patterns[] = '/(?<![^\s\p{P}])(' . $entity_term . ')(?![^\s\p{P}])/u';
            } else {
                $patterns[] = '/(' . $entity_term . ')/u';
            }
        }

        return $patterns;
    }

  /**
  * HTMLタグ内のコンテンツを一時的にプレースホルダーに置換する
  *
  * @access private
  * @param string $content 処理対象の文字列
  * @param array $exclude_tags 除外するタグ配列
  * @return array 処理結果の配列
  */
    // private function preserve_html_tags(string $content, array $exclude_tags): array
    // {
    //     $replacements = [];

    //     // すべてのHTMLタグを一時的に退避
    //     $processed_content = preg_replace_callback(
    //         '#<[^>]+>|</[^>]+>#i',  // デリミタを # に変更し、パターンを修正
    //         function ($matches) use (&$replacements) {
    //             $placeholder = '<!--HTML_TAG_' . md5($matches[0]) . '-->';  // 不要な改行を削除
    //             $replacements[$placeholder] = $matches[0];
    //             return $placeholder;
    //         },
    //         $content
    //     );

    //     if ($processed_content === null) {
    //         throw new \Exception('HTML tag preservation failed');
    //     }

    //     return [
    //     'processed_content' => $processed_content,
    //     'replacements' => $replacements
    //     ];
    // }


      /**
      * プレースホルダーを元のコンテンツに戻す
      *
      * @access private
      * @param string $content プレースホルダーを含む文字列
      * @param array $replacements プレースホルダーと元の文字列のマッピング
      * @return string 復元された文字列
      */
    private function restore_preserved_content(string $content, array $replacements): string
    {
      return str_replace(
          array_keys($replacements),
          array_values($replacements),
          $content
      );
    }

/**
 * 用語のタグを削除する
 *
 * @param string $content 処理対象の文字列
 * @param array $term_data 削除する用語の情報
 * @return string タグ削除後の文字列
 */
    private function remove_term_tags(string $content, array $term_data): string
    {
        $term = preg_quote(esc_attr($term_data['term']), '/');

        // 用語のタグを削除するパターン
        $pattern = '/<span[^>]*\bclass="glossary-term"[^>]*\bdata-term="' . $term . '"[^>]*>([^<]+)<\/span>/i';
        $content = preg_replace($pattern, '$1', $content);

        // 属性順序が異なる場合にも対応
        $pattern = '/<span[^>]*\bdata-term="' . $term . '"[^>]*\bclass="glossary-term"[^>]*>([^<]+)<\/span>/i';
        return preg_replace($pattern, '$1', $content);
    }


/**
 * 用語にツールチップタグを付与する
 *
 * @param string $content 処理対象の文字列
 * @param array $term_data 用語情報
 * @param array $custom_options オプション設定
 * @return string タグ付与後の文字列
 */
    private function apply_term_tags(string $content, array $term_data, array $custom_options = []): string
    {
        // デフォルトのオプションとカスタムオプションをマージ
        $options = array_merge($this->options, $custom_options);

        // 除外タグ内のコンテンツを退避
        $preserved = $this->preserve_html_tags($content, $options['exclude_tags']);
        $processing_content = $preserved['processed_content'];

        // 用語にタグを付与
        $processing_content = $this->wrap_term_with_span(
            $processing_content,
            $term_data,
            $options  // マージ済みのオプションを渡す
        );

        // 退避したコンテンツを復元
        return $this->restore_preserved_content(
            $processing_content,
            $preserved['replacements']
        );
    }

/**
 * 用語削除時の処理
 *
 * @param int $post_id 削除される投稿のID
 */
    public function handle_term_deletion(int $post_id): void
    {
        $post = get_post($post_id);
        if (!$post || $post->post_type !== self::POST_TYPE_GLOSSARY) {
            return;
        }

        // 削除される用語のデータを保持
        $term_data = [
        'ID' => $post->ID,
        'term' => $post->post_title,
        'description' => $post->post_excerpt ?: wp_trim_words($post->post_content, 55),
        'permalink' => get_permalink($post->ID)
        ];

        try {
            $posts = $this->get_target_blog_posts();
            foreach ($posts as $post) {
                $content = $post->post_content;
                $updated_content = $this->remove_term_tags($content, $term_data);

                if ($content !== $updated_content) {
                    $this->save_post_content($post->ID, $updated_content);
                }
            }
        } catch (\Exception $e) {
            $this->log_error('Term deletion processing error', [
            'term_id' => $post_id,
            'error' => $e->getMessage()
            ]);
        }
    }


  /**
  * HTMLタグ内のコンテンツを一時的にプレースホルダーに置換する
  *
  * @access private
  * @param string $content 処理対象の文字列
  * @param array $exclude_tags 除外するタグ配列
  * @return array 処理結果の配列
  */
    private function preserve_html_tags(string $content, array $exclude_tags): array
    {
        $replacements = [];

        // ユニークな識別子を生成（処理の重複を防ぐ）
        $unique_id = uniqid('preserve_', true);

        // タグパターンを構築（デリミタ用にエスケープ）
        $tag_pattern = implode('|', array_map(function ($tag) {
            return preg_quote($tag, '#');
        }, $exclude_tags));

        try {
            // 1. 除外タグとその中身を含めて退避（開始タグと終了タグのペア）
            $processed_content = preg_replace_callback(
                "#<({$tag_pattern})(?:\s[^>]*)?>(.*?)</\\1>#is",
                function ($matches) use (&$replacements, $unique_id) {
                                        $placeholder = sprintf(
                                            '
                <!--EXCLUDED_TAG_%s_%s-->',
                                            $unique_id,
                                            md5($matches[0] . uniqid())
                                        );
                                                    $replacements[$placeholder] = $matches[0];
                                                    return $placeholder;
                },
                $content
);

            if ($processed_content === null) {
            throw new \Exception('Failed to process paired HTML tags');
            }

// 2. 自己終了タグを処理（img, input等）
$processed_content = preg_replace_callback(
    "#<({$tag_pattern})(?:\s[^>]*)?/?>#i",
    function ($matches) use (&$replacements, $unique_id) {
                        $placeholder = sprintf(
                            '
    <!--EXCLUDED_SINGLE_TAG_%s_%s-->',
                            $unique_id,
                            md5($matches[0] . uniqid())
                        );
                                  $replacements[$placeholder] = $matches[0];
                                  return $placeholder;
    },
    $processed_content
);

            if ($processed_content === null) {
            throw new \Exception('Failed to process self-closing HTML tags');
            }

  // 3. 処理結果の検証
            if (empty($replacements) && $this->has_excluded_tags($content, $exclude_tags)) {
            $this->log_error('Tag preservation warning: No replacements made despite presence of excluded
  tags', [
            'content' => substr($content, 0, 100) . '...',
            'exclude_tags' => $exclude_tags
            ]);
            }

  return [
  'processed_content' => $processed_content,
  'replacements' => $replacements
  ];
        } catch (\Exception $e) {
        $this->log_error('HTML tag preservation failed', [
        'error' => $e->getMessage(),
        'content_length' => strlen($content)
        ]);
        // エラー時は元のコンテンツを返す（安全側に倒す）
        return [
        'processed_content' => $content,
        'replacements' => []
        ];
        }
    }

  /**
  * 除外対象のタグが存在するか確認
  */
    private function has_excluded_tags(string $content, array $exclude_tags): bool
    {
        foreach ($exclude_tags as $tag) {
            if (stripos($content, "<{$tag}") !== false) {
    return true;
            }
        } return false;
    }
}
