<?php

namespace functions;

/**
 * 用語集による本文処理を制御するクラス
 */
class GlossaryShowContent
{
    /**
     * 処理対象の投稿タイプ配列
     *
     * @var array
     */
    private const TARGET_POST_TYPES = [
        'blog',
        'glossary'
    ];

    /**
     * 対象外の単語を登録する。ここに登録されている単語にはツールチップタグを付与しない
     * glossary投稿のスラッグ「exclude_keywords」の本文に改行区切りで設定されているキーワードを配列として取得し、保持する
     *
     * @var array
     */
    public array $exclude_keywords = [];


    /**
     * カスタム投稿タイプ: 用語集 のスラッグ
     */
    private const POST_TYPE_GLOSSARY = 'glossary';

    /**
     * 用語変換から除外するHTMLタグ配列
     *
     * @var array
     */
    private const EXCLUDE_TAGS = [
        'a',        // リンク
        'h1',       // 見出し
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'img',
        'pre',      // 整形済みテキスト
        'code',     // コード
        'script',   // スクリプト
        'style',    // スタイル
        'textarea', // テキストエリア
        'input',    // 入力フィールド
        'button',   // ボタン
    ];

    /**
     * 用語にツールチップタグを付与する回数
     * 用語毎にカウントする
     */
    public int $tooltip_count_setting = 3;

    /**
     * 初期化処理とフックの登録
     */
    public function __construct()
    {
        // the_contentフィルターに単一のハンドラーを登録
        add_filter('the_content', [$this, 'handleTheContent'], 10, 1);
        // Autoptimizeのキャッシュから用語集処理を除外
        add_filter('autoptimize_filter_noptimize', function ($noptimize) {
            if (in_array(get_post_type(), self::TARGET_POST_TYPES)) {
                return true;
            }
            return $noptimize;
        }, 10, 1);

        // exclude_keywordsスラッグの用語集投稿を取得
        $exclude_post = get_posts([
            'post_type' => 'glossary',
            'post_name' => 'glossary-setting',
            'posts_per_page' => 1,
            'post_status' => 'private'
        ]);
        $exclude_post = !empty($exclude_post) ? $exclude_post[0] : null;

        // 用語集除外キーワードを取得
        if ($exclude_post) {
            $keywords = explode("\n", $exclude_post->post_content);
            $filtered_keywords = array_filter(array_map('trim', $keywords));
            $this->exclude_keywords = array_merge($filtered_keywords);
            $this->tooltip_count_setting = get_field('tooltip_count_setting', $exclude_post->ID);
        } else {
            $this->exclude_keywords = [];
        }
    }

    /**
     * 用語集の投稿を取得する
     *
     * @return mixed
     */
    public function getGlossaryTerms()
    {
        $args = [
            'post_type' => self::POST_TYPE_GLOSSARY,
            'posts_per_page' => -1,
        ];
        $query = new \WP_Query($args);
        if ($query->have_posts()) {
            $glossary_posts = $query->posts;
            return $glossary_posts;
        } else {
            return null;
        }
    }
    /**
     * コンテンツに用語集の用語を適用する
     *
     * @param string $content 処理対象のコンテンツ
     * @return string 処理済みのコンテンツ
     */
    public function handleTheContent(string $content): string
    {
        try {
            if (!$this->shouldProcessContent()) {
                return $content;
            }

            $terms = $this->prepareGlossaryTerms();
            if (empty($terms)) {
                return $content;
            }

            return $this->processContentWithTerms($content, $terms);
        } catch (\Exception $e) {
            $this->logError('Glossary processing error', $e);
            return $content;
        }
    }

    /**
     * コンテンツを処理すべきかどうかを判定
     *
     * @return bool
     */
    private function shouldProcessContent(): bool
    {
        return in_array(get_post_type(), self::TARGET_POST_TYPES);
    }

    /**
     * 用語集データを準備
     *
     * @return array
     */
    private function prepareGlossaryTerms(): array
    {
        $glossary_posts = $this->getGlossaryTerms();
        if (!$glossary_posts) {
            return [];
        }

        $current_post = get_post();
        $terms = [];

        foreach ($glossary_posts as $post) {
            if ($this->isCurrentGlossaryTerm($current_post, $post)) {
                continue;
            }

            $terms[] = $this->createTermData($post);
        }

        return $this->sortTermsByLength($terms);
    }

    /**
     * 現在の投稿が用語集の同じ項目、または親用語の一部かどうかを判定
     *
     * @param \WP_Post $current_post
     * @param \WP_Post $glossary_post
     * @return bool
     */
    private function isCurrentGlossaryTerm(\WP_Post $current_post, \WP_Post $glossary_post): bool
    {
        // 同じ用語集ページの場合
        if ($current_post->post_type === self::POST_TYPE_GLOSSARY) {
            // 完全一致（自分自身）の場合
            if ($current_post->ID === $glossary_post->ID) {
                return true;
            }

            // チェック対象の用語が、現在のページの用語の一部である場合はスキップ
            // 例: 現在のページが「ホワイトペーパー」で、チェック対象が「ホワイト」の場合
            $check_term = $glossary_post->post_title;
            $current_term = $current_post->post_title;

            if (mb_strpos($current_term, $check_term) !== false && mb_strlen($current_term) > mb_strlen($check_term)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 用語データを作成
     *
     * @param \WP_Post $post
     * @return array
     */
    private function createTermData(\WP_Post $post): array
    {
        return [
            'ID' => $post->ID,
            'term' => $post->post_title,
            'description' => $post->post_excerpt ?: wp_trim_words($post->post_content),
            'permalink' => get_permalink($post->ID),
            'length' => mb_strlen($post->post_title)
        ];
    }

    /**
     * 用語を文字列長でソート
     *
     * @param array $terms
     * @return array
     */
    private function sortTermsByLength(array $terms): array
    {
        usort($terms, function ($a, $b) {
            return $b['length'] - $a['length'];
        });
        return $terms;
    }

    /**
     * コンテンツに用語を適用
     *
     * @param string $content
     * @param array $terms
     * @return string
     */
    private function processContentWithTerms(string $content, array $terms): string
    {
        $preserved = $this->preserveHtmlTags($content);
        $processing_content = $preserved['processed_content'];

        foreach ($terms as $term_data) {
            $processing_content = $this->wrapTermWithSpan($processing_content, $term_data);
        }

        return $this->restorePreservedContent(
            $processing_content,
            $preserved['replacements']
        );
    }
    /**
     * HTMLタグ内のコンテンツを一時的にプレースホルダーに置換
     *
     * @param string $content
     * @return array
     * @throws \Exception
     */
    private function preserveHtmlTags(string $content): array
    {
        $replacements = [];
        $unique_id = uniqid('preserve_', true);

        // 除外タグのパターンを構築
        $tag_pattern = implode('|', array_map(function ($tag) {
            return preg_quote($tag, '#');
        }, self::EXCLUDE_TAGS));

        try {
            // 除外タグとその中身を含めて退避
            $processed_content = preg_replace_callback(
                "#<({$tag_pattern})(?:\s[^>]*)?>(.*?)</\\1>#is",
                function ($matches) use (&$replacements, $unique_id) {
                    $placeholder = sprintf(
                        '<!--EXCLUDED_TAG_%s_%s-->',
                        $unique_id,
                        md5($matches[0] . uniqid())
                    );
                    $replacements[$placeholder] = $matches[0];
                    return $placeholder;
                },
                $content
            );

            // 自己終了タグを処理
            $processed_content = preg_replace_callback(
                "#<({$tag_pattern})(?:\s[^>]*)?/?>#i",
                function ($matches) use (&$replacements, $unique_id) {
                    $placeholder = sprintf(
                        '<!--EXCLUDED_SINGLE_TAG_%s_%s-->',
                        $unique_id,
                        md5($matches[0] . uniqid())
                    );
                    $replacements[$placeholder] = $matches[0];
                    return $placeholder;
                },
                $processed_content
            );

            return [
                'processed_content' => $processed_content,
                'replacements' => $replacements
            ];
        } catch (\Exception $e) {
            throw new \Exception('HTML tag preservation failed: ' . $e->getMessage());
        }
    }

    /**
     * 用語をツールチップ用のaタグで囲む
     *
     * @param string $content
     * @param array $term_data
     * @return string
     */
    private function wrapTermWithSpan(string $content, array $term_data): string
    {
        if (empty($content)) {
            return '';
        }

        $term = $term_data['term'];

        // HTMLを一時的なトークンに分割
        $tokens = preg_split('/(<[^>]+>)/u', $content, -1, PREG_SPLIT_DELIM_CAPTURE);
        $result = '';
        $insideGlossaryTerm = false;
        $tooltip_count = 0; // ツールチップの出現回数をカウント

        foreach ($tokens as $token) {
            // aタグの開始をチェック
            if (preg_match('/<a[^>]*class="glossary-term"[^>]*>/u', $token)) {
                $insideGlossaryTerm = true;
                $result .= $token;
                continue;
            }

            // aタグの終了をチェック
            if ($token === '</a>') {
                $insideGlossaryTerm = false;
                $result .= $token;
                continue;
            }

            // タグの場合はそのまま追加
            if (preg_match('/^<[^>]+>$/u', $token)) {
                $result .= $token;
                continue;
            }

            // glossary-term a内の場合はそのまま追加
            if ($insideGlossaryTerm) {
                $result .= $token;
                continue;
            }

            // 除外ワードの中に含まれる用語はツールチップを付与しない
            if (!$this->isTermInExcludeWord($token, $term)) {
                // 用語の置換を実行
                $pattern = '/(' . preg_quote($term, '/') . ')/ui';
                $replacement = function ($matches) use ($term_data, &$tooltip_count, $term) {  // $termを追加
                    // ツールチップの制限回数に達した場合は置換せずそのまま返す
                    if ($tooltip_count >= $this->tooltip_count_setting) {
                        return $matches[0];
                    }

                    $tooltip_count++;
                    return sprintf(
                        '<a href="%s" class="glossary-term" data-term="%s" data-description="%s" data-id="%d">%s</a>',
                        esc_url($term_data['permalink']),
                        esc_attr($term),
                        esc_attr($term_data['description']),
                        $term_data['ID'],
                        $matches[0]
                    );
                };


                $processed_token = preg_replace_callback($pattern, $replacement, $token);
                if ($processed_token === null) {
                    // エラーが発生した場合は元のトークンを使用
                    error_log('preg_replace error for term: ' . $term);
                    $result .= $token;
                } else {
                    $result .= $processed_token;
                }
            } else {
                $result .= $token;
            }
        }

        return $result;
    }

    /**
     * 用語検索パターンを生成
     *
     * @param string $term
     * @return string
     */
    private function getTermPattern(string $term): string
    {
        $quoted_term = preg_quote($term, '/');
        // シンプルに用語をマッチ
        return '/(' . $quoted_term . ')/u';
    }

    /**
     * 用語が除外ワードに含まれているかチェック
     *
     * @param string $text 検査対象のテキスト
     * @param string $term 検査する用語
     * @return bool 除外ワードに含まれている場合はtrue
     */
    private function isTermInExcludeWord(string $text, string $term): bool
    {
        $term_lower = mb_strtolower($term);

        // 除外ワードリストをチェック
        foreach ($this->exclude_keywords as $exclude_word) {
            $exclude_word_lower = mb_strtolower($exclude_word);

            // 対象テキストに除外ワードが含まれている場合
            if (mb_stripos($text, $exclude_word) !== false) {
                // 除外ワード内に用語が含まれているか確認
                // 例: 除外ワード「appmart」に用語「ma」が含まれている
                if (mb_stripos($exclude_word_lower, $term_lower) !== false) {
                    // 除外ワードを含む部分を特定
                    $positions = $this->findAllPositions($text, $exclude_word);
                    foreach ($positions as $pos) {
                        $end_pos = $pos + mb_strlen($exclude_word);

                        // 用語の出現位置を全て取得
                        $term_positions = $this->findAllPositions($text, $term);
                        foreach ($term_positions as $term_pos) {
                            $term_end_pos = $term_pos + mb_strlen($term);

                            // 用語が除外ワードの範囲内に収まっているかチェック
                            if ($term_pos >= $pos && $term_end_pos <= $end_pos) {
                                return true; // 除外ワード内に用語が含まれている
                            }
                        }
                    }
                }
            }
        }

        return false;
    }
    /**
     * 文字列内での全ての部分文字列の位置を見つける
     *
     * @param string $haystack 検索対象の文字列
     * @param string $needle 検索する部分文字列
     * @return array 見つかった位置の配列
     */
    private function findAllPositions(string $haystack, string $needle): array
    {
        $positions = [];
        $pos = mb_stripos($haystack, $needle);

        while ($pos !== false) {
            $positions[] = $pos;
            $pos = mb_stripos($haystack, $needle, $pos + 1);
        }

        return $positions;
    }


    /**
     * プレースホルダーを元のコンテンツに戻す
     *
     * @param string $content
     * @param array $replacements
     * @return string
     */
    private function restorePreservedContent(string $content, array $replacements): string
    {
        return str_replace(
            array_keys($replacements),
            array_values($replacements),
            $content
        );
    }
    /**
     * エラーログを記録
     *
     * @param string $message
     * @param \Exception $exception
     * @return void
     */
    private function logError(string $message, \Exception $exception): void
    {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log($message . ': ' . $exception->getMessage());
        }
    }
}
