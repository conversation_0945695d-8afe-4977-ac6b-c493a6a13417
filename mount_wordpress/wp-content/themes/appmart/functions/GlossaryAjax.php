<?php

namespace functions;

class GlossaryAjax
{
    public function __construct()
    {
        add_action('wp_ajax_get_glossary_tooltip', [$this, 'get_tooltip_template']);
        add_action('wp_ajax_nopriv_get_glossary_tooltip', [$this, 'get_tooltip_template']);
    }

    public function get_tooltip_template()
    {
        try {
            // POSTデータの存在確認
            if (!isset($_POST['action']) || !isset($_POST['nonce'])) {
                wp_send_json_error(['message' => 'Invalid request parameters'], 400);
                return;
            }

            // nonceの検証
            $nonce_valid = wp_verify_nonce($_POST['nonce'], 'glossary_tooltip');
            $new_nonce = wp_create_nonce('glossary_tooltip');

            if (!$nonce_valid) {
                error_log('Nonce validation failed. Received: ' . $_POST['nonce']);
                wp_send_json_error([
                    'message' => 'Security token expired',
                    'new_nonce' => $new_nonce
                ], 401);
                return;
            }

            $term = sanitize_text_field($_POST['term']);
            $description = sanitize_text_field($_POST['description']);
            $permalink = esc_url_raw($_POST['permalink']);
            $post_id = isset($_POST['ID']) ? absint($_POST['ID']) : 0;

            ob_start();
            get_template_part('template-parts/glossary/tooltip', null, [
                'term' => $term,
                'description' => $description,
                'permalink' => $permalink,
                'ID' => $post_id
            ]);
            $html = ob_get_clean();

            wp_send_json_success([
                'html' => $html,
                'new_nonce' => $new_nonce
            ]);
        } catch (\Exception $e) {
            error_log('Tooltip error: ' . $e->getMessage());
            wp_send_json_error([
                'message' => 'Error processing tooltip request',
                'new_nonce' => wp_create_nonce('glossary_tooltip')
            ]);
        }
    }
}
