<?php

/**
 * ツールチップ投稿関連機能
 */

// 絶対パスで指定
require_once dirname(__FILE__) . '/GlossaryContentProcessor.php';
require_once dirname(__FILE__) . '/GlossaryShowContent.php';
require_once dirname(__FILE__) . '/GlossaryAjax.php';

use functions\GlossaryContentProcessor;
use functions\GlossaryShowContent;
use functions\GlossaryAjax;

//  カスタム投稿:glossaryを作成
function create_glossary_post_type()
{
    $labels = array(
        'name' => '用語集',
        'singular_name' => '用語集',
        'add_new' => '用語を追加',
        'add_new_item' => '用語を追加',
        'edit_item' => '用語を編集',
        'new_item' => '新しい用語',
        'view_item' => '用語を表示',
        'search_items' => '用語を検索',
        'not_found' => '用語が見つかりません',
        'not_found_in_trash' => 'ゴミ箱に用語がありません',
        'parent_item_colon' => '親用語:',
        'menu_name' => '用語集'
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'glossary'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => null,
        'supports' => array('title', 'editor', 'excerpt', 'thumbnail'),
    );
    register_post_type('glossary', $args);
}
add_action('init', 'create_glossary_post_type');

// ////////////////////////////////////////////////////
// 本文処理
// ////////////////////////////////////////////////////
/**
 * GlossaryContentProcessorのインスタンスを作成
 */
// function init_glossary_content_processor()
// {
//     try {
//         new GlossaryContentProcessor();
//     } catch (\Exception $e) {
//         if (defined('WP_DEBUG') && WP_DEBUG) {
//             error_log('Failed to initialize GlossaryContentProcessor: ' . $e->getMessage());
//         }
//     }
// }
// add_action('init', 'init_glossary_content_processor', 20);

/**
 * GlossaryShowContentのインスタンスを作成
 */
function init_glossary_show_content()
{
    try {
        new GlossaryShowContent();
    } catch (\Exception $e) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Failed to initialize GlossaryShowContent: ' . $e->getMessage());
        }
    }
}
add_action('init', 'init_glossary_show_content', 20);
// ////////////////////////////////////////////////////
// スクリプトとAjax処理
// ////////////////////////////////////////////////////
/**
 * スクリプトとスタイルを登録
 */
function enqueue_glossary_scripts()
{
    // nonceの有効期限を延長
    add_filter('nonce_life', function () {
        return 12 * HOUR_IN_SECONDS;
    });

    // nonceを生成
    $nonce = wp_create_nonce('glossary_tooltip');
    error_log('Generated initial nonce: ' . $nonce);

    // CSSの登録
    wp_enqueue_style(
        'glossary-tooltip',
        get_template_directory_uri() . '/assets/css/glossary.css',
        [],
        '1.0.0'
    );

    // JSの登録
    wp_enqueue_script(
        'glossary-tooltip',
        get_template_directory_uri() . '/assets/js/tooltip.js',
        ['jquery'],
        '1.0.0',
        true
    );

    // スクリプトのローカライズ
    $localize_data = [
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => $nonce,
        'maxRetries' => 1
    ];

    wp_localize_script('glossary-tooltip', 'appmart', $localize_data);
    error_log('Localized script data: ' . print_r($localize_data, true));
}
add_action('wp_enqueue_scripts', 'enqueue_glossary_scripts');

// blogまたはglossary投稿の保存時、autoptimizeのキャッシュをクリアする
function clear_autoptimize_cache($post_id)
{
    // blogまたはglossary投稿の保存時
    if (in_array(get_post_type($post_id), ['post', 'glossary'])) {
        // リビジョンや下書きの保存時は処理をスキップ
        if (wp_is_post_revision($post_id) || wp_is_post_autosave($post_id)) {
            return;
        }
        // autoptimizeがインストールされていて、clearallメソッドが存在する場合
        if (class_exists('autoptimizeCache') && method_exists('autoptimizeCache', 'clearall')) {
            // キャッシュをクリア
            autoptimizeCache::clearall();
            return true;
        }
        return false;
    }
}
add_action('save_post', 'clear_autoptimize_cache');

// Ajaxハンドラーの初期化
new GlossaryAjax();
