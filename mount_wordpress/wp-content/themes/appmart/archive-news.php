<?php
/*
Template Name: お知らせ一覧
Template Post Type: post,page
*/
?>

<?php get_header(); ?>

<div class="page__header"
  style="background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/photo/news.jpg);">
  <div class="container">
    <div class="page__header__title">
      <span>NEWS</span>
      <h1 class="page__header__main">お知らせ</h1>
    </div>
  </div>
  <div class="page__scroll"></div>
</div>

<main class="content__main news">
  <?php echo get_template_part('template-parts/breadcrumb'); ?>
  <section class="section">
    <div class="container archive-news">
      <div class="section__title__left">
        <span>news</span>
        <h2>お知らせ</h2>
      </div>
      <ul class="news-list">
        <?php
        while (have_posts()) :
            the_post();
            ?>
        <li class="news-list__item">
          <div class="news-list__item--date">
            <time datetime="<?php the_time('Y-m-d'); ?>"><?php the_time('Y/m/d'); ?></time>
          </div>
          <div class="news-list__item--title">
            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
          </div>
        </li>
        <?php
        endwhile;
        ?>
      </ul>
    </div>

    <div class="pagination">
      <?php
            the_posts_pagination(array(
            'prev_text' => '<',
            'next_text' => '>',
            'mid_size' => '0',

            ));
            ?>
    </div>

  </section>
</main>

<?php get_template_part('template-parts/section_contact'); ?>

<?php get_footer(); ?>