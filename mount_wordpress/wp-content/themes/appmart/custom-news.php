<?php

function create_news_post_type()
{
    $labels = array(
        'name' => 'news',
        'singular_name' => 'news',
        'add_new' => '新規追加',
        'add_new_item' => '新規お知らせを追加',
        'edit_item' => 'お知らせを編集',
        'view_item' => 'お知らせを表示',
        'search_items' => 'お知らせを検索',
        'menu_name' => 'お知らせ'
    );

    $args = array(
        'labels' => $labels,
        'public' => true,
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-megaphone',
        'supports' => array('title', 'editor'),
        'rewrite' => array('slug' => 'news'),
    );

    register_post_type('news', $args);
}
add_action('init', 'create_news_post_type');

// news系ページのenqueue
function news_enqueue()
{
    wp_enqueue_style('news', get_template_directory_uri() . '/assets/css/news.min.css', array(), '1.0.0');
}
add_action('wp_enqueue_scripts', 'news_enqueue');

// 1ページに表示する記事数
function news_posts_per_page($query)
{
    if (is_admin() || !$query->is_main_query()) {
        return;
    }
    $query->set('posts_per_page', 10);
}
add_action('pre_get_posts', 'news_posts_per_page');