#!/bin/bash

# MCPサーバー自動インストールスクリプト
# JSONファイルからMCPサーバー設定を読み取り、claude mcp add-jsonコマンドを実行
# 既存のMCPサーバーは一度削除してから再インストールするため、設定変更にも対応
# 
# 使用方法:
#   ./install-mcp-servers.sh                    # デフォルト: ~/.claude/.mcp.json を使用
#   ./install-mcp-servers.sh /path/to/file.json # カスタムファイルを指定

# デフォルトのJSONファイルパス
# 引数が指定されていない場合は ~/.claude/.mcp.json を使用
JSON_FILE="${1:-$HOME/.claude/.mcp.json}"

# 色付けのための定数
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ヘッダー表示
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  MCP Servers Auto-Installation Script  ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo -e "${YELLOW}設定ファイル: $JSON_FILE${NC}"
echo ""

# jqコマンドの存在確認
if ! command -v jq &> /dev/null; then
    echo -e "${RED}エラー: jqコマンドが見つかりません。${NC}"
    echo "jqをインストールしてください："
    echo "  Ubuntu/Debian: sudo apt-get install jq"
    echo "  macOS: brew install jq"
    echo "  Windows: choco install jq"
    exit 1
fi

# JSONファイルの存在確認
if [ ! -f "$JSON_FILE" ]; then
    echo -e "${RED}エラー: JSONファイル '$JSON_FILE' が見つかりません。${NC}"
    echo ""
    echo "以下のいずれかの方法で対処してください："
    echo "1. デフォルトの場所にファイルを作成: ~/.claude/.mcp.json"
    echo "2. カスタムファイルを指定: $0 /path/to/your/mcp-config.json"
    exit 1
fi

# JSONファイルの妥当性確認
if ! jq empty "$JSON_FILE" 2>/dev/null; then
    echo -e "${RED}エラー: JSONファイルの形式が正しくありません。${NC}"
    exit 1
fi

# サーバー数のカウント
SERVER_COUNT=$(jq -r '.mcpServers | keys | length' "$JSON_FILE")
echo -e "${YELLOW}検出されたMCPサーバー数: $SERVER_COUNT${NC}"
echo ""

# 既存のMCPサーバーを削除するフェーズ
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    既存MCPサーバーの削除処理          ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# JSONファイルに定義されているサーバー名を取得して削除
DELETED_COUNT=0
jq -r '.mcpServers | keys[]' "$JSON_FILE" | while read -r server_name; do
    echo -e "${YELLOW}削除中: $server_name${NC}"
    if claude mcp remove "$server_name" 2>/dev/null; then
        echo -e "  ${GREEN}✓ 削除しました${NC}"
        ((DELETED_COUNT++))
    else
        echo -e "  ${YELLOW}○ 存在しないかすでに削除済み${NC}"
    fi
done

echo ""
echo -e "${GREEN}削除処理が完了しました。${NC}"
echo ""

# インストールフェーズ
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    MCPサーバーのインストール処理      ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 成功・失敗カウンター
SUCCESS_COUNT=0
FAILED_COUNT=0

# 結果を一時ファイルに保存（サブシェルでのカウンター問題を回避）
TEMP_FILE=$(mktemp)
echo "0" > "$TEMP_FILE.success"
echo "0" > "$TEMP_FILE.failed"

# 各MCPサーバーをループ処理
jq -r '.mcpServers | to_entries | .[] | @base64' "$JSON_FILE" | while read -r entry; do
    # Base64デコード
    _jq() {
        echo "$entry" | base64 -d | jq -r "$1"
    }
    
    # サーバー名と設定を取得
    SERVER_NAME=$(_jq '.key')
    CONFIG=$(_jq '.value')
    
    echo -e "${BLUE}処理中: $SERVER_NAME${NC}"
    
    # JSON設定の構築
    COMMAND=$(echo "$CONFIG" | jq -r '.command')
    ARGS=$(echo "$CONFIG" | jq -c '.args // []')
    ENV=$(echo "$CONFIG" | jq -c '.env // {}')
    
    # claude mcp add-json用のJSONを構築
    if [ "$ENV" != "{}" ]; then
        # 環境変数がある場合
        JSON_CONFIG=$(jq -n \
            --arg cmd "$COMMAND" \
            --argjson args "$ARGS" \
            --argjson env "$ENV" \
            '{"type": "stdio", "command": $cmd, "args": $args, "env": $env}')
    else
        # 環境変数がない場合
        JSON_CONFIG=$(jq -n \
            --arg cmd "$COMMAND" \
            --argjson args "$ARGS" \
            '{"type": "stdio", "command": $cmd, "args": $args}')
    fi
    
    # コマンドの実行
    echo -e "  実行: claude mcp add-json $SERVER_NAME ... -s user"
    
    if claude mcp add-json "$SERVER_NAME" "$JSON_CONFIG" -s user 2>/dev/null; then
        echo -e "  ${GREEN}✓ 成功${NC}"
        SUCCESS_COUNT=$(($(cat "$TEMP_FILE.success") + 1))
        echo "$SUCCESS_COUNT" > "$TEMP_FILE.success"
    else
        echo -e "  ${RED}✗ 失敗${NC}"
        FAILED_COUNT=$(($(cat "$TEMP_FILE.failed") + 1))
        echo "$FAILED_COUNT" > "$TEMP_FILE.failed"
    fi
    
    echo ""
done

# 結果の表示
SUCCESS_COUNT=$(cat "$TEMP_FILE.success")
FAILED_COUNT=$(cat "$TEMP_FILE.failed")

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}          インストール結果             ${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}成功: $SUCCESS_COUNT${NC}"
echo -e "${RED}失敗: $FAILED_COUNT${NC}"
echo ""

# 一時ファイルを削除
rm -f "$TEMP_FILE" "$TEMP_FILE.success" "$TEMP_FILE.failed"

# インストール済みMCPサーバーの一覧表示
echo -e "${YELLOW}インストール済みMCPサーバー一覧:${NC}"
claude mcp list

echo ""
echo -e "${GREEN}処理が完了しました！${NC}"
