<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FVセクション表示確認</title>
    <link rel="stylesheet" href="http://localhost:20085/wp-content/themes/appmart/assets/css/owned-media.css?v=1.0.3">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: "Noto Sans JP", sans-serif;
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <p>画面幅: <span id="screen-width"></span>px</p>
        <p>CSSファイル: owned-media.css</p>
        <p>背景SVG: pc_fv.svg / sp_fv.svg</p>
    </div>

    <div class="owned-media">
        <!-- FVセクションのみ表示 -->
        <section class="owned-media__fv">
            <div class="owned-media__fv-bg">
                <img src="http://localhost:20085/wp-content/themes/appmart/assets/images/s-owned/pc_fv.svg" alt="" class="owned-media__pc" />
                <img src="http://localhost:20085/wp-content/themes/appmart/assets/images/s-owned/sp_fv.svg" alt="" class="owned-media__sp" />
            </div>
            <div class="owned-media__fv-container">
                <div class="owned-media__fv-left">
                    <div class="owned-media__fv-catch">
                        <div class="owned-media__fv-catch-main">
                            <span class="owned-media__fv-catch-label">オウンドメディア</span>
                            <span class="owned-media__fv-catch-sub">の運用</span>
                        </div>
                        
                        <div class="owned-media__fv-catch-target">
                            <div class="owned-media__fv-catch-target-decorations">
                                <span class="owned-media__fv-catch-target-circle"></span>
                                <span class="owned-media__fv-catch-target-circle"></span>
                                <span class="owned-media__fv-catch-target-circle"></span>
                                <span class="owned-media__fv-catch-target-circle"></span>
                                <span class="owned-media__fv-catch-target-circle"></span>
                            </div>
                            <span class="owned-media__fv-catch-target-main">Webマーケティングひとり担当</span>
                            <span class="owned-media__fv-catch-target-sub">兼任担当者へ</span>
                            <div class="owned-media__fv-catch-target-lines">
                                <span class="owned-media__fv-catch-target-line"></span>
                                <span class="owned-media__fv-catch-target-line"></span>
                            </div>
                        </div>
                        
                        <div class="owned-media__fv-catch-action">
                            <span class="owned-media__fv-catch-maru">まる</span>
                            <span class="owned-media__fv-catch-goto">ごと</span>
                            <span class="owned-media__fv-catch-text">代行します</span>
                        </div>
                    </div>
                </div>
                <div class="owned-media__fv-right">
                    <div class="owned-media__fv-form">
                        <div class="owned-media__fv-form-wrapper">
                            <div style="padding: 40px; text-align: center;">
                                <p style="color: #7DC8B6; font-size: 18px; font-weight: bold;">お問い合わせフォーム</p>
                                <p style="color: #666; font-size: 14px;">（外部フォームをiframeで表示）</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        document.getElementById('screen-width').textContent = window.innerWidth;
        window.addEventListener('resize', function() {
            document.getElementById('screen-width').textContent = window.innerWidth;
        });
    </script>
</body>
</html>