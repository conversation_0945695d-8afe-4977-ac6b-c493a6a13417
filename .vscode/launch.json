{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}/mount_wordpress",
      "runtimeArgs": [
        // "--load-extension=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Extensions\\eadndfjplgieldjbigjakmdgkmoaaaoc\\1.6.1_0"
        // "--load-extension=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Extensions\\aeblfdkhhhdcdjpifhhbdiojplfjncoa\\8.10.54.22_0"
        // "--load-extension=C:\\Users\\<USER>\\OneDrive\\tool\\xdebug-extension\\src"
      ]
    },
    {
      "name": "Listen for Xdebug",
      "type": "php",
      "request": "launch",
      "port": 9003,
      "hostname": "0.0.0.0",
      "pathMappings": {
        "/var/www/html": "${workspaceRoot}/mount_wordpress"
      }
    }
  ]
}
