#!/bin/bash

# MCP Manager インストールスクリプト
# グローバルコマンドとしてmcp-managerを使用可能にします

# 色付けのための定数
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# インストール先の設定
INSTALL_DIR="$HOME/.local/bin"
SCRIPT_NAME="mcp-manager"
SOURCE_FILE="$(dirname "$0")/mcp-manager.sh"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    MCP Manager Installation            ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# ソースファイルの確認
if [ ! -f "$SOURCE_FILE" ]; then
    echo -e "${RED}エラー: mcp-manager.sh が見つかりません。${NC}"
    exit 1
fi

# インストールディレクトリの作成
if [ ! -d "$INSTALL_DIR" ]; then
    echo -e "${YELLOW}インストールディレクトリを作成: $INSTALL_DIR${NC}"
    mkdir -p "$INSTALL_DIR"
fi

# スクリプトのコピー
echo -e "${YELLOW}スクリプトをインストール: $INSTALL_DIR/$SCRIPT_NAME${NC}"
cp "$SOURCE_FILE" "$INSTALL_DIR/$SCRIPT_NAME"
chmod +x "$INSTALL_DIR/$SCRIPT_NAME"

# PATHの確認と設定
if [[ ":$PATH:" != *":$INSTALL_DIR:"* ]]; then
    echo ""
    echo -e "${YELLOW}PATHに $INSTALL_DIR を追加する必要があります。${NC}"
    echo ""
    echo "以下のコマンドを実行するか、シェルの設定ファイルに追加してください："
    echo ""
    
    # シェルの種類を判定
    if [ -n "$ZSH_VERSION" ]; then
        SHELL_CONFIG="$HOME/.zshrc"
        echo "echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.zshrc"
        echo "source ~/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        SHELL_CONFIG="$HOME/.bashrc"
        echo "echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.bashrc"
        echo "source ~/.bashrc"
    else
        echo "export PATH=\"\$HOME/.local/bin:\$PATH\""
    fi
    
    echo ""
    read -p "自動的にPATHを追加しますか？ (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -n "$SHELL_CONFIG" ]; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$SHELL_CONFIG"
            echo -e "${GREEN}✓ $SHELL_CONFIG にPATHを追加しました。${NC}"
            echo -e "${YELLOW}  新しいターミナルを開くか、'source $SHELL_CONFIG' を実行してください。${NC}"
        fi
    fi
fi

echo ""
echo -e "${GREEN}✓ インストールが完了しました！${NC}"
echo ""
echo "使用方法:"
echo "  mcp-manager help    # ヘルプを表示"
echo ""

# エイリアスの提案
echo -e "${BLUE}便利なエイリアスの提案:${NC}"
echo ""
echo "以下をシェルの設定ファイルに追加すると便利です："
echo ""
echo "alias mcp='mcp-manager'              # 短縮形"
echo "alias mcpi='mcp-manager install'      # インストール"
echo "alias mcpl='mcp-manager list'         # プロジェクトのMCP一覧"
echo "alias mcpla='mcp-manager list-all'    # 全プロジェクトのMCP一覧"
echo ""