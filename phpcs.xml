<?xml version="1.0"?>
<ruleset name="CustomWordPress">
    <description>WordPress標準を継承し、特定のルールを除外するカスタムルールセット。</description>

    <!-- WordPressのコーディング標準を継承 -->
    <rule ref="PSR12">
        <!-- error -->
        <exclude name="Generic.WhiteSpace.DisallowSpaceIndent.SpacesUsed"/> // インデントにスペースを使用することを許可
        <exclude name="Squiz.PHP.CommentedOutCode.Found"/> // コメントアウトされたコードを許可
        <exclude name="Squiz.Commenting.FileComment.MissingPackageTag"/> // @packageの省略を許可
        <exclude name="Squiz.Commenting.InlineComment.InvalidEndChar"/> // コメントの終了文字を許可
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentFullStop"/> // コメントの終了文字を許可
        <exclude name="Generic.Commenting.DocComment.ShortNotCapital"/> // コメント先頭文字の小文字を許可
        <exclude name="WordPress.Security.EscapeOutput.OutputNotEscaped"/> // 未エスケープを許可
        <exclude name="WordPress.WP.EnqueuedResources.NonEnqueuedStylesheet"/> // linkタグのスタイルシートを許可
        <exclude name="WordPress.WP.EnqueuedResourceParameters.NoExplicitVersion"/> // WordPressバージョンの省略を許可
        <exclude name="Generic.WhiteSpace.ScopeIndent.Incorrect"/>
        <exclude name="Squiz.Commenting.FileComment.Missing"/>

        <!-- warning -->
        <exclude name="WordPress.WP.EnqueuedResourceParameters.MissingVersion"/> // WordPressバージョンの省略を許可
        <exclude name="WordPress.DB.SlowDBQuery.slow_db_query_meta_query"/>
        <exclude name="PSR1.Files.SideEffects.FoundWithSymbols"/>
        <exclude name="Generic.Files.LineLength.TooLong"/>
        <exclude name="WordPress.DB.SlowDBQuery.slow_db_query_tax_query"/> // コストの重いクエリを許可
        <exclude name="PSR2.Files.EndFileNewline.NoneFound"/> <!-- ファイル末尾の改行を無視 -->

        <include name="Generic.WhiteSpace.DisallowTabIndent.TabsUsed"/> <!-- タブの使用を禁止 -->

    </rule>
    <file>
        <include-pattern>mount_wordpress/wp-content/themes/appmart/.*</include-pattern>
    </file>
</ruleset>