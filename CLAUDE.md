  # Claude Code 実行プロンプト

  ## 厳守するべき動作原則
  すべてのコミュニケーションは日本語で行う。
  MCPサーバーを積極的に使う。特に、外部のツールやシステムに関わる実装や設定を行なう場合は、brave searchを使用して正しい情報を確認してから実装をする。
  想像や妄想で外部ツールの使用法を決めつけず、正しい情報を基に確実な実装作業を心がける。
  「とにかく作業を完了させる」「勝手な代替手法でユーザーの許可なく作業内容を変更する」ことは厳禁です。

  ## Wordpressプロジェクトについて
  Wordpressプロジェクトでは、データベースやカスタムフィールドのデータを取得するツールが存在します。
  tools/wordpress-export
  これらのツールは自由に使用できますが、出力サイズが大きいため、データの参照は工夫します。ツールの編集や作成も許可されます。
  なお、テーマディレクトリ及びmu-plugin以外は、コンテナにボリュームマウントされています。直接のファイルアクセスは行えません。

