#!/bin/bash

# MCP GitHub Sync - MCP設定をGitHub Secretsと同期するツール
# 
# 使用方法:
#   mcp-github-sync push [project]    # MCP設定をGitHub Secretsにアップロード
#   mcp-github-sync pull [project]    # GitHub SecretsからMCP設定を復元
#   mcp-github-sync setup             # GitHub CLIの設定確認

# 色付けのための定数
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# コマンドとプロジェクトの取得
COMMAND="${1:-help}"
PROJECT="${2:-$(basename $(pwd))}"
PROJECT_DIR="$(pwd)"

# GitHub CLIの確認
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        echo -e "${RED}エラー: GitHub CLI (gh) がインストールされていません。${NC}"
        echo ""
        echo "インストール方法:"
        echo "  macOS: brew install gh"
        echo "  Ubuntu/Debian: sudo apt install gh"
        echo "  その他: https://cli.github.com/"
        exit 1
    fi
    
    # 認証状態の確認
    if ! gh auth status &> /dev/null; then
        echo -e "${RED}エラー: GitHub CLIが認証されていません。${NC}"
        echo ""
        echo "以下のコマンドで認証してください:"
        echo "  gh auth login"
        exit 1
    fi
}

# GitHubリポジトリ情報の取得
get_repo_info() {
    local repo_info=$(gh repo view --json nameWithOwner 2>/dev/null)
    if [ $? -ne 0 ]; then
        echo -e "${RED}エラー: GitHubリポジトリが見つかりません。${NC}"
        echo "現在のディレクトリがGitリポジトリであることを確認してください。"
        exit 1
    fi
    echo "$repo_info" | jq -r '.nameWithOwner'
}

# MCP設定をGitHub Secretsにプッシュ
push_to_github() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Push MCP Config to GitHub Secrets   ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_gh_cli
    
    local repo=$(get_repo_info)
    local config_file=".claude/mcp-config.json"
    local secret_name="MCP_CONFIG_${PROJECT^^}"
    
    # 設定ファイルの確認
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}エラー: MCP設定ファイルが見つかりません: $config_file${NC}"
        echo "mcp-manager init でプロジェクトを初期化してください。"
        exit 1
    fi
    
    echo -e "${CYAN}リポジトリ: $repo${NC}"
    echo -e "${CYAN}プロジェクト: $PROJECT${NC}"
    echo -e "${CYAN}Secret名: $secret_name${NC}"
    echo ""
    
    # Base64エンコード
    local encoded=$(base64 -w 0 "$config_file" 2>/dev/null || base64 "$config_file")
    
    # GitHub Secretの設定
    echo -e "${YELLOW}GitHub Secretを設定中...${NC}"
    if echo "$encoded" | gh secret set "$secret_name" --repo "$repo"; then
        echo -e "${GREEN}✓ 設定しました: $secret_name${NC}"
        
        # 環境変数のリストも保存（環境変数名のみ）
        local env_vars=$(jq -r '.mcpServers | to_entries | .[].value.env // {} | keys[]' "$config_file" | sort -u)
        if [ -n "$env_vars" ]; then
            echo ""
            echo -e "${YELLOW}必要な環境変数:${NC}"
            echo "$env_vars" | while read -r var; do
                echo "  • $var"
            done
            
            # 環境変数リストも保存
            echo "$env_vars" | gh secret set "${secret_name}_ENV_VARS" --repo "$repo"
        fi
    else
        echo -e "${RED}✗ 設定に失敗しました${NC}"
        exit 1
    fi
    
    echo ""
    echo -e "${GREEN}完了！${NC}"
    echo ""
    echo "GitHub Actionsで使用するには、ワークフローに以下を追加:"
    echo ""
    cat << EOF
- name: Restore MCP Configuration
  run: |
    mkdir -p .claude
    echo "\${{ secrets.$secret_name }}" | base64 -d > .claude/mcp-config.json
    
- name: Install MCP Servers
  run: |
    # Claude CLIのインストール（必要に応じて）
    # npm install -g @anthropic-ai/claude-cli
    
    # mcp-managerを使用してMCPサーバーをインストール
    curl -sSL https://raw.githubusercontent.com/$repo/main/mcp-manager.sh -o mcp-manager.sh
    chmod +x mcp-manager.sh
    ./mcp-manager.sh install
EOF
}

# GitHub SecretsからMCP設定をプル
pull_from_github() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}   Pull MCP Config from GitHub Secrets  ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_gh_cli
    
    local repo=$(get_repo_info)
    local secret_name="MCP_CONFIG_${PROJECT^^}"
    local config_file=".claude/mcp-config.json"
    
    echo -e "${CYAN}リポジトリ: $repo${NC}"
    echo -e "${CYAN}プロジェクト: $PROJECT${NC}"
    echo -e "${CYAN}Secret名: $secret_name${NC}"
    echo ""
    
    # Secretの取得とデコード
    echo -e "${YELLOW}GitHub Secretを取得中...${NC}"
    
    # GitHub APIを使用してSecretの存在確認
    if ! gh api "repos/$repo/actions/secrets/$secret_name" &> /dev/null; then
        echo -e "${RED}エラー: Secret '$secret_name' が見つかりません。${NC}"
        echo "先に mcp-github-sync push でアップロードしてください。"
        exit 1
    fi
    
    echo -e "${YELLOW}注意: GitHub Secretsの値は直接取得できません。${NC}"
    echo ""
    echo "GitHub Actionsワークフロー内で復元するか、"
    echo "ローカルにバックアップしたファイルから復元してください:"
    echo "  mcp-manager import <backup-file>"
}

# セットアップとヘルプ
setup_github() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    GitHub Integration Setup            ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # GitHub CLIの確認
    echo -n "GitHub CLI (gh): "
    if command -v gh &> /dev/null; then
        echo -e "${GREEN}✓ インストール済み${NC}"
        
        echo -n "認証状態: "
        if gh auth status &> /dev/null; then
            echo -e "${GREEN}✓ 認証済み${NC}"
            local user=$(gh api user --jq '.login')
            echo "  ユーザー: $user"
        else
            echo -e "${RED}✗ 未認証${NC}"
            echo ""
            echo "以下のコマンドで認証してください:"
            echo "  gh auth login"
        fi
    else
        echo -e "${RED}✗ 未インストール${NC}"
        echo ""
        echo "GitHub CLIをインストールしてください:"
        echo "  https://cli.github.com/"
    fi
    
    echo ""
    
    # 現在のリポジトリ情報
    echo -n "現在のリポジトリ: "
    if repo=$(get_repo_info 2>/dev/null); then
        echo -e "${GREEN}$repo${NC}"
    else
        echo -e "${RED}Gitリポジトリではありません${NC}"
    fi
}

# ヘルプ表示
show_help() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}      MCP GitHub Sync - Help            ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo "使用方法:"
    echo "  mcp-github-sync push [project]    # MCP設定をGitHub Secretsにアップロード"
    echo "  mcp-github-sync pull [project]    # GitHub SecretsからMCP設定を復元（情報のみ）"
    echo "  mcp-github-sync setup             # GitHub CLIの設定確認"
    echo "  mcp-github-sync help              # このヘルプを表示"
    echo ""
    echo "プロジェクト名を省略した場合は、現在のディレクトリ名が使用されます。"
    echo ""
    echo "必要な準備:"
    echo "1. GitHub CLI (gh) のインストール"
    echo "2. gh auth login で認証"
    echo "3. リポジトリへの書き込み権限"
}

# メイン処理
case "$COMMAND" in
    push)
        push_to_github
        ;;
    pull)
        pull_from_github
        ;;
    setup)
        setup_github
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}エラー: 不明なコマンド '$COMMAND'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac